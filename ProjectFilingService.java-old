package com.shca.module.project.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.MetadataRequest;
import com.shca.cromwell.api.MetadataResponse;
// import com.shca.cromwell.api.WorkflowsRequest;
// import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.cromwell.vo.Workflow;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.Page;
import com.ywang.sql.support.Db;
// import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;

import java.io.*;
import java.util.*;

public class ProjectFilingService {

    private final static Logger LOGGER = Logger.getLogger(ProjectFilingService.class);

    private Db db;

    private CromwellClient client;

    private String fileTransferWDL;

    private SysConfigManager config = SysConfigManager.getInstance();

    private File filingDir = new File(config.getValue("workspace.filing_path"));

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    public void setDb(Db db) {
        this.db = db;
    }

    public void setFileTransferWDL(String fileTransferWDL) {
        this.fileTransferWDL = fileTransferWDL;
    }

    /**
     * @param projectNo
     * @return
     */

    public Map<String, Object> task() {
        // SysConfigManager config = SysConfigManager.getInstance();
        // File filingDir = new File(config.getValue("workspace.filing_path"));
//        __deleteEmptyDir( filingDir);
        Map<String, Object> ret = new HashMap<>();

        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -100); // -100天策略需要调整
        Date holdDate = calendar.getTime();

        List<Map<String, Object>> projects = db.select("SELECT project_name FROM shca_project_project " +
                        " WHERE lims is not null and is_filing=? and create_time >=? and create_time <=? and status=? " +
                        " and project_name not in (select project_name from shca_project_project where is_filing != ?)" +
                        " order by id   "
                , new Page( 10), '8', ConvertUtils.str2Date("2021-02-01"), holdDate, "finished", '0');

        if (!projects.isEmpty()) {
            for (Map<String, Object> project : projects) {
                LOGGER.info( "filing - " + project.get( "project_name"));
                System.out.println( "filing2 - " + project.get( "project_name"));
                ret.put((String) project.get("project_name"), filing((String) project.get("project_name")));
            }
        }
        return ret;
    }

    public void deleteEmptyDir() {
        __deleteEmptyDir( new File( ""));
    }

    private void __deleteEmptyDir(File file) {
        if (file == null) {
            return;
        }
        if (file.isFile()) {
            return;
        }
        for (File _file : file.listFiles()) {
            __deleteEmptyDir( _file);
        }
        if (file.listFiles().length == 0) {
            file.delete();
        }
    }

    public ProjectFilingService() {
        super();
    }

    /**
     * @param projectName
     * @return
     */
    // public Map<String, Object> filing(@MethodParam(name = "project_name") String projectName) {
    public Map<String, Object> filing(String projectName) {
        int errCode = 1000;
        String errMsg = "归档错误: ";
        Date now = new Date();
        String defaultHomePath = "/mydata/shca";
        Map<String, Object> ret = new HashMap<>();
        SysConfigManager config = SysConfigManager.getInstance();
        BufferedWriter bufferedWriter = null;

        try {
            // get a project name which is not filing and select the last one by id order.
            Map<String, Object> project = db.selectUnique("SELECT id,project_no,project_name,sample_name_0,sample_name_1,is_filing,cancer_kind " +
                    " ,(select count(*) from shca_project_project b where (b.sample_name_0=a.sample_name_0 or b.sample_name_1=a.sample_name_0) and status not in (?) and is_filing=?) num_0 " +
                    " ,(select count(*) from shca_project_project b where (b.sample_name_0=a.sample_name_1 or b.sample_name_1=a.sample_name_1) and status not in (?) and is_filing=?) num_1 " +
                    " ,create_time " +
                    " from shca_project_project a where project_name=? and status=? order by id desc ", "deleted", '0', "deleted", '0', projectName, "finished");

            // errCode = 1010;
            // if ((boolean) project.get("is_filing")) {
            //     ret.put("id", project.get("id"));
            //     ret.put("errcode", errCode);
            //     ret.put("errmsg", errMsg + "项目已归档[" + projectName + "]");
            //     return ret;
            // }

            errCode = 999;
            if (CollectionUtils.isEmpty( project )) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + "项目不存在[" + projectName + "]");
                return ret;
            }

            File filingDir = new File( this.filingDir + "/" + ConvertUtils.date2Str( (Date) project.get("create_time"), "yyyyMM") + "/" + project.get("project_name"));
            if (!filingDir.exists()) {
                filingDir.mkdirs();
            }

            File logDir = new File( defaultHomePath  + "/filing_log/" + ConvertUtils.date2Str((Date) project.get("create_time"), "yyyyMM") + "/" + project.get("project_name"));
            if (!logDir.exists()) {
                logDir.mkdirs();
            }

            errCode = 1020;
            File logFile = new File(logDir.getAbsolutePath() + "/filing_log.txt");
            File workflowFile = new File(logDir.getAbsolutePath() + "/filing_workflow.txt");
            File backupDir = new File(defaultHomePath + "/backup/" + ConvertUtils.date2Str((Date) project.get("create_time"), "yyyyMM") + "/" + project.get("project_name"));

            bufferedWriter = new BufferedWriter(new FileWriter(logFile, true));

            errCode = 1013;
            File projectDir = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name"));
            if (!projectDir.exists()) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + "项目文件夹不存在[" + projectName + "]: " + projectDir.getAbsolutePath());
                bufferedWriter.write("项目文件夹不存在[" + projectName + "]: " + projectDir.getAbsolutePath() + "\n");
                return ret;
            }

            errCode = 1021;
            // get all the fastq files by sample name in cancer file table.
            List<Map<String, Object>> fastqs = db.select("select id,file_name,store_path from shca_project_cancer_file where sample_name in (?,?) "
                    , project.get("sample_name_0"), project.get("sample_name_1"));

            if (workflowFile.exists()) {
                BufferedReader reader = new BufferedReader(new FileReader(workflowFile));
                String workflowId = reader.readLine();
                reader.close();

                if (!workflowId.isBlank()) {
                    MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
                    Workflow workflow = metadataResponse.getWorkflow();

                    if ("Succeeded".equals(workflow.getStatus())) {
                        File tar = new File(filingDir.getAbsolutePath() + "/" + project.get("project_name") + ".tar");
                        bufferedWriter.write("项目压缩文件是否存在[" + tar.getAbsolutePath() + "]: " + tar.exists() + "\n");
                        // after filing, set all of the project's is_filing record with this project_name to '4', means duplicated
                        db.update("update shca_project_project set update_time=?,update_username=?,is_filing=? where project_name=? "
                                , now, "ProjectFilingService.filing", '4', project.get("project_name"));

                        // change the right record's is_filing from '4' to '1'
                        db.update("UPDATE shca_project_project set update_time=?, update_username=?, is_filing=? WHERE id=?"
                                , new Date(), "ProjectFilingService.filing", '1', project.get("id") );

                        bufferedWriter.write("归档完成: " + ConvertUtils.date2Str(now, "yyyy-MM-dd HH:mm:ss") + "\n");
                    }

                    if ("Failed".equals( workflow.getStatus())) {
                        db.update("update shca_project_project set update_time=?,update_username=?,is_filing=? where project_name=? "
                                , now, "ProjectFilingService.filing", '9', project.get("project_name"));
                        bufferedWriter.write("归档失败: \n");
                        bufferedWriter.write( JSON.toJSONString( workflow, SerializerFeature.PrettyFormat).replaceAll("\\t", "    ") + "\n");
                    }

                    ret.put("errcode", 10);
                    ret.put("errmsg", "ok");
                    ret.put("id", project.get("id"));
                    ret.put("workflow_id", workflowId);
                    ret.put("workflow", workflow);
                    ret.put( "workflow_path", workflowFile.getAbsolutePath());
                    return ret;
                }
            }

            bufferedWriter.write("开始归档: " + ConvertUtils.date2Str(now, "yyyy-MM-dd HH:mm:ss") + "\n");

            errCode = 1022;
            LOGGER.info("moving fasstqs of Project [ " + projectName + " ]");
            // List<String> fastqList = new ArrayList<>();
            // for (Map<String, Object> fastq : fastqs) {
            //     bufferedWriter.write("移动Fastq文件:" + fastqs.size() + "\n");
            //     File target = new File(filingDir.getAbsolutePath() + "/" + fastq.get("file_name"));
            //     File source = new File((String) fastq.get("store_path"));
            //     if (source.exists()) {
            //         bufferedWriter.write("更新Fastq存储路径[" + fastq.get("file_name") + "]:" + target.getAbsolutePath() + "\n");
            //         cmd("mv -f " + source.getAbsolutePath() + " " + target.getAbsolutePath(), bufferedWriter);
            //         db.update("update shca_project_cancer_file set update_time=?,update_username=?,store_path=? where id=? "
            //                 , now, "ProjectFilingService.filing", target.getAbsolutePath(), fastq.get("id"));
            //     }

                // fastqList.add((String) fastq.get("store_path"));
            // }

            errCode = 1030;
            File workflowSource = new File(fileTransferWDL);
            if (!workflowSource.exists()) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + "WDL文件不存在 - " + workflowSource.getAbsolutePath());
                return ret;
            }

            String cancerName = "";
            String cancerKind = (String) project.get("cancer_kind");
            // todo:
            if ("breast_cancer".equals(cancerKind)) {
                cancerName = "breast";
            }
            if ("colorectal_cancer".equals(cancerKind)) {
                cancerName = "colon";
            }
            if ("gastric".equals(cancerKind)) {
                cancerName = "gastric";
            }
            if ("pancreatic".equals(cancerKind)) {
                cancerName = "pancreatic";
            }
            if ("hepa".equals(cancerKind)) {
                cancerName = "hepa";
            }
            if ("gist".equals(cancerKind)) {
                cancerName = "gist";
            }
            if ("fuke".equals(cancerKind)) {
                cancerName = "fuke";
            }
            if ("pan".equals(cancerKind)) {
                cancerName = "pan";
            }
            if ("bone".equals(cancerKind)) {
                cancerName = "bone";
            }
            if ("neuro".equals(cancerKind)) {
                cancerName = "neuro";
            }
            if ("ctDNA_pan-cancer".equals(cancerKind)) {
                cancerName = "ctDNA";
            }

            if (cancerKind.startsWith("peixi") || cancerKind.endsWith("peixi")) {
                cancerName = "peixi";
            }

            // if ("breast_peixi".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixipancreatic".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixipan".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixifuke".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixihepa".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixicolon".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixipanMLPA".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }
            // if ("peixigastric".equals(cancerKind)) {
            //     cancerName = "peixi";
            // }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fileBackup.ACTION_WF", "backup");
            jsonObject.put("fileBackup.cancerName_WF", cancerName);
            jsonObject.put("fileBackup.projectHomePath_WF", config.getValue("workspace.project_path"));
            jsonObject.put("fileBackup.projectName_WF", project.get("project_name"));
            jsonObject.put("fileBackup.normalSampeName_WF", project.get("sample_name_0"));
            jsonObject.put("fileBackup.targetPath_WF", filingDir.getAbsolutePath());
            jsonObject.put("fileBackup.tmpPath_WF", backupDir.getAbsolutePath());

            if (StringUtils.isBlank((String)project.get("sample_name_1"))) {
                jsonObject.put("fileBackup.tumorSampleName_WF", "");
            } else {
                jsonObject.put("fileBackup.tumorSampleName_WF", project.get("sample_name_1"));
            }

            LOGGER.info("project: " + projectName + "is preparing json file ...., and cancerName is: " + cancerName );
            File prettyJson = new File(logDir.getAbsolutePath() + "/fileBackup_" + ConvertUtils.date2Str(new Date(), "yyyyMMddHHmm") + ".json");
            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter(prettyJson));
            prettyWriter.write(JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat).replaceAll("\\t", "    "));
            bufferedWriter.write("执行WDL:\n" + JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat).replaceAll("\\t", "    ") + "\n");
            prettyWriter.close();

            // WorkflowsResponse response = client.send(new WorkflowsRequest(workflowSource, prettyJson, null, null));
            // bufferedWriter.write("Workflow ID: " + response.getId() + "\n");
            // BufferedWriter workflowBufferWriter = new BufferedWriter(new FileWriter(workflowFile));
            // workflowBufferWriter.write(response.getId());
            // workflowBufferWriter.close();

            LOGGER.info("project: " + projectName + "is sending task to cromwell ....");

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");
            ret.put("id", project.get("id"));

            return ret;
        } catch (Exception e) {
            LOGGER.error(LogUtils.toString(e));
            try {
                bufferedWriter.write("异常错误: \n" + LogUtils.toString(e) + "\n");
            } catch (Exception except) {
                except.printStackTrace();
            }

            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());

            return ret;
        } finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void clearSampleName() {
        List<Map<String,Object>> samples = db.select( "select id,sample_name from shca_project_cancer");
        Date now = new Date();
        for (Map<String,Object> sample : samples) {
            File dir = new File( "/mydata/shca/samples/" + sample.get( "sample_name"));
            System.out.println( dir.getAbsolutePath() + " = " + dir.exists());
            if (!dir.exists()) {
                db.update( "update shca_project_cancer set delete_time=? where id=? ", now, sample.get( "id"));
            }
        }
    }

    private void cmd(String cmd, BufferedWriter bufferedWriter) throws Exception {
        String result = Cmd.exec( cmd);
        bufferedWriter.write(cmd + ": " + result + "\n");
    }
}
