<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>
    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <!-- 内容区域 -->
    <div class="tpl-content-wrapper" >
        <div id="config-grid"></div>
    </div>


</div>

<div id="dialog"></div>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        癌症种类：
        <select id="cancer_kind" name="cancer_kind" class="filter-input" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;">
            <option value="">请选择癌症种类</option>
            <c:forEach items="${kindList}" var="kind">
                <option value="${kind['name']}">${kind['value']}</option>
            </c:forEach>
        </select>
        <%--        <input id="cancer_kind" class="filter-input" type="text" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;" />--%>
        <div id="query"  class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-search"></i> 查询
        </div>
        <%--<div id="save" class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-plus"></i> 添加
        </div>--%>
        <div id="deleteBtn" class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
</script>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>

<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    var row = null;

    $(function(){
        function editColumn(dataItem) {
            console.log( dataItem)
            return false;
        }

        var $grid = $("#config-grid").kendoGrid({
            dataSource: {
                transport: {
                    read: {url:"${CTX}/system_config_dataGrid.json",dataType: "json"},
                    update: function(options) {
                    },
                    parameterMap: function (options, operation) {
                        if (operation == "update" && options.models) {
                            $.ajax({
                                url: "${CTX}/system_config_update.json",
                                type: "post",
                                dataType: "json",
                                async: false,
                                data: options.models[0],
                                success: function(ret) {
                                    if (ret.errcode != 0) {
                                        alert( "[" + ret.errcode + "]: " + ret.errmsg);
                                        return;
                                    }
                                    alert( "系统配置设置成功")
                                }
                            })
                        }
                    }
                },
                batch: true,
                autoSync: true,
                serverPaging: false,
                serverFiltering: true,
                group: {
                    field: "配置组",
                    dir: "desc"
                },
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id",
                    }
                }
            },
         //   toolbar: kendo.template($("#toolbar").html()),

            height: $(window).height() - 56,
            sortable: false,
            groupable: false,
            editable: true,
            pageable: false,
            filterable: {
                extra: false
            },
            columns: [
                {field:"config_title",title:"参数名称",width:"320px",editable:function(){return false;},filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"config_value",title:"参数配置",editable:function(dataItem){return dataItem.can_edit;},filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
                // {command: [{text:"详情",click:view}], title: "操作", width: "120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });




    });




</script>

</body>

</html>