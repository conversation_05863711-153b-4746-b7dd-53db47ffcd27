<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        #config-grid .k-grid-toolbar {
            padding: 10px 8px;
            font-size: 14px;
            height: 25px;
        }
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <!-- 内容区域 -->
    <div class="tpl-content-wrapper" >
        <div id="config-grid"></div>
    </div>


</div>

<div id="dialog"></div>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div id="add" class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-plus"></i> 添加
        </div>
        <div id="saveBtn" class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-save"></i> 保存
        </div>
        <div id="viewBtn" class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-file"></i> 查看文件
        </div>
    </div>
</script>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56- 2).find( ".widget-body").height( height - 56 - 2 - 46);
    }
</script>

<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    var row = null;

    $(function(){
        function editColumn(dataItem) {
            console.log( dataItem)
            return false;
        }

        var $grid = $("#config-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/system_cromwell_config.json<c:if test="${not empty param['id']}">?id=${param['id']}</c:if>",
                    dataType: "json",
                    update: function(options) {
                    },
                    parameterMap: function (options, operation) {
                        //alert(operation);
                        if (operation == "update" && options.models) {
                           /* $.ajax({
                                url: "${CTX}/system_cromwell_update.json",
                                type: "post",
                                dataType: "json",
                                async: false,
                                data: options.models[0],
                                success: function(ret) {
                                    if (ret.errcode != 0) {
                                        alert( "[" + ret.errcode + "]: " + ret.errmsg);
                                        return;
                                    }
                                    alert( "系统配置设置成功")
                                }
                            })*/
                        }
                    }
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },

            toolbar: kendo.template($("#toolbar").html()),

            height: $(window).height() - 56,
            editable: true,
            filterable: {
                extra: false
            },
            columns: [
                {field:"key",title:"参数名称",width:"320px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"value",title:"参数配置",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
                // {command: [{text:"详情",click:view}], title: "操作", width: "120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });

        $("#add").click(function(){
            $('#config-grid').data('kendoGrid').addRow();
        });

        $("#saveBtn").click(function(){
            var $grid =  $('#config-grid').data('kendoGrid');
            var allRows = $grid.items();
            var json = new Object();
            for(var i=0;i<allRows.length;i++){
                var dataItem = $grid.dataItem(allRows[i]);
                json[dataItem.key] = dataItem.value;
            }
            $.ajax({
                url: "${CTX}/system_cromwell_update.json",
                type: "post",
                dataType: "json",
                async: false,
                data: {
                    <c:if test="${not empty param['id']}">"id":"${param['id']}",</c:if>
                    "content":JSON.stringify(json)
                },
                success: function(ret) {
                    if (ret.errcode != 0) {
                        alert( "[" + ret.errcode + "]: " + ret.errmsg);
                        return;
                    }
                    alert( "配置设置成功");
                    var grid = $("#config-grid").data("kendoGrid");
                    grid.dataSource.query();
                    //window.location.href="${CTX}/system_cromwell_list.htm";
                }
            })
        });


        $("#viewBtn").click(function(){
            $("#dialog").kendoWindow({
                width: "680px",
                height:"480px",
                scrollable: true,
                content: "${CTX}/system_cromwell_viewFile.htm<c:if test="${not empty param['id']}">?id=${param['id']}</c:if>",
                iframe: true,
                visible: false,
                actions: ["Refresh", "Maximize", "Close"],
            }).data("kendoWindow").center().open();
        });

    });





</script>

</body>

</html>