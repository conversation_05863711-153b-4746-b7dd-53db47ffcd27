<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .k-grid td {
            overflow: hidden;
            text-overflow: ellipsis;

        }

        .warning {color: #F37B1D}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <!-- 内容区域 -->
    <div class="tpl-content-wrapper" >
        <div id="project-grid"></div>
    </div>


</div>

<div id="dialog"></div>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        癌症种类：
        <select id="cancer_kind" name="cancer_kind" class="filter-input" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;">
        <option value="">请选择癌症种类</option>
            <c:forEach items="${kindList}" var="kind">
                <option value="${kind['name']}">${kind['value']}</option>
            </c:forEach>
        </select>
<%--        <input id="cancer_kind" class="filter-input" type="text" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;" />--%>
        <div id="query"  class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-search"></i> 查询
        </div>
        <%--<div id="save" class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-plus"></i> 添加
        </div>--%>
     <%--   <div id="deleteBtn" class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-trash-o"></i> 删除
        </div>--%>

        <div  class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer" onclick="view1()">
            <i class="am-icon-gears"></i> 全局参数
        </div>
    </div>
</script>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>

<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    var row = null;

    $(function(){
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/system_cromwell_list.json",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            toolbar: kendo.template($("#toolbar").html()),
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                buttonCount: 5,
                pageSizes: ["all",5,10,20,40],
            },
            filterable: {
                extra: false
            },
            columns: [
               /* { selectable: true, width: "30px",headerAttributes:{style:"text-align:center;vertical-align:middle;padding:7px"},attributes:{style:"text-align:center;padding:7px"}},*/
                {field:"cancer_kind",title:"癌症种类",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"cancer_script",title:"脚本名称",width:"180px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
                {field:"script_step",title:"脚本类型",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"auto_script",title:"默认",width:"45px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"cromwell_workflow_source",title:"测序脚本",width:"400px",filterable:false, headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
                {field:"cromwell_workflow_dependencies",title:"依赖脚本",width:"400px",filterable:false, headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},//,template: "#= (status==0 ? '待审批' : '') # "
                {field:"create_time",title:"创建时间",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: "#= (create_time ? kendo.toString( new Date( create_time.time), 'yyyy-MM-dd HH:mm') : '') # " },
                {command: [
                    {text:"删除",click:deleteItem},
                    {text:"脚本参数",click:view}],
                    title: "操作", width: "150px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });

        $("#save").click(function () {
            <%--window.open("${CTX}/system_cromwell_create.htm");--%>
            window.location.href = "${CTX}/system_cromwell_create.htm";
        });

        $("#deleteBtn").click(function () {
            if(!confirm("注意将删除该条记录，删除后无法恢复")){
                return false;
            }
            var grid  =  $("#grid").data("kendoGrid");
            $.ajax({
                url:"${CTX}/system_cromwell_deleteAll.json",
                type:"post",
                async:true,
                cache: false,
                data: {
                    "id[]":grid.selectedKeyNames()
                },
                success:function(ret){
                    if (ret.errcode != 0) {
                        alert(ret.errmsg);
                    } else {
                        alert("删除成功");
                        window.location.reload();
                    }
                },
                error:function(error){
                    console.log(error);
                }
            })
        });



        $("#query").click(function () {
            query();
        })

    });

    function deleteItem(e){
        if(!confirm("确认要删除该条记录？")){
            return false;
        }
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        $.ajax({
            url:"${CTX}/system_cromwell_deleteAll.json",
            type:"post",
            async:true,
            cache: false,
            data: {
                "id[]":row.id
            },
            success:function(ret){
                if (ret.errcode != 0) {
                    alert(ret.errmsg);
                } else {
                    alert("删除成功");
                    window.location.reload();
                }
            },
            error:function(error){
                console.log(error);
            }
        })
    }
    function query() {

        var cancerKind = $("#cancer_kind").val();
        var grid = $("#project-grid").data("kendoGrid");
        grid.dataSource.query({
            "str-cancer_kind-lk" : cancerKind,
            page:1,
            pageSize:40
        });
    }

    function view(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/system_cromwell_config.htm?id="+row.id;
    }

    function view1(){
        window.location.href = "${CTX}/system_cromwell_config.htm";
    }


</script>

</body>

</html>