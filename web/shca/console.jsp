<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .toolbar {float: right;margin-right:20px}

        .cards{
            display: flex;
            padding: 15px 0 15px 15px;
        }

        .card{
            width: 19%;
            margin-right: 1%;
            display: flex;
            height: 90px;
            background-color: #fff;
            border-radius: 6px;
        }
        .card_line{
            background-color: #00a5fb;
            height: 90px;
            width: 6px;
            border-radius: 6px 0 0 6px;
        }
        .card_img{
            background-color: #00a5fb;
            height: 44px;
            width: 40px;
            border-radius: 50%;
            padding: 8px 10px;
            opacity: 0.6;
            margin: 15px 20px;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
        }
        .card_txt{
            padding: 15px 0;
            font-size: 16px;
        }
        .card_txt p{
            margin:0;
        }
        .card_title{
            line-height: 26px;
        }
        .card_num{
            font-size: 26px;
        }
    </style>
</head>

<body class="theme-white" data-type="index" style="overflow-y:auto">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->
    <div class="tpl-content-wrapper" style="background-color:#FFF;">
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;height:50px;">
            <div style="float:left;margin-left:20px;color:#000;font-size:14px;padding-top:8px;">
                组织结构：<select id="organization" style="width:300px;margin-right:8px;"></select>
                报表时间：<input id="start" class="kendo-datepicker" value="<fmt:formatDate value="${start}" pattern="yyyy-MM-dd" />" title="" style="width:140px;margin-right:8px;" /> 至
                <input id="end" class="kendo-datepicker" value="" title="" style="width:140px;margin-right:8px;margin-left:8px;" />
                统计方式：<select id="type" style="width:100px;margin-right:8px;">
                    <option>周</option>
                    <option>月</option>
                    <option>季度</option>
                </select>
                <span class="am-form-group" style="margin-left:8px;" onclick="refresh()">
                    <i class="am-icon-search"></i> 查询
                </span>
                <span class="am-form-group" style="margin-left:8px;" onclick="refresh()">
                    <i class="am-icon-download"></i> 导出
                </span>
            </div>

        </div>

        <div class="cards" style="background-color: #f3f3f3;margin: 0">
                <div class="card">
                    <div class="card_line"></div>
                    <div class="card_img"><img src="icon.png"></div>
                    <div class="card_txt">
                        <p class="card_title" >警务室大厅人数</p>
                        <p class="card_num">350,294</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card_line" style="background-color: #01d4ff;"></div>
                    <div class="card_img" style="background-color: #01d4ff;"><img src="icon.png"></div>
                    <div class="card_txt">
                        <p class="card_title">关注数</p>
                        <p class="card_num">51,456</p>
                    </div>
                </div>
            <div class="card">
                <div class="card_line" style="background-color: #ffbb2a;"></div>
                <div class="card_img" style="background-color: #ffbb2a;"><img src="icon.png"></div>
                <div class="card_txt">
                    <p class="card_title">社区民警</p>
                    <p class="card_num">5,488</p>
                </div>
            </div>
                <div class="card">
                    <div class="card_line" style="background-color: #5ec265;"></div>
                    <div class="card_img" style="background-color: #5ec265;"><img src="icon.png"></div>
                    <div class="card_txt">
                        <p class="card_title">可依靠力量数</p>
                        <p class="card_num">298,838</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card_line" style="background-color: #5e43e8;"></div>
                    <div class="card_img" style="background-color: #5e43e8;"><img src="icon.png"></div>
                    <div class="card_txt">
                        <p class="card_title">信息感知数</p>
                        <p class="card_num">2,899,066</p>
                    </div>
                </div>
            </div>


    <%-- PV/UV --%>
        <div class="am-u-sm-12 am-u-md-6" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-10" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>
        <div class="am-u-sm-12 am-u-md-6" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-11" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>

        <%--  --%>
        <div class="am-u-sm-12 am-u-md-4" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-20" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>
        <div class="am-u-sm-12 am-u-md-4" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-21" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>
        <div class="am-u-sm-12 am-u-md-4" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-22" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>

        <div class="am-u-sm-12 am-u-md-6 am-tabs" data-am-tabs style="margin:20px 0 0;padding:0;border-radius:0;height:450px;background-color:#FFF">
            <ul class="am-tabs-nav am-nav am-nav-tabs" style="border:0;height:40px;">
                <li class="am-active"><a href="#tab1" style="border:0;height:40px;font-size:18px;">民警排行榜</a></li>
                <li><a href="#tab2" style="border:0;height:40px;font-size:18px;">可依靠力量排行榜</a></li>
            </ul>

            <div class="am-tabs-bd" style="border:0;height:410px;">
                <div class="am-tab-panel am-fade am-in am-active" id="tab1" style="border:0;height:410px;">
                    <div id="dashboard-31" style="width:100%;height:100%;background-color:#FFF;margin:0">
                    </div>
                </div>

                <div class="am-tab-panel am-fade am-in" id="tab2" style="border:0;height:410px;">
                    <div id="dashboard-32" style="width:100%;height:100%;background-color:#FFF;margin:0">
                    </div>
                </div>

            </div>
        </div>

        <div class="am-u-sm-12 am-u-md-6" style="margin:20px 0 0;padding:0;border-radius:0;height:450px;">
            <div id="dashboard-33" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>

        <div class="am-u-sm-12 am-u-md-12" style="margin:20px 0 0;padding:0;border-radius:0;height:350px;">
            <div id="dashboard-40" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>

    </div>
</div>
<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>
<script src="${CTX}/plugin/echarts/echarts.min-4.8.js" type="text/javascript" charset="utf-8"></script>
<script src="${CTX}/plugin/echarts/macarons.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    $("input.kendo-datepicker").kendoDatePicker({
        // display month and year in the input
        format: "yyyy-MM-dd",
        // specifies that DateInput is used for masking the input element
        dateInput: true,
    })

    $("#type").kendoDropDownList();

    var serviceRoot = "https://demos.telerik.com/kendo-ui/service";
    homogeneous = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: serviceRoot + "/Employees",
                dataType: "jsonp"
            }
        },
        schema: {
            model: {
                id: "EmployeeId",
                hasChildren: "HasEmployees"
            }
        }
    });
    $("#organization").kendoDropDownTree({
        placeholder: "请选择",
        dataSource:homogeneous,
        dataTextField: "FullName"
    });

    $(function() {
        refresh();
        console.log( "success");
    });

    var dashboard_10 = echarts.init( document.getElementById( 'dashboard-10'), 'macarons');
    var dashboard_11 = echarts.init( document.getElementById( 'dashboard-11'), 'macarons');
    var dashboard_20 = echarts.init( document.getElementById( 'dashboard-20'), 'macarons');
    var dashboard_21 = echarts.init( document.getElementById( 'dashboard-21'), 'macarons');
    var dashboard_22 = echarts.init( document.getElementById( 'dashboard-22'), 'macarons');
    var dashboard_31 = echarts.init( document.getElementById( 'dashboard-31'), 'macarons');
    var dashboard_32 = echarts.init( document.getElementById( 'dashboard-32'), 'macarons');
    var dashboard_33 = echarts.init( document.getElementById( 'dashboard-33'), 'macarons');
    var dashboard_40 = echarts.init( document.getElementById( 'dashboard-40'), 'macarons');
    function refresh() {
        var start = $("#start").val();
        var end = $("end").val();

        if (start == undefined) {
            start = "";
        }
        if (end == undefined) {
            end = "";
        }

        console.log(start, end);

        dashboard_10.setOption({
            title: {
                text: '数据统计',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['问题感知数', '问题处理数']
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '问题感知数',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 90, 230, 210]
                },
                {
                    name: '问题处理数',
                    type: 'line',
                    stack: '总量',
                    data: [220, 182, 191, 234, 290, 330, 310]
                }
            ]
        });
        var per = ["20%", "20%", "15%", "8%", "7%", "11%", "13%"];
        dashboard_11.setOption({
            title: {
                text: '新增可依靠数及占比',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: [120, 200, 150, 80, 70, 110, 130],
                type: 'bar',
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(220, 220, 220, 0.8)'
                },
                label: {
                    show: true,
                    position: "top"
                },
            }],
            tooltip: {
                trigger: 'item',
                formatter: function(params){
                    return params.name + '<br />' + '人数:'+params.value+'<br/>占比:'+per[params.dataIndex];
                }
            },
        });
         dashboard_20.setOption({
            title: {
                text: '问题分类统计',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            legend: {
                left: 'center',
                top: 'bottom',
                padding:[5,5,30,5],
                data: ['警务问题', '非警务问题']
            },
            series: [{
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                data: [{value: 335, name: '警务问题'}, {value: 510, name: '非警务问题'},],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        });

        dashboard_21.setOption({
            title: {
                text: '警务类问题分类统计',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            legend: {
                left: 'center',
                top: 'bottom',
                padding:[5,5,30,5],
                data: ['可疑人', '可疑物','可疑房', '可疑事','可疑组织']
            },
            series: [{
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                data: [{value: 335, name: '可疑人'}, {value: 510, name: '可疑物'},{value: 365, name: '可疑房'}, {value: 440, name: '可疑事'},{value: 202, name: '可疑组织'}],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        });


        dashboard_22.setOption({
            title: {
                text: '非警务类问题分类统计',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            legend: {
                left: 'center',
                top: 'bottom',
                padding:[5,5,30,5],
                data: ['公共秩序', '社区环境','安全隐患', '民生关爱','其他投诉']
            },
            series: [{
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                data: [{value: 335, name: '公共秩序'}, {value: 510, name: '社区环境'},{value: 365, name: '安全隐患'}, {value: 440, name: '民生关爱'},{value: 202, name: '其他投诉'}],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        });

        dashboard_31.setOption( {
            title: {
                text: '世界人口总量',
                subtext: '数据来自网络'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['2011年', '2012年']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: ['巴西', '印尼', '美国', '印度', '中国', '世界人口(万)']
            },
            series: [
                {
                    name: '2011年',
                    type: 'bar',
                    data: [18203, 23489, 29034, 104970, 131744, 630230],
                    label: {
                        show: true,
                        position: ["90%","30%"]
                    },
                }
            ]
        });

        dashboard_32.setOption( {
            title: {
                text: '世界人口总量2',
                subtext: '数据来自网络'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['2011年', '2012年']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: ['巴西', '印尼', '美国', '印度', '中国', '世界人口(万)']
            },
            series: [
                {
                    name: '2011年',
                    type: 'bar',
                    data: [18203, 23489, 29034, 104970, 131744, 630230]
                }
            ]
        });

        dashboard_33.setOption({
            title: {
                text: '可依靠力量年龄统计',
                textStyle:{color: '#000',fontWeight:'bold'},
                padding:[5,5,5,20],
            },
            legend: {
                left: 'center',
                top: 'bottom',
                padding:[5,5,30,5],
                data: ['35岁以下', '35岁 - 65岁','65岁以上']
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)'
            },
            visualMap: {
                show: false,
                min: 80,
                max: 600,
                inRange: {colorLightness: [0, 1]}
            },
            series: [{
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                data: [{value: 335, name: '35岁以下'},{value: 310, name: '35岁 - 65岁'},{value: 274, name: '65岁以上'}].sort(function (a, b) { return a.value - b.value; }),
                roseType: 'radius',
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return Math.random() * 200;
                }
            }]
        })

        dashboard_40.setOption({
            title: {
                text: '',
            },
            yAxis: {
                type: 'category',
                data: ['您好，警棋！','习见平向中国人民警察队伍授旗并致训词','【青浦保安】忠诚担当勇奉献','【黄浦公安】安全责任不放松'],
                axisLabel: {
                    interval: 0,
                    formatter: function (value) {
                        // 格式化成月/日，只在第一个刻度显示年份
                        var length = 9;
                        if (value.length <= 9) {
                            return value;
                        }
                        var val = value;
                        if (val.length >= length * 2) {
                            return val.substring( 0, length) + "\n" + val.substring( length, length * 2 - 1) + "...";
                        }
                        return val.substring( 0, length) + "\n" + val.substring( length);
                    }
                },
                splitLine: {
                    show: false
                },
                width:'100%'
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: function(value) { return kendo.format("{0:#,0}", +value)}
                },
            },
            legend: {
                data: ['阅读数', '转发数'],
                left: 'center',
                top: 'top',
                padding:[30,5,5,5],
            },
            series: [{
                name: '阅读数',
                data: [1200, 2020, 1505, 1280],
                type: 'bar',
                label: {
                    normal: {
                        position: 'right',
                        show: true
                    }
                },
            },{
                name: '转发数',
                data: [467, 1030, 505, 380],
                type: 'bar',
                label: {
                    normal: {
                        position: 'right',
                        show: true
                    }
                },
            }]
        })

    }







</script>
</body>
</html>