<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center;cursor: pointer}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->
    <div class="tpl-content-wrapper">
        <ul id="statusTab" class="am-tabs-nav am-nav am-nav-tabs">

            <li class="am-active" ><a>全部</a></li>
            <li workflow_status="sample"><a >上传成功</a></li>
                <li workflow_status="duplicate"><a >重复上传</a></li>
            <li workflow_status="cracked"><a>文件不完整</a></li>
            <li workflow_status="expired"><a>旧版本上传</a></li>
            <li workflow_status="parse_error"><a>文件名错误</a></li>
        </ul>

        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div id="project-grid"></div>
        </div>
    </div>
    <%--<div class="tpl-content-wrapper">
        <div id="project-grid"></div>
    </div>--%>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36- 43).find( ".widget-body").height( height - 56 - 36 - 46 - 43);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        文件名：<input id="file_name" class="filter-input" type="text" style="width:160px;padding:4px;margin-left:4px;margin-right:12px;" />
        上传时间：<input id="start_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        至 <input id="end_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0;margin-right:12px;" />
        文件大小（MB）：<input id="file_size_min" class="filter-input" type="text" style="width:80px;padding:4px;margin-left:1px;" />
        至 <input id="file_size_max" class="filter-input" type="text" style="width:80px;padding:4px;margin-left:1px;margin-right:12px;" />
       <%-- 癌症种类：
        <select id="cancer_kind" name="cancer_kind" data-placeholder="请选择癌症种类" class="chosen" style="width:120px;margin-right:12px;height:27px;">
            <option value=""></option>
            <c:forEach items="${kindList}" var="kind">
                <option value="${kind['name']}">${kind['value']}</option>
            </c:forEach>
        </select>--%>


        <div id="search-btn" class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-search"></i> 查询
        </div>
    </div>
    <div class="toolbar">

    </div>
</script>
<script type="text/x-kendo-template" id="row-progress">

    <div class="am-progress" style="margin-bottom:0;height:18px;">
        # if(status == "completed") { # <div class="am-progress-bar am-progress-bar-success"  style="width:100%">#= total_step# / #= total_step#</div>
        # } else if(status == "wait_running") {# <div class="am-progress-bar"  style="width:100%;background-color:white;color:black">0 / #= total_step#</div>
        # } else if(status == "running") {# <div class="am-progress-bar am-progress-bar-success"  style="width:#= parseInt( current_step / total_step * 100)#%">#= current_step# / #= total_step#</div>
        # } else if(status == "failure") {# <div class="am-progress-bar am-progress-bar-danger"  style="width:#= parseInt( current_step / total_step * 100)#%">#= current_step# / #= total_step#</div>
        # }#
    </div>
</script>



<script type="text/x-kendo-template" id="directory_template">
        # if(directory == "sample") { # 解析成功
        # } else if(directory == "duplicate") {# 样本重复
        # } else if(directory == "cracked") {# 文件不完整
        # } else if(directory == "expired") {# 旧版本上传
        # } else if(directory == "parse_error") {# 文件名错误
        # } else {# 其他
        # }#
    </div>
</script>

<script type="text/javascript">

    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if(size==0) return "--";
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }



    $(function(){
        $("#statusTab li").click(function(){
            if($(this).hasClass("am-active")) return;
            $("#statusTab li").removeClass("am-active");
            $(this).addClass("am-active");

            $(".btnContainer input").val("");//置空查询条件

            var grid = $("#project-grid").data("kendoGrid");
            var dataSource1 = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: "${CTX}/ftp_file_list.json"+($(this).attr("workflow_status")?"?str-directory-eq="+$(this).attr("workflow_status"):""),
                        dataType: "json"
                    }
                },
                pageSize: 40,
                pageable: {
                    refresh: true,
                    buttonCount: 5,
                    pageSizes: ["all",5,10,20,40],
                },
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            });
            grid.setDataSource(dataSource1);
        });
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/ftp_file_list.json",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 56 - 43,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                buttonCount: 5,
                pageSizes: ["all",5,10,20,40],
            },
            filterable: {
                extra: false
            },
            toolbar: kendo.template($("#toolbar").html()),
            columns: [
                // {field:"project_no",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"file_name",title:"文件名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                /*{field:"store_path",title:"存储路径",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},*/
                {field:"md5",title:"文件md5码",width:"250px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"file_size",title:"文件大小",width:"80px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"sample_name",title:"样本编号",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template : "#= descValue( sample_name) #"},
                {field:"cancer_kind",title:"癌症种类",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template : "#= descValue( cancer_kind) #"},
                {field:"rawdata",title:"Rawdata",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template : "#= descValue( rawdata) #"},
                {field:"nova",title:"Nova号",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template : "#= nova!=-1?nova:'--' #"},
               /* {field:"memo",title:"状态",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
               */ {field:"memo",title:"处理结果",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}/*,template:$("#directory_template").html()*/},
               /* {title:"文件最后修改时间",width:"120px",template : "#= kendo.toString(new Date(modified_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                */{title:"上传时间",width:"120px",template : "#= kendo.toString(new Date(create_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });

        $("#search-btn", $grid).click(function () {

            var options = {
                "str-file_name-lk": $("#file_name", $grid).val(),
              /*  "cancer_kind": $("#cancer_kind option:selected").attr("value"),*/

            }
            if($("#file_size_min", $grid).val()) {
                options["str-file_size-gt"] = $("#file_size_min", $grid).val() * 1024 * 1024;
            }
            if($("#file_size_max", $grid).val()) {
                options["str-file_size-lt"] = $("#file_size_max", $grid).val() * 1024 * 1024;
            }
            if($("#start_time").val()){
                options[ "str-create_time-gteq"] = $("#start_time").val();
            }
            if($("#end_time").val()){
                options[ "str-create_time-lteq"] = $("#end_time").val()+" 23:59:59";
            }

            var status = $("#statusTab .am-active").attr("workflow_status");
            var grid1 = $("#project-grid").data("kendoGrid");
            var dataSource1 = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: "${CTX}/ftp_file_list.json"+(status?"?str-directory-eq="+status:""),
                        dataType: "json",
                        data : options
                    }
                },
                pageSize: grid1.getOptions().dataSource.pageSize,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            });

            console.log(grid1.getOptions().dataSource.pageSize);
            grid1.setDataSource(dataSource1);
            //$grid.data( "kendoGrid").dataSource.read(options);
        })


        //日期控件初始化
        $("input.kendo-datepicker", $grid).kendoDatePicker({
            // display month and year in the input
            format: "yyyy-MM-dd",
            // specifies that DateInput is used for masking the input element
            dateInput: false
        });

    });

    function descValue(value){
        if(value) return value;
        return "--";
    }
</script>

</body>

</html>