<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center;cursor: pointer}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}


    </style>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}



        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}

        #project-grid > table
       {
           table-layout: fixed;
       }

        .am-form-file {
            position: relative;
            overflow: inherit;
        }
    </style>
</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->
    <%-- <div class="tpl-content-wrapper">
         <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
             <ul class="row-fluid">
                 <li class="span3"><a href="javascript:step(0)" class="step active">
                     <span class="number">1</span>
                     <span class="desc"> 全部</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(1)" class="step"  status="Running">
                     <span class="number">2</span>
                     <span > 执行中</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(2)" class="step"  status="wait">
                     <span class="number">3</span>
                     <span> 排队中</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(3)" class="step"  status="finished">
                     <span class="number">4</span>
                     <span > 执行完成</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(4)" class="step"  status="failure">
                     <span class="number">5</span>
                     <span > 执行失败</span>
                 </a></li>

             </ul>

         </div>
         <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
             <div id="project-grid"></div>
         </div>
     </div>--%>
    <div class="tpl-content-wrapper">
        <ul id="statusTab" class="am-tabs-nav am-nav am-nav-tabs">
            <%-- wait_rawdata,Running0,wait_match,Running1,Running2,finished,failured--%>
            <li class="am-active" ><a>全部</a></li>
            <li status="Running"><a >执行中</a></li>
           <%-- <li status="Wait"><a >排队中</a></li>--%>
            <li status="Succeeded"><a>执行完成</a></li>
            <li status="Failed"><a>执行失败</a></li>
                <li status="Abort"><a>执行终止</a></li>
                <li onclick="location.href='${CTX}/project_compare_listDel.htm'"><a>已删除</a></li>
        </ul>

        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div id="project-grid"></div>
        </div>
    </div>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
    /*function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }*/
    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");


        /*for (var i = 0; i < index; i++) {
                    $step.eq( i).addClass( "active").find( "span").eq( 1).addClass( "desc");
                }*/
        /* $(".step-container").css( "display", "none");
         console.log( $("#step-container-" + index).length)
         $("#step-container-" + index).css( "display", "block");*/

        var grid = $("#project-grid").data("kendoGrid");
        var dataSource1 = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "${CTX}/project_compare_list.json"+($this.attr("status")?"?str-status-rlk="+$this.attr("status"):""),
                    dataType: "json"
                }
            },
            pageSize: 40,
            serverPaging: true,
            serverFiltering: true,
            schema: {
                "total" : "total",
                "data" : "rows",
                model: {
                    id: "id"
                }
            }
        });
        grid.setDataSource(dataSource1);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        项目编号：<input id="project_name" class="filter-input" type="text" style="width:140px;padding:4px;margin-left:4px;margin-right:12px;" />
        样本编号：<input id="sample_name" class="filter-input" type="text" style="width:140px;padding:4px;margin-left:4px;margin-right:12px;" />
        测序时间：<input id="start_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        至 <input id="end_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        <div id="query"  class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-search"></i> 查询
        </div>
        <form id="uploadform"  action="${CTX}/project_compare_importExcel.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;display: inline-block;">
        <div class="am-form-group am-form-file" style="font-size:14px;margin-left: 4px;cursor: pointer">
            <i class="am-icon-arrow-circle-down"></i> 优先级导入
            <input type="hidden" name="parent_id" value="50" />
            <input class="upload-image-form-file"   id="upload-file" type="file" name="file" accept=".xls"  style="height: 30px">
        </div>
        </form>
        <a href="${CTX}/shca/template.xls" target="_blank" style="font-size:14px;margian-left:12px;cursor: pointer">
            <i class="am-icon-download"></i> 模板下载
        </a>
    </div>
    <div class="toolbar">

    </div>
</script>

<script type="text/x-kendo-template" id="row-start-end">
    #if(start!=null && end!=null){#
    #=   kendo.toString( new Date( start.time), 'yyyy-MM-dd HH:mm') + " 至 " +  kendo.toString( new Date( end.time), 'HH:mm')  #
    #} else  {
    if(start!=null){ #
    #=   kendo.toString( new Date( start.time), 'yyyy-MM-dd HH:mm') + " 至 --:--"   #
    #} else { #
        #if((sample_name_0!=null &&sample_status_0=="finished" && sample_name_1!=null && sample_status_1=="finished") || (sample_name_0!=null && sample_status_0=="finished" && sample_name_1==null)){#
            排队中
        #} else { #
            等待样本
        #}  #
    #} }#
</script>

<script type="text/x-kendo-template" id="row-runtime">
    # if(start!=null){#
    #= descRuntime (runtime)  #
    #} else { # <div>-- -- --</div>
    # } #
</script>
<script type="text/x-kendo-template" id="row-status">
    #= descStatus (workflow_status)  #
</script>

<script type="text/x-kendo-template" id="row-sample_name_0">
    # if(sample_name_0 ){#
        # if(sample_status_0){#
            #= sample_name_0  #
        #} else { #
            #= "<div style='color:red'>"+sample_name_0 +"</div>" #
        #} #
    #} else { # <div>--</div>
    # } #
</script>

<script type="text/x-kendo-template" id="row-sample_name_1">
    # if(sample_name_1 ){#
        # if(sample_status_1){#
            #= sample_name_1  #
        #} else { #
            #= "<div style='color:red'>"+sample_name_1 +"</div>" #
        #} #
    #} else { # <div>--</div>
    # } #
</script>

<script type="text/javascript">
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.floor( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }

        var cnt =  Math.floor( runtime / ONE_HOUR);
        var minus = runtime - cnt * ONE_HOUR;
        if (minus <= 0) {
            return cnt + "小时";
        }
        minus = Math.round( minus / ONE_MINUTE);
        return cnt + "小时" + minus + "分";

        return "";
    }

    function descStatus(status) {
        if (status == null) {
            return "--";
        }
        if (status=="Running") {
            return "执行中";
        } else  if (status.indexOf("Wait")==0) {
            return "排队中";
        } else  if (status=="Succeeded") {
            return "执行完成";
        } else  if (status=="Failed") {
            return "执行失败";
        } else  if (status=="Aborting") {
            return "终止中";
        } else  if (status=="Aborted") {
            return "已终止";
        }

        return "--";
    }
    $(function(){
        //tab切换
        $("#statusTab li").click(function(){
            if($(this).hasClass("am-active")) return;
            $("#statusTab li").removeClass("am-active");
            $(this).addClass("am-active");

            $(".btnContainer input").val("");//置空查询条件

            var grid = $("#project-grid").data("kendoGrid");
            var dataSource1 = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: "${CTX}/project_compare_list.json"+($(this).attr("status")?"?str-workflow_status-rlk="+$(this).attr("status"):""),
                        dataType: "json"
                    }
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            });
            grid.setDataSource(dataSource1);
        });

        //列表初始化
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/project_compare_list.json",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 110,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                buttonCount: 5,
                pageSizes: ["all",5,10,20,40],
            },
            filterable: {
                extra: false
            },
            dataBound: onDataBound,
            toolbar: kendo.template($("#toolbar").html()),
            columns: [
                {field:"rownum",title:"序号",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"project_name",title:"项目编号",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"cancer_kind",title:"癌症种类",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"sample_name_0",title:"样本编号1",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:$("#row-sample_name_0").html()},
                {field:"sample_name_1",title:"样本编号2",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:$("#row-sample_name_1").html()},
                {title:"任务执行时间",width:"180px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-start-end").html())},
                {title:"任务执行时长",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-runtime").html()) },
                /*{title:"执行状态",width:"80px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: kendo.template($("#row-status").html())/!* kendo.template($("#row-progress").html())*!/},
               */
                {field:"priority",title:"优先级",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},

                {title:"执行状况",width:"180px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: "<span id='chart_#= project_no#' class='sparkline-chart'></span>"},
                /*{title:"lims",width:"50px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: "#= lims_result == 'true' ?'成功':(lims_result == 'false'?'失败':'--') #"},*/
                { command: [
                    {text: "项目详情",click: viewFile},
                    {text: "执行情况",click:showScript},
                    <c:if test="${sessionScope['role_code'] eq 1}">
                    {text: "终止分析",click:stop,visible: function(dataItem) { return dataItem.workflow_status=="Running"}},
                    {text: "终止中...",visible: function(dataItem) { return dataItem.workflow_status=="Aborting"}},
                    {text: "重新分析",click:restart,visible:  function(dataItem) {
                        return dataItem.project_status=="finished" ||  dataItem.project_status=="failed" || dataItem.project_status=="aborted"}}
                    </c:if>
                ], title: " ", width: "220px"  }//{text: "撤回流程", click: cancelApproval }
            ]
        });

        //日期控件初始化
        $("input.kendo-datepicker", $grid).kendoDatePicker({
            // display month and year in the input
            format: "yyyy-MM-dd",
            // specifies that DateInput is used for masking the input element
            dateInput: false,
        });

        //列表工具条查询按钮事件
        $("#query").click(function () {
            query();
        })

        $("#upload-file").on("change", function() {
            var $file = $(this);
            if ($file.val().length == 0) {
                return;
            }
            $form = $("#uploadform");

            var fd = new FormData($form[0]);


            $form.ajaxSubmit({
                dataType: "json",
                success: function(ret) {

                    if(ret.errcode==0) {
                        alert(ret.errmsg);
                        $file.val("");
                    } else {
                        alert(ret.errcode+":"+ret.errmsg);
                    }
                }

            });
        });

        setInterval(function(){
            var status = $("#statusTab .am-active").attr("workflow_status");
            if("Succeeded" == status) return;
            var grid = $("#project-grid").data("kendoGrid");
            $(".k-loading-mask").width(0).height(0);
            grid.dataSource.query();
        },60*1000);
    });

    function query() {
        var sampleName = $("#sample_name").val();
        var projectName = $("#project_name").val();
        var startTime = $("#start_time").val();
        var endTime = $("#end_time").val();

        if(startTime&&endTime){
            if (endTime<startTime) {
                alert("结束时间不能小于开始时间");
                return false;
            }
            endTime = endTime+" 23:59:59";
        }
        var status = $("#statusTab .am-active").attr("status");
        var dataSource1 = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "${CTX}/project_compare_list.json"+(status?"?str-workflow_status-rlk="+status:""),
                    dataType: "json",
                    data:{
                        "str-project_name-lk":projectName,
                        "sample_name":sampleName,
                        "str-start-gteq":startTime,
                        "str-start-lteq":endTime
                    }
                }
            },
            pageSize: 40,
            serverPaging: true,
            serverFiltering: true,
            schema: {
                "total" : "total",
                "data" : "rows",
                model: {
                    id: "id"
                }
            }
        });
        var grid1 = $("#project-grid").data("kendoGrid");
        grid1.setDataSource(dataSource1);
    }


    function startApproval(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        console.log(row);
        alert( "开发中");
        return;
    }


    function viewFile(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.location.href = "${CTX}/project_file_list.htm?project_no=" + row.project_no;--%>
        <%--window.location.href = "${CTX}/project_cancerfile_view.htm?id="+row.sample_name_0_id+"&sample_name_0_id="+row.sample_name_0_id+"&sample_name_1_id="+row.sample_name_1_id+"&project=1";--%>
        window.location.href = "${CTX}/project_compare_view.htm?id=" + row.id;
    }

    function showScript(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_compare_script.htm?id=" + row.id;
    }

    function viewQC(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_project_qcReport.htm?id="+row.id ;
    }

    function viewReport(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_project_report.htm?id="+row.id;
    }

    function stop(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));

        if (window.confirm("确认要终止分析吗？")) {
            kendo.ui.progress($("#project-grid"), true);
            $.ajax({
                url: "project_compare_stop.htm",
                data: {"id":row.id,"workflow_id":row.workflow_id},
                dataType: "json",
                type: "post",
                success: function (data) {
                    setTimeout(function(){
                        alert("提交成功，正在终止中，大约需要2-3分钟");
                        kendo.ui.progress($("#project-grid"), false);
                        var grid = $("#project-grid").data("kendoGrid");
                        grid.dataSource.query();
                    },3000);

                }
            })
        }
        /*kendo.confirm("确认要终止分析吗？")
            .done(function(){

            })
            .fail(function(){
                console.log("User rejected");
            });*/

    }
    function  restart(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        if (window.confirm("确认要重新分析吗？")) {
            kendo.ui.progress($("#project-grid"), true);
            $.ajax({
                url: "project_compare_restart.htm",
                data: {"id": row.id},
                dataType: "json",
                type: "post",
                success: function (data) {
                    alert("提交成功，正在重新分析准备中");
                    kendo.ui.progress($("#project-grid"), false);
                    var grid = $("#project-grid").data("kendoGrid");
                    grid.dataSource.query();
                }
            })
        }
    }


    function onDataBound(e) {
        var grid = this;
        grid.table.find("tr").each(function () {
            var dataItem = grid.dataItem(this);
            var persent = parseInt( dataItem.current_step / dataItem.total_step * 100);

            if(dataItem.workflow_status!="Succeeded" ){
                if(persent >= 100) persent = 99;
            } else {
                persent = 100;
            }
            $(this).find(".sparkline-chart").kendoSparkline({

                legend: {
                    visible: false
                },
                data: [persent],
                type: "bar",
                chartArea: {
                    margin: 0,
                    width: 150,
                    background: "transparent",
                },
                seriesDefaults: {
                    labels: {
                        visible: true,
                        format: '{0}%',
                        background: 'none',
                    },
                    color: function(){
                        if(dataItem.workflow_status=="Failed"){
                            return "#DD1815";
                        } else if(dataItem.workflow_status=="Aborting"){
                            return "#FF7C07";
                        } else if(dataItem.workflow_status=="Aborted"){
                            return "#FF7C07";
                        } else {
                            return "#41CA6E";
                        }
                    }
                },
                categoryAxis: {
                    majorGridLines: {
                        visible: false
                    },
                    majorTicks: {
                        visible: false
                    }
                },
                valueAxis: {
                    type: "numeric",
                    min: 0,
                    max: 130,
                    color: "#000",
                    visible: false,
                    labels: {
                        visible: false
                    },
                    minorTicks: {visible: false},
                    majorGridLines: {visible: false}
                },
                tooltip: {
                    visible: false
                }
            });

            kendo.bind($(this), dataItem);
        });
    }


</script>

</body>

</html>