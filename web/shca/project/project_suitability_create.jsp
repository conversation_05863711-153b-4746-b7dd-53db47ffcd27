<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}


        #project-suitability-form .k-multiselect-wrap{padding:6px 12px 6px 4px;height:120px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <%-- <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
             <ul class="row-fluid">
                 <li class="span3"><a href="javascript:step(0)" class="step active">
                     <span class="number">1</span>
                     <span class="desc"> 填写项目基本信息</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(1)" class="step active">
                     <span class="number">2</span>
                     <span> 选择样本文件</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(2)" class="step">
                     <span class="number">3</span>
                     <span class="desc"> 配置脚本参数</span>
                 </a></li>
             </ul>
         </div>--%>
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;display:inline;">
            <div class="widget am-cf am-u-sm-12" style="padding:0">
                <div class="widget-head am-cf" style="padding:10px 15px;font-size:14px;height:46px;">
                    <div class="widget-title am-fl" style="width:100%">
                        <ol class="k-breadcrumb-container" style="width:100%;padding:0;margin:0;">
                            <li class="k-breadcrumb-item  k-breadcrumb-root-item" style="display:inline-block;"><a href="${CTX}/project_suitability_list.htm" class="k-breadcrumb-root-link " title="相似度项目">相似度项目</a><span class="k-breadcrumb-delimiter-icon k-icon k-i-arrow-chevron-right" aria-hidden="true" style="display:inline-block;margin-top:-4px;"></span></li>
                            <li class="k-breadcrumb-item  k-breadcrumb-last-item" style="display:inline-block;"><a href="${CTX}/project_suitability_create.htm" class="  k-breadcrumb-link k-state-disabled" aria-current="page" title="新建相似度项目">新建相似度项目</a></li></ol>
                    </div>
                </div>
                <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">
                    <form id="project-suitability-form"  action="${CTX}/project_suitability_save.json" method="post"  class="am-form tpl-form-border-form" style="padding-top:30px;">
                        <%--<div class="am-form-group">--%>
                            <%--<label for="project_no" class="am-u-sm-1 am-form-label">项目编号<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>--%>
                            <%--<div class="am-u-sm-4">--%>
                                <%--<input type="text" class="tpl-form-input" name="project_no" id="project_no" value="" placeholder="系统自动生成" readonly="readonly" />--%>
                                <%--<small></small>--%>
                            <%--</div>--%>
                            <%--<div class="am-u-sm-6"></div>--%>
                        <%--</div>--%>

                        <div class="am-form-group">
                            <label for="project_name" class="am-u-sm-1 am-form-label">备注名<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="project_name" id="project_name" value="" placeholder="请输入备注名" style="width:480px;" />
                                <small></small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label for="cancer_kind" class="am-u-sm-1 am-form-label">癌症类型	<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="cancer_kind" name="cancer_kind" data-placeholder="请选择癌症类型" class="chosen" style="width:220px;">
                                    <option value="breast">乳腺癌</option>
                                    <option value="colon">结直肠癌</option>
                                    <option value="gastric">胃癌</option>
                                    <option value="hepa">肝癌</option>
                                    <option value="pancreatic">胰腺</option>
                                    <option value="pan">泛癌组织</option>
                                    <option value="fuke">妇科</option>
                                    <option value="gist">胃肠间质瘤</option>
                                </select>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="sample_name_select" class="am-u-sm-1 am-form-label">分析样本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <textarea name="sample_name_select" id="sample_name_select" style="width:480px;" rows="5"></textarea>
                                <div id="sample_name_div"></div>
                                <small>一行单独一个样本，系统按行分割样本号</small>
                            </div>
                        </div>


                        <%--<div class="am-form-group">--%>
                            <%--<label for="description" class="am-u-sm-1 am-form-label">描述 <span class="tpl-form-line-small-title"></span></label>--%>
                            <%--<div class="am-u-sm-11">--%>
                                <%--<textarea class="form-field" rows="6" id="description" name="description" placeholder="请输入描述" style="width:480px"></textarea>--%>
                                <%--<small></small>--%>
                            <%--</div>--%>
                        <%--</div>--%>
                        <div class="am-form-group">
                            <div class="am-u-sm-12 am-u-sm-push-1">
                                <a id="submit-btn" class="blue-btn" style="display:inline-block;text-align:justify;text-align-last:justify;">
                                    提交分析 <i class="icon-angle-right"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    $("select.chosen").chosen();
    $("input.kendo-datetimepicker").kendoDateTimePicker({
        // display month and year in the input
        format: "yyyy-MM-dd HH:mm",
        // specifies that DateInput is used for masking the input element
        dateInput: false,
    });

    var CANCER_KIND = {
        "breast": "breast_cancer",
        "colon": "colorectal_cancer",
        "gastric": "gastric",
        "hepa": "hepa",
        "pancreatic": "pancreatic",
        "pan": "pan",
        "fuke": "fuke",
        "gist": "gist",
    }


    $(function(){


  /*      var $multiSelect = $("#sample_name").kendoMultiSelect({
            placeholder: "请输入分析样本列表",
            dataTextField: "sample_name",
            dataValueField: "sample_name",
            autoBind: false,
            dataSource: {
                type: "json",
                serverFiltering: true,
                transport: {
                    read: "${CTX}/project_suitability_autoComplete.json"
                }
            }
        }).data("kendoMultiSelect");*/

        $("#cancer_kind").change(function() {
            console.log( CANCER_KIND[this.value], this.value);
           /* $multiSelect.value( "");
            $multiSelect.dataSource.filter([{
                field: "cancer_kind",
                operator: "eq",
                value: CANCER_KIND[this.value],
            }]);*/

            /*  $autoComplete.setDataSource(new kendo.data.DataSource({
             type: "json",
             serverFiltering: true,
             transport: {
             read: "${CTX}/project_suitability_autoComplete.json?str-cancer_kind-eq=colorectal_cancer"
             }
             })); */
        });


        $("#submit-btn").click(function() {
            var samples = $("#sample_name_select").val();
            var samples_array = samples.split(/[(\r\n)\r\n]+/);
            $("#sample_name_div").empty();
            for(var i=0; i < samples_array.length;i++) {
                var sample = samples_array[i];
                $("#sample_name_div").append("<input type='hidden' name='sample_name' value='"+sample+"'>");
            }
            var t = new Date().getTime();
            $("#project-suitability-form").ajaxSubmit({
                url:"${CTX}/project_suitability_save.json?t="+t,
                type:"post",
                dataType: "json",
                success: function (ret) {
                    if(ret.errcode == 0){
                        alert( "保存成功");
                        window.location.href="${CTX}/project_suitability_list.htm";
                    } else {
                        alert( "[" + ret.errcode + "]" + ret.errmsg);
                    }
                }
            });
        })
    });






</script>

</body>

</html>