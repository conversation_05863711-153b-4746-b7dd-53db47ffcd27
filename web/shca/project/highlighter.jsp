<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>基因测序</title>
	<%@include file="/include/include-head.jsp"%>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shCore.js"></script>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shBrushBash.js"></script>
	
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shCore.css"/>
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shThemeDefault.css"/>
	<script type="text/javascript">
		SyntaxHighlighter.config.clipboardSwf = '${CTX}/plugin/syntaxhighlighter/scripts//clipboard.swf';
        SyntaxHighlighter.config.tagName = "div";
        SyntaxHighlighter.defaults['class-name'] = "span_red";
		SyntaxHighlighter.all();
	</script>

	<style >
		.syntaxhighlighter, .syntaxhighlighter div, .syntaxhighlighter code, .syntaxhighlighter table, .syntaxhighlighter table td, .syntaxhighlighter table tr, .syntaxhighlighter table tbody {

			white-space: nowrap;
		}
		.span_red{
			color:red;
		}
	</style>
</head>

<body style="overflow: auto">

<div id="highlighter_690200" class="syntaxhighlighter span_red ">

		<div class="line">
			<table>
				<tbody>
				<tr>
					<td class="number"><code>03</code></td>
					<td class="content">/mydata/shca/BatchBvskSuitability/2021/2021082601/tmp_vcf/BZ2005589B&nbsp;
						-0.116938338932352&nbsp; -0.120804609401877&nbsp; 1&nbsp;&nbsp;
							0.997165359993577&nbsp;&nbsp; 0.00443053725127759 0.0039419231475385&nbsp;
							-0.0556341162170484 -0.0534321218341759 0.139666526528093&nbsp;&nbsp; 0.139666526528093&nbsp;&nbsp;
							-0.0956210142791609 -0.095718021866331&nbsp; 0.0373727502997991&nbsp; 0.0373537886649922&nbsp;
							-0.14978116368057&nbsp;&nbsp; -0.144821035502287&nbsp; -0.105700386777933&nbsp;
							-0.105700386777933&nbsp; -0.0790985444889112 -0.080632037720629&nbsp; -0.0499890015281047
							-0.0499890015281047 0.0766240940332247&nbsp; 0.0747854932561127&nbsp; -0.0525801948545496
							-0.0538129141560752 -0.0321221436977995 -0.0321221436977995 -0.032298158867118&nbsp;
							-0.032298158867118&nbsp; 0.0124534659993626&nbsp; 0.0137637363690339&nbsp;
							-0.0358183605513776 -0.03565143825644&nbsp;&nbsp; -0.0558355458872994 -0.0645852069089236
							-0.0284200189630235 -0.0284200189630235 -0.0764265038029899 -0.0768298140526644
							-0.0183715180269656 -0.0188809697254986 -0.0393197265523369 -0.0397965579744489
							-0.0316535227459132 -0.0311712796844639 -0.0153023625412236 -0.0153023625412236
							-0.056435694982716&nbsp; -0.0568648897392564 -0.0514527671966205 -0.0514527671966205
							-0.0783295344425266 -0.0763546195207967 -0.0943083727275331 -0.0929477455391531
							0.147354374791654&nbsp;&nbsp; 0.147354374791654&nbsp;&nbsp; -0.0223690231621448
							-0.0223690231621448</td>
				</tr>
				</tbody>
			</table>
		</div>
</div>

<script type="text/javascript">
    /*$("#preDiv").append(JSON.stringify(JSON.parse("${cromwell_workflow_inputs}"), null, 4));*/
</script>
</html>
