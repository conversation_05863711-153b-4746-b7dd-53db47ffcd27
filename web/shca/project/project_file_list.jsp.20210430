<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <ul id="contentTab" class="am-tabs-nav am-nav am-nav-tabs">
            <%-- wait_rawdata,running_step0,wait_match,running_step1,running_step2,finished,failured--%>
            <li for="infoForm" class="am-active"><a>样本信息</a></li>
            <li for="dataList"><a >样本数据</a></li>
        </ul>
        <div id="tabContent">
            <div id="infoForm">
                <form id="upload-form"  action="${CTX}/project_project_create.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">
                    <div class="am-form-group">
                        <label for="project_no" class="am-u-sm-1 am-form-label">样本号<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <input type="text" class="tpl-form-input" name="project_no" id="project_no" value="" placeholder="请输入样本号" />
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label for="project_name" class="am-u-sm-1 am-form-label">项目名称<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <input type="text" class="tpl-form-input" name="project_name" id="project_name" value="" placeholder="请输入项目名称" />
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label for="project_kind" class="am-u-sm-1 am-form-label">测序种类<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <select id="project_kind" name="project_kind" data-placeholder="请选择测序种类" class="chosen" style="width:160px;">
                                <option value=""></option>
                                <option value="breast_cancer">乳腺癌</option>
                                <option value="colorectal_cancer">结直肠癌</option>
                                <option value="gastric_cancer">胃癌</option>
                                <option value="hepatocellular_cancer">肝癌</option>
                                <option value="ctDNA_pan-cancer">ctDNA泛癌</option>
                                <option value="ovarian_cancer">卵巢癌</option>
                                <option value="pan-cancer_tissue">泛癌组织</option>
                                <option value="urinary">泌尿</option>
                            </select>
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label for="project_script" class="am-u-sm-1 am-form-label">测序脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <select id="project_script" name="project_script" data-placeholder="请选择测序脚本" class="chosen" style="width:160px;">
                                <option value=""></option>
                                <c:forEach items="${cancer_script}" var="list" varStatus="id">
                                    <option value="${list.id}">${list.cancer_script}</option>
                                </c:forEach>
                                <%--                                    <option value="breast_cancer">乳腺癌</option>--%>
                            </select>
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label for="run_time" class="am-u-sm-1 am-form-label">执行时间<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11 ">
                            <label class="am-radio" style="line-height: 1.6;margin-top:5px"><input type="radio" id="run_time" name="run_time" value="manual" checked>手动执行</label>
                            <label class="am-radio-inline" style="line-height: 1.6;margin-top:5px;margin-right:18px;"><input type="radio" name="run_time" value="auto" >自动执行</label><input class="kendo-datetimepicker" type="text" style="width:180px;border-width:0;height:28px;" />
                            <small></small>
                        </div>
                    </div>


                    <div class="am-form-group">
                        <label for="description" class="am-u-sm-1 am-form-label">描述 <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <textarea class="form-field" rows="10" id="description" name="description" placeholder="请输入描述"></textarea>
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-12 am-u-sm-push-1">
                            <a href="javascript:;" class="white-btn" style="">
                                <i class="icon-angle-right"></i> 上一步
                            </a>
                            <a class="blue-btn" style="" onclick="next()">
                                下一步 <i class="icon-angle-right"></i>
                            </a>
                            <a href="javascript:;" class="green-btn" style="">
                                默认创建 <i class="icon-angle-right"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
            <div id="dataList" style="display: none">
                <div id="project-file-grid"></div>
            </div>
        </div>

    </div>

</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
<%--                <input type="hidden" name="project_no" value="${param["project_no"]}" />--%>
                <input type="hidden" name="sample_name" value="${param["sample_name"]}" />
            </form>
        </div>
        <%--<div  class="am-form-group am-form-file" id="file_edit" >--%>
        <%--<i class="am-icon-pencil"></i> 编辑--%>
        <%--</div>--%>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>
<script type="text/x-kendo-template" id="row-file_name">
<%--    #= file_name.replace( /${param["project_no"]}/g, "<span class='warning'>${param["project_no"]}</span>") #--%>
    #= file_name.replace( /${"sample_name"}/g, "<span class='warning'>${"sample_name"}</span>") #
</script>

<script type="text/javascript">

    $(function(){

        //tab切换
        $("#contentTab li").click(function(){
            $("#contentTab li").removeClass("am-active");
            $(this).addClass("am-active");

           var showId = $(this).attr("for");
            $("#tabContent > div").hide();
           $("#"+showId).show();
        });

        var $grid = $("#project-file-grid").kendoGrid({
            // dataSource: [
            //     {"seq_no": 1,"file_name": "R20015682-nova77-B2000617B_combined_R1.fastq.gz","update_time": {"time": 1594738800000}, "creator_name": "FTP导入", "file_size": 18444096 * 13, "store_path": "/home/<USER>/R20015682/raw_data/"},
            //     {"seq_no": 2, "file_name": "R20015682-nova77-B2000617B_combined_R2.fastq.gz","update_time": {"time": 1594914600000}, "creator_name": "王贇", "file_size": 16299417 * 25, "store_path": "/home/<USER>/R20015682/raw_data/"},
            // ],
            dataSource: {
                type: "json",
                <%--read: "${CTX}/project_file_list.json?project_no="+"${project_no}",--%>
                transport: {
                    read: "${CTX}/project_cancerfile_list.json?sample_name="+"${sample_name}",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            selectable: "true",
            pageable: {
                refresh: true,
                pageSizes: true,
                buttonCount: 5
            },
            filterable: {
                extra: false
            },
            //toolbar: kendo.template($("#toolbar").html()),
            columns: [
                {field:"seq_no",title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"文件名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},template: kendo.template($("#row-file_name").html())},
                {field:"store_path",title:"存储路径",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"file_size",title:"文件大小",width:"120px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"creator_name",title:"创建人",width:"80px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"update_time",title:"上传时间",width:"120px",template : "#= kendo.toString(new Date(update_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });


        $("#upload-file", $grid).on("change", function() {
            var $file = $(this);
            if ($file.val().length == 0) {
                return;
            }


            var $form = $("#upload-file-form", $grid);


//            $form.ajaxSubmit({
//                dataType: "json",
//                success: function(ret) {
//                    $file.val( "");
//                    if (ret.errcode != 0) {
//                        alert( ret.errmsg);
//                        return;
//                    }
//                    $grid.data( "kendoGrid").dataSource.read({
//                        "lon-parent_id-eq": selectData.id
//                    })
//                    kendo.ui.progress( $grid, false);
//                }
//            });

        });
    });







</script>

</body>

</html>