<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center;cursor: pointer}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}


    </style>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}



        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}

        #project-grid > table
        {
            table-layout: fixed;
        }
    </style>
</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->
    <%-- <div class="tpl-content-wrapper">
         <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
             <ul class="row-fluid">
                 <li class="span3"><a href="javascript:step(0)" class="step active">
                     <span class="number">1</span>
                     <span class="desc"> 全部</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(1)" class="step"  status="Running">
                     <span class="number">2</span>
                     <span > 执行中</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(2)" class="step"  status="wait">
                     <span class="number">3</span>
                     <span> 排队中</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(3)" class="step"  status="finished">
                     <span class="number">4</span>
                     <span > 执行完成</span>
                 </a></li>
                 <li class="span3"><a href="javascript:step(4)" class="step"  status="failure">
                     <span class="number">5</span>
                     <span > 执行失败</span>
                 </a></li>

             </ul>

         </div>
         <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
             <div id="project-grid"></div>
         </div>
     </div>--%>
    <div class="tpl-content-wrapper">
        <ul id="statusTab" class="am-tabs-nav am-nav am-nav-tabs">
            <%-- wait_rawdata,Running0,wait_match,Running1,Running2,finished,failured--%>
                <li  status="1" ><a>核查项目</a></li>
                <li  status="1_1" ><a>已同步</a></li>
                <li class="am-active" status="1_2" ><a>待同步</a></li>
                <li status="1_3" ><a>同步中</a></li>
                <li  status="1_4" ><a>未同步</a></li>
                <li status="2"><a >已完成项目</a></li>
                <li status="3"><a>归档项目</a></li>

        </ul>

        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div id="project-grid"></div>
        </div>
    </div>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
    /*function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }*/
    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");


        /*for (var i = 0; i < index; i++) {
                    $step.eq( i).addClass( "active").find( "span").eq( 1).addClass( "desc");
                }*/
        /* $(".step-container").css( "display", "none");
         console.log( $("#step-container-" + index).length)
         $("#step-container-" + index).css( "display", "block");*/

        var grid = $("#project-grid").data("kendoGrid");

        var dataSource1 = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "${CTX}/project_file_listNew2.json?status="+$this.attr("status"),
                    dataType: "json"
                }
            },
            pageSize: 40,
            serverPaging: true,
            serverFiltering: true,
            schema: {
                "total" : "total",
                "data" : "rows",
                model: {
                    id: "id"
                }
            }
        });
        grid.setDataSource(dataSource1);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        项目编号：<input id="project_name" class="filter-input" type="text" style="width:140px;padding:4px;margin-left:4px;margin-right:12px;" />
        样本编号：<input id="sample_name" class="filter-input" type="text" style="width:140px;padding:4px;margin-left:4px;margin-right:12px;" />
        <span id="create">同步时间：</span><span id="remove" style="display: none">完成时间：</span>
        <input id="start_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        至 <input id="end_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        <div id="query"  class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer">
            <i class="am-icon-search"></i> 查询
        </div>

        <div id="delAll"  class="am-form-group" style="font-size:14px;margin-left:12px;cursor: pointer" onclick="delAll()">
            <i class="am-icon-check-square-o"></i> 完成报告
        </div>
    </div>
    <div class="toolbar">

    </div>
</script>

<script type="text/x-kendo-template" id="row-start-end">

    #=   kendo.toString( new Date( start.time), 'yyyy-MM-dd HH:mm')  #

</script>

<script type="text/x-kendo-template" id="row-runtime">
    # if(start!=null){#
    #= descRuntime (runtime)  #
    #} else { # <div>-- -- --</div>
    # } #
</script>
<script type="text/x-kendo-template" id="row-status">
    #= descStatus (workflow_id,workflow_type,workflow_status,workflow_status2,workflow_status3)  #
</script>

<script type="text/x-kendo-template" id="row-sample_name">
    # if(sample_name_1){#
    #= sample_name_1  #
    #} else { # <div>--</div>
    # } #
</script>

<script type="text/javascript">
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        } else {
            return "1天以上";
        }

        return "";
    }

    function descStatus(workflow_id,workflow_type,workflow_status,workflow_status2,workflow_status3) {

        if(tabstatus==3) {
            return "已归档";
        }


        if (workflow_type==null) {
            return "未同步";
        } else if (workflow_type=="create"){
            if(workflow_status=="Succeeded" && workflow_status2=="Succeeded" && workflow_status=="Succeeded"){
                return "已同步";
            } else if(workflow_status=="Failed" || workflow_status2=="Failed" || workflow_status2=="Failed"){
                return "同步出错";
            } else {
                return "同步中";
            }
        }  else if (workflow_type=="remove"){
            if(workflow_status=="Succeeded" && workflow_status2=="Succeeded" && workflow_status=="Succeeded"){
                return "已完成";
            } else if(workflow_status=="Failed" || workflow_status2=="Failed" || workflow_status2=="Failed"){
                return "完成出错";
            } else {
                return "完成中";
            }
        }

    }
    var tabstatus = 1;
    $(function(){
        //tab切换
        $("#statusTab li").click(function(){
            if($(this).hasClass("am-active")) return;
            $("#statusTab li").removeClass("am-active");
            $(this).addClass("am-active");

            $(".btnContainer input").val("");//置空查询条件
            tabstatus = $(this).attr("status");
            if("3" == tabstatus){
                window.location.href = "${CTX}/project_file_list3.htm";
            } else if("2" == tabstatus){
                window.location.href = "${CTX}/project_file_list2.htm";
            } else {
                if("1"==tabstatus){
                    window.location.href = "${CTX}/project_file_listNew.htm";
                } else if("1_1"==tabstatus){
                    window.location.href = "${CTX}/project_file_listNew1.htm";
                } else if("1_2"==tabstatus){
                    window.location.href = "${CTX}/project_file_listNew2.htm";
                } else if("1_3"==tabstatus){
                    window.location.href = "${CTX}/project_file_listNew3.htm";
                } else if("1_4"==tabstatus){
                    window.location.href = "${CTX}/project_file_listNew4.htm";
                }

            }
        });

        //列表初始化
        var status = $("#statusTab .am-active").attr("status");
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read:  "${CTX}/project_file_listNew2.json"+(status?"?status="+status:""),
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 110,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                buttonCount: 5,
                pageSizes: ["all",5,10,20,40],
            },
            filterable: {
                extra: false
            },

            toolbar: kendo.template($("#toolbar").html()),
       /*     dataBound: function(e) {
                var _this = this;
                $(".k-checkbox-label").click(function(e){
                    var tr = $(this).parent().parent()[0];
                    var row = _this.dataItem( tr );
                    if(row.workflow_type=="create"){
                        if(row.workflow_status=="Succeeded" && row.workflow_status2=="Succeeded" && row.workflow_status=="Succeeded"){
                            return true;
                        } else if(row.workflow_status=="Failed" || row.workflow_status2=="Failed" || row.workflow_status2=="Failed"){
                            alert("请先进行同步")
                            return false;
                        } else {
                            alert("正在同步中");
                            return false;
                        }
                    }
                });
            },*/
            columns: [
                { selectable: true, width: "30px"},
                {field:"rownum",title:"序号",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"project_name",title:"项目编号",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"cancer_kind",title:"癌症种类",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"sample_name_0",title:"样本编号1",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"sample_name_1",title:"样本编号2",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:$("#row-sample_name").html()},
                /*{title:"报告系统上传时间",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: kendo.template($("#row-start-end").html())},
                {title:"报告系统上传状态",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: "已完成"},
                */{title:"文件同步",width:"90px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:kendo.template($("#row-status").html())},
                {title:"同步时间",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:kendo.template($("#row-start-end").html())},

                { command: [
                  {text: "完成报告",click:del,visible: function(dataItem) {
                      return ((dataItem.workflow_type=="create" && dataItem.workflow_status=="Succeeded" && dataItem.workflow_status2=="Succeeded" && dataItem.workflow_status3=="Succeeded"))}},
                  {text: "重新同步",click:create,visible: function(dataItem) {
                      return (dataItem.workflow_type=="create" && (dataItem.workflow_status=="Failed" || dataItem.workflow_status2=="Failed" || dataItem.workflow_status3=="Failed"))}},
                  {text: "项目详情",click: viewFile,visible: function(dataItem) {
                      return ((dataItem.workflow_type=="create" && dataItem.workflow_status=="Succeeded" && dataItem.workflow_status2=="Succeeded" && dataItem.workflow_status3=="Succeeded"))}},
                ], title: " ", width: "140px"  }//{text: "撤回流程", click: cancelApproval }
            ]
        });


       /* var grid = $("#project-grid").data("kendoGrid");
        grid.bind("change", selectRow);


        function selectRow(e) {
            var selectedRows = this.select();
            var selectedDataItems = [];
            var flag = false;
            for (var i = 0; i < selectedRows.length; i++) {
                var dataItem = this.dataItem(selectedRows[i]);
                if(dataItem.id==474){
                    flag = true;
                    grid.clearSelection();
                } else {
                    selectedDataItems.push(selectedRows[i]);
                }
            }


        }*/

        //日期控件初始化
        $("input.kendo-datepicker", $grid).kendoDatePicker({
            // display month and year in the input
            format: "yyyy-MM-dd",
            // specifies that DateInput is used for masking the input element
            dateInput: false,
        });

        //列表工具条查询按钮事件
        $("#query").click(function () {
            query();
        })


        /*setInterval(function(){
            var status = $("#statusTab .am-active").attr("workflow_status");
            if("Succeeded" == status) return;
            var grid = $("#project-grid").data("kendoGrid");
            $(".k-loading-mask").width(0).height(0);
            grid.dataSource.query();
        },60*1000);*/
    });

    function query() {
        var sampleName = $("#sample_name").val();
        var projectName = $("#project_name").val();
        var startTime = $("#start_time").val();
        var endTime = $("#end_time").val();

        if(startTime&&endTime){
            if (endTime<startTime) {
                alert("结束时间不能小于开始时间");
                return false;
            }
            endTime = endTime+" 23:59:59";
        }
        var status = $("#statusTab .am-active").attr("status");
        var dataSource1 = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "${CTX}/project_file_listNew2.json"+(status?"?status="+status:""),
                    dataType: "json",
                    data:{
                        "str-project_name-lk":projectName,
                        "sample_name":sampleName,
                        "str-start-gteq":startTime,
                        "str-start-lteq":endTime,
                        "status":tabstatus
                    }
                }
            },
            pageSize: 40,
            serverPaging: true,
            serverFiltering: true,
            schema: {
                "total" : "total",
                "data" : "rows",
                model: {
                    id: "id"
                }
            }
        });
        var grid1 = $("#project-grid").data("kendoGrid");
        grid1.setDataSource(dataSource1);
    }




    function viewFile(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));

        //alert("浏览器不支持打开本地系统文件")
        window.location.href = "shca://Z:/"+row.project_name;
    }

    function del(e){

        var row = this.dataItem( $(e.currentTarget).closest("tr"));
         //window.location.href = "${CTX}/project_file_script.htm?id=" + row.id;
        if (window.confirm("确认要完成报告吗？")) {
            kendo.ui.progress($("#project-grid"), true);
            $.ajax({
                url: "project_file_del.htm",
                data: {"project_name":row.project_name,"id":row.id},
                dataType: "json",
                type: "post",
                success: function (data) {
                    alert("提交成功，正在处理中，请稍候");
                    setTimeout(function(){
                        kendo.ui.progress($("#project-grid"), false);
                        var grid = $("#project-grid").data("kendoGrid");
                        grid.dataSource.query();
                    },3000);
                }
            })
        }
    }

    function delAll(){
        var grid = $("#project-grid").data("kendoGrid");
        var selectedRows = grid.select();
        if(selectedRows.length==0){
            alert("请先勾选要完成报告的项目");
            return false;
        }
        console.log(grid.selectedKeyNames());
        for (var i = 0; i < selectedRows.length; i++) {
            var row = grid.dataItem(selectedRows[i]);
            if(row.workflow_type=="create"){
                if(row.workflow_status=="Succeeded" && row.workflow_status2=="Succeeded" && row.workflow_status=="Succeeded"){

                } else if(row.workflow_status=="Failed" || row.workflow_status2=="Failed" || row.workflow_status2=="Failed"){
                    alert(row.project_name+"无法操作完成报告");
                    return false;
                } else {
                    alert(row.project_name+"无法操作完成报告");
                    return false;
                }
            }
        }



        if (window.confirm("确认要完成报告吗？")) {
            kendo.ui.progress($("#project-grid"), true);

            $.ajax({
                url: "project_file_delAll.htm",
                data: {"ids":grid.selectedKeyNames()},
                dataType: "json",
                type: "post",
                success: function (data) {
                    alert("提交成功，正在处理中，请稍候");
                    setTimeout(function(){
                        kendo.ui.progress($("#project-grid"), false);
                        var grid = $("#project-grid").data("kendoGrid");
                        grid.dataSource.query();
                    },3000);
                }
            })
        }
    }

    function create(e){
        //alert("待开发");
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        if (window.confirm("确认要生成报告吗？")) {
            kendo.ui.progress($("#project-grid"), true);
            $.ajax({
                url: "project_file_create.htm",
                data: {"project_name":row.project_name,"id":row.id},
                dataType: "json",
                type: "post",
                success: function (data) {
                    alert("提交成功，正在处理中，请稍候");
                    setTimeout(function(){
                        kendo.ui.progress($("#project-grid"), false);
                        var grid = $("#project-grid").data("kendoGrid");
                        grid.dataSource.query();
                    },3000);
                }
            })
        }

        //window.location.href = "${CTX}/project_file_script.htm?id=" + row.id;
    }

</script>

</body>

</html>