<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>基因测序</title>
	<%@include file="/include/include-head.jsp"%>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shCore.js"></script>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shBrushBash.js"></script>
	<link rel="stylesheet" href="${CTX}/shca/css/style.css" />
	
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shCore.css"/>
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shThemeDefault.css"/>

	<style>
		.theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
		.theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
		h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
		#project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
		.btnContainer {display: inline-block;padding-left: 4px;  }
		.btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
		.toolbar {float: right;}
		.filter-input {
			width: 380px;
			box-sizing: border-box;
			padding: 6px;
			border-radius: 3px;
			border: 1px solid #d9d9d9;
			margin-top: -4px;
		}

		.am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
		.am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
		.am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}


	</style>
	<style>
		.theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
		.theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
		h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
		a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
			margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
			text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
		a.white-btn:hover{color:#333;background-color:#f5f5f5;}
		a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
			margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
			text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
		a.blue-btn:hover{color:#fff;background-color:#006dcc;}
		a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
			margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
			text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
		a.green-btn:hover{color:#fff;background-color:#5bb75b;}
		i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

		.row-fluid{display:flex;}
		.row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
		.form-wizard li a {color:#666;font-weight:800;}
		.form-wizard li a span{font-size:14px;font-weight:300}
		.form-wizard li a.active span{font-size:14px;font-weight:600}
		.form-wizard .active .number{background-color:#41CA6E;color:#fff;}
		.form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


		#project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
		.btnContainer {display: inline-block;padding-left: 4px;  }
		.btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
		.toolbar {float: right;}
		#filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
		.tpl-header-navbar a {display: inline}
		.tpl-header-logo {display: inline}
		.syntaxhighlighter{
			height:97% !important;
			overflow: auto;
		}
	</style>

</head>
<%--style="overflow: auto"--%>
<body class="theme-white" data-type="index" style="font-size: 1.6rem">
<div class="am-g tpl-g">
	<!-- 头部 -->
	<%@include file="/shca/include/include-header.jsp"%>


	<!-- 侧边导航栏 -->
	<%@include file="/shca/include/include-menu.jsp"%>

	<!-- 内容区域 -->

	<div class="tpl-content-wrapper" style="font-size: 1.6rem">
		<c:if test="${empty cromwell_workflow_inputs}">
			<div style="width: 100%;padding: 40px;color:red">${errmsg}</div>
		</c:if>
		<c:if test="${not empty cromwell_workflow_inputs }">
			<pre id="preDiv" class="brush: bash;">
					${cromwell_workflow_inputs}
			</pre>
		</c:if>
	</div>
</div>



<script type="text/javascript">
	// function resizeContent() {
	// 	var height = $(window).height();
	// 	$(".syntaxhighlighter").height(height).find( ".tpl-content-wrapper").height( height - 46);
	// }

	SyntaxHighlighter.config.clipboardSwf = '${CTX}/plugin/syntaxhighlighter/scripts//clipboard.swf';
	SyntaxHighlighter.all();

    <%--$("#preDiv").append(JSON.stringify(JSON.parse("${cromwell_workflow_inputs}"), null, 4));--%>
</script>
</body>
</html>
