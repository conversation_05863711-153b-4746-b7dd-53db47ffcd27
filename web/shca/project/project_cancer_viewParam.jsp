<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>基因测序</title>
	<%@include file="/include/include-head.jsp"%>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shCore.js"></script>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shBrushBash.js"></script>
	
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shCore.css"/>
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shThemeDefault.css"/>
	<script type="text/javascript">
		SyntaxHighlighter.config.clipboardSwf = '${CTX}/plugin/syntaxhighlighter/scripts//clipboard.swf';
		SyntaxHighlighter.all();
	</script>
</head>

<body style="overflow: auto">
<c:if test="${empty cromwell_workflow_inputs}">
<div style="width: 100%;padding: 40px;color:red">${errmsg}</div>
</c:if>
<c:if test="${not empty cromwell_workflow_inputs }">
	<c:if test="${empty param.filePath}">
	<pre id="preDiv" class="brush: bash;">${cromwell_workflow_inputs}</pre>
	</c:if>
	<c:if test="${not empty param.filePath}">

<c:if test="${cromwell_workflow_inputs.length()>30000}">
<pre id="preDiv" class="brush: bash;">文件过大，请查看源文件。
源文件地址：${param.filePath}
</pre>
</c:if>
<c:if test="${cromwell_workflow_inputs.length()<=30000}">
<pre id="preDiv" class="brush: bash;">${cromwell_workflow_inputs}</pre>
</c:if>

	</c:if>


</c:if>


<script type="text/javascript">
    /*$("#preDiv").append(JSON.stringify(JSON.parse("${cromwell_workflow_inputs}"), null, 4));*/
</script>
</html>
