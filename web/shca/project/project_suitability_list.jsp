<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {
            display: inline-block;
            padding-left: 4px;
            margin-bottom: 5px;
        }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {
            display: inline-block;
            padding-left: 4px;
            margin-bottom: 5px;
        }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
    </style>

</head>

<body class="theme-white" data-type="index" style="overflow-y:auto">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper" style="font-size:14px">
        <div id="project-grid"></div>
    </div>

</div>
<script type="text/javascript">
    function resizeContent() {
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <%--<div class="am-btn-group" style="margin-bottom:8px;">--%>
    <%--<button class="am-btn am-btn-success" onclick="addQuestion('template-input')"><i class="am-icon-plus"></i>&nbsp;单行文本</button>--%>
    <%--<button class="am-btn am-btn-success" onclick="addQuestion('template-textarea')"><i class="am-icon-plus"></i>&nbsp;多行文本</button>--%>
    <%--<button class="am-btn am-btn-success" onclick="addQuestion('template-radio')"><i class="am-icon-plus"></i>&nbsp;单选</button>--%>
    <%--<button class="am-btn am-btn-success" onclick="addQuestion('template-checkbox')"><i class="am-icon-plus"></i>&nbsp;多选</button>--%>
    <%--</div>--%>
    <div class="btnContainer">
        备注名：<input id="project_name" name="project_name" class="filter-input" type="text" style="width:140px;padding:4px;margin-left:4px;margin-right:12px;" />
        执行时间：<input id="start_time" name="start_time" class="kendo-datepicker" type="text"style="width:130px;border-width:0" />
        至 <input id="end_time" name="end_time" class="kendo-datepicker" type="text" style="width:130px;border-width:0" />
        <span id="search-btn" class="am-form-group" style="font-size:14px;margin-left:12px;cursor:pointer">
            <i class="am-icon-search"></i> 查询
        </span>
        <span class="am-form-group" style="font-size:14px;margin-left:12px;cursor:pointer" onclick="window.location.href='${CTX}/project_suitability_create.htm';">
            <i class="am-icon-plus"></i> 创建
        </span>
    </div>
</script>
<script type="text/x-kendo-template" id="row-runtime">
    # if(start_time!=null){#
    #= descRuntime (runtime)  #
    #} else { # <div>-- -- --</div>
    # } #
</script>
<script type="text/x-kendo-template" id="row-start-end">
    #if(start_time!=null && end_time!=null){#
    #=   kendo.toString( new Date( start_time.time), 'yyyy-MM-dd HH:mm') + " 至 " +  kendo.toString( new Date( end_time.time), 'HH:mm')  #
    #} else  {
    if(start_time!=null){ #
    #=   kendo.toString( new Date( start_time.time), 'yyyy-MM-dd HH:mm') + " 至 --:--"   #
    #} else { #
    等待执行
    #} }#
</script>

<script type="text/javascript">
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        }

        return "";
    }

    var WORKFLOW_STATUS = {
        "Submitted": "提交成功",
        "Aborted": "流程终止",
        "Failed": "运行失败",
        "Succeeded": "运行完成",
        "Running": "运行中"
    };

    var CANCER_KIND = {
        "breast": "乳腺癌",
        "colon": "结直肠癌",
        "gastric": "胃癌",
        "hepa": "肝癌",
        "pancreatic": "胰腺",
        "pan": "泛癌种组织",
        "fuke": "妇科",
        "gist": "胃肠间质瘤",
        "ctDNA": "ctDNA"
    }


    $(function(){
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/project_suitability_dataGrid.json",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            selectable: "row",
            pageable: {
                refresh: true,
                pageSizes: true,
                buttonCount: 5
            },
            toolbar: kendo.template($("#toolbar").html()),
            filterable: {
                extra: false
            },
            scrollable: true,
            columns: [
                // {field:"project_no",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"row_num",title:"行号",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"project_sn",title:"分析编号",width:"160px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"project_name",title:"备注名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {title:"分析样本",filterable:false,width:"480px",template: "#= cromwell_workflow_inputs['CalculateBatchBvskSuitability.sampleNames_WF'].join(',') #", headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {title:"执行状态",filterable:false,width:"120px",template: "#= formatNull(WORKFLOW_STATUS[workflow_status]=='运行失败'?'<div style=\"color:red\">运行失败</div>':WORKFLOW_STATUS[workflow_status]) #", headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"癌症类型",width:"120px",template: "#= CANCER_KIND[cancer_kind] #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"任务执行时间",width:"240px",template:kendo.template($("#row-start-end").html()),filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"任务执行时长",width:"120px",template:kendo.template($("#row-runtime").html()),filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
               { command: [
                    {text: "执行情况",click:viewScript},
                    {text: "结果查看",click:viewResult,visible: function(dataItem) { return dataItem.workflow_status=="Succeeded"}},
                    {text: "结果下载",click:downloadResult,visible: function(dataItem) { return dataItem.workflow_status=="Succeeded"}},
                ], title: " ", width: "260px"  }
            ]
        });

        $("#search-btn", $grid).click(function () {
            /*$grid.data( "kendoGrid").dataSource.filter([{
                field: "project_name",
                operator: "contains",
                value: $("#file_name", $grid).val()
//                "str-file_name-lk": $("#file_name", $grid).val()
            }]);*/

            var projectName = $("#project_name").val();
            var startTime = $("#start_time").val();
            var endTime = $("#end_time").val();

            if(startTime&&endTime){
                if (endTime<startTime) {
                    alert("结束时间不能小于开始时间");
                    return false;
                }
                endTime = endTime + " 23:59:59";
            }

            var params = {
                "str-project_name-lk":projectName,
                "str-b.start_timestamp-gteq":startTime,
                "str-b.start_timestamp-lteq":endTime,
            };

            var grid1 = $("#project-grid").data("kendoGrid");
            var dataSource1 = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: "${CTX}/project_suitability_dataGrid.json",
                        dataType: "json",
                        data : params
                    }
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            });
            var grid1 = $("#project-grid").data("kendoGrid");
            grid1.setDataSource(dataSource1);
        });

        //日期控件初始化
        $("input.kendo-datepicker", $grid).kendoDatePicker({
            // display month and year in the input
            format: "yyyy-MM-dd",
            // specifies that DateInput is used for masking the input element
            dateInput: false,
        });




    });

    function  viewResult(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        $("#dialog").kendoWindow({
            width: "800px",
            height:"500px",
            scrollable: true,
            title:"结果查看",
            content: "${CTX}/project_suitability_viewResult.htm?project_sn="+row.project_sn,
            iframe: true,
            visible: false,
            actions: ["Refresh", "Maximize", "Close"],
        }).data("kendoWindow").center().open();
    }

    function viewScript(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_suitability_script.htm?workflow_id=" + row.cromwell_workflow_id_0;
    }

    function downloadResult(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_suitability_downloadResult.htm?project_sn="+row.project_sn;
    }

    function formatNull(str){
        if(str){
            return str;
        }
        return "--";
    }
</script>
<div id="dialog"></div>
</body>

</html>