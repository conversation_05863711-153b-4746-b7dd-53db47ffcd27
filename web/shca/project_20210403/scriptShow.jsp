<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>脚本展示</title>
	<%@include file="/include/include-head.jsp"%>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shCore.js"></script>
	<script type="text/javascript" src="${CTX}/plugin/syntaxhighlighter/scripts/shBrushBash.js"></script>
	
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shCore.css"/>
	<link type="text/css" rel="stylesheet" href="${CTX}/plugin/syntaxhighlighter/styles/shThemeDefault.css"/>
	<script type="text/javascript">
		SyntaxHighlighter.config.clipboardSwf = '${CTX}/plugin/syntaxhighlighter/scripts//clipboard.swf';
		SyntaxHighlighter.all();
	</script>
</head>

<body >
<pre class="brush: bash;">
#!/bin/bash

cd /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution
tmpDir=$(mkdir -p "/home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/tmp.3c04acce" && echo "/home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/tmp.3c04acce")

export _JAVA_OPTIONS=-Djava.io.tmpdir="$tmpDir"
export TMPDIR="$tmpDir"
export HOME="$HOME"
(
cd /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution

)
outf6799b17="${tmpDir}/out.$$" errf6799b17="${tmpDir}/err.$$"
mkfifo "$outf6799b17" "$errf6799b17"
trap 'rm "$outf6799b17" "$errf6799b17"' EXIT
tee '/home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution/stdout' < "$outf6799b17" &
tee '/home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution/stderr' < "$errf6799b17" >&2 &
(
cd /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution


set -e
mkdir -p "$(dirname /home/<USER>//varscan2/B2001187B.mpileup)"

/usr/local/bin/samtools mpileup \
-f /home/<USER>/kbase/human_g1k_v37_decoy/human_g1k_v37_decoy.fasta \
/home/<USER>/B2001187K/gatk/variation/B2001187K_recal.bam /home/<USER>/B2001187B/gatk/variation/B2001187B_recal.bam \
> /home/<USER>//varscan2/B2001187B.mpileup
)  > "$outf6799b17" 2> "$errf6799b17"
echo $? > /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution/rc.tmp
(
# add a .file in every empty directory to facilitate directory delocalization on the cloud
cd /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution
find . -type d -exec sh -c '[ -z "$(ls -A '"'"'{}'"'"')" ] && touch '"'"'{}'"'"'/.file' \;
)
(
cd /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution
sync


)
mv /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution/rc.tmp /home/<USER>/bin/cromwell-executions/geneMutualDetect/f6799b17-57e0-4798-a6cd-14b7fa3cdfbc/call-varscanPileup/execution/rc

</pre>
</html>
