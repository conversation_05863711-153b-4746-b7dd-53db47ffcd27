<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent: 2em;}

    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">


        <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3"><a href="javascript:step(0)" class="step active">
                    <span class="number">1</span>
                    <span class="desc"> 填写项目基本信息</span>
                </a></li>
                <li class="span3"><a href="javascript:step(1)" class="step active">
                    <span class="number">2</span>
                    <span> 选择样本文件</span>
                </a></li>
                <li class="span3"><a href="javascript:step(2)" class="step">
                    <span class="number">3</span>
                    <span class="desc"> 配置脚本参数</span>
                </a></li>
            </ul>
        </div>
        <div id="step-container-0" class="am-u-sm-12 am-u-md-12 step-container" style="margin:0;padding:0 1px;border-radius:0;display:none;">
            <div class="widget am-cf am-u-sm-12" style="padding:0">
                <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">
                    <form id="upload-form"  action="${CTX}/fs_upload_upload.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">
                        <div class="am-form-group">
                            <label for="project_no" class="am-u-sm-1 am-form-label">样本号<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="project_no" id="project_no" value="" placeholder="请输入样本号" />
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="project_name" class="am-u-sm-1 am-form-label">项目名称<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="project_name" id="project_name" value="" placeholder="请输入项目名称" />
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="project_kind" class="am-u-sm-1 am-form-label">测序种类<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="project_kind" name="project_kind" data-placeholder="请选择测序种类" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <option value="breast_cancer">乳腺癌</option>
                                    <option value="colorectal_cancer">结直肠癌</option>
                                    <option value="gastric_cancer">胃癌</option>
                                    <option value="hepatocellular_cancer">肝癌</option>
                                    <option value="ctDNA_pan-cancer">ctDNA泛癌</option>
                                    <option value="ovarian_cancer">卵巢癌</option>
                                    <option value="pan-cancer_tissue">泛癌组织</option>
                                    <option value="urinary">泌尿</option>
                                </select>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="script" class="am-u-sm-1 am-form-label">测序脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="script" name="script" data-placeholder="请选择测序脚本" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <option value="breast_cancer">乳腺癌</option>
                                </select>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="script" class="am-u-sm-1 am-form-label">执行时间<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11 ">
                                <label class="am-radio" style="line-height: 1.6;margin-top:5px"><input type="radio" name="run_time" value="manual" checked>手动执行</label>
                                <label class="am-radio-inline" style="line-height: 1.6;margin-top:5px;margin-right:18px;"><input type="radio" name="run_time" value="auto" checked>自动执行</label><input class="kendo-datetimepicker" type="text" style="width:180px;border-width:0;height:28px;" />
                                <small></small>
                            </div>
                        </div>


                        <div class="am-form-group">
                            <label for="description" class="am-u-sm-1 am-form-label">描述 <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <textarea class="form-field" rows="10" id="description" name="description" placeholder="请输入描述"></textarea>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <div class="am-u-sm-12 am-u-sm-push-1">
                                <a href="javascript:;" class="white-btn" style="">
                                    <i class="icon-angle-right"></i> 上一步
                                </a>
                                <a href="javascript:;" class="blue-btn" style="">
                                    下一步 <i class="icon-angle-right"></i>
                                </a>
                                <a href="javascript:;" class="green-btn" style="">
                                    默认创建 <i class="icon-angle-right"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div id="project-file-grid"></div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>
<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
                <input type="hidden" name="project_no" value="${param["project_no"]}" />
            </form>
        </div>
        <%--<div  class="am-form-group am-form-file" id="file_edit" >--%>
        <%--<i class="am-icon-pencil"></i> 编辑--%>
        <%--</div>--%>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>
<script type="text/x-kendo-template" id="row-file_name">
    #= file_name.replace( /B2000617B/g, "<span class='warning'>${param["project_no"]}</span>") #
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    $("select.chosen").chosen();
    $("input.kendo-datetimepicker").kendoDateTimePicker({
        // display month and year in the input
        format: "yyyy-MM-dd HH:mm",
        // specifies that DateInput is used for masking the input element
        dateInput: false,
    });


    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }

    $(function(){
        var $grid = $("#project-file-grid").kendoGrid({
            dataSource: [
                {"seq_no": 1,"file_name": "R20015682-nova77-B2000617B_combined_R1.fastq.gz","update_time": {"time": 1594738800000}, "creator_name": "FTP导入", "file_size": 18444096 * 13, "store_path": "/home/<USER>/R20015682/raw_data/"},
                {"seq_no": 2, "file_name": "R20015682-nova77-B2000617B_combined_R2.fastq.gz","update_time": {"time": 1594914600000}, "creator_name": "王贇", "file_size": 16299417 * 25, "store_path": "/home/<USER>/R20015682/raw_data/"},
            ],
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            selectable: "true",
            pageable: {
                refresh: true,
                pageSizes: true,
                buttonCount: 5
            },
            filterable: {
                extra: false
            },
            toolbar: kendo.template($("#toolbar").html()),
            columns: [
                {field:"seq_no",title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"file_name",title:"文件名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"store_path",title:"存储路径",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"file_size",title:"文件大小",width:"120px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"creator_name",title:"创建人",width:"80px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"update_time",title:"上传时间",width:"120px",template : "#= kendo.toString(new Date(update_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
            ]
        });


        $("#upload-file", $grid).on("change", function() {
            var $file = $(this);
            if ($file.val().length == 0) {
                return;
            }


            var $form = $("#upload-file-form", $grid);


//            $form.ajaxSubmit({
//                dataType: "json",
//                success: function(ret) {
//                    $file.val( "");
//                    if (ret.errcode != 0) {
//                        alert( ret.errmsg);
//                        return;
//                    }
//                    $grid.data( "kendoGrid").dataSource.read({
//                        "lon-parent_id-eq": selectData.id
//                    })
//                    kendo.ui.progress( $grid, false);
//                }
//            });

        });

        console.log( "success")
    });



    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        $step.eq( index).addClass( "active");

        for (var i = 0; i < index; i++) {
            $step.eq( i).addClass( "active").find( "span").eq( 1).addClass( "desc");
        }

        $(".step-container").css( "display", "none");
        console.log( $("#step-container-" + index).length)
        $("#step-container-" + index).css( "display", "block");
    }





</script>

</body>

</html>