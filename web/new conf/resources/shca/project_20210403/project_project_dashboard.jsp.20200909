<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}

    </style>

</head>

<body class="theme-white" data-type="index" style="overflow-y:auto">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:18px 8px 0;border-radius:0;height:350px;background-color:#FFF;">
            <div id="dashboard-0" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:18px 8px 0;border-radius:0;height:400px;background-color:#FFF;">
            <div id="dashboard-1" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>

        <div class="am-u-sm-12 am-u-md-8" style="margin:0;padding:0 1px;border-radius:0;height:600px;">
            <div id="dashboard-2" style="width:100%;height:100%;background-color:#FFF;margin:0"></div>
        </div>



        <div class="am-u-sm-12 am-u-md-4" style="margin:0;padding:0 1px;border-radius:0;height:400px;">
            <h2 style="font-size:18px;font-weight:400;color:#158bca;background-color:#FFF;border-width:0;padding-bottom:18px;">信息员积分排行榜</h2>
            <ul class="am-list am-list-static am-list-border" style="border-left-width:0;">
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    1. 王贇<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">1,205</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    2. 熊庭智<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">1,189</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    3. 安然<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">1,159</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    4. 王燕<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">1,126</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    5. 沈吉<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">1,004</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    6. 易爱冬<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">998</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    7. 徐<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">987</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    8. 王贇<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">972</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    7. 徐<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">987</span>
                </li>
                <li class="item item-linked" style="border-left-width:0;font-size:16px;">
                    <img width="48" src="${CTX}/shca/image/avatar.png">
                    8. 王贇<span class="am-badge am-badge-success" style="color:#158bca;background-color:#FFF;margin-top:10px;text-align:right;padding-right:8px;display:inline-block;font-size:16px;">972</span>
                </li>

            </ul>
        </div>

    </div>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>


<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
//        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>
<script src="${CTX}/plugin/echarts/echarts.min-4.8.js" type="text/javascript" charset="utf-8"></script>
<script src="${CTX}/plugin/echarts/macarons.js" type="text/javascript" charset="utf-8"></script>



<script type="text/javascript">



    $(function(){
        var dashboard0 = echarts.init( document.getElementById( 'dashboard-0'), 'macarons');
        dashboard0.setOption({
            title: {
                text: '近30天信息上报统计',
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            toolbox: {
                show: false,
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['07-01', '07-02', '07-03', '07-04', '07-05', '07-06', '07-08', '07-08', '07-09', '07-10', '07-11', '07-12', '07-13', '07-14', '07-15', '07-16', '07-17', '07-18', '07-19', '07-20']
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: function(value) { return kendo.format("{0:#,0}", +value)}
                },
                axisPointer: {
                    snap: true
                }
            },
            visualMap: {
                show: false,
                dimension: 0,
                pieces: [{
                    lte: 7,
                    color: 'green'
                }, {
                    gt: 7,
                    lte: 10,
                    color: 'red'
                }, {
                    gt: 10,
                    lte: 16,
                    color: 'green'
                },{
                    gt: 16,
                    color: 'red'
                }]
            },
            series: [
                {
                    name: '信息上报数',
                    type: 'line',
                    smooth: true,
                    data: [300, 280, 250, 260, 270, 300, 550, 500, 400, 390, 380, 390, 400, 500, 600, 750, 1001, 700, 600, 400],
                    markArea: {
                        data: [ [{
                            name: '专项任务',
                            xAxis: '07-08'
                        }, {
                            xAxis: '07-11'
                        }], [{
                            name: '专项任务',
                            xAxis: '07-17'
                        }, {
                            xAxis: '07-20'
                        }] ]
                    }
                }
            ]
        });


        var dashboard1 = echarts.init( document.getElementById( 'dashboard-1'), 'macarons');
        dashboard1.setOption({
            title: [{
                text: '分局信息上报统计分析',
            },{
                text: '浦东分局',
                left: '15%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '黄浦分局',
                left: '25%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '静安分局',
                left: '35%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '徐汇分局',
                left: '45%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '长宁分局',
                left: '55%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '普陀分局',
                left: '65%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '虹口分局',
                left: '75%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '杨浦分局',
                left: '85%',
                top: '30%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '宝山分局',
                left: '15%',
                top: '60%',
                textAlign: 'center',
                textStyle: {fontSize: 14}
            },{
                text: '闵行分局',
                    left: '25%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text:  '嘉定分局',
                    left: '35%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text: '金山分局',
                    left: '45%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text: '松江分局',
                    left: '55%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text: '青浦分局',
                    left: '65%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text: '奉贤分局',
                    left: '75%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        },{
            text: '崇明分局',
                    left: '85%',
                    top: '60%',
                    textAlign: 'center',
                    textStyle: {fontSize: 14}
        }],
            legend: {},
            tooltip: {},
            dataset: {
                source: [
                    ['分局', '浦东分局', '黄浦分局', '静安分局', '徐汇分局', '长宁分局', '普陀分局', '虹口分局', '杨浦分局', '宝山分局',
                        '闵行分局', '嘉定分局', '金山分局', '松江分局', '青浦分局', '奉贤分局', '崇明分局'],
                    ['采用线索数', 41, 30, 65, 53, 83, 98, 77, 54, 102, 45, 38, 72, 68, 56, 35, 27],
                    ['关注线索数', 86, 92, 85, 83, 73, 55, 101, 121, 134, 92, 78, 68, 96, 121, 115, 108],
                    ['有效线索数', 24, 67, 79, 86, 65, 82, 23, 36, 48, 51, 59, 57, 44, 43, 36, 41],
                    ['无效线索数', 55, 67, 69, 72, 53, 39, 12, 32, 21, 8, 27, 31, 42, 18, 14, 23]
                ]
            },
            series: [{
                type: 'pie',
                radius: 40,
                center: ['15%', '20%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '浦东分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['25%', '20%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '黄浦分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['35%', '20%'],
                encode: {
                    itemName: '分局',
                    value: '静安分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['45%', '20%'],
                encode: {
                    itemName: '分局',
                    value: '徐汇分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['55%', '20%'],
                encode: {
                    itemName: '分局',
                    value: '长宁分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['65%', '20%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '普陀分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['75%', '20%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '虹口分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['85%', '20%'],
                encode: {
                    itemName: '分局',
                    value: '杨浦分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['15%', '50%'],
                encode: {
                    itemName: '分局',
                    value: '宝山分局'
                }
            },{
                type: 'pie',
                radius: 40,
                center: ['25%', '50%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '闵行分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['35%', '50%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '嘉定分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['45%', '50%'],
                encode: {
                    itemName: '分局',
                    value: '金山分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['55%', '50%'],
                encode: {
                    itemName: '分局',
                    value:  '松江分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['65%', '50%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value:  '青浦分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                center: ['75%', '50%'],
                label: {
                    show: false,
                    position: 'inside'
                },
                encode: {
                    itemName: '分局',
                    value: '奉贤分局'
                }
            }, {
                type: 'pie',
                radius: 40,
                label: {
                    show: false,
                    position: 'inside'
                },
                center: ['85%', '50%'],
                encode: {
                    itemName: '分局',
                    value: '崇明分局'
                }
            }]
        })

        var dashboard2 = echarts.init( document.getElementById( 'dashboard-2'), 'macarons');
        dashboard2.setOption({
            title: {
                text: '信息上报分类统计',
            },
            yAxis: {
                type: 'category',
                data: ['黄赌毒','偷盗抢','诈骗','敲诈勒索','非法拘禁（绑架）','殴打他人','假冒伪劣','公共安全','危险物品','破坏财物','黑车、黄牛','犬类','其他'],
                axisLabel: {
                    interval: 0,
                    rotate: 30
                },
                splitLine: {
                    show: false
                },
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: function(value) { return kendo.format("{0:#,0}", +value)}
                },
            },
            series: [{
                data: [1200, 2020, 1505, 1280, 2070, 1130, 1350, 2234, 1895, 1547, 456, 987, 1305],
                type: 'bar',

                label: {
                    normal: {
                        position: 'right',
                        show: true
                    }
                },
            }]
        })

        console.log( "success");
    });







</script>

</body>

</html>