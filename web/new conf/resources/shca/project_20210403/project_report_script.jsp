<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3"><a href="javascript:step(0);" class="step active" for="dataList">
                    <span class="number">1</span>
                    <span class="desc"> 执行情况</span>
                </a></li>

              <%--  <li class="span3"><a href="javascript:step(1)" class="step" for="viewParam">
                    <span class="number">2</span>
                    <span > 脚本参数</span>
                </a></li>--%>

                <li style="margin-left: 20%">
                    <a href="javascript:step(1);" style="color:#fff" class="step" for="viewParam">
                        。
                    </a>
                </li>
            </ul>

        </div>
        <%-- <ul id="scriptTab" class="am-tabs-nav am-nav am-nav-tabs">
             &lt;%&ndash; wait_rawdata,running_step0,wait_match,running_step1,running_step2,finished,failured&ndash;%&gt;
             <li  class="am-active" index="0"><a>脚本1</a></li>
             <li  index="1"><a >脚本2</a></li>
                 <li  index="2"><a >脚本3</a></li>
         </ul>--%>
        <div id="tabContent" style="background: #fff">
            <div id="viewParam" style="display: none">
                <iframe frameborder="0" src=""></iframe><%--${CTX}/project_cancer_viewParam.json?id=${id}&scriptIndex=0--%>
            </div>
            <div id="dataList" >
                <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
                    <div id="project-file-grid"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="dialog"></div>
<div id="dialog2"></div>

<script type="text/x-kendo-template" id="row-name">
    #=  cutOut (call_name) #
</script>

<script type="text/x-kendo-template" id="row-runtime">
    #if(start!=null && end !=null){#
    #=   descRuntime (end.time-start.time)  #
    #} else if(start!=null){#
    #=   descRuntime (new Date().getTime()-start.time)  #
    #} else { #<div>-- -- --</div>
    # } #
</script>

<script type="text/x-kendo-template" id="row-status">
    #if(execution_status == "Running"){#<div>执行中</div>
    #} else if(execution_status == "Done"){#<div>完成</div>
    #} else if(execution_status == "QueueinCromwell"){#<div>排队中</div>
    #} else if(execution_status == "Failed"){#<div>失败</div>
    #} else {#<div>等待</div>
    #} #
</script>

<script type="text/x-kendo-template" id="row-num">
    #=  counter++ #
</script>
<script type="text/javascript">
    function cutOut(call_name) {
        var index = call_name.lastIndexOf(".");
        call_name = call_name.substring(index+1,call_name.length);
        return call_name;
    }

    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        }

        return "";
    }


    function resizeContent() {
        var height = $(window).height();
        $("#tabContent").height( height - 56 - 79);
    }


    var counter = 1;
    var tabIndex = 0;
    var status = 0;

    function step(index) {
        tabIndex = index;
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");

        var showId = $this.attr("for");
        $("#tabContent > div").hide();
        $("#"+showId).show();


        if(index==0){
            var loading = $this.attr("loading");
            if(!loading){
                $this.attr("loading","loading");
                var $grid = $("#project-file-grid").kendoGrid({
                    // dataSource: [
                    //     {"seq_no": 1,"file_name": "R20015682-nova77-B2000617B_combined_R1.fastq.gz","update_time": {"time": 1594738800000}, "creator_name": "FTP导入", "file_size": 18444096 * 13, "store_path": "/home/<USER>/R20015682/raw_data/"},
                    //     {"seq_no": 2, "file_name": "R20015682-nova77-B2000617B_combined_R2.fastq.gz","update_time": {"time": 1594914600000}, "creator_name": "王贇", "file_size": 16299417 * 25, "store_path": "/home/<USER>/R20015682/raw_data/"},
                    // ],
                    dataSource: {
                        type: "json",
                        <%--read: "${CTX}/project_file_list.json?project_no="+"${project_no}",--%>
                        transport: {
                            read: "${CTX}/project_report_script.json?id=${id}&scriptIndex=0",
                            dataType: "json"
                        },
                        serverPaging: true,
                        serverFiltering: true,
                        group: {
                            field: "样本编号",
                            dir: "asc"
                        },
                        schema: {
                            "total" : "total",
                            "data" : "rows",
                            model: {
                                id: "id"
                            }
                        }
                    },
                    height: $(window).height() - 135,
                    groupable: false,
                    sortable: false,
                    selectable: "true",
                    /* pageable: {
                         refresh: true,
                         pageSizes: true,
                         buttonCount: 5
                     },*/
                    filterable: {
                        extra: false
                    },
                    /*toolbar: kendo.template($("#toolbar").html()),*/
                    dataBound: onDataBound,
                    columns: [
                        {field:"seq_no",title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template:kendo.template($("#row-num").html())},
                        {title:"执行步骤",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},template: kendo.template($("#row-name").html()) },
                        {title:"开始时间",width:"100px",template : "#= start!=null?kendo.toString(new Date(start.time), 'yyyy-MM-dd HH:mm:ss'):'-- -- --' #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {title:"结束时间",width:"100px",template : "#= end!=null?kendo.toString(new Date(end.time), 'yyyy-MM-dd HH:mm:ss'):'-- -- --' #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {title:"执行时长",width:"50px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-runtime").html()) },
                        {title:"执行结果",width:"50px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-status").html())},
                        // { command: [{text: "commandline" , click: viewCommand },{text: "stdout", click: viewOut },{text: "stderr", click: viewErr }], title: " ", width: "120px"  }
                        { command: [{text: "stdout", click: viewOut },{text: "stderr", click: viewErr }], title: " ", width: "100px"  }
                    ]
                });

                /*$("#dialog").kendoWindow({
                    width: "600px",
                    height:"480px",
                    scrollable: true,
                    content: "${CTX}/project_report_viewParam.json?id=${id}&scriptIndex=0",
                    iframe: true,
                    visible: false
                }).data("kendoWindow").center();*/

                $("#upload-file", $grid).on("change", function() {
                    var $file = $(this);
                    if ($file.val().length == 0) {
                        return;
                    }



                    var $form = $("#upload-file-form", $grid);


//            $form.ajaxSubmit({
//                dataType: "json",
//                success: function(ret) {
//                    $file.val( "");
//                    if (ret.errcode != 0) {
//                        alert( ret.errmsg);
//                        return;
//                    }
//                    $grid.data( "kendoGrid").dataSource.read({
//                        "lon-parent_id-eq": selectData.id
//                    })
//                    kendo.ui.progress( $grid, false);
//                }
//            });

                });
            }
        }else {
            viewParam2();
        }

    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">

    $(function(){
        // viewParam2();
        // viewParam();
        step(0);

        setInterval(function(){
            if(tabIndex == 1) return;
            if(status ==  1) return;
            counter = 1;
            var grid = $("#project-file-grid").data("kendoGrid");
            grid.dataSource.query();
        },10*1000);
    });



    function  viewCommand(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.open("${CTX}/project_report_viewCommand.htm?id=${id}&metaId="+row.id);
    }

    function  viewOut(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.open("${CTX}/project_report_viewLog.htm?type=stdout&id=${id}&metaId="+row.id);--%>
        $("#dialog2").kendoWindow({
            width: "680px",
            height:"480px",
            scrollable: true,
            content: "${CTX}/project_report_viewLog.htm?type=stdout&filePath="+row.stdout,
            iframe: true,
            visible: false,
            actions: ["Refresh", "Maximize", "Close"],
        }).data("kendoWindow").center().open();
    }

    function  viewErr(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.open("${CTX}/project_report_viewLog.htm?type=stderr&id=${id}&metaId="+row.id);--%>
        $("#dialog2").kendoWindow({
            width: "680px",
            height:"480px",
            scrollable: true,
            content: "${CTX}/project_report_viewLog.htm?type=stdout&filePath="+row.stderr,
            iframe: true,
            visible: false,
            actions: ["Refresh", "Maximize", "Close"],
        }).data("kendoWindow").center().open();
    }


    function viewParam(){

        var dialog = $("#dialog").data("kendoWindow");
        //dialog.content("${CTX}/project_report_viewparam.json?id=${id}&scriptIndex="+tabIndex);
        dialog.open();
    }

    function viewParam2(){

        var height = $(window).height();
        $("iframe").height( height - 56 - 80);
        $("iframe").width("100%");
        $("iframe").attr("src","${CTX}/project_report_viewParam.json?id=${id}&scriptIndex=0")
    }

    function onDataBound(e) {
        var grid = this;
        var flag = true;
        grid.table.find("tr").each(function () {
            var dataItem = grid.dataItem(this);
            if(dataItem.execution_status != "Done" && dataItem.execution_status != "Failed"){
                flag = false
            }
        });
        if(flag){
            status = 1;
        }
    }

</script>

</body>

</html>