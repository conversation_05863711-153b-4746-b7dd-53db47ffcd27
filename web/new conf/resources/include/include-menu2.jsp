<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ include file="include-taglib.jsp"%>
<style>
    .left-sidebar {
        transition: all 0.4s ease-in-out;
        width: 240px;
        min-height: 100%;
        padding-top: 0;
        position: absolute;
        z-index: 1104;
        top: 0;
        left: 0px;
    }
</style>
<div class="left-sidebar">
    <!-- 菜单 -->
    <ul class="sidebar-nav">
        <li class="sidebar-nav-link">
            <a href="javascript:;" class="sidebar-nav-sub-title">
                <i class="am-icon-database sidebar-nav-link-logo"></i> 后台管理
                <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
            </a>
            <ul class="sidebar-nav sidebar-nav-sub">

                <li class="sidebar-nav-link">
                    <a id="shrd_notice_list" href="${CTX}/shrd_notice_list.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span> 通知管理
                    </a>
                </li>
                <li class="sidebar-nav-link">
                    <a id="hr_approve_list" href="#">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span> 文件管理
                    </a>
                </li>


            </ul>
        </li>

    </ul>
</div>
<script type="text/javascript">
    $('.sidebar-nav-sub-title').on('click', function() {
        $(this).siblings('.sidebar-nav-sub')
                .slideToggle( 80)
                .end()
                .find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');
        console.log("1 click")
    })

    //选中菜单
    $("#${__menu_id}").addClass( "active").parents( ".sidebar-nav-sub").css( "display", "block").prev( ".sidebar-nav-sub-title").find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');

</script>