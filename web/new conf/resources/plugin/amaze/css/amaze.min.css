/*Thu Apr 28 2016 11:45:47 GMT+0800 (CST)*/
#amz-home, #doc-widget-frame {
    overflow-x: hidden
}

#amz-header ul, #widget-info ul, .amz-footer ul, .doc-toc-bd .md-toc li, .ios-signal {
    list-style: none
}

#am-wechat, .amz-compare thead th, .amz-credits header, .amz-credits header h1, .amz-features h2, .amz-pager, .tester-list tbody td:last-child {
    text-align: center
}

body, html {
    margin: 0
}

.amz-container {
    /*margin-left: auto;*/
    /*margin-right: auto;*/
    width: 100%;
    /*max-width: 1000px;*/
    padding-left: 1rem;
    padding-right: 1rem
}

@media only screen and (min-width: 641px) {
    .amz-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }
}

.am-projects {
    background: #333;
    height: 40px;
    overflow: hidden
}

.am-projects a {
    position: relative;
    display: block;
    float: left;
    height: 40px;
    padding-left: 20px;
    padding-right: 20px;
    color: #eee;
    line-height: 38px;
    font-size: 14px
}

.am-projects a:hover {
    background: #303030;
    color: #fff
}

.am-projects a.active {
    background: 0 0;
    cursor: default;
    pointer-events: none
}

.am-projects a.active:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 0;
    height: 0;
    width: 0;
    margin-left: -8px;
    border-color: transparent transparent #0e90d2;
    border-style: none solid solid;
    border-width: 0 8px 8px
}

.am-projects-nav {
    float: left
}

.am-projects-misc {
    float: right
}

.am-projects-misc a {
    font-weight: 700
}

#amz-header {
    width: 100%;
    background: #0e90d2
}

.amz-index #amz-header {
    top: 0;
    z-index: 1000
}

#amz-header a {
    color: #fff;
    display: block;
    line-height: 50px
}

#amz-header h1 {
    position: relative;
    margin: 0;
    float: left;
    font-weight: 400;
    font-size: 20px;
    font-variant: small-caps
}

#amz-header h1 a {
    display: block;
    height: 50px;
    width: 165px;
    text-indent: -9999px;
    background: url(http://s.amazeui.org/media/i/brand/amazeui-w.png) left center no-repeat;
    -webkit-background-size: 125px 24px;
    background-size: 125px 24px
}

#amz-header h1 .am-badge {
    position: absolute;
    top: 5px;
    right: 0;
    padding: .25em .4em;
    font-weight: 100
}

#amz-header nav {
    margin-left: -15px;
    margin-right: -15px;
    clear: both
}

#amz-header nav a {
    position: relative;
    padding: 0 20px;
    font-size: 15px;
    color: #f2f2f2;
    overflow: hidden;
    -webkit-transition: .3s;
    transition: .3s
}

@media only screen and (min-width: 641px) and (max-width: 1024px) {
    #amz-header nav a {
        padding: 0 12px
    }
}

#amz-header nav a:hover {
    background: rgba(0, 0, 0, .1);
    color: #fff
}

#amz-header nav .am-active a {
    background: #0b76ac;
    color: #fff
}

#amz-header .nav-profile img {
    margin-right: 5px
}

#amz-header .nav-profile.need-check {
    display: none
}

#amz-header ul {
    padding: 0;
    margin: 0
}

#amz-header .am-btn {
    border: none;
    float: right;
    margin-top: 5px;
    margin-right: -5px
}

@media only screen and (min-width: 641px) {
    .amz-header-nav li {
        position: relative
    }

    .amz-header-nav .nav-react .am-badge {
        font-size: 10px;
        padding: 2px;
        position: absolute;
        right: 0;
        top: 4px
    }

    .amz-header-nav .am-active a:after {
        position: absolute;
        left: 50%;
        bottom: 0;
        content: "";
        height: 0;
        width: 0;
        margin-left: -8px;
        border-color: transparent transparent #ededed;
        border-style: none solid solid;
        border-width: 0 8px 8px
    }
}

.am-standalone body {
    padding-top: 50px
}

.am-standalone #amz-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000
}

.am-standalone #amz-offcanvas {
    z-index: 2000
}

.am-standalone.m body {
    padding-top: 0
}

#amz-hero {
    background: #10a0ea;
    padding: 40px 0 0
}

.amz-mascot img {
    display: block;
    margin-left: -12%
}

.amz-hero-intro {
    padding-bottom: 30px
}

.amz-hero-intro .amz-hero-headings {
    color: #fff;
    padding: 10px
}

.amz-hero-intro h1, .amz-hero-intro h2, .amz-hero-intro h3 {
    margin: 5px 0
}

.amz-hero-intro h1 {
    font-size: 32px;
    font-weight: 400
}

.amz-hero-intro h2 {
    font-size: 24px;
    font-weight: 400;
    margin-bottom: 15px
}

.amz-hero-intro h3 {
    font-size: 16px;
    font-weight: 400
}

.amz-hero-intro .am-btn {
    margin-left: 10px;
    margin-bottom: 5px
}

.github-status {
    margin-top: 0;
    padding-left: 10px;
    margin-bottom: 20px
}

.github-status a {
    color: #eee
}

.github-status li {
    display: inline
}

.github-status li + li {
    margin-left: 10px
}

.amz-q-group {
    padding-left: 10px;
    color: #eee;
    margin-bottom: 15px
}

.amz-q-group .am-icon-qq {
    margin-right: 5px
}

.amz-notify {
    padding: 1em;
    background-color: rgba(0, 0, 0, .15);
    color: #ddd
}

.amz-notify a {
    color: #eee
}

.amz-notify a .amz-notify-divider {
    color: #ccc;
    padding: 0 5px
}

.amz-notify .am-g {
    position: relative
}

.subscribe-field {
    margin: 10px 0
}

.subscribe-alert {
    position: absolute;
    right: 0;
    top: 100%;
    z-index: 1000;
    display: none;
    padding-left: 15px !important;
    padding-right: 10px !important;
    font-size: 16px
}

.doc-heading, .doc-toc h2 {
    position: relative
}

@media only screen and (min-width: 641px) {
    .amz-notify .am-g .subscribe-field {
        padding-right: 5px;
        margin: 0
    }

    .amz-notify {
        line-height: 40px
    }

    .amz-notify .am-btn, .amz-notify .am-form-field {
        height: 40px;
        padding-top: 0;
        padding-bottom: 0
    }
}

.amz-features {
    padding: 15px 0
}

.amz-features img {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.doc-anchor:before, .external-link:after {
    display: inline-block;
    transform: translate(0, 0);
    text-rendering: auto;
    -moz-osx-font-smoothing: grayscale
}

.amz-features h2 {
    margin-top: 10px
}

.amz-features [class*=col-] {
    padding: 15px
}

.amz-credits {
    padding: 35px 0;
    background-color: #eee
}

#amz-main, .amz-banner {
    padding-top: 0px;
    padding-bottom: 30px
}

.amz-credits header h1 {
    font-size: 40px;
    font-weight: 400
}

.amz-credits header h2 {
    margin-top: 20px;
    font-size: 18px;
    font-weight: 300
}

.amz-credits-list {
    margin-top: 35px
}

.amz-credits-list > div {
    padding-left: 20px;
    padding-right: 20px;
    height: 100%
}

.amz-credits-list > div:last-child {
    border-right: none
}

.amz-credits-list > div h2 {
    font-weight: 400;
    margin-bottom: 25px;
    font-variant: small-caps
}

.amz-banner h1:last-child, .doc-toc h2 {
    margin-bottom: 0
}

.amz-credits-list > div p {
    font-size: 15px;
    font-weight: 300
}

.amz-banner {
    background: url(data:image/png;base64,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) left top #f5f5f5;
    overflow-y: hidden
}

.amz-banner h1 {
    font-weight: 400;
    font-size: 36px
}

.amz-banner p {
    color: #555
}

.external-link:after {
    content: "\f08e";
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    font-size: inherit;
    -webkit-font-smoothing: antialiased;
    margin-left: 3px;
    margin-right: 2px
}

a[href*='github.com'].external-link:after, a[href*='github.io'].external-link:after {
    content: "\f09b"
}

a[href*='apple.com'].external-link:after {
    content: "\f179"
}

a[href*='google.com'].external-link:after, a[href*='chrome.com'].external-link:after {
    content: "\f1a0"
}

a[href*='microsoft.com'].external-link:after, a[href*='windows.com'].external-link:after, a[href*='msdn.com'].external-link:after {
    content: "\f17a"
}

a[href*='w3.org'].external-link:after {
    content: "\f02d"
}

.doc-content {
    overflow: hidden;
    word-wrap: break-word;
    counter-reset: header2
}

.doc-content > h2 {
    counter-increment: header2;
    counter-reset: header3
}

.doc-content > h2:before {
    content: counter(header2) ". "
}

.doc-content > h3 {
    counter-increment: header3;
    counter-reset: header4
}

.doc-content > h3:before {
    content: counter(header2) "." counter(header3) " "
}

.doc-content > h4 {
    counter-increment: header4
}

.doc-content > h4:before {
    content: counter(header2) "." counter(header3) "." counter(header4) " "
}

.doc-content > h2:before, .doc-content > h3:before, .doc-content > h4:before {
    color: #888;
    font-weight: 400
}

.doc-anchor {
    float: right;
    color: #ccc
}

.doc-anchor:before {
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    -webkit-font-smoothing: antialiased;
    content: "\f0c1";
    font-size: 14px
}

h1 .doc-anchor {
    display: none
}

.doc-toc {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: -ms-flex;
    display: flex;
    border-top: 1px solid #eee;
    border-bottom: 2px solid #eee
}

.doc-toc h2 {
    background: #f8f8f8;
    padding: 10px 0 10px 20px;
    font-size: 16px
}

.doc-toc h2 span {
    background: #0e90d2;
    display: inline-block;
    padding: 8px 10px 8px 20px;
    font-weight: 400;
    color: #fff
}

.doc-toc h2:before {
    content: "";
    width: 0;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0
}

.doc-toc-bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding: 10px 10px 10px 20px
}

.doc-toc-bd > ul {
    padding: 0
}

.doc-toc-bd .md-toc {
    counter-reset: toc-1;
    margin-bottom: 0
}

.doc-toc-bd .md-toc > li {
    line-height: 1.8;
    counter-increment: toc-1
}

.doc-toc-bd .md-toc ul {
    margin-top: 5px
}

.doc-toc-bd .md-toc ul a {
    color: #555
}

.doc-toc-bd .md-toc ul a:hover {
    color: #0e90d2
}

.doc-toc-bd .md-toc li:before {
    color: #8c8c8c
}

.doc-toc-bd .md-toc > li:before {
    content: counter(toc-1) '. '
}

.doc-toc-bd .md-toc > li > ul {
    counter-reset: toc-2
}

.doc-toc-bd .md-toc > li > ul > li {
    counter-increment: toc-2
}

.doc-toc-bd .md-toc > li > ul > li:before {
    content: counter(toc-1) '.' counter(toc-2) ' '
}

.doc-toc-bd .am-parent > a:after {
    display: inline-block;
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translate(0, 0);
    content: "\f196";
    margin-left: 5px;
    color: #999
}

.doc-toc-bd .am-open > a:after {
    content: "\f147"
}

.issue-list a {
    position: relative;
    padding-right: 60px !important
}

.issue-list [class*=am-icon-]:before {
    font-size: 10px;
    color: #999;
    margin-right: 5px
}

.issue-list .am-badge {
    position: absolute;
    right: 5px;
    top: 15px
}

#doc-widget-frame {
    width: 320px;
    height: 480px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    -webkit-box-shadow: 0 0 5px 2px rgba(0, 0, 0, .1);
    box-shadow: 0 0 5px 2px rgba(0, 0, 0, .1)
}

#doc-widget-frame::-webkit-scrollbar {
    display: none
}

.amz-sidebar .am-nav > li > a {
    color: #777;
    -webkit-transition: .15s;
    transition: .15s
}

.amz-sidebar .am-nav > li.am-active a {
    background: #f5f5f5;
    color: #333
}

.amz-footer {
    padding: 20px 0 10px;
    background: #555;
    color: #999
}

.amz-footer a {
    color: #999
}

.amz-footer a:hover {
    color: #eee
}

.amz-footer ul {
    margin: 0;
    padding: 0
}

.amz-footer .amz-fd {
    display: inline-block;
    opacity: .85;
    margin-right: 15px;
    margin-bottom: 10px
}

.amz-footer .amz-fd a {
    display: block;
    width: 92px;
    height: 30px;
    text-indent: -9999px;
    background: url(http://s.amazeui.org/assets/2.x/i/ui/am-logo.png) left center no-repeat;
    -webkit-background-size: 92px 30px;
    background-size: 92px 30px
}

.amz-footer .amz-links {
    display: inline-block
}

.amz-footer .amz-links a {
    color: #fff
}

.amz-footer .amz-cp {
    font-size: 13px;
    margin: 10px 0
}

.amz-footer .amz-social {
    text-align: center;
    margin-bottom: 10px
}

.amz-footer .amz-social li {
    display: inline-block;
    margin-left: 10px
}

.amz-footer .amz-social li:first-child {
    margin-left: 0
}

.amz-footer .amz-social a {
    display: block;
    height: 40px;
    width: 40px;
    background: #999;
    color: #555;
    font-size: 28px;
    line-height: 40px;
    border-radius: 50%;
    -webkit-transition: .15s;
    transition: .15s
}

.amz-footer .amz-social a:hover {
    background: #eee
}

@media print {
    .amz-footer {
        display: none
    }
}

#am-wechat .am-modal-dialog {
    background: #fff
}

.amz-toolbar {
    position: fixed;
    right: 10px;
    bottom: 70px;
    z-index: 999
}

.amz-toolbar a {
    display: none;
    opacity: .7
}

.amz-toolbar a:hover {
    opacity: 1
}

.amz-toolbar a.am-active {
    display: block
}

.amz-toolbar .am-icon-faq {
    display: block;
    opacity: 1;
    background-color: #dd514c;
    color: #fff;
    margin-top: 10px;
    -webkit-transition: all .3s ease-in;
    transition: all .3s ease-in
}

.amz-toolbar .am-icon-faq:hover {
    -webkit-transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    transform: rotate(720deg)
}

.amz-sidebar-toggle {
    position: fixed;
    right: 10px;
    bottom: 10px;
    z-index: 1000;
    opacity: .5;
    -webkit-transition: .2s;
    transition: .2s
}

.amz-sidebar-toggle:hover {
    -webkit-transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    transform: rotate(720deg);
    opacity: 1
}

.auth-card {
    max-width: 280px;
    background: #f8f8f8;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .25);
    box-shadow: 0 0 2px rgba(0, 0, 0, .25);
    padding-top: 20px;
    padding-bottom: 20px
}

#user-avatar, #widget-info .md-icon img {
    -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, .1)
}

.auth-card .am-alert {
    padding: 5px 10px
}

#user-avatar {
    width: 72px;
    height: 72px;
    background: url(http://www.gravatar.com/avatar/00000000000000000000000000000000?d=mm&f=y&s=144) center center no-repeat;
    -webkit-background-size: 72px 72px;
    background-size: 72px 72px;
    margin-bottom: 20px;
    box-shadow: 0 0 1px rgba(0, 0, 0, .1)
}

.amz-pager {
    display: none;
    position: absolute;
    width: 20px;
    right: 0;
    top: 50%;
    margin-top: -24px;
    border: 1px solid #dfdfdf;
    background-color: #efefef;
    border-radius: 2px;
    z-index: 1500
}

.amz-pager a {
    display: block;
    height: 24px;
    line-height: 24px;
    color: #999
}

.amz-pager a + a {
    border-top: 1px solid #dfdfdf
}

.amz-pager a:hover {
    color: #555;
    background-color: #ddd
}

.amz-pager a:active {
    outline: 0
}

.amz-pager a.am-disabled {
    opacity: .45;
    cursor: not-allowed;
    color: #999;
    background-color: transparent
}

@media only screen and (max-width: 640px) {
    .amz-sidebar .am-nav > li.am-nav-header, .amz-sidebar .am-nav > li > a {
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, .05);
        border-top: 1px solid rgba(0, 0, 0, .3)
    }

    .amz-sidebar .am-nav > li > a {
        color: #ccc;
        border-radius: 0;
        padding-top: .5em;
        padding-bottom: .5em
    }

    .amz-sidebar .am-nav > li > a:hover {
        background: #404040;
        color: #fff
    }

    .amz-sidebar .am-nav > li + li {
        margin-top: 0
    }

    .amz-sidebar .am-nav > li.am-nav-header {
        color: #777;
        background: #404040;
        text-shadow: 0 1px 0 rgba(0, 0, 0, .5);
        font-weight: 400;
        font-size: 75%;
        margin-top: 0;
        margin-bottom: 0
    }

    .amz-sidebar .am-nav > li.am-active > a {
        background: #1a1a1a;
        color: #fff;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, .3)
    }

    .amz-sidebar .am-nav-en {
        margin-left: 5px;
        color: #999;
        font-size: 12px;
        font-weight: 200
    }
}

@media only screen and (min-width: 641px) {
    .amz-toolbar {
        bottom: 10px
    }

    #amz-header ul, .doc-offcanvas.am-sticky .amz-pager.am-active {
        display: block
    }

    #amz-header nav {
        float: right;
        clear: none;
        margin: 0
    }

    #amz-header ul li {
        float: left
    }

    #amz-header .nav-profile a {
        padding: 0 10px
    }

    .amz-notify {
        font-size: 1.4rem
    }

    .amz-hero-intro {
        margin-top: -60px;
        float: right
    }

    .amz-hero-intro h1 {
        font-size: 56px
    }

    .amz-hero-intro h2 {
        font-size: 32px
    }

    .amz-hero-intro h3 {
        font-size: 18px
    }

    .amz-features [class*=col-] {
        padding-left: 20px;
        padding-right: 20px
    }

    .amz-sidebar, .doc-content {
        font-size: 1.4rem
    }

    .amz-sidebar {
        border-right: 1px solid #eee
    }

    .amz-sidebar .am-nav > li.am-nav-header {
        border-left: 3px solid #3bb4f2;
        padding: 0 1em;
        margin-top: .4em;
        margin-bottom: .4em
    }

    .amz-sidebar .am-nav > li + li {
        margin-top: 0
    }

    .amz-sidebar .am-nav > li + li.am-nav-header {
        margin-top: 10px
    }

    .amz-sidebar .am-nav > li > a {
        padding-top: .3em;
        padding-bottom: .3em
    }

    .amz-sidebar .am-nav > li > a:hover {
        color: #555
    }

    .amz-sidebar .am-nav-en {
        margin-left: 5px;
        color: #aaa;
        font-size: 10px;
        vertical-align: bottom
    }

    .doc-offcanvas {
        display: block;
        position: static;
        background: 0 0;
        padding-right: 10px;
        -webkit-animation-duration: .3s;
        animation-duration: .3s;
        -webkit-animation-timing-function: linear;
        animation-timing-function: linear
    }

    .doc-offcanvas.am-sticky {
        position: fixed;
        overflow-y: hidden
    }

    .doc-offcanvas.am-sticky .amz-sidebar {
        overflow: hidden
    }

    .doc-offcanvas .am-offcanvas-bar {
        position: static;
        width: auto;
        background: 0 0;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0)
    }

    .doc-offcanvas .am-offcanvas-bar:after {
        display: none
    }

    .amz-footer {
        padding: 30px 0 15px;
        font-size: 1.4rem
    }

    .amz-social {
        float: right
    }
}

#widget-info ul:after, .amz-compare ul, .demo .demo-header:after, .demo-bar:after, .editor-row:after, .ios-signal:after {
    clear: both
}

.doc-code-filed {
    display: none
}

.doc-example {
    border: 1px solid #eee;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    padding: 0 15px 15px
}

.doc-example:after {
    content: '';
    display: table;
    clear: both
}

.doc-example:before {
    content: 'Demo';
    display: block;
    color: #bbb;
    text-transform: uppercase;
    margin: 0 -15px 15px;
    padding: 4px 10px;
    font-size: 12px
}

#dbg-header h2, .amz-compare ul, .doc-example > p:last-child {
    margin-bottom: 0
}

.doc-example + .doc-code {
    margin-top: -1px
}

.doc-example + .doc-code pre {
    margin-top: 0;
    border-color: #eee;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.doc-example [data-am-widget] {
    max-width: 320px;
    margin-left: auto;
    margin-right: auto
}

.doc-example [data-am-widget=gallery], .doc-example [data-am-widget=navbar], .doc-example [data-am-widget=divider] {
    max-width: none
}

.doc-example > [data-am-widget=menu] {
    display: none
}

.doc-code {
    position: relative
}

.doc-code:after {
    position: absolute;
    right: 5px;
    bottom: 5px;
    color: #ccc;
    display: inline-block;
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translate(0, 0);
    content: "\f121"
}

.doc-code .am-pre-scrollable::-webkit-scrollbar {
    width: 5px
}

.doc-code .am-pre-scrollable::-webkit-scrollbar-track-piece {
    background: 0 0
}

.doc-code .am-pre-scrollable::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .15);
    border-radius: 5px
}

.doc-code .am-pre-scrollable::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, .3)
}

.doc-actions {
    position: relative;
    z-index: 999
}

.doc-act-inner {
    font-size: 12px;
    position: absolute;
    right: 0;
    top: 0;
    color: #555;
    border: 1px solid #eee;
    border-width: 0 0 1px 1px;
    border-radius: 0 2px;
    cursor: pointer;
    z-index: 9999
}

.doc-act-inner span {
    padding: 3px 8px
}

.doc-act-inner span + span {
    border-left: 1px solid #eee
}

.doc-act-inner span:first-child {
    border-bottom-left-radius: 2px
}

.doc-act-inner span:last-child {
    border-top-right-radius: 2px
}

.ios .doc-act-clip {
    display: none
}

.doc-act-clip:hover, .doc-act-newwin:hover {
    background: #0e90d2;
    color: #fff
}

.tester-list {
    font-size: 14px
}

.ds-thread {
    margin-top: 30px
}

.amz-compare {
    background: #f8f8f8
}

.amz-compare table {
    table-layout: fixed
}

@media only screen and (max-width: 640px) {
    .amz-compare thead {
        display: none
    }

    .amz-compare td, .amz-compare th {
        display: block
    }

    .amz-compare td {
        background: #fff
    }

    .amz-compare td:nth-of-type(1):before {
        content: "Web:"
    }

    .amz-compare td:nth-of-type(2):before {
        content: "Touch:"
    }

    .amz-compare td:before {
        display: inline-block;
        word-wrap: normal;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: 60px;
        float: left;
        text-align: center;
        margin-right: 8px;
        background: #dfdfdf
    }
}

@media only screen and (min-width: 641px) {
    .amz-compare tbody th {
        text-align: right
    }

    .amz-compare tbody tr:hover {
        background: #ffd
    }
}

.hljs-comment, .hljs-title {
    color: #8e908c
}

.css .hljs-class, .css .hljs-id, .css .hljs-pseudo, .hljs-attribute, .hljs-regexp, .hljs-tag, .hljs-variable, .html .hljs-doctype, .ruby .hljs-constant, .xml .hljs-doctype, .xml .hljs-pi, .xml .hljs-tag .hljs-title {
    color: #c82829
}

.hljs-built_in, .hljs-constant, .hljs-literal, .hljs-number, .hljs-params, .hljs-pragma, .hljs-preprocessor {
    color: #f5871f
}

.css .hljs-rules .hljs-attribute, .ruby .hljs-class .hljs-title {
    color: #eab700
}

.hljs-header, .hljs-inheritance, .hljs-string, .hljs-value, .ruby .hljs-symbol, .xml .hljs-cdata {
    color: #718c00
}

.css .hljs-hexcolor {
    color: #3e999f
}

.coffeescript .hljs-title, .hljs-function, .javascript .hljs-title, .perl .hljs-sub, .python .hljs-decorator, .python .hljs-title, .ruby .hljs-function .hljs-title, .ruby .hljs-title .hljs-keyword {
    color: #4271ae
}

.hljs-keyword, .javascript .hljs-function {
    color: #8959a8
}

.hljs {
    display: block;
    overflow-x: auto;
    background: #fff;
    color: #4d4d4c;
    padding: .5em
}

.debugger, .debugger #layout, .debugger body {
    height: 100%;
    overflow: hidden
}

.coffeescript .javascript, .javascript:not(html) .xml, .tex .hljs-formula, .xml .css, .xml .hljs-cdata, .xml .javascript, .xml .vbscript {
    opacity: .5
}

.dbg-link {
    padding: 10px 15px
}

.debugger select {
    font-size: 13px !important;
    color: #555
}

.debugger #layout {
    position: relative;
    min-width: 800px
}

#dbg-header {
    z-index: 200;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: #0e90d2;
    color: #fff;
    border-bottom: 1px solid #0b76ac;
    padding-left: 10px
}

#dbg-header h1 {
    float: left
}

#dbg-header h1 a {
    line-height: 50px;
    color: #fff;
    font-size: 1.8rem;
    font-weight: 400;
    display: inline-block;
    height: 50px;
    width: 135px;
    text-indent: -9999px;
    background: url(http://s.amazeui.org/media/i/brand/amazeui-cw.svg) left center no-repeat;
    -webkit-background-size: 125px 24px;
    background-size: 125px 24px
}

#code-area, #code-area .CodeMirror, #dbg-main {
    height: 100%
}

#dbg-header h2 {
    margin-top: 15px;
    font-size: 16px;
    font-weight: 400
}

#dbg-header .dbg-toolbar {
    position: absolute;
    right: 10px;
    top: 10px
}

.dbg-sidebar {
    z-index: 100;
    width: 180px;
    position: fixed;
    top: 50px;
    bottom: 0;
    left: 180px;
    background: #f5f5f5;
    margin-left: -180px;
    overflow: auto;
    padding: 10px 0
}

.dbg-sidebar:after {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: block;
    content: "";
    width: 1px;
    background: #ddd;
    box-shadow: 0 0 3px 2px #ddd
}

.dbg-sidebar select {
    display: block;
    width: 100%
}

.dbg-sidebar select option {
    display: block;
    word-wrap: normal;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.dbg-sidebar h2 {
    margin-top: 0;
    font-size: 14px;
    font-weight: 400;
    padding: 5px 10px;
    color: #555;
    margin-bottom: 10px
}

.dbg-sidebar .bar-item {
    padding: 0 10px
}

#dbg-main {
    padding-left: 180px;
    padding-right: 360px;
    padding-top: 50px
}

.editor-row {
    height: 33.3333%;
    border-bottom: 1px solid #ddd
}

.editor-row:after, .editor-row:before {
    content: " ";
    display: table
}

.editor-row.last {
    border-bottom: none
}

.editor-item {
    height: 100%;
    position: relative
}

.editor-item::after {
    color: #999;
    position: absolute;
    right: 0;
    top: 0;
    background: #f8f8f8;
    font-size: 12px;
    width: 90px;
    border: 1px solid #ddd;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 2px;
    text-align: center;
    padding: 0 20px 0 5px;
    z-index: 98
}

.editor-full-trigger {
    position: absolute;
    right: 5px;
    top: -1px;
    z-index: 99;
    display: block;
    width: 20px;
    text-align: center;
    font-size: 14px
}

.ios-carrier, .ios-clock {
    font-size: 12px;
    line-height: 20px
}

.full-active .editor-full-trigger {
    position: fixed;
    right: 5px;
    top: 5px;
    z-index: 10100;
    background: #f8f8f8;
    border: 1px solid #dedede;
    border-radius: 2px;
    -webkit-transition: all .1s;
    transition: all .1s
}

#demo-area:after, #demo-wrap {
    top: 0;
    display: block;
    position: absolute;
    bottom: 0;
    left: 0
}

.full-active .editor-full-trigger::before {
    content: "\f066"
}

.full-active .editor-full-trigger:hover {
    background: #0e90d2;
    color: #fff
}

.editor-style .editor-item {
    width: 50%;
    float: left
}

#form-getdemo {
    display: block;
    height: 100%;
    width: 100%
}

#code-hbs:after {
    content: "Template"
}

#code-css:after {
    content: "Core CSS"
}

#code-theme:after {
    content: "Theme"
}

#code-js {
    border-bottom: none
}

#code-js:after {
    content: "Data"
}

#demo-area:after, .ui-safari::after {
    content: ""
}

#demo-area {
    min-height: 578px;
    margin-right: -360px;
    width: 360px;
    position: fixed;
    right: 360px;
    top: 50px;
    bottom: 0;
    background: #eee
}

#demo-area:after {
    width: 1px;
    background: #ccc;
    -webkit-box-shadow: 0 0 3px 2px #ddd;
    box-shadow: 0 0 3px 2px #ddd
}

#demo-wrap {
    width: 330px;
    height: 578px;
    overflow: auto;
    margin: auto;
    right: 0;
    border: 1px solid #ddd;
    -webkit-box-shadow: 0 0 0 1px rgba(14, 144, 210, .15);
    box-shadow: 0 0 0 1px rgba(14, 144, 210, .15);
    background-color: #fff
}

#demo-viewport {
    display: block;
    height: 504px;
    width: 100%;
    -webkit-transition: opacity .45s;
    transition: opacity .45s
}

#demo-viewport::-webkit-scrollbar {
    position: absolute;
    display: none
}

.ios-status-bar {
    height: 20px;
    position: relative;
    font-family: "Helvetica Neue Light", HelveticaNeue-Light, "Helvetica Neue", Calibri, Helvetica, Arial
}

.ui-safari {
    background: #f8f8f8;
    border-bottom: 1px solid #dcdcdc
}

.ui-safari::after {
    display: block;
    height: 6px;
    width: 100%;
    border-bottom: 1px solid #fff
}

.ios-signal {
    position: absolute;
    left: 6px;
    top: 7px;
    padding-left: 0
}

.ios-signal li {
    height: 6px;
    width: 6px;
    background: #000;
    border-radius: 50%;
    margin-right: 1px;
    float: left
}

.ios-signal:after, .ios-signal:before {
    content: " ";
    display: table
}

#notification:after, .ios-battery::after {
    content: ""
}

.ios-carrier {
    position: absolute;
    left: 45px;
    top: 0;
    font-weight: 300;
    height: 20px
}

.ios-clock {
    text-align: center;
    font-weight: 500
}

#widget-info h3, .bar-options label {
    font-weight: 400
}

.ios-battery {
    position: absolute;
    right: 7px;
    top: 5px;
    background: #000;
    width: 20px;
    height: 10px;
    border: 1px solid #c8c7cc
}

.ios-battery::after {
    width: 2px;
    height: 4px;
    position: absolute;
    right: -3px;
    top: 2px;
    background: #000
}

.ios-address {
    padding: 4px 9px;
    position: relative
}

.ios-address input {
    width: 100%;
    background: #e6e6e8;
    border-radius: 3px;
    border: none;
    height: 29px;
    line-height: 21px;
    outline: 0;
    padding: 4px 5px;
    font-size: 13px
}

.ios-address #demo-qrcode-trigger {
    width: 20px;
    font-size: 20px;
    line-height: 1;
    display: block;
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer
}

#demo-qrcode-wrap {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .7);
    -webkit-transition: all .3s;
    transition: all .3s;
    width: 0;
    overflow: hidden;
    display: block
}

#demo-qrcode {
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -100px;
    margin-top: -100px;
    width: 210px;
    height: 0;
    overflow: hidden;
    padding: 5px;
    background: #fff;
    -webkit-transition: all .3s;
    transition: all .3s
}

#demo-qrcode:hover {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg)
}

.qr-active #demo-qrcode-wrap {
    width: 100%
}

.qr-active #demo-qrcode {
    height: 210px;
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg)
}

#widget-info {
    padding: 10px;
    font-size: 12px;
    position: relative
}

#widget-info .md-icon {
    position: absolute;
    right: 10px;
    top: 0;
    background: #fff;
    border-radius: 2px
}

#widget-info .md-icon img {
    width: 28px;
    height: 28px;
    box-shadow: 0 0 1px rgba(0, 0, 0, .1)
}

#widget-info h3 {
    font-size: 18px
}

#widget-info ul {
    padding-left: 0
}

#widget-info ul li {
    float: left;
    width: 50%
}

#widget-info ul li a {
    margin: 5px;
    display: block;
    font-size: 12px
}

#widget-info ul:after, #widget-info ul:before {
    content: " ";
    display: table
}

#demo-color-sets {
    padding: 10px 5px;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 180px;
    right: 0;
    z-index: 11111;
    color: #555;
    -webkit-transition: all .3s ease-in-out .1s;
    transition: all .3s ease-in-out .1s;
    -webkit-transform: translate(0, 100%);
    -ms-transform: translate(0, 100%);
    transform: translate(0, 100%);
    -webkit-box-shadow: 0 -1px 3px 1px #dedede;
    box-shadow: 0 -1px 3px 1px #dedede
}

#demo-color-sets label {
    font-weight: 400;
    font-size: 13px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

#demo-color-sets li {
    padding: 10px 15px
}

#demo-color-sets.am-active {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0)
}

#color-picker-wrap {
    width: 200px;
    height: 200px;
    position: fixed;
    z-index: 999999;
    background-color: rgba(0, 0, 0, .15);
    box-shadow: 0 0 5px rgba(255, 255, 255, .15);
    border-radius: 5px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    top: 120%
}

#color-picker-wrap .am-icon-times-circle {
    color: #aaa;
    font-size: 20px;
    position: absolute;
    right: -10px;
    top: -10px;
    -webkit-transition: all .15s;
    transition: all .15s;
    cursor: pointer
}

#mobile-index, #notification {
    -webkit-transition: all .3s ease-in-out
}

#color-picker-wrap .am-icon-times-circle:hover {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg)
}

#color-sets-trigger {
    outline: 0;
    background: 0 0;
    border: none;
    width: 32px
}

#color-sets-trigger span {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-top: 2px;
    margin-right: 2px;
    background: #dd514c;
    float: left;
    line-height: 0;
    overflow: hidden
}

#color-sets-trigger .csq-2 {
    background: #F37B1D
}

#color-sets-trigger .csq-3 {
    background: #3bb4f2
}

#color-sets-trigger .csq-4 {
    background: #5eb95e
}

#notification {
    padding: 10px 20px;
    background: rgba(255, 255, 255, .8);
    position: fixed;
    top: 50px;
    right: 0;
    z-index: 11111;
    font-size: 1.4rem;
    color: #ddd;
    box-shadow: 0 0 3px rgba(255, 255, 255, .55);
    transition: all .3s ease-in-out;
    -webkit-transform: translate(105%, -105%);
    -ms-transform: translate(105%, -105%);
    transform: translate(105%, -105%)
}

.demo-bar, .m .demo-header {
    box-shadow: 0 0 3px rgba(0, 0, 0, .15)
}

#notification.am-active {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0)
}

#notification.msg-error {
    color: #dd514c
}

#notification:after {
    position: absolute;
    right: 0;
    bottom: -14px;
    display: block;
    width: 0;
    height: 0;
    border-width: 7px;
    border-style: solid;
    border-color: rgba(255, 255, 255, .1) transparent transparent rgba(255, 255, 255, .1);
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg)
}

@media screen and (max-width: 1023px) {
    #dbg-main {
        padding-right: 0
    }

    #demo-area {
        display: none
    }
}

.bar-options, .bar-options .am-input-sm {
    font-size: 13px !important
}

.debugger .am-input-sm {
    padding: .4em !important;
    font-size: 13px
}

.debugger .am-form-group {
    margin-bottom: 10px
}

.debugger .am-popover-inner {
    font-size: 13px;
    padding: 5px
}

.cst-widget-themes {
    display: none;
    margin-top: 3px;
    margin-bottom: 5px
}

.cst-widget-themes.am-active {
    display: block
}

.demo .demo-header:after, .demo .demo-header:before, .demo-bar:after, .demo-bar:before {
    content: " ";
    display: table
}

#amz-compile {
    width: 100%
}

.customize input[type=radio], .customize input[type=checkbox] {
    margin-top: 6px
}

.customize .am-checkbox {
    margin-top: 0;
    margin-bottom: 6px
}

.page {
    padding: 0
}

.pg-demo .ds-thread {
    padding: 20px 10px 10px
}

.pg-demo > .am-pagination {
    margin: 20px 10px 0;
    font-size: 12px
}

.demo #demo-area {
    padding: 20px 40px
}

.demo .demo-header, .demo-bar {
    position: relative;
    height: 50px;
    font-size: 20px;
    color: #FFF;
    padding: 10px
}

.am-demo {
    background: #FFF;
    margin: 1rem 0 2rem
}

.demo .demo-header h1, .demo-bar h1 {
    margin: 0;
    font-weight: 400;
    text-align: center
}

.demo-bar {
    background: #3bb4f2;
    line-height: 29px
}

.demo-bar h1 {
    font-size: 20px
}

.demo .demo-header {
    background: #69778A;
    line-height: 30px
}

.am-page, .m body, html.m {
    height: 100%
}

.demo .demo-header h1 {
    font-size: 16px
}

.demo-icon-home {
    position: absolute;
    color: #eee;
    -webkit-transition: all .2s;
    transition: all .2s;
    padding: 0 10px;
    border-radius: 2px;
    left: 10px
}

.am-page, .m body {
    position: relative;
    width: 100%;
    overflow: hidden
}

.demo-icon-home:hover {
    background: rgba(255, 255, 255, .3)
}

.demo-name {
    font-size: 1.8rem;
    border-bottom: 1px solid #DDD;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    font-weight: 400
}

.demo-helper {
    margin: 10px
}

.am-page .am-list > li > a {
    margin-bottom: 0;
    padding-left: 10px
}

#demo-list, #widget-list {
    position: absolute;
    top: 49px;
    bottom: 0;
    z-index: 10;
    width: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch
}

.m .demo-header, .m-widget-list li {
    position: relative
}

.m .demo-header {
    z-index: 100;
    background: #0e90d2;
    color: #FFF
}

.m .demo-header h1 {
    height: 49px;
    line-height: 49px;
    padding-left: 10px;
    font-weight: 400;
    text-align: center;
    margin: 0;
    font-size: 18px
}

.m-widget-list {
    margin: 0
}

.m-widget-list .widget-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px
}

.m-widget-list a {
    -webkit-transition: all .15s;
    transition: all .15s
}

.m-widget-list a:hover {
    background: #F5F5F5
}

.m-widget-list a::after {
    display: inline-block;
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translate(0, 0);
    content: "\f054";
    position: absolute;
    right: 10px;
    color: #ccc;
    top: 50%;
    margin-top: -8px
}

#mobile-index {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    transition: all .3s ease-in-out
}

#demo-list-page {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #FFF;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    -webkit-transform: translate(100%, 0);
    -ms-transform: translate(100%, 0);
    transform: translate(100%, 0)
}

.demo-list-active {
    height: 100%
}

.demo-list-active #mobile-index {
    -webkit-transform: translate(-100%, 0);
    -ms-transform: translate(-100%, 0);
    transform: translate(-100%, 0)
}

.demo-list-active #demo-list-page {
    display: block;
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0)
}

.widget-hd {
    position: relative
}

.widget-hd #btn-back {
    display: inline-block;
    width: 44px;
    height: 43px;
    position: absolute;
    left: 5px;
    top: 50%;
    margin-top: -21.5px;
    text-align: center;
    line-height: 43px;
    cursor: pointer
}

.widget-hd #btn-back:hover {
    background: rgba(0, 0, 0, .1)
}

.widget-hd h1 {
    padding-left: 42px
}

html.doc-ide {
    overflow: hidden;
    height: 100%
}

.doc-ide #layout, .doc-ide body {
    height: 100%
}

.doc-ide #main {
    padding-right: 0;
    padding-left: 180px;
    height: 100%;
    padding-top: 50px
}

.doc-ide .main-content {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden
}

.doc-icon-list a, .doc-ide .md-toc a, .doc-to {
    word-wrap: normal;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.doc-ide #dbg-sidebar ul {
    list-style: none;
    padding-left: 0;
    font-size: 14px
}

.doc-ide #dbg-sidebar ul ul {
    margin-top: 0
}

.doc-ide .md-toc .am-nav-divider {
    color: #333;
    margin-top: 8px;
    border-bottom: 1px solid #dedede;
    padding: 5px 10px;
    font-weight: 700;
    margin-bottom: 5px
}

.doc-ide .md-toc ul {
    display: none
}

.doc-ide .md-toc ul a {
    padding: 3px 15px 3px 35px;
    font-size: 13px
}

.doc-ide .md-toc a {
    color: #555;
    padding: 4px 15px;
    display: block
}

.doc-ide .md-toc .am-active > a {
    color: #0e90d2;
    font-weight: 500
}

.doc-ide .md-toc .am-active ul {
    display: block
}

.doc-ide .doc-content {
    padding: 30px 25px
}

.doc-ide .doc-content h2:before, .doc-ide .doc-content h3:before, .doc-ide .doc-content h4:before {
    display: none
}

.doc-ide .doc-content .doc-divider {
    margin: 25px 0 30px;
    border-top: 1px dotted #aaa
}

.doc-am-g + .doc-am-g, .doc-example .am-container + * {
    margin-top: 10px
}

.doc-animations > [class^=col-], .doc-animations > [class^=am-u-], .doc-example .am-input-group, .doc-example > .am-btn, .doc-gradient {
    margin-bottom: 10px
}

.doc-content .doc-am-g {
    margin-left: initial;
    margin-right: initial
}

.doc-am-g > [class*=col-], .doc-am-g > [class*=am-u-] {
    border: 1px solid #dd514c;
    padding-top: 5px;
    padding-bottom: 5px
}

.doc-am-g > [class*=col-]:nth-child(2n), .doc-am-g > [class*=am-u-]:nth-child(2n) {
    background: #eee
}

.doc-example .am-container {
    border: 1px solid #00f
}

.doc-example .am-container > .am-g {
    margin-left: -1rem;
    margin-right: -1rem
}

@media only screen and (min-width: 641px) {
    .doc-example .am-container > .am-g {
        margin-left: -1.5rem;
        margin-right: -1.5rem
    }
}

.demo-icon-actions .am-badge + .am-badge, .doc-example [class*=am-icon-] + [class*=am-icon-] {
    margin-left: 5px
}

.doc-icon-list li {
    position: relative
}

.doc-icon-list li.copy-active .demo-icon-actions {
    opacity: 1;
    display: block
}

.doc-icon-list a {
    color: #666;
    padding: .5em .25em;
    display: block;
    font-size: 14px
}

.doc-icon-list [class*=am-icon-] {
    margin-right: 5px
}

.demo-icon-actions {
    display: none;
    position: absolute;
    right: 5px;
    top: 5px
}

.demo-icon-actions .am-badge {
    cursor: pointer
}

.doc-icon-custom:before {
    display: inline-block;
    font: normal normal normal 1.6rem/1 FontAwesome, sans-serif;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translate(0, 0);
    content: "\f18a"
}

.doc-icon-hd {
    margin-top: 2rem
}

.doc-block-grid {
    margin-left: -5px;
    margin-right: -5px
}

.doc-block-grid > li {
    padding: 0 5px 10px
}

.doc-block-grid > li img {
    border: 1px solid #CCC;
    padding: 2px;
    background: #FFF
}

.doc-cf .am-fl, .doc-cf .am-fr {
    width: 30%;
    border: 1px solid #DDD
}

.doc-example .am-pagination {
    margin-left: 0;
    margin-right: 0
}

.doc-slide-in {
    -webkit-animation: doc-slide-in .5s linear;
    animation: doc-slide-in .5s linear
}

.doc-slide-out {
    -webkit-animation: doc-slide-out .35s linear;
    animation: doc-slide-out .35s linear
}

@-webkit-keyframes doc-slide-in {
    0% {
        opacity: 0
    }
    100% {
        opacity: 1
    }
}

@keyframes doc-slide-in {
    0% {
        -webkit-transform: translate3d(100%, 0, 0);
        -ms-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
    100% {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@-webkit-keyframes doc-slide-out {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    100% {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

@keyframes doc-slide-out {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    100% {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

.doc-cf .am-fl {
    height: 50px;
    background: #CCC
}

.doc-cf .am-fr {
    height: 80px;
    background: #DCEFB4
}

.doc-placeholder::-moz-placeholder {
    color: red;
    opacity: 1
}

.doc-placeholder:-ms-input-placeholder {
    color: red
}

.doc-placeholder::-webkit-input-placeholder {
    color: red
}

.doc-to {
    display: block;
    width: 100px
}

.doc-ir a {
    display: block;
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    border: 0;
    background: url(http://www.yunshipei.com/static/img/logo.png) no-repeat #000;
    height: 38px
}

.doc-box {
    height: 80px;
    width: 120px;
    margin-right: 20px;
    background: #5F3A7A;
    color: #EEE;
    float: left;
    padding: 5px
}

.doc-gradient-1, .doc-gradient-2, .doc-gradient-3 {
    background-repeat: repeat-x
}

.doc-box.doc-br-top {
    border-top-right-radius: 5px;
    border-top-left-radius: 5px
}

.doc-box.doc-br-right {
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px
}

.doc-box.doc-br-bottom {
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px
}

.doc-box.doc-br-left {
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px
}

.doc-box-shadow {
    -webkit-box-shadow: 0 0 5px #000;
    box-shadow: 0 0 5px #000
}

.doc-gradient {
    width: 50%;
    float: left;
    height: 50px
}

.doc-caret-d, .doc-caret-l, .doc-caret-r, .doc-caret-u {
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    transform: rotate(360deg)
}

.doc-gradient-1 {
    background-image: -webkit-gradient(linear, 0 top, 100% top, from(red), to(black));
    background-image: -webkit-linear-gradient(left, color-stop(red 0), color-stop(black 100%));
    background-image: -moz-linear-gradient(left, red 0, #000 100%);
    background-image: linear-gradient(to right, red 0, #000 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffff0000', endColorstr='#ff000000', GradientType=1)
}

.doc-gradient-2 {
    background-image: -webkit-gradient(linear, left 0, left 100%, from(red), to(black));
    background-image: -webkit-linear-gradient(top, red 0, #000 100%);
    background-image: -moz-linear-gradient(top, red 0, #000 100%);
    background-image: linear-gradient(to bottom, red 0, #000 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffff0000', endColorstr='#ff000000', GradientType=0)
}

.doc-gradient-3 {
    background-image: -webkit-linear-gradient(45deg, red, #000);
    background-image: -moz-linear-gradient(45deg, red, #000);
    background-image: linear-gradient(45deg, red, #000)
}

.doc-gradient-4, .doc-gradient-5, .doc-gradient-6 {
    background-repeat: no-repeat
}

.doc-gradient-4 {
    background-image: -webkit-gradient(left, linear, 0 0, 0 100%, from(#00b3ee), color-stop(50%, #7a43b6), to(#c3325f));
    background-image: -webkit-linear-gradient(left, #00b3ee, #7a43b6 50%, #c3325f);
    background-image: -moz-linear-gradient(left, #00b3ee, #7a43b6 50%, #c3325f);
    background-image: linear-gradient(to right, #00b3ee, #7a43b6 50%, #c3325f);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00b3ee', endColorstr='#ffc3325f', GradientType=1)
}

.doc-gradient-5 {
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#00b3ee), color-stop(50%, #7a43b6), to(#c3325f));
    background-image: -webkit-linear-gradient(#00b3ee, #7a43b6 50%, #c3325f);
    background-image: -moz-linear-gradient(top, #00b3ee, #7a43b6 50%, #c3325f);
    background-image: linear-gradient(#00b3ee, #7a43b6 50%, #c3325f);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00b3ee', endColorstr='#ffc3325f', GradientType=0)
}

.doc-gradient-6 {
    background-image: -webkit-gradient(radial, center center, 0, center center, 460, from(red), to(black));
    background-image: -webkit-radial-gradient(circle, red, #000);
    background-image: -moz-radial-gradient(circle, red, #000);
    background-image: radial-gradient(circle, red, #000)
}

.doc-gradient-7 {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(.25, #00f), color-stop(.25, transparent), color-stop(.5, transparent), color-stop(.5, #00f), color-stop(.75, #00f), color-stop(.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, #00f 25%, transparent 25%, transparent 50%, #00f 50%, #00f 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, #00f 25%, transparent 25%, transparent 50%, #00f 50%, #00f 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, #00f 25%, transparent 25%, transparent 50%, #00f 50%, #00f 75%, transparent 75%, transparent)
}

.doc-caret-u {
    border-bottom: 10px solid red;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-top: 0 dotted
}

.doc-caret-d {
    border-top: 15px solid purple;
    border-right: 15px solid transparent;
    border-left: 15px solid transparent;
    border-bottom: 0 dotted
}

.doc-caret-l {
    border-right: 20px solid #00f;
    border-top: 20px solid transparent;
    border-bottom: 20px solid transparent;
    border-left: 0 dotted
}

.doc-caret-r {
    border-left: 25px solid green;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    border-right: 0 dotted
}

.doc-arrow, .doc-arrow-d, .doc-arrow-l, .doc-arrow-u {
    display: inline-block;
    width: 10px;
    height: 10px
}

.doc-arrow {
    border: solid purple;
    border-width: 3px 3px 0 0;
    transform: rotate(45deg);
    margin-right: 10px
}

.doc-arrow-d {
    border: solid red;
    border-width: 3px 3px 0 0;
    transform: rotate(135deg)
}

.doc-arrow-u {
    border: solid green;
    border-width: 3px 3px 0 0;
    transform: rotate(-45deg)
}

.doc-arrow-l {
    border: solid #00f;
    border-width: 3px 3px 0 0;
    transform: rotate(-135deg)
}

.doc-ir-demo {
    background: #3bb4f2
}

.doc-ir-demo h1 {
    margin: 0;
    padding: 10px
}

.doc-ir-demo a {
    display: block;
    height: 30px;
    width: 125px;
    background: url(http://a.static.amazeui.org/assets/i/ui/logo.png) left center no-repeat;
    -webkit-background-size: 125px 24px;
    background-size: 125px 24px
}

.am-topbar .am-text-ir {
    display: block;
    margin-right: 10px;
    height: 50px;
    width: 125px;
    background: url(http://a.static.amazeui.org/assets/i/ui/logo.png) left center no-repeat;
    -webkit-background-size: 125px 24px;
    background-size: 125px 24px
}

.amz-cse {
    display: none
}

.amz-cse.am-active .cse-form {
    width: 180px;
    background-color: #fff
}

.amz-cse.am-active .cse-form input[type=text] {
    margin-left: 30px;
    width: 140px;
    color: #333;
    outline: 0
}

.amz-cse.am-active .cse-form input[type=text]::-moz-placeholder {
    color: #999;
    opacity: 1
}

.amz-cse.am-active .cse-form input[type=text]:-ms-input-placeholder {
    color: #999
}

.amz-cse.am-active .cse-form input[type=text]::-webkit-input-placeholder {
    color: #999
}

.amz-cse.am-active .cse-form button {
    color: #555;
    z-index: 110
}

.cse-form {
    position: relative;
    margin-top: 9px;
    width: 40px;
    height: 32px;
    -webkit-transition: .15s;
    transition: .15s
}

.cse-form input[type=text] {
    position: relative;
    z-index: 100;
    border: none;
    background-color: transparent;
    color: transparent;
    width: 40px;
    height: 32px;
    font-size: 14px;
    line-height: 32px;
    -webkit-appearance: none;
    -webkit-transition: all .15s;
    transition: all .15s
}

.cse-form input[type=text]::-moz-placeholder {
    color: transparent;
    opacity: 1
}

.cse-form input[type=text]:-ms-input-placeholder {
    color: transparent
}

.cse-form input[type=text]::-webkit-input-placeholder {
    color: transparent
}

.cse-form button {
    position: absolute;
    top: 3px;
    left: 2px;
    z-index: 90;
    -webkit-appearance: none;
    background: 0 0;
    border: none;
    outline: 0;
    color: #f2f2f2
}

@media only screen and (min-width: 1025px) {
    .amz-cse {
        display: block
    }

    .amz-cse form {
        width: 0
    }
}

#MicrosoftTranslatorToolbar {
    background: #000 !important;
    border: none !important
}

#MSTTProgressBar {
    height: 3px !important;
    background-image: none !important
}

.MSTTLanguageBox, .MSTTOptBox {
    border-left-color: #ccc !important
}

.lte9 a[href*="/css/animation"], .lte9 a[href*="/javascript/fastclick"], .lte9 a[href*="/javascript/hammer"], .lte9 a[href*="/javascript/fullscreen"], .lte9 a[href*="/javascript/scrollspy"], .lte9 a[href*="/javascript/smooth-scroll"], .lte9 a[href*="javascript/iscroll-lite"] {
    display: none
}

.lte9

#
amz-go-top {
    display: none !important
}

.lte9 .ie-warning {
    margin-bottom: 0;
    text-align: center;
    border: none
}

.lte9 .ie-warning a {
    font-weight: 700;
    border-bottom: 1px dashed #fff
}

.lte8 a[href*="/javascript/tabs"] {
    display: none
}