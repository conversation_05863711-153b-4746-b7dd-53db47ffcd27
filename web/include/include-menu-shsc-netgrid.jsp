<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ include file="include-taglib.jsp"%>
<style>
    .left-sidebar {
        transition: all 0.4s ease-in-out;
        width: 240px;
        min-height: 100%;
        padding-top: 0;
        position: absolute;
        z-index: 1104;
        top: 0;
        left: 0px;
    }
</style>
<div class="left-sidebar">
    <!-- 菜单 -->
    <ul class="sidebar-nav">
        <li class="sidebar-nav-link">

        </li>

    </ul>
</div>
<script type="text/javascript">
    $('.sidebar-nav-sub-title').on('click', function() {
        $(this).siblings('.sidebar-nav-sub')
                .slideToggle( 80)
                .end()
                .find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');
        console.log("1 click")
    })

    //选中菜单
    $("#${__menu_id}").addClass( "active").parents( ".sidebar-nav-sub").css( "display", "block").prev( ".sidebar-nav-sub-title").find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');

</script>