<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ include file="include-taglib.jsp"%>
<script src="${CTX}/plugin/jquery/jquery.form.js"></script>

<script src="${CTX}/plugin/amaze/js/amazeui.min.js"></script>
<script src="${CTX}/plugin/amaze/js/amazeui.datatables.min.js"></script>
<script src="${CTX}/plugin/amaze/js/dataTables.responsive.min.js"></script>
<script src="${CTX}/plugin/chosen/chosen.jquery.min.js"></script>
<script src="${CTX}/plugin/kendoui.2018.2.516/js/kendo.all.min.js"></script>
<script src="${CTX}/plugin/kendoui.2018.2.516/js/messages/kendo.messages.zh-CN.min.js"></script>
<script src="${CTX}/plugin/kendoui.2018.2.516/js/cultures/kendo.culture.zh-CN.min.js"></script>
<script src="${CTX}/comm/commUtil.js"></script>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js" ></script>
<%--<script src="${CTX}/plugin/jssdk/jssdk.js.jsp?url=${pageContext.request.requestURL}"></script>--%>
<%--<script src="http://webapi.amap.com/maps?v=1.4.0&key=8334b5c8169697aa9bd725f71b969ce3"></script>--%>
<%--<script src="${CTX}/plugin/ol3/ol.min.js"></script>--%>
<script type="text/javascript">
    kendo.culture("zh-CN");



    autoLeftNav();
    var height = $(window).height() - 56;
    var $content = $('.tpl-content-wrapper');
    $content.css("min-height", height);
    try {
        if (resizeContent) {
            resizeContent();
        }
    }catch(e){

    }
    $(window).resize(function() {
        autoLeftNav();
        if (resizeContent) {
            resizeContent();
        }
    });

    // 侧边菜单开关
    function autoLeftNav() {
        var width = $(window).width();
        var $menu = $('.left-sidebar');
        var $content = $('.tpl-content-wrapper');
        $('.tpl-header-switch-button').on('click', function() {
            if ($menu.is('.active')) {
                if (width > 1024) {
//                    $content.removeClass('active');
                }
                $menu.removeClass('active');
            } else {
                if (width > 1024) {
//                    $content.addClass('active');
                }
                $menu.addClass('active');
            }
        })

        if (width < 1024) {
//            $menu.addClass('active');
        } else {
//            $menu.removeClass('active');
        }


        //henry 高度适配


    }

    /**
     * form表单转json
     **/
    function form2Obj(selector,$container,type) {
        var obj = {};
        if(!$container) $container = $("body");

        var _selector = "input[type!=radio][type!=checkbox]" + (selector ? "[name^=" + selector + "]" : "");
        $( _selector, $container).each(function() {
            var $this = $(this);
            if(!$this.val()) {
                return;
            }
            var name = $this.attr( "name");
            if (selector) {
                name = name.replace( selector, "");
            }
            obj[name] = $this.val();
//            console.log( name, obj[name], $this.hasClass( "kendo-datepicker"))
            if (!$this.hasClass( "kendo-datepicker")) {
                return;
            }

            var picker = $this.data("kendoDatePicker").value();
            if (picker) {
                obj[name + "_timestamp"] = picker.getTime();
            }

        });

        _selector = "input[type=radio]" + (selector ? "[name^=" + selector + "]" : "") + ":checked";
        $( _selector, $container).each(function() {
            var $this = $(this);
            if(!$this.val()) {
                return;
            }
            var name = $this.attr( "name");
            if (selector) {
                name = name.replace( selector, "");
            }
            obj[name] = $this.val();
        });

        _selector = "input[type=checkbox]" + (selector ? "[name^=" + selector + "]" : "") + ":checked";
        $( _selector, $container).each(function(){
            var $this = $(this);
            if(!$this.val()) {
                return;
            }
            var name = $this.attr( "name");
            if (selector) {
                name = name.replace( selector, "");
            }
            obj[name] = (obj[name] ? (obj[name] + "," + $this.val()) : $this.val());
        });

        _selector = "select" + (selector ? "[name^=" + selector + "]" : "");
        $( _selector, $container).each(function() {
            var $this = $(this);
            var name = $this.attr( "name");
            if (selector) {
                name = name.replace( selector, "");
            }
            obj[name]  = $this.find("option:selected").map(function() {
                return $(this).val();
            }).get().join( ",");
        });

        _selector = "textarea" + (selector ? "[name^=" + selector + "]" : "");
        $( _selector, $container).each(function() {
            var $this = $(this);
            if(!$this.val()) {
                return;
            }
            var name = $this.attr( "name");
            if (selector) {
                name = name.replace( selector, "");
            }
            obj[name] = $this.val();
        });


        return obj;
    }


    /**
     * 从身份证号码中提取信息
     * @param idCard
     */
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    var ID_CARD_REG = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    function getIdCardInfo(idCard) {
        var ret = {};
        if (!ID_CARD_REG.test(idCard)) {
            return null;
        }
        var now = new Date();
        if (idCard.length == 18) {
            var year = +idCard.substr( 6,4);
            var month = +idCard.substr( 10,2);
            var day = +idCard.substr( 12,2);
            console.log( year, month, day, +now.getFullYear() - year, now.getFullYear());
            ret.age = +now.getFullYear() - year;
            ret.birthday = new Date( year, month - 1, day);

            return ret;
        }

        return null;
    }



    <c:if test="${__jsapi_ticket != null}">
    try {
        //alert("wx.config...!!");
        wx.config({
            beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
            debug: false, //开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId:  "${__jsapi_ticket["app_id"]}", // 必填，企业微信的corpID
            timestamp: +"${__jsapi_ticket["timestamp"]}", // 必填，生成签名的时间戳
            nonceStr:  "${__jsapi_ticket["nonce_str"]}", // 必填，生成签名的随机串
            signature: "${__jsapi_ticket["signature"]}",// 必填，签名，见附录1
            jsApiList: ["onMenuShareAppMessage","onMenuShareWechat","startRecord","stopRecord","onVoiceRecordEnd",
                "playVoice","pauseVoice","stopVoice","onVoicePlayEnd","uploadVoice","downloadVoice","chooseImage",
                "previewImage","uploadImage","downloadImage","getNetworkType","openLocation","getLocation",
                "hideOptionMenu","showOptionMenu","hideMenuItems","showMenuItems","hideAllNonBaseMenuItem",
                "showAllNonBaseMenuItem","closeWindow","scanQRCode","chooseVideo","uploadVideo","downloadVideo",
                "agentConfig","getStepCount","getAllPhoneContacts","addCalendarEvent","showWatermark","launch3rdApp",
                "getInstallState","openUserProfile","selectExternalContact","selectEnterpriseContact","bioassayAuthentication",
                "openUrl","openEnterpriseApp","request3rdApp"] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
        });
    } catch (e) {
        //alert("wx.config.error!!");
        console.log( e)
    }
    </c:if>
    <c:if test="${__jsapi_ticket == null}">

    </c:if>
</script>
