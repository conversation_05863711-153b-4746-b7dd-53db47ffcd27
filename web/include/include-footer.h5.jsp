<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ include file="include-taglib.jsp"%>
<script src="${CTX}/plugin/jquery/jquery.form.js"></script>
<script src="${CTX}/plugin/amaze/js/amazeui.min.js"></script>
<%--<script src="${CTX}/plugin/amazeui/dist/ks.fef30.min.js"></script>--%>

<script src="${CTX}/plugin/kendoui.2018.2.516/js/kendo.all.min.js"></script>
<script src="${CTX}/plugin/kendoui.2018.2.516/js/messages/kendo.messages.zh-CN.min.js"></script>
<script src="${CTX}/plugin/kendoui.2018.2.516/js/cultures/kendo.culture.zh-CN.min.js"></script>
<script src="${CTX}/plugin/mescroll/mescroll.min.js" type="text/javascript" charset="utf-8"></script>
<script src="${CTX}/plugin/weekdate/script.js?v=1.8"></script>
<script src="${CTX}/comm/commUtil.js"></script>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js" ></script>


<script type="text/javascript">
    kendo.culture("zh-CN");


    var $searchBar = $('#searchBar'),
            $searchResult = $('#searchResult'),
            $searchText = $('#searchText'),
            $searchInput = $('#searchInput'),
            $searchClear = $('#searchClear'),
            $searchCancel = $('#searchCancel');

    function hideSearchResult(){
        $searchResult.hide();
        $searchInput.val('');
    }
    function cancelSearch(){
        hideSearchResult();
        $searchBar.removeClass('weui-search-bar_focusing');
        $searchText.show();
    }

    $searchText.on('click', function(){
        $searchBar.addClass('weui-search-bar_focusing');
        $searchInput.focus();
    });
    $searchInput
            .on('blur', function () {
                if(!this.value.length) cancelSearch();
            })
            .on('input', function(){
                if(this.value.length) {
                    $searchResult.show();
                } else {
                    $searchResult.hide();
                }
            })
    ;
    $searchClear.on('click', function(){
        hideSearchResult();
        $searchInput.focus();
    });
    $searchCancel.on('click', function(){
        cancelSearch();
        $searchInput.blur();
    });


    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function beforeDesc(date) {
        if (date == null) {
            return "";
        }

        var between = +new Date() - date;

        if (between < ONE_MINUTE) {
            return "一分钟内";
        }
        if (between < ONE_HOUR) {
            return Math.round( between / ONE_MINUTE) + "分钟前";
        }
        if (between < ONE_DATE) {
            return Math.round(between / ONE_HOUR) + "小时前";
        }
        if (between < ONE_MONTH) {
            return Math.round(between / ONE_DATE) + "天前";
        }
        return Math.round(between / ONE_MONTH) + "个月前";
    }


    <c:if test="${__jsapi_ticket != null}">
        try {
            wx.config({
                beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                debug: false, //开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId:  "${__jsapi_ticket["app_id"]}", // 必填，企业微信的corpID
                timestamp: +"${__jsapi_ticket["timestamp"]}", // 必填，生成签名的时间戳
                nonceStr:  "${__jsapi_ticket["nonce_str"]}", // 必填，生成签名的随机串
                signature: "${__jsapi_ticket["signature"]}",// 必填，签名，见附录1
                jsApiList: ["onMenuShareAppMessage","onMenuShareWechat","startRecord","stopRecord","onVoiceRecordEnd",
                    "playVoice","pauseVoice","stopVoice","onVoicePlayEnd","uploadVoice","downloadVoice","chooseImage",
                    "previewImage","uploadImage","downloadImage","getNetworkType","openLocation","getLocation",
                    "hideOptionMenu","showOptionMenu","hideMenuItems","showMenuItems","hideAllNonBaseMenuItem",
                    "showAllNonBaseMenuItem","closeWindow","scanQRCode","chooseVideo","uploadVideo","downloadVideo",
                    "agentConfig","getStepCount","getAllPhoneContacts","addCalendarEvent","showWatermark","launch3rdApp",
                    "getInstallState","openUserProfile","selectExternalContact","selectEnterpriseContact","bioassayAuthentication",
                    "openUrl","openEnterpriseApp","request3rdApp"] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            });
        } catch (e) {
            console.log( e)
        }
    </c:if>
    <%--<c:if test="${__agentconfig_ticket != null}">--%>
        <%--try {--%>
            <%--wx.agentConfig({--%>
                <%--corpid:  "${__agentconfig_ticket["corp_id"]}", // 必填，企业微信的corpid，必须与当前登录的企业一致--%>
                <%--agentid: "${__agentconfig_ticket["agent_id"]}", // 必填，企业微信的应用id--%>
                <%--timestamp: +"${__agentconfig_ticket["timestamp"]}", // 必填，生成签名的时间戳--%>
                <%--nonceStr: "${__agentconfig_ticket["nonce_str"]}", // 必填，生成签名的随机串--%>
                <%--signature: "${__agentconfig_ticket["signature"]}",// 必填，签名，见附录1--%>
                <%--jsApiList: ["selectExternalContact","openUserProfile", "thirdPartyOpenPage", "getCurExternalContact"], //必填--%>
                <%--success:function() {--%>
                    <%--alert( "success");   --%>
                <%--},--%>
                <%--fail: function (res) {--%>
                    <%--if (res.errMsg.indexOf('function not exist') > -1) {--%>
                        <%--alert('版本过低请升级');--%>
                    <%--}--%>
                <%--}--%>
            <%--});   --%>
        <%--} catch (e) {--%>
            <%--console.log( e);--%>
        <%--}--%>
    <%--</c:if>--%>

</script>