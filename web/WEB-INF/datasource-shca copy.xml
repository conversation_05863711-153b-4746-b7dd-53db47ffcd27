<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass -->
        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver" />

        <!-- 基本属性 url、user、password -->
        <property name="url" value="*************************************************************************************************************" />
        <property name="username" value="root" />
        <property name="password"><value><![CDATA[9a9*5b9*GGl1234gg]]></value></property>

        <!--<property name="url" value="****************************************************************************************************************" />-->
        <!--<property name="username" value="shhd" />-->
        <!--<property name="password"><value><![CDATA[Ao15*&3cA#1d#2**76sdx^^a]]></value></property>-->

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="5" />
        <property name="minIdle" value="8" />
        <property name="maxActive" value="20" />

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000" />

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000" />

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />

    </bean>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <tx:advice id="advice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="*" propagation="REQUIRED" isolation="READ_COMMITTED" />
        </tx:attributes>
    </tx:advice>

    <aop:config>
        <aop:pointcut id="pointcut" expression="execution(* com.*..*.*Service.*(..)) or execution(* com.*..*.*View.*(..))"  />
        <aop:advisor pointcut-ref="pointcut" advice-ref="advice"/>
    </aop:config>

    <bean id="transaction.statement" class="com.ywang.sql.support.adapter.MySQLPageStatement">
        <constructor-arg>
            <bean class="com.ywang.sql.support.TranscationStatememt">
                <constructor-arg ref="dataSource"/>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="db" class="com.ywang.sql.support.Db">
        <property name="statement" ref="transaction.statement"/>
    </bean>
</beans>
