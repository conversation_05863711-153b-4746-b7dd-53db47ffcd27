<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd
        http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util-4.3.xsd">

    <mvc:annotation-driven />
    <context:annotation-config/>
    <context:component-scan base-package="com.shca.config, com.shca.controller" />
    <context:property-placeholder location="classpath:application.properties"/>
    <!-- 该 BeanPostProcessor 将自动对标注 @Autowired 的 Bean 进行注入 -->
    <bean class="org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor"/>
    <bean class="com.ywang.framework.mvc.Context" />
    <bean id="view-index" class="com.shca.view.ShcaIndexView"></bean>
    <bean id="view-system.cromwell" class="com.shca.module.system.view.CromwellView">
    </bean>

    <util:map id="cancer.qcsummary">
        <entry key="breast_cancer">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />

                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="colorectal_cancer">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="ctDNA_pan-cancer">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="300" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="99" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="gastric">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="hepa">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="pancreatic">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="pan">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="fuke">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="gist">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="breast_peixi">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixicolon">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixipan">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixigastric">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixihepa">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixipancreatic">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixifuke">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixigist">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>

        <entry key="neuro">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixineuro">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="bone">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixibone">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixipanMLPA">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="fukeHRD">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Q20_R1" value="90" />
                        <entry key="Q20_R2" value="90" />
                        <entry key="Q30_R1" value="80" />
                        <entry key="Q30_R2" value="80" />
                        <entry key="Mapping_rate(%)" value="90" />
                        <entry key="Coverage_of_target_region(%)" value="90" />
                        <entry key="Average_sequencing_depth_on_target" value="90" />
                        <entry key="Fraction_of_target_covered_with_at_least_10x(%)" value="90" />
                        <entry key="BvsKsuitability" value="0.7" />
                    </map>
                </property>
            </bean>
        </entry>
        <entry key="peixirna">
            <bean class="com.shca.module.cancer.service.support.QcSummaryByBreastCancer">
                <property name="references">
                    <map>
                        <entry key="Raw_GC(%)-Min" value="35" />
                        <entry key="Raw_GC(%)-Max" value="65" />
                        <entry key="Clean_Reads" value="20" />
                        <entry key="Clean_Bases(G)" value="6" />
                        <entry key="Clean_Bases_Prop(%)" value="70" />
                        <entry key="Duplication_Rate(%)" value="50" />
                        <entry key="Insert_Size_Peak" value="100" />
                        <entry key="Mapping_rate(%)" value="70" />
                        <entry key="rRNA(%)" value="20" />
                    </map>
                </property>
            </bean>
        </entry>

    </util:map>
    <util:map id="cancer.process">
        <entry key="breast_cancer">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="colorectal_cancer">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="ctDNA_pan-cancer">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="gastric">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="hepa">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="pancreatic">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="pan">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="fuke">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="gist">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="breast_peixi">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixicolon">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixipan">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixigastric">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixihepa">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixipancreatic">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixifuke">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixigist">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="neuro">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixineuro">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="bone">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixibone">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixipanMLPA">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="fukeHRD">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastCancer">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>
        <entry key="peixirna">
            <bean class="com.shca.module.cancer.service.support.ProcessServiceByBreastPeiXi">
                <property name="client" ref="cromwellClient" />
            </bean>
        </entry>

    </util:map>

    <!-- register the services START -->
    <bean id="ftpMonitor" class="com.shca.module.ftp.service.FtpMonitorByLog">
        <constructor-arg index="0" value="(AJ|BJ|B|BZ|C|CZ|G|GZ|H|HZ|P|PC|OH|PZ|GI|UT|LA|LB|LC|CJ|HJ|GJ|IJ|PJ|DJ|AZ|DZ|NZ|NJ|SZ|SJ|PM|BR)[0-9]{7}(B|K|S|F|C)(bu){0,1}(M[a-zA-Z0-9]+){0,1}[0-9]*" />
        <constructor-arg index="1" value="R(1|2)" />
        <constructor-arg index="2" value="[0-9]+" />
        <property name="db" ref="db" />
        <property name="threshold" value="30000" />
    </bean>

    <bean id="cromwellClient" class="com.shca.cromwell.CromwellClient">
        <constructor-arg index="0" value="http://0.0.0.0:8899" />
        <constructor-arg index="1" value="v1" />
    </bean>

    <bean id="cromwellService" class="com.shca.cromwell.service.CromwellService3">
        <constructor-arg ref="dataSource" />
        <property name="client" ref="cromwellClient" />
        <property name="processServiceMap" ref="cancer.process" />
        <property name="qcSummaryServiceMap" ref="cancer.qcsummary" />
    </bean>

    <!-- <bean id="fileService"  class="com.shca.file.service.FileService">
        <property name="client" ref="cromwellClient" />
    </bean> -->

    <bean id="projectFilingService" class="com.shca.module.project.service.ProjectFilingService">
        <property name="db" ref="db" />
        <property name="client" ref="cromwellClient" />
        <property name="fileTransferWDL" value="D:\Workspace\\geneAnalysis-bitbucket\\fileBackup-1008.wdl" />
        <property name="fileTransferWDL2" value="/mydata/test/testfiles/varscanPlus.wdl" />
    </bean>

    <bean id="limsService" class="com.shca.lims.service.LimsService2">
        <property name="db" ref="db" />
        <property name="interval" value="10" />
        <property name="client" ref="cromwellClient" />
        <property name="projectListUrl" value="http://0.0.0.0:8999/rest/RestfulService/informationCapture" />
        <property name="uploadQcSummaryUrl" value="http://0.0.0.0:8999/rest/RestfulService/getAnalysisResult" />
    </bean>

    <bean id="reportService" class="com.shca.report.service.ReportService">
        <property name="db" ref="db" />
        <property name="queryUrl" value="http://0.0.0.0:9027/api/v1/queryForm" />
        <property name="uploadUrl" value="http://0.0.0.0:9027/api/v1/uploadResultZip" />
    </bean>
    <!-- register the services END -->

    <!-- view beans START -->
    <bean id="view-project.cancer" class="com.shca.module.project.view.CancerView">
        <property name="processServiceMap" ref="cancer.process" />
    </bean>

    <bean id="view-project.cancerfile" class="com.shca.module.project.view.CancerFileView">
    </bean>

    <bean id="view-project.project" class="com.shca.module.project.view.ProjectView">
    </bean>

    <bean id="view-project.suitability" class="com.shca.module.project.view.ProjectSuitabilityView">
        <constructor-arg index="0" value="/myTools/geneAnalysisPipeline/CreateBatchBvskSuitability.wdl" />
        <property name="client" ref="cromwellClient" />
    </bean>

    <bean id="view-project.compare" class="com.shca.module.project.view.ProjectCompareView">
        <property name="processServiceMap" ref="cancer.process" />
    </bean>

    <bean id="view-project.report" class="com.shca.module.project.view.ProjectReportView">
        <property name="processServiceMap" ref="cancer.process" />
    </bean>

    <bean id="view-project.upload" class="com.shca.module.project.view.ProjectUploadView">
        <property name="processServiceMap" ref="cancer.process" />
    </bean>

    <!-- <bean id="view-project.file" class="com.shca.module.project.view.ProjectFileView">
        <property name="client" ref="cromwellClient" />
    </bean> -->

    <bean id="view-project.file2" class="com.shca.module.project.view.ProjectFile2View">
    </bean>

    <bean id="view-project.test" class="com.shca.module.project.view.TestView">
        <property name="client" ref="cromwellClient" />
    </bean>

    <bean id="view-system.config" class="com.shca.module.system.view.ConfigView">
    </bean>

    <bean id="view-ftp.file" class="com.shca.module.ftp.view.FtpFileView">
        <property name="client" ref="cromwellClient" />
    </bean>
    <!-- view beans END -->

    <!-- register the quartz service to MethodInvokingJobDetailFactoryBean -->
    <!-- and then trigger service for quartz with Object and method -->

    <!-- backup and filing check task  -->
    <bean id="task-projectFilingService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="projectFilingService" />
        <property name="targetMethod" value="task" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="trigger-projectFilingService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-projectFilingService" />
        <property name="cronExpression" value="0 0/2 * * * ?" />
    </bean>

    <!-- the cromwell server schedule (send the analysis tasks) -->
    <bean id="task-cromwellService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="cromwellService" />
        <property name="targetMethod" value="task" />
        <property name="concurrent" value="false" />
    </bean>

    <bean id="trigger-cromwellService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-cromwellService" />
        <property name="cronExpression" value="0 0/2 * * * ?" />
    </bean>

    <!-- ftp monitor service schedule  -->
    <bean id="task-ftpService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="ftpMonitor" />
        <property name="targetMethod" value="sync" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="trigger-ftpService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-ftpService" />
        <property name="cronExpression" value="0 0/1 * * * ?" />
    </bean>

    <!-- schedule of the lims check and upload  -->
    <bean id="task-limsService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="limsService" />
        <property name="targetMethod" value="sync" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="trigger-limsService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-limsService" />
        <property name="cronExpression" value="0 0/10 * * * ?" />
    </bean>

    <!-- schedule of the report check and upload  -->
    <bean id="task-reportService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="reportService" />
        <property name="targetMethod" value="sync" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="trigger-reportService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-reportService" />
        <property name="cronExpression" value="0 0/5 * * * ?" />
    </bean>


    <!-- <bean id="task-fileService" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="fileService" />
        <property name="targetMethod" value="createTask1" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="task-fileService2" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="fileService" />
        <property name="targetMethod" value="createTask2" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="task-fileService3" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="fileService" />
        <property name="targetMethod" value="createTask3" />
        <property name="concurrent" value="false" />
    </bean>
    <bean id="trigger-fileService" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-fileService" />
        <property name="cronExpression" value="0 0/3 * * * ?" />
    </bean>
    <bean id="trigger-fileService2" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-fileService2" />
        <property name="cronExpression" value="0 * * * * ?" />
    </bean>
    <bean id="trigger-fileService3" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="task-fileService3" />
        <property name="cronExpression" value="30 * * * * ?" />
    </bean> -->

    <!-- emit the scheduled tasks -->
    <bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="triggers">
            <list>
                <!-- <ref bean="trigger-cromwellService" /> -->
                <!-- <ref bean="trigger-ftpService" /> -->
                <!-- <ref bean="trigger-limsService" /> -->
                <!-- <ref bean="trigger-reportService" /> -->
                <!-- <ref bean="trigger-fileService" />
                <ref bean="trigger-fileService2" />
                <ref bean="trigger-fileService3" /> -->
                <ref bean="trigger-projectFilingService" />
           </list>
       </property>
   </bean>
</beans>
