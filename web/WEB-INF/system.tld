<?xml version="1.0" encoding="UTF-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
        version="2.0">
    <description>系统方法</description>
    <display-name>JSTL System functions</display-name>
    <tlib-version>1.1</tlib-version>
    <short-name>system</short-name>
    <!--   <uri>http://java.sun.com/jsp/jstl/functionss</uri> -->
    <function>
        <description>字符串拼接</description>
        <name>value</name>
        <function-class>com.ywang.http.tld.SystemTag</function-class>
        <function-signature>java.lang.String getValue(java.lang.String)</function-signature>
        <example>${system:getValue(str1)}</example>
    </function>
</taglib>