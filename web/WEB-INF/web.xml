<?xml version="1.0" encoding="UTF-8"?>


<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns:context="http://www.springframework.org/schema/context"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
		  http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">


<absolute-ordering/>
    <context-param>
        <!-- <param-name>contextConfigLocation</param-name>
        <param-value>/WEB-INF/datasource-shca.xml,/WEB-INF/shca.xml</param-value> -->
        <param-name>contextConfigLocation</param-name>
        <param-value>shca.xml</param-value>

    </context-param>
    <!-- <listener>
        <listener-class>com.ywang.framework.mvc.controller.SpringContextListener</listener-class>
    </listener> -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>com.ywang.http.filter.support.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <!-- <filter>
        <filter-name>oauthFilter</filter-name>
        <filter-class>com.shca.web.filter.RbacFilter</filter-class>
        <init-param>
            <param-name>simple_mode</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter> -->
    <!-- <filter>
        <filter-name>springFilter</filter-name>
        <filter-class>com.ywang.framework.mvc.controller.SpringControllerFilter</filter-class>
        <init-param>
            <param-name>action_type</param-name>
            <param-value>.htm</param-value>
        </init-param>
    </filter> -->

    <!-- <filter>
        <filter-name>serviceFilter</filter-name>
        <filter-class>com.ywang.framework.mvc.controller.ServiceControllerFilter</filter-class>
    </filter> -->

    <!-- <filter>
         <filter-name>springSessionRepositoryFilter</filter-name>
         <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
     </filter>

     <filter-mapping>
         <filter-name>springSessionRepositoryFilter</filter-name>
         <url-pattern>/*</url-pattern>
     </filter-mapping>-->

    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- <filter-mapping>
        <filter-name>oauthFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping> -->
    <!-- <filter-mapping>
        <filter-name>springFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping> -->

    <!-- <filter-mapping>
        <filter-name>serviceFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping> -->

    <welcome-file-list>
        <welcome-file>index.htm</welcome-file>
    </welcome-file-list>

    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <jsp-config>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <el-ignored>false</el-ignored>
            <page-encoding>UTF-8</page-encoding>
            <scripting-invalid>false</scripting-invalid>
        </jsp-property-group>
    </jsp-config>

    <mime-mapping>
        <extension>xls</extension>
        <mime-type>application/msexcel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>json</extension>
        <mime-type>application/json</mime-type>
    </mime-mapping>
</web-app>
