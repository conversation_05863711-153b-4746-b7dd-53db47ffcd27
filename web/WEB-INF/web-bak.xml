<?xml version="1.0" encoding="UTF-8"?>

<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
		  http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5">


    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>/WEB-INF/shca.xml</param-value>
    </context-param>
    <servlet>
        <servlet-name>dispatcher</servlet-name>
	<servlet-class>
	    org.springframework.web.servlet.DispatcherServlet
	</servlet-class>
	<init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value></param-value>
	</init-param>
	<load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
	<servlet-name>dispatcher</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>
    <listener>
        <listener-class>com.ywang.framework.mvc.controller.SpringContextListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.jasig.cas.client.session.SingleSignOutHttpSessionListener</listener-class>
    </listener>

    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>com.ywang.http.filter.support.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <filter-class>org.jasig.cas.client.session.SingleSignOutFilter</filter-class>
        <!--高版本client需要配置-->
        <init-param>
            <param-name>casServerUrlPrefix</param-name>
            <param-value>http://127.0.0.1:8080/cas</param-value>
        </init-param>

    </filter>

    <!-- 该过滤器负责用户的认证工作，必须启用它 -->
    <filter>
        <filter-name>CASFilter</filter-name>
        <filter-class>org.jasig.cas.client.authentication.AuthenticationFilter</filter-class>
        <init-param>
            <param-name>casServerLoginUrl</param-name>
            <param-value>http://47.100.173.115:9090/cas/login</param-value>
        </init-param>
        <init-param>
            <!--这里是当前接入CAS服务的应用服务地址及端口 -->
            <param-name>serverName</param-name>
            <param-value>http://127.0.0.1:80</param-value>
        </init-param>
    </filter>

    <!-- 该过滤器负责对Ticket的校验工作，必须启用它 -->
    <filter>
        <filter-name>CAS Validation Filter</filter-name>
        <filter-class>
            org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter
        </filter-class>
        <init-param>
            <param-name>casServerUrlPrefix</param-name>
            <param-value>http://47.100.173.115:9090/cas</param-value>
        </init-param>
        <init-param>
            <!--这里是当前接入CAS服务的应用服务地址及端口 -->
            <param-name>serverName</param-name>
            <param-value>http://127.0.0.1:80</param-value>
        </init-param>
    </filter>

    <!-- 该过滤器负责实现HttpServletRequest请求的包裹， 比如允许开发者通过HttpServletRequest的getRemoteUser()方法获得SSO登录用户的登录名，可选配置。 -->
    <filter>
        <filter-name>CAS HttpServletRequest Wrapper Filter</filter-name>
        <filter-class>
            org.jasig.cas.client.util.HttpServletRequestWrapperFilter
        </filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CAS HttpServletRequest Wrapper Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- 该过滤器使得开发者可以通过org.jasig.cas.client.util.AssertionHolder来获取用户的登录名。 比如AssertionHolder.getAssertion().getPrincipal().getName()。 -->
    <filter>
        <filter-name>CAS Assertion Thread Local Filter</filter-name>
        <filter-class>org.jasig.cas.client.util.AssertionThreadLocalFilter</filter-class>
    </filter>


    <filter>
        <filter-name>oauthFilter</filter-name>
        <filter-class>com.shca.web.filter.RbacFilter</filter-class>
        <init-param>
            <param-name>simple_mode</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>



    <filter>
        <filter-name>springFilter</filter-name>
        <filter-class>com.ywang.framework.mvc.controller.SpringControllerFilter</filter-class>
        <init-param>
            <param-name>action_type</param-name>
            <param-value>.htm</param-value>
        </init-param>
    </filter>

    <filter>
        <filter-name>serviceFilter</filter-name>
        <filter-class>com.ywang.framework.mvc.controller.ServiceControllerFilter</filter-class>
    </filter>

    <!--<filter>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>-->
    <filter-mapping>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CASFilter</filter-name>
        <url-pattern>/app/portHttpsLogin/*</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CAS Validation Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CAS Assertion Thread Local Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>



    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>oauthFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <filter-mapping>
        <filter-name>springFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>serviceFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <welcome-file-list>
        <welcome-file>index.htm</welcome-file>
    </welcome-file-list>

    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <jsp-config>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <el-ignored>false</el-ignored>
            <page-encoding>UTF-8</page-encoding>
            <scripting-invalid>false</scripting-invalid>
        </jsp-property-group>
    </jsp-config>

    <mime-mapping>
        <extension>xls</extension>
        <mime-type>application/msexcel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>json</extension>
        <mime-type>application/json</mime-type>
    </mime-mapping>

</web-app>
