<?xml version="1.0" encoding="UTF-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
        version="2.0">
    <description>字符串方法</description>
    <display-name>JSTL string functions</display-name>
    <tlib-version>1.1</tlib-version>
    <short-name>string</short-name>
    <!--   <uri>http://java.sun.com/jsp/jstl/functionss</uri> -->
    <function>
        <description>字符串拼接</description>
        <name>append</name>
        <function-class>com.ywang.http.tld.StringTag</function-class>
        <function-signature>java.lang.String append(java.lang.String,java.lang.String)</function-signature>
        <example>${str:append(str1, str2)}</example>
    </function>
</taglib>