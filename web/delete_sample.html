<!--
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-05 15:14:23
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-11 10:59:28
 * @FilePath: /analysis_engine/web/shca/project/delete_project.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        input[type="text"] {
            width: 25%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            width: 15%;
            padding: 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        button:hover {
            background-color: #218838;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f9f9f9;
        }
        .delete-button {
            background-color: #dc3545;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .delete-button:hover {
            background-color: #c82333;
        }
        .delete-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input, button {
            display: inline-block;
        }
    </style>
    <script>
        function dateFormatter(timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            let month = date.getMonth() + 1; // note: months are 0-based
            month = (month < 10 ? "0" : "") + month;
            let day = date.getDate();
            day = (day < 10 ? "0" : "") + day;
            let hour = date.getHours();
            let minute = date.getMinutes();
            hour = (hour < 10 ? "0" : "") + hour;
            minute = (minute < 10 ? ":0" : ":") + minute;
            const formattedDate = `${year}-${month}-${day} ${hour}:${minute}`;
            return formattedDate;
        }
        var host = 'http://localhost:8080/shca/custom'
        // var host = 'http://***************:8080/shca/custom'
        async function fetchSamples(sampleName) {
            try {
                const response = await fetch(host +'/getSampleRecordList?sample_name=' + sampleName,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                }}); // Update your API endpoint accordingly
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const responseData = await response.json();
                // if (responseData.status === 'error') {
                //     throw new Error(responseData.message);
                // }
                if (responseData.error) {
                    throw new Error(responseData['error']);
                }
                const samples = responseData['samples'];
                populateSampleTable(samples);
            } catch (error) {
                console.error('Error fetching samples:', error);
                alert(`Error: ${error.message}`);
            }
        }

        async function fetchProjects(sampleName) {
            try {
                const response = await fetch(host +'/getProjectsBySampleName?sample_name=' + sampleName,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                }}); // Update your API endpoint accordingly
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const responseData = await response.json();
                // if (responseData.status === 'error') {
                //     throw new Error(responseData.message);
                // }
                if (responseData.error) {
                    throw new Error(responseData['error']);
                }
                const projects = responseData['projects'];
                populateProjectTable(projects);
            } catch (error) {
                console.error('Error fetching samples:', error);
                alert(`Error: ${error.message}`);
            }
        }
        function populateSampleTable(samples) {
            const tableHeader = document.getElementById('sampleTableHeader');
            tableHeader.innerHTML =  '';
            tableHeader.innerHTML = `
                <tr>
                    <th>Sample Name</th>
                    <th>Status</th>
                    <th>Create Time</th>
                    <th>Update Time</th>
                    <th>Actions</th>
                </tr>
            `;
            const tableBody = document.getElementById('sampleTableBody');
            tableBody.innerHTML = ''; // Clear existing rows
            samples.forEach(sample => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${sample.sample_name}</td>
                    <td>${sample.status}</td>
                    <td>${dateFormatter(sample.create_time)}</td>
                    <td>${dateFormatter(sample.update_time)}</td>
                    <td>
                        <input class="delete-button" type="button"
                            onclick="deleteSample('${sample.sample_name}')"
                            value="Delete"
                            ${ifValidStatus(sample.status) ? '' : 'disabled'} />
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function ifValidStatus(status) {
            if (status !== 'running_step0' && status !== 'filed') {
                return true;
            }
            return false;
        }
        function populateProjectTable(projects) {
            const tableHeader = document.getElementById('sampleTableHeader');
            tableHeader.innerHTML = '';
            tableHeader.innerHTML = `
                <tr>
                    <th>Project Name</th>
                    <th>Project SN</th>
                    <th>Sample</th>
                    <th>Sample</th>
                    <th>Status</th>
                    <th>Create Time</th>
                    <th>lims status</th>
                    <th>lims upload time</th>
                    <th>file upload time</th>
                </tr>
            `;
            const tableBody = document.getElementById('sampleTableBody');
            tableBody.innerHTML = ''; // Clear existing rows
            projects.forEach(project => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${project.project_name}</td>
                    <td>${project.project_sn}</td>
                    <td>${project.sample_name_0}</td>
                    <td>${project.sample_name_1}</td>
                    <td>${project.status}</td>
                    <td>${dateFormatter(project.create_time)}</td>
                    <td>${project.lims_status}</td>
                    <td>${dateFormatter(project.lims_time)}</td>
                    <td>${dateFormatter(project.upload_time)}</td>
                `;
                tableBody.appendChild(row);
            });
        }
        // Mock function to simulate a backend method
    async function deleteSample(inputValue) {
        try {
            const response = await fetch(host +'/deleteSample?sample_name=' + inputValue, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            if (data.error) {
                throw new Error(data['error']);
            }
            alert(data); // assuming your backend returns a JSON response
        } catch (error) {
            console.error('Error fetching data:', error);
            // return `Error: ${error.message}`;
            alert(`Error: ${error.message}`);
        }
    }

    async function handleInput(inputElementName) {
        const inputElement = document.getElementById(inputElementName);  //'searchSamplesInput', searchProjectsInput
        const inputValue = inputElement.value;
        if (!inputValue) {
            alert('Please enter something');
            return;
        }
        // Call the backend method
        if (inputElementName === 'searchSamplesInput') {
            await fetchSamples(inputValue);
        } else if (inputElementName === 'searchProjectsInput') {
            await fetchProjects(inputValue);
        }
        // const response = await fetchSamples(inputValue);
        // respose = await fetchProjects(inputValue);
        // Alert the response
        // alert(response);
    }
    </script>
</head>
<body>
    <div class="container">
        <h1>Search and Delete Example</h1>
        <div display: inline-flex>
            <input type="text" id='searchSamplesInput' placeholder="Enter Sample Name" />
            <button onclick="handleInput('searchSamplesInput')">Search Samples</button>
        </div>
        <div display: inline-flex>
            <!-- Add a new input element for project search -->
            <input type="text" id="searchProjectsInput" placeholder="Enter Sample Name" />
            <button onclick="handleInput('searchProjectsInput')">Search Projects</button>
        </div>

        <table>
            <thead id="sampleTableHeader">
            </thead>
            <tbody  id="sampleTableBody">
            </tbody>
        </table>
    </div>
</body>
</html>
