/**
* 字符串转时间戳（以毫秒为单位） stringTime,eg."2014-07-10 10:21:12"
*/
function str2timestamp(time) {
    time = time.replace(/-/g,':').replace(' ',':');
    time = time.split(':');
    var time1 = new Date(time[0],(time[1]-1),time[2],time[3],time[4],time[5]);
    return time1.getTime();
}

/**
 * 时间戳（以毫秒为单位）转字符串
 * timestamp
 * formatStr 格式，默认为"yyyy-MM-dd h:m:s"
 */
function timestamp2str(timestamp, formatStr) {
    if(!formatStr)
        formatStr = "yyyy-MM-dd hh:mm:ss";
    var newDate = new Date(timestamp);
    return newDate.format(formatStr);
}

//取得当前时间戳
function getCurTimestamp(){
    var timestamp = Date.parse(new Date());
    return timestamp;
}



//本周第一天
function getWeekFirstDay(formatStr)
{
    if(!formatStr)
        formatStr = "yyyy-MM-dd";
    var Nowdate=new Date();
    var WeekFirstDay=new Date(Nowdate-(Nowdate.getDay()-1)*86400000);
    return WeekFirstDay.format(formatStr);
}

//本周最后一天
function getWeekLastDay(formatStr)
{
    if(!formatStr)
        formatStr = "yyyy-MM-dd";
    var Nowdate=new Date();
    var WeekFirstDay=new Date(Nowdate-(Nowdate.getDay()-1)*86400000);
    var WeekLastDay=new Date((WeekFirstDay/1000+6*86400)*1000);
    return WeekLastDay.format(formatStr);
}
//本月第一天
function showMonthFirstDay(formatStr)
{
    if(!formatStr)
        formatStr = "yyyy-MM-dd";
    var Nowdate=new Date();
    var MonthFirstDay=new Date(Nowdate.getFullYear(),Nowdate.getMonth(),1);
    return MonthFirstDay.format(formatStr);
}

//本月最后一天
function showMonthLastDay(formatStr)
{
    if(!formatStr)
        formatStr = "yyyy-MM-dd";
    var Nowdate=new Date();
    var MonthNextFirstDay=new Date(Nowdate.getFullYear(),Nowdate.getMonth()+1,1);
    var MonthLastDay=new Date(MonthNextFirstDay-86400000);
    return MonthLastDay.format(formatStr);
}

Date.prototype.format = function(format) {
    var date = {
        "M+" : this.getMonth() + 1,
        "d+" : this.getDate(),
        "h+" : this.getHours(),
        "m+" : this.getMinutes(),
        "s+" : this.getSeconds(),
        "q+" : Math.floor((this.getMonth() + 3) / 3),
        "S+" : this.getMilliseconds()
    };
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (this.getFullYear() + '')
            .substr(4 - RegExp.$1.length));
    }
    for ( var k in date) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? date[k]
                : ("00" + date[k]).substr(("" + date[k]).length));
        }
    }
    return format;
};

//var curTimestamp = getCurTimestamp();
//var curTimeStr = timestamp2str(curTimestamp, "yyyy-MM-dd");
//var curTimestamp2 = str2timestamp(curTimeStr);
//console.log(curTimestamp, curTimeStr, curTimestamp2);


function getUrlParms(name){
    var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if(r!=null)
        return unescape(r[2]);
    return null;
}


function num2Chinese(num){
    var chinese = ["", "一","二","三","四","五","六","七","八","九"];
    var numStr = num+"";
    if(numStr.length==1){
        return chinese[numStr];
    }else{
        return "十" + chinese[numStr.charAt(1)];
    }
}

function chinese2Num(chinese){
    var chineseArr = ["", "一","二","三","四","五","六","七","八","九","十"];
    var numStr = ["0", "1", "2","3","4","5","6","7","8","9", "10"];
    var retunNum;
    if(chinese.length==1){
        retunNum = numStr[chineseArr.indexOf(chinese)];
    }else{
        retunNum = "1" + numStr[chineseArr.indexOf(chinese.charAt(1))];
    }
    return parseInt(retunNum);
}

function prefixInteger(num, n) {
    return (Array(n).join(0) + num).slice(-n);
}

function myPost(url, params) {
    var temp = document.createElement("form");
    temp.action = url;
    temp.method = "post";
    temp.style.display = "none";
    for (var x in params) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = params[x]; // alert(opt.name)
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit(); return temp;
}


function showPdf(file, password){
    if(password){
        sessionStorage.setItem('pdf_password', password);
    }else{
        sessionStorage.removeItem('pdf_password');
    }

    // sessionStorage.setItem('pdf_password', '111111');/////测试！！！

    window.location.href = "plugins/pdfjs-1.9.426-dist/web/viewer.html?file="+file;
}

//全部替换
String.prototype.myReplace=function(f,e){//吧f替换成e
    var reg=new RegExp(f,"g"); //创建正则RegExp对象
    return this.replace(reg,e);
}

//去掉所有空格（包括字符串两端、中间的空格、换行符、制表符
String.prototype.myTrim=function(f,e){//吧f替换成e
    return this.replace(/[\s\n\t]+/g, "");
}


