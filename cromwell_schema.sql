-- MySQL dump 10.15  Distrib 10.0.38-MariaDB, for Linux (x86_64)
--
-- Host: localhost    Database: cromwell
-- ------------------------------------------------------
-- Server version	10.0.38-MariaDB-wsrep

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `CALL_CACHING_AGGREGATION_ENTRY`
--

DROP TABLE IF EXISTS `CALL_CACHING_AGGREGATION_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CALL_CACHING_AGGREGATION_ENTRY` (
  `CALL_CACHING_AGGREGATION_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CALL_CACHING_ENTRY_ID` int(11) NOT NULL,
  `BASE_AGGREGATION` varchar(255) NOT NULL,
  `INPUT_FILES_AGGREGATION` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CALL_CACHING_AGGREGATION_ENTRY_ID`),
  KEY `IX_CALL_CACHING_AGGREGATION_ENTRY_BA_IFA` (`BASE_AGGREGATION`,`INPUT_FILES_AGGREGATION`),
  KEY `FK_CALL_CACHING_AGGREGATION_ENTRY_CALL_CACHING_ENTRY_ID` (`CALL_CACHING_ENTRY_ID`),
  CONSTRAINT `FK_CALL_CACHING_AGGREGATION_ENTRY_CALL_CACHING_ENTRY_ID` FOREIGN KEY (`CALL_CACHING_ENTRY_ID`) REFERENCES `CALL_CACHING_ENTRY` (`CALL_CACHING_ENTRY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CALL_CACHING_DETRITUS_ENTRY`
--

DROP TABLE IF EXISTS `CALL_CACHING_DETRITUS_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CALL_CACHING_DETRITUS_ENTRY` (
  `CALL_CACHING_DETRITUS_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `DETRITUS_KEY` varchar(255) NOT NULL,
  `DETRITUS_VALUE` longtext,
  `CALL_CACHING_ENTRY_ID` int(11) NOT NULL,
  PRIMARY KEY (`CALL_CACHING_DETRITUS_ENTRY_ID`),
  UNIQUE KEY `UC_CALL_CACHING_DETRITUS_ENTRY_CCEI_DK` (`CALL_CACHING_ENTRY_ID`,`DETRITUS_KEY`),
  CONSTRAINT `FK_CALL_CACHING_DETRITUS_ENTRY_CALL_CACHING_ENTRY_ID` FOREIGN KEY (`CALL_CACHING_ENTRY_ID`) REFERENCES `CALL_CACHING_ENTRY` (`CALL_CACHING_ENTRY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CALL_CACHING_ENTRY`
--

DROP TABLE IF EXISTS `CALL_CACHING_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CALL_CACHING_ENTRY` (
  `CALL_CACHING_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `CALL_FULLY_QUALIFIED_NAME` varchar(255) NOT NULL,
  `JOB_INDEX` int(11) NOT NULL,
  `RETURN_CODE` int(11) DEFAULT NULL,
  `ALLOW_RESULT_REUSE` tinyint(4) NOT NULL DEFAULT '1',
  `JOB_ATTEMPT` int(11) DEFAULT NULL,
  PRIMARY KEY (`CALL_CACHING_ENTRY_ID`),
  UNIQUE KEY `UC_CALL_CACHING_ENTRY_WEU_CFQN_JI` (`WORKFLOW_EXECUTION_UUID`,`CALL_FULLY_QUALIFIED_NAME`,`JOB_INDEX`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CALL_CACHING_HASH_ENTRY`
--

DROP TABLE IF EXISTS `CALL_CACHING_HASH_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CALL_CACHING_HASH_ENTRY` (
  `CALL_CACHING_HASH_ENTRY_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `HASH_KEY` varchar(255) NOT NULL,
  `HASH_VALUE` varchar(255) NOT NULL,
  `CALL_CACHING_ENTRY_ID` int(11) NOT NULL,
  PRIMARY KEY (`CALL_CACHING_HASH_ENTRY_ID`),
  UNIQUE KEY `UC_CALL_CACHING_HASH_ENTRY_CCEI_HK` (`CALL_CACHING_ENTRY_ID`,`HASH_KEY`),
  CONSTRAINT `FK_CALL_CACHING_HASH_ENTRY_CALL_CACHING_ENTRY_ID` FOREIGN KEY (`CALL_CACHING_ENTRY_ID`) REFERENCES `CALL_CACHING_ENTRY` (`CALL_CACHING_ENTRY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CALL_CACHING_SIMPLETON_ENTRY`
--

DROP TABLE IF EXISTS `CALL_CACHING_SIMPLETON_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CALL_CACHING_SIMPLETON_ENTRY` (
  `CALL_CACHING_SIMPLETON_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SIMPLETON_KEY` varchar(255) NOT NULL,
  `SIMPLETON_VALUE` longtext,
  `WDL_TYPE` varchar(255) NOT NULL,
  `CALL_CACHING_ENTRY_ID` int(11) NOT NULL,
  PRIMARY KEY (`CALL_CACHING_SIMPLETON_ENTRY_ID`),
  UNIQUE KEY `UC_CALL_CACHING_SIMPLETON_ENTRY_CCEI_SK` (`CALL_CACHING_ENTRY_ID`,`SIMPLETON_KEY`),
  CONSTRAINT `FK_CALL_CACHING_SIMPLETON_ENTRY_CALL_CACHING_ENTRY_ID` FOREIGN KEY (`CALL_CACHING_ENTRY_ID`) REFERENCES `CALL_CACHING_ENTRY` (`CALL_CACHING_ENTRY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CUSTOM_LABEL_ENTRY`
--

DROP TABLE IF EXISTS `CUSTOM_LABEL_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CUSTOM_LABEL_ENTRY` (
  `CUSTOM_LABEL_ENTRY_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CUSTOM_LABEL_KEY` varchar(255) NOT NULL,
  `CUSTOM_LABEL_VALUE` varchar(255) NOT NULL,
  `WORKFLOW_EXECUTION_UUID` varchar(100) NOT NULL,
  PRIMARY KEY (`CUSTOM_LABEL_ENTRY_ID`),
  UNIQUE KEY `UC_CUSTOM_LABEL_ENTRY_CLK_WEU` (`CUSTOM_LABEL_KEY`,`WORKFLOW_EXECUTION_UUID`),
  KEY `IX_CUSTOM_LABEL_ENTRY_CLK_CLV` (`CUSTOM_LABEL_KEY`,`CUSTOM_LABEL_VALUE`),
  KEY `FK_CUSTOM_LABEL_ENTRY_WORKFLOW_EXECUTION_UUID` (`WORKFLOW_EXECUTION_UUID`),
  CONSTRAINT `FK_CUSTOM_LABEL_ENTRY_WORKFLOW_EXECUTION_UUID` FOREIGN KEY (`WORKFLOW_EXECUTION_UUID`) REFERENCES `WORKFLOW_METADATA_SUMMARY_ENTRY` (`WORKFLOW_EXECUTION_UUID`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=121366 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DATABASECHANGELOG`
--

DROP TABLE IF EXISTS `DATABASECHANGELOG`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DATABASECHANGELOG` (
  `ID` varchar(255) NOT NULL,
  `AUTHOR` varchar(255) NOT NULL,
  `FILENAME` varchar(255) NOT NULL,
  `DATEEXECUTED` datetime NOT NULL,
  `ORDEREXECUTED` int(11) NOT NULL,
  `EXECTYPE` varchar(10) NOT NULL,
  `MD5SUM` varchar(35) DEFAULT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `COMMENTS` varchar(255) DEFAULT NULL,
  `TAG` varchar(255) DEFAULT NULL,
  `LIQUIBASE` varchar(20) DEFAULT NULL,
  `CONTEXTS` varchar(255) DEFAULT NULL,
  `LABELS` varchar(255) DEFAULT NULL,
  `DEPLOYMENT_ID` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DATABASECHANGELOGLOCK`
--

DROP TABLE IF EXISTS `DATABASECHANGELOGLOCK`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DATABASECHANGELOGLOCK` (
  `ID` int(11) NOT NULL,
  `LOCKED` bit(1) NOT NULL,
  `LOCKGRANTED` datetime DEFAULT NULL,
  `LOCKEDBY` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DOCKER_HASH_STORE_ENTRY`
--

DROP TABLE IF EXISTS `DOCKER_HASH_STORE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DOCKER_HASH_STORE_ENTRY` (
  `DOCKER_HASH_STORE_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `DOCKER_TAG` varchar(255) NOT NULL,
  `DOCKER_HASH` varchar(255) NOT NULL,
  `DOCKER_SIZE` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`DOCKER_HASH_STORE_ENTRY_ID`),
  UNIQUE KEY `UC_DOCKER_HASH_STORE_ENTRY_WEU_DT` (`WORKFLOW_EXECUTION_UUID`,`DOCKER_TAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JOB_KEY_VALUE_ENTRY`
--

DROP TABLE IF EXISTS `JOB_KEY_VALUE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JOB_KEY_VALUE_ENTRY` (
  `JOB_KEY_VALUE_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `CALL_FULLY_QUALIFIED_NAME` varchar(255) NOT NULL,
  `JOB_INDEX` int(11) NOT NULL,
  `JOB_ATTEMPT` int(11) NOT NULL,
  `STORE_KEY` varchar(255) NOT NULL,
  `STORE_VALUE` varchar(255) NOT NULL,
  PRIMARY KEY (`JOB_KEY_VALUE_ENTRY_ID`),
  UNIQUE KEY `UC_JOB_KEY_VALUE_ENTRY_WEU_CFQN_JI_JA_SK` (`WORKFLOW_EXECUTION_UUID`,`CALL_FULLY_QUALIFIED_NAME`,`JOB_INDEX`,`JOB_ATTEMPT`,`STORE_KEY`)
) ENGINE=InnoDB AUTO_INCREMENT=1060456 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JOB_STORE_ENTRY`
--

DROP TABLE IF EXISTS `JOB_STORE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JOB_STORE_ENTRY` (
  `JOB_STORE_ENTRY_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `CALL_FULLY_QUALIFIED_NAME` varchar(255) NOT NULL,
  `JOB_INDEX` int(11) NOT NULL,
  `JOB_ATTEMPT` int(11) NOT NULL,
  `JOB_SUCCESSFUL` tinyint(4) NOT NULL,
  `RETURN_CODE` int(11) DEFAULT NULL,
  `EXCEPTION_MESSAGE` longtext,
  `RETRYABLE_FAILURE` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`JOB_STORE_ENTRY_ID`),
  UNIQUE KEY `UC_JOB_STORE_ENTRY_WEU_CFQN_JI_JA` (`WORKFLOW_EXECUTION_UUID`,`CALL_FULLY_QUALIFIED_NAME`,`JOB_INDEX`,`JOB_ATTEMPT`),
  KEY `IX_JOB_STORE_ENTRY_WEU` (`WORKFLOW_EXECUTION_UUID`)
) ENGINE=InnoDB AUTO_INCREMENT=1060410 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JOB_STORE_SIMPLETON_ENTRY`
--

DROP TABLE IF EXISTS `JOB_STORE_SIMPLETON_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JOB_STORE_SIMPLETON_ENTRY` (
  `JOB_STORE_SIMPLETON_ENTRY_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SIMPLETON_KEY` varchar(255) NOT NULL,
  `SIMPLETON_VALUE` longtext,
  `WDL_TYPE` varchar(255) NOT NULL,
  `JOB_STORE_ENTRY_ID` bigint(20) NOT NULL,
  PRIMARY KEY (`JOB_STORE_SIMPLETON_ENTRY_ID`),
  UNIQUE KEY `UC_JOB_STORE_SIMPLETON_ENTRY_JSEI_SK` (`JOB_STORE_ENTRY_ID`,`SIMPLETON_KEY`),
  CONSTRAINT `FK_JOB_STORE_SIMPLETON_ENTRY_JOB_STORE_ENTRY_ID` FOREIGN KEY (`JOB_STORE_ENTRY_ID`) REFERENCES `JOB_STORE_ENTRY` (`JOB_STORE_ENTRY_ID`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14633681 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `METADATA_ENTRY`
--

DROP TABLE IF EXISTS `METADATA_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `METADATA_ENTRY` (
  `METADATA_JOURNAL_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `METADATA_KEY` varchar(255) NOT NULL,
  `CALL_FQN` varchar(255) DEFAULT NULL,
  `JOB_SCATTER_INDEX` int(11) DEFAULT NULL,
  `JOB_RETRY_ATTEMPT` int(11) DEFAULT NULL,
  `METADATA_VALUE` longtext,
  `METADATA_TIMESTAMP` datetime(6) NOT NULL,
  `METADATA_VALUE_TYPE` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`METADATA_JOURNAL_ID`),
  KEY `METADATA_WORKFLOW_IDX` (`WORKFLOW_EXECUTION_UUID`)
) ENGINE=InnoDB AUTO_INCREMENT=82277251 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SQLMETADATADATABASECHANGELOG`
--

DROP TABLE IF EXISTS `SQLMETADATADATABASECHANGELOG`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SQLMETADATADATABASECHANGELOG` (
  `ID` varchar(255) NOT NULL,
  `AUTHOR` varchar(255) NOT NULL,
  `FILENAME` varchar(255) NOT NULL,
  `DATEEXECUTED` datetime NOT NULL,
  `ORDEREXECUTED` int(11) NOT NULL,
  `EXECTYPE` varchar(10) NOT NULL,
  `MD5SUM` varchar(35) DEFAULT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `COMMENTS` varchar(255) DEFAULT NULL,
  `TAG` varchar(255) DEFAULT NULL,
  `LIQUIBASE` varchar(20) DEFAULT NULL,
  `CONTEXTS` varchar(255) DEFAULT NULL,
  `LABELS` varchar(255) DEFAULT NULL,
  `DEPLOYMENT_ID` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SQLMETADATADATABASECHANGELOGLOCK`
--

DROP TABLE IF EXISTS `SQLMETADATADATABASECHANGELOGLOCK`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SQLMETADATADATABASECHANGELOGLOCK` (
  `ID` int(11) NOT NULL,
  `LOCKED` bit(1) NOT NULL,
  `LOCKGRANTED` datetime DEFAULT NULL,
  `LOCKEDBY` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SUB_WORKFLOW_STORE_ENTRY`
--

DROP TABLE IF EXISTS `SUB_WORKFLOW_STORE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SUB_WORKFLOW_STORE_ENTRY` (
  `SUB_WORKFLOW_STORE_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ROOT_WORKFLOW_ID` int(11) NOT NULL,
  `PARENT_WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `CALL_FULLY_QUALIFIED_NAME` varchar(255) NOT NULL,
  `CALL_INDEX` int(11) NOT NULL,
  `CALL_ATTEMPT` int(11) NOT NULL,
  `SUB_WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  PRIMARY KEY (`SUB_WORKFLOW_STORE_ENTRY_ID`),
  UNIQUE KEY `UC_SUB_WORKFLOW_STORE_ENTRY_PWEU_CFQN_CI_CA` (`PARENT_WORKFLOW_EXECUTION_UUID`,`CALL_FULLY_QUALIFIED_NAME`,`CALL_INDEX`,`CALL_ATTEMPT`),
  KEY `IX_SUB_WORKFLOW_STORE_ENTRY_PWEU` (`PARENT_WORKFLOW_EXECUTION_UUID`),
  KEY `FK_SUB_WORKFLOW_STORE_ENTRY_ROOT_WORKFLOW_ID` (`ROOT_WORKFLOW_ID`),
  CONSTRAINT `FK_SUB_WORKFLOW_STORE_ENTRY_ROOT_WORKFLOW_ID` FOREIGN KEY (`ROOT_WORKFLOW_ID`) REFERENCES `WORKFLOW_STORE_ENTRY` (`WORKFLOW_STORE_ENTRY_ID`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=91508 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SUMMARY_QUEUE_ENTRY`
--

DROP TABLE IF EXISTS `SUMMARY_QUEUE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SUMMARY_QUEUE_ENTRY` (
  `METADATA_JOURNAL_ID` bigint(20) NOT NULL,
  PRIMARY KEY (`METADATA_JOURNAL_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SUMMARY_STATUS_ENTRY`
--

DROP TABLE IF EXISTS `SUMMARY_STATUS_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SUMMARY_STATUS_ENTRY` (
  `SUMMARY_STATUS_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SUMMARY_NAME` varchar(255) NOT NULL,
  `SUMMARY_POSITION` bigint(20) NOT NULL,
  PRIMARY KEY (`SUMMARY_STATUS_ENTRY_ID`),
  UNIQUE KEY `UC_SUMMARY_STATUS_ENTRY_SN` (`SUMMARY_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `WORKFLOW_METADATA_SUMMARY_ENTRY`
--

DROP TABLE IF EXISTS `WORKFLOW_METADATA_SUMMARY_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `WORKFLOW_METADATA_SUMMARY_ENTRY` (
  `WORKFLOW_METADATA_SUMMARY_ENTRY_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(100) NOT NULL,
  `WORKFLOW_NAME` varchar(100) DEFAULT NULL,
  `WORKFLOW_STATUS` varchar(50) DEFAULT NULL,
  `START_TIMESTAMP` datetime(6) DEFAULT NULL,
  `END_TIMESTAMP` datetime(6) DEFAULT NULL,
  `SUBMISSION_TIMESTAMP` datetime(6) DEFAULT NULL,
  `PARENT_WORKFLOW_EXECUTION_UUID` varchar(100) DEFAULT NULL,
  `ROOT_WORKFLOW_EXECUTION_UUID` varchar(100) DEFAULT NULL,
  `METADATA_ARCHIVE_STATUS` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`WORKFLOW_METADATA_SUMMARY_ENTRY_ID`),
  UNIQUE KEY `UC_WORKFLOW_METADATA_SUMMARY_ENTRY_WEU` (`WORKFLOW_EXECUTION_UUID`),
  KEY `IX_WORKFLOW_METADATA_SUMMARY_ENTRY_PWEU` (`PARENT_WORKFLOW_EXECUTION_UUID`),
  KEY `IX_WORKFLOW_METADATA_SUMMARY_ENTRY_RWEU` (`ROOT_WORKFLOW_EXECUTION_UUID`),
  KEY `IX_WORKFLOW_METADATA_SUMMARY_ENTRY_WN` (`WORKFLOW_NAME`),
  KEY `IX_WORKFLOW_METADATA_SUMMARY_ENTRY_WS` (`WORKFLOW_STATUS`),
  KEY `IX_WORKFLOW_METADATA_SUMMARY_ENTRY_MAS` (`METADATA_ARCHIVE_STATUS`)
) ENGINE=InnoDB AUTO_INCREMENT=411193 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `WORKFLOW_STORE_ENTRY`
--

DROP TABLE IF EXISTS `WORKFLOW_STORE_ENTRY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `WORKFLOW_STORE_ENTRY` (
  `WORKFLOW_STORE_ENTRY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `WORKFLOW_EXECUTION_UUID` varchar(255) NOT NULL,
  `WORKFLOW_DEFINITION` longtext,
  `WORKFLOW_INPUTS` longtext,
  `WORKFLOW_OPTIONS` longtext,
  `WORKFLOW_STATE` varchar(20) NOT NULL,
  `SUBMISSION_TIME` datetime(6) NOT NULL,
  `IMPORTS_ZIP` longblob,
  `CUSTOM_LABELS` longtext NOT NULL,
  `WORKFLOW_TYPE` varchar(30) DEFAULT NULL,
  `WORKFLOW_TYPE_VERSION` varchar(255) DEFAULT NULL,
  `WORKFLOW_ROOT` varchar(100) DEFAULT NULL,
  `CROMWELL_ID` varchar(100) DEFAULT NULL,
  `HEARTBEAT_TIMESTAMP` datetime(6) DEFAULT NULL,
  `WORKFLOW_URL` varchar(2000) DEFAULT NULL,
  `HOG_GROUP` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`WORKFLOW_STORE_ENTRY_ID`),
  UNIQUE KEY `UC_WORKFLOW_STORE_ENTRY_WEU` (`WORKFLOW_EXECUTION_UUID`),
  KEY `IX_WORKFLOW_STORE_ENTRY_WS` (`WORKFLOW_STATE`)
) ENGINE=InnoDB AUTO_INCREMENT=60670 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-24 15:11:37
