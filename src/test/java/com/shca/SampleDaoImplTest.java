/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 13:12:37
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 23:42:47
 * @FilePath: /analysis_engine/src/test/java/com/shca/SampleDaoImplTest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.shca.config.ApplicationConfiguration;
import com.shca.dao.daoimpl.SampleDaoImpl;
import com.shca.entity.Sample;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
public class SampleDaoImplTest {

    @Autowired
    private SampleDaoImpl sampleDao;

    @Test
    public void selectByIdTest() {
        Sample result = sampleDao.selectById(100);
        assertNull(result);
        result = sampleDao.selectById(11107);
        assertNotNull(result);
        assertEquals(11107, result.getId());
        assertEquals("BZ2110313B01", result.getSampleName());
        assertEquals("SUCCEEDED", result.getStatus().toString());
    }

    @Test
    public void insertTest() {
        int sampleId = 100;
        Sample testSample = sampleDao.selectById(sampleId);
        long result = sampleDao.insert(testSample);
        assertEquals(0, result);
        sampleId = 11107;
        testSample = sampleDao.selectById(sampleId);
        assertNotNull(testSample);
        int originPriority = testSample.getPriority();
        testSample.setPriority(1000);
        result = sampleDao.update(testSample);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        testSample = sampleDao.selectById(sampleId);
        assertEquals(1000, testSample.getPriority());
        testSample.setPriority(originPriority);
        result = sampleDao.update(testSample);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        assertEquals(originPriority, testSample.getPriority());
        result = sampleDao.insert(testSample);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        testSample = sampleDao.selectById(result);
        assertNotNull(testSample);
        sampleDao.deleteById(result);
        testSample = sampleDao.selectById(result);
        assertNull(testSample);
    }

    @Test
    public void getReadySampleListTest() {
        List<Sample> resultList = sampleDao.getReadySampleList();
        assertEquals(0, resultList.size());
    }

}