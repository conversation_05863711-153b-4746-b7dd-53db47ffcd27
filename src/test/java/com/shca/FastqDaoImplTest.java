/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 13:12:37
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 23:54:57
 * @FilePath: /analysis_engine/src/test/java/com/shca/FastqDaoImplTest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.shca.config.ApplicationConfiguration;
import com.shca.dao.daoimpl.FastqDaoImpl;
import com.shca.entity.Fastq;
import com.shca.entity.enums.FastqStatusEnum;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
public class FastqDaoImplTest {

    @Autowired
    private FastqDaoImpl fastqDao;

    @Test
    public void selectByIdTest() {
        Fastq result = fastqDao.selectById(500);
        assertNotNull(result);
        assertEquals(500, result.getId());
        assertEquals(FastqStatusEnum.CLEARED, result.getStatus());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        assertEquals("BZ2002610Kbu", result.getSampleName());
        assertEquals(
            LocalDateTime.parse("2020-11-18 20:55:00", formatter),
            result.getCreateTime());
        assertEquals("324332f4811630e5ffa11b8005dc2341", result.getMd5());
        assertEquals("BZ", result.getCancerName());
        assertEquals("R1", result.getRawdata());
        assertEquals("shca-Nova333-BZ2002610Kbu_merge_R1.fastq.gz", result.getFileName());
        assertEquals(890129451, result.getFileSize());
        assertEquals(
            "/mydata/shca/rawdata/20201118/BZ2002610Kbu/shca-Nova333-BZ2002610Kbu_merge_R1.fastq.gz",
            result.getStorePath());
        assertEquals(
            LocalDateTime.parse("2022-01-30 10:27:56", formatter),
            result.getUpdateTime());
        assertEquals("administrator", result.getCreateUsername());
        assertEquals("FtpMonitorByLog", result.getUpdateUsername());
        result = this.fastqDao.selectById(10);
        assertNull(result);
    }

    @Test
    public void insertTest() {
        Fastq testFastq = fastqDao.selectById(100);
        long result = fastqDao.insert(testFastq);
        assertNotEquals(100, result);
        assertEquals(0, result);
        testFastq = fastqDao.selectById(500);
        assertNotNull(testFastq);
        result = fastqDao.insert(testFastq);
        assertNotEquals(0, result);
        fastqDao.deleteById(result);
    }

    @Test
    public void getReadyFastqListTest() {
        Fastq testFastq = fastqDao.selectById(500);
        FastqStatusEnum originStatus = testFastq.getStatus();
        testFastq.setStatus(FastqStatusEnum.READY);
        fastqDao.update(testFastq);
        List<Fastq> result = fastqDao.getReadyFastqList();
        assertEquals(9, result.size());
        assertEquals(FastqStatusEnum.READY, result.get(0).getStatus());
        testFastq.setStatus(originStatus);
        fastqDao.update(testFastq);
        result = fastqDao.getReadyFastqList();
        assertEquals(8, result.size());
    }

}
