/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 13:12:37
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 22:23:06
 * @FilePath: /analysis_engine/src/test/java/com/shca/FtpMonitorByLogTest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import org.junit.jupiter.api.Test;

import com.shca.module.ftp.service.FtpLog;
import com.shca.module.ftp.service.FtpMonitorByLog;
import com.shca.module.ftp.support.FtpLogSupport;
import com.shca.support.DbSysConfigManager;
import com.ywang.sql.support.Db;

// @ExtendWith(SpringExtension.class)
// @ContextConfiguration(locations = { "classpath:shca.xml" })
// @TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FtpMonitorByLogTest {
    // @Autowired
    FtpMonitorByLog ftpMonitor;
    // @Autowired
    Db db;
    // @Autowired
    DbSysConfigManager config;
    private Map<String, FtpLog> ftpLogMap;
    Map<String, HashMap<String, Object>> sampleNameObjectMap = new HashMap<>();

    // @BeforeAll
    public void testParseLogFiles() {
        File dir = new File("/Users/<USER>/analysis_engine/src/main/resources/test_resources/ftplog");
        File[] targetLogFiles = dir.listFiles();
        Map<String, FtpLog> ftpLogMap = FtpLogSupport.parseLogFiles(targetLogFiles);
        for (FtpLog ftpLog : ftpLogMap.values()) {
            System.out.println(ftpLog.toString());
        }
        this.ftpLogMap = ftpLogMap;
    }

    @Test
    public void parseFastqFileByFtpLogTest() {
        for (FtpLog ftpLog : this.ftpLogMap.values()) {
            if (ftpLog.getFileName().endsWith(".fastq.gz")) {
                System.out.println(ftpLog.toString());
                HashMap<String, Object> ftpFastqFileParseMap = FtpLogSupport.parseFastqFileByFtpLog(ftpLog,
                        this.ftpMonitor.getPatternMap(), db);
                this.sampleNameObjectMap.put((String) ftpFastqFileParseMap.get("sample_name"), ftpFastqFileParseMap);
                // assertTrue(FtpLogSupport.fastqExists(ftpFastqFileParseMap, db));
                for (String key : ftpFastqFileParseMap.keySet()) {
                    System.out.println(key + " : " + ftpFastqFileParseMap.get(key));
                }
                ftpFastqFileParseMap.put("store_path", "/Users/<USER>/test");
                FtpLogSupport.createFtpFileRecord(ftpFastqFileParseMap, db);
                if (!FtpLogSupport.fastqExists(ftpFastqFileParseMap, db)) {
                    FtpLogSupport.createCancerFileRecord(ftpFastqFileParseMap, db);
                } else {
                    if (FtpLogSupport.shouldUpdate(ftpFastqFileParseMap, db)) {
                        FtpLogSupport.updateCancerFileRecordFileInfo(ftpFastqFileParseMap, db);
                        FtpLogSupport.updateProjectCancerRecordStatus(ftpFastqFileParseMap, db);
                    }
                }
            }
        }
        FtpLogSupport.createSingleSampleRecordBySampleName(this.sampleNameObjectMap, config, db);
    }

    @Test
    public void threshholdTest() {
        LocalDateTime now = LocalDateTime.now();
        int threshold = 3600;
        LocalDateTime ftpTime = LocalDateTime.now().minusSeconds(threshold);
        System.out.println(now.toEpochSecond(null));
        System.out.println(ftpTime.toEpochSecond(null));
        boolean result = now.toEpochSecond(null) - ftpTime.toEpochSecond(null) <= threshold;
        assertFalse(result);
    }

}
