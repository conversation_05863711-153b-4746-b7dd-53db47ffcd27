/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-27 22:05:29
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 22:20:05
 * @FilePath: /analysis_engine/src/test/java/com/shca/FtpLogTest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import javax.annotation.Resource;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.shca.config.JdbcConfig;
import com.shca.module.ftp.support.FtpLogSupport;
import com.shca.config.SystemConfigService;
import com.shca.util.DbService;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {JdbcConfig.class})
public class TestFtpLogTest {
    @Resource DbService db;
    @Resource SystemConfigService config;

    private class FtpLog {
        private String timeStampStr;
        private String actionStr;
        private String filePathStr;
        public FtpLog(String timeStampStr, String actionStr, String filePathStr) {
            this.timeStampStr = timeStampStr;
            this.actionStr = actionStr;
            this.filePathStr = filePathStr;
        }

        public String getFilePathStr() {
            return this.filePathStr;
        }
        @Override
        public String toString() {
            return "timestamp: " + timeStampStr + " action: " + actionStr + " filePath: " + filePathStr;
        }

        public String getActionStr() {
            return actionStr;
        }
    }
    static final String MONITOR_STR = "/Users/<USER>/analysis_engine/src/main/resources/test_resources/ftplog";

    // private Predicate<FtpLog> filterByAction(String actionStr) {
    //     return new Predicate<FtpLog>() {
    //         @Override
    //         public boolean test(FtpLog ftpLog) {
    //             return ftpLog.getFilePathStr().equals(actionStr);
    //         }
    //     };
    // }
    private Predicate<FtpLog> filterByAction = ftplog ->
        "CREATE".equals(ftplog.getActionStr()) || "CLOSE_WRITE,CLOSE".equals(ftplog.getActionStr());
    // @Test
    @Test
    public void test() {
        Boolean isDebug = true;
        Assertions.assertTrue(isDebug);
        System.out.println("test debug succeeded");
        // Path monitorPath = Paths.get(MONITOR_STR);
        // String[] fileStrs = monitorPath.toFile().list();
        // // Stream<String> fileStrStream = Stream.of(fileStrs);
        // // Stream.of(fileStrs).filter(filterByAction);
        // Stream.of(fileStrs).forEach(System.out::println);
        // Map<String, List<FtpLog>> ftpLogMap = Stream.of(fileStrs)
        //     .map(logStr -> {
        //         String[] infoArray = logStr.split(" ");
        //         return new FtpLog(infoArray[0], infoArray[1], infoArray[2]);
        // }).filter(filterByAction).collect(Collectors.groupingBy(FtpLog::getFilePathStr))
        // .entrySet().stream().filter(entry -> entry.getValue().size()==2)
        // .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // ftpLogMap.entrySet().forEach(System.out::println);
        // System.out.println(ftpLogMap.size());

        // ftpLogList.stream().collect(Collectors.groupingBy(FtpLog::getFilePathStr)).forEach((k, v) -> System.out.println(k + " " + v.size()));
    }

    @Test
    public void createSingleSampleRecordByCheckResultTest() {
        Map<String, List<Map<String, Object>>> readyCancerList = FtpLogSupport.checkCancerRecord(db);
        readyCancerList.entrySet().forEach(entry -> System.out.println(entry.getKey() + " " + entry.getValue().size()));
        FtpLogSupport.createSingleSampleRecordByCheckResult(readyCancerList, db, config);
    }
}
