package com.shca.demo;

import java.io.ByteArrayInputStream;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

// @RunWith(SpringJUnit4ClassRunner.class)
// @WebAppConfiguration(value = "src/main/java/com/shca")
// @ContextConfiguration(classes={AppConfig.class})
// @FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class SpringSessionJdbcApplicationTest {

    // @LocalServerPort
    // private int port;

    @Autowired
    MockMvc mockMvc;

    @Test
    public void whenH2DbIsQueried_thenSessionInfoIsEmpty() throws SQLException {

        assertEquals(
        0, getSessionIdsFromDatabase().size());
        assertEquals(
        0, getSessionAttributeBytesFromDb().size());
    }

    @Test
    public void whenH2DbIsQueried_thenOneSessionIsCreated() throws Exception {
        MvcResult result = this.mockMvc.perform(
            get("/")).andExpect(status().isOk()).andDo(MockMvcResultHandlers.print()).andReturn();
        // assertThat(this.testRestTemplate.getForObject(
        // "http://localhost:" + port + "/", String.class))
        // .isNotEmpty();
        result = this.mockMvc.perform(
            get("/")).andExpect(status().isOk()).andDo(MockMvcResultHandlers.print()).andReturn();
        System.out.println(result);
        assertEquals(1, getSessionIdsFromDatabase().size());
    }

    @Test
    public void whenH2DbIsQueried_thenSessionAttributeIsRetrieved() throws Exception {

        // MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        // map.add("color", "red");
        // Object[] vars = new Object[1];
        // vars[0]=map;
        ResultActions actions = this.mockMvc.perform(post("/saveColor").param("color","red")).andDo(MockMvcResultHandlers.print());
        // this.testRestTemplate.postForObject(
        //     "http://localhost:" + port + "/saveColor", map, String.class);
        List<byte[]> queryResponse = getSessionAttributeBytesFromDb();

        System.out.println(actions);

        assertEquals(1, queryResponse.size());
        ObjectInput in = new ObjectInputStream(
            new ByteArrayInputStream(queryResponse.get(0)));
        List<String> obj = (List<String>) in.readObject();
        assertEquals("red", obj.get(0));
    }

    private List<String> getSessionIdsFromDatabase() throws SQLException {

        List<String> result = new ArrayList<>();
        ResultSet rs = getResultSet(
          "SELECT * FROM SPRING_SESSION");

        while (rs.next()) {
            result.add(rs.getString("SESSION_ID"));
        }
        return result;
    }

    private List<byte[]> getSessionAttributeBytesFromDb() throws SQLException {

      List<byte[]> result = new ArrayList<>();
      ResultSet rs = getResultSet(
        "SELECT * FROM SPRING_SESSION_ATTRIBUTES");

      while (rs.next()) {
          System.out.println("reult created");
          result.add(rs.getBytes("ATTRIBUTE_BYTES"));
      }
      return result;
  }

    private ResultSet getResultSet(String sql) throws SQLException {

        Connection conn = DriverManager
          .getConnection("jdbc:h2:mem:testdb", "sa", "");
        Statement stat = conn.createStatement();
        return stat.executeQuery(sql);
    }



}
