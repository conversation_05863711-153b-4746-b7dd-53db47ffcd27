package com.shca.lims;

import java.io.EOFException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.config.ApplicationConfiguration;
import com.shca.lims.service.LimsService;
import com.shca.support.SysConfigManager;
import com.ywang.sql.support.Db;

// import mockwebserver3.Dispatcher;
// import mockwebserver3.MockResponse;
// import mockwebserver3.MockWebServer;
// import mockwebserver3.RecordedRequest;

@ExtendWith(SpringExtension.class)
// @ContextConfiguration(locations={"classpath:shca.xml", "classpath:datasource-shca.xml"})
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class LimsServiceTest {

    @Autowired Db db;
    @Autowired RestTemplate restTemplate;
    @Autowired SysConfigManager config;
    @Autowired ObjectMapper objectMapper;

    // @Autowired
    // private ApplicationContext ctx;
    @Autowired LimsService limsService;

    @Autowired JdbcTemplate jmt;

    private LimsService testLimsService;
    // private MockWebServer mockWebServer;
    private String limsStr =  "{\"success\":\"true\",\"analysisTaskResult\":[{\"code\":[{\"DJ2104486B01\":[\"DJ2104486B01\"]}],\"identification\":null,\"productId\":\"DJ2104486\",\"runningNumber\":\"210726055\",\"concentration\":96.764,\"identityCard\":\"320911196609206624\",\"reanalysis\":\"DJ2104486,DJ2104486B01\",\"state\":\"0\",\"productMask\":\"DJ\"},{\"code\":[{\"CZ2103962B01\":[\"CZ2103962B01\"]},{\"CZ2103962S01\":[\"CZ2103962S01\"]}],\"identification\":\"null,null\",\"productId\":\"CZ2103962\",\"runningNumber\":\"210726006\",\"concentration\":\"160.21,436.16\",\"identityCard\":\"32032619750627441X\",\"reanalysis\":\"CZ2103962,CZ2103962S01\",\"state\":\"0,0\",\"productMask\":\"280200000004\"},{\"code\":[{\"CZ2103991B01\":[\"CZ2103991B01\"]},{\"CZ2103991S01\":[\"CZ2103991S01\"]}],\"identification\":\"null,null\",\"productId\":\"CZ2103991\",\"runningNumber\":\"210726007\",\"concentration\":\"83.563,218.038\",\"identityCard\":\"360281195212068028\",\"reanalysis\":\"CZ2103991\",\"state\":\"0,0\",\"productMask\":\"280200000004\"},{\"code\":[{\"HZ2103858B01\":[\"HZ2103858B01\"]},{\"HZ2103858S01\":[\"HZ2103858S01\"]}],\"identification\":\"null,null\",\"productId\":\"HZ2103858\",\"runningNumber\":\"210723001\",\"concentration\":\"26.0,494.757\",\"identityCard\":\"342422196211087271\",\"reanalysis\":\"HZ2103858,HZ2103858B01\",\"state\":\"0,0\",\"productMask\":\"280500000007\"}]}";
    private String uploasStr = "{\"success\":\"true\"}";

    private Object invokeReflectMethod(String methodName, Object... args) {
        return ReflectionTestUtils.invokeMethod(testLimsService, methodName, args);
    }

    @BeforeAll
    public void setup() {
        this.testLimsService = new LimsService();
        ReflectionTestUtils.setField(testLimsService, "db", this.db);
        ReflectionTestUtils.setField(testLimsService, "restTemplate", this.restTemplate);
        ReflectionTestUtils.setField(testLimsService, "config", this.config);
        ReflectionTestUtils.setField(testLimsService, "objectMapper", this.objectMapper);
        // testLimsService.setProjectListUrl("http://localhost:9099/lims");
        // testLimsService.setUploadQcSummaryUrl("http://localhost:9099/lims/upload");
    }

    @BeforeAll
    public void init() throws IOException {
        // MockWebServer not available - commenting out for now
        // mockWebServer = new MockWebServer();
        // mockWebServer.start( 9099);
        // final Dispatcher dispatcher = new Dispatcher() {
        //     @Override
        //     public MockResponse dispatch(RecordedRequest request) {
        //         if (request.getPath().equals("/lims")) {
        //             return new MockResponse()
        //                 .setResponseCode(200)
        //                 .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
        //                 .setBody(limsStr);
        //         }
        //         if (request.getPath().equals("/lims/upload") && request.getMethod().equals("GET")) {
        //             return  new MockResponse()
        //                 .setResponseCode(200)
        //                 .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
        //                 .setBody(uploasStr);
        //         }
        //         if (request.getPath().equals("/lims/upload") && request.getMethod().equals("POST")) {
        //             return  new MockResponse()
        //                 .setResponseCode(200)
        //                 .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
        //                 .setBody(uploasStr);
        //         }
        //         return new MockResponse().setResponseCode(404);
        //     }
        // };
        // mockWebServer.setDispatcher(dispatcher);
        // try {
        //     mockWebServer.start(9099);
        // } catch (IOException e) {
        //     e.printStackTrace();
        //     System.exit(0);
        // }
    }

    @Test
    @SuppressWarnings("unchecked")
    public void getProjectListFromLimsTest() {
        LocalDate startDate = LocalDate.of(2023, 11, 20);
        LocalDate endDate = LocalDate.of(2023, 11, 25);
        assertNotNull(testLimsService);
        Map<String, Object> result = (Map<String, Object>)
            invokeReflectMethod("getProjectListFromLims", startDate, endDate);
        assertNotNull(result);
        Assertions.assertEquals(0, ((List<Object>)result.get("projects")).size());
        assertEquals(4, ((List<Object>)result.get("delete_projects")).size());
        System.out.println(result);
    }

    @Transactional
    @Rollback
    @ParameterizedTest
    @CsvSource({
        ", , false",
        "2203848, 220522045, false",
        ", 220522045, true",
        "8484, 220522045, true",
        "8481, 220522045, false",
        "8464, 220522045, false"
    })
    public void ifTransferFinishedTest(Long id, String projectSn, boolean boolResult)  {
        Map<String, Object> testProjectMap = new HashMap<>();
        testProjectMap.put("id", id);
        testProjectMap.put("project_sn", projectSn);
        boolean result = (boolean)
            invokeReflectMethod("ifTransferFinished", testProjectMap);
        assertNotNull(result);
        assertEquals(boolResult, result);
    }

    @Transactional
    @Rollback
    @ParameterizedTest
    @CsvSource({
        "202508017, QcSummary上传失败: 项目不存在",
        "201208017, QcSummary上传失败: 项目状态异常 - cleared",
        "220522004, QcSummary上传失败: 项目同步未完成 - finished",
        "220522045, ok"
    })
    @SuppressWarnings("unchecked")
    public void uploadQcSummaryTest(String projectSn, String expect) {
        Map<String, Object> result = (Map<String, Object>)
            invokeReflectMethod("uploadQcSummary", projectSn);
        assertNotNull(result);
        assertEquals(expect, result.get("errMsg"));
    }

    // private ObjectNode getObjectNodeFromMockServer() throws InterruptedException, EOFException, IOException {
    //     RecordedRequest request = mockWebServer.takeRequest();
    //     ObjectNode objectNode = (ObjectNode) objectMapper.readTree(request.getBody().readUtf8Line());
    //     return objectNode;
    // }

    @Transactional
    @Rollback
    @ParameterizedTest
    @CsvSource({
        "220522009,'', 1, 删除配对任务失败: delete names are empty",
        "220522009,'   ,', 1, 删除配对任务失败: project name is empty",
        "230522012, PZ2203912, 2, 删除配对任务失败: project not exists",
        "220522012, PZ2203912, 3, 删除配对任务失败: project is analyzing",
        "220522009, CZ2203838, 4, 删除配对任务失败: lims info error",
        "220522009, PZ2203912, 0, OK",
        "220522039, 'AZ2203899,   ', 5, 删除配对任务失败: sample name is empty",
        // "220522039, 'AZ2203899,AZ2203899B01, ', 5, 删除配对任务失败: sample name is empty",
        "220522039, 'AZ2203899, AZ2203899B05', 6, 删除配对任务失败: sample not exists",
        "220522039, 'AZ2203899, BZ2110313B01', 4, 删除配对任务失败: lims info error",
        "220522039, 'AZ2203899,AZ2203899B01', 9, 删除配对任务失败: delete sample error",
        "220522061, 'AZ2203813, AZ2203813F02', 8, 删除配对任务失败: fastq file not exists"
    })
    public void Z_deleteProject_CSV_PARAMETER_Test(
        String projectSn, String deleteNames, int errCode, String errMsg)
        throws EOFException, InterruptedException, IOException {

        invokeReflectMethod("deleteProject", projectSn, deleteNames);
        // Test disabled - MockWebServer not available
        // ObjectNode requestParamsNode = getObjectNodeFromMockServer();
        // assertNotNull(requestParamsNode);
        // System.out.println("requestParamsNode ->" + requestParamsNode);
        // assertEquals(errCode, requestParamsNode.get("errCode").asInt());
        // assertEquals(errMsg, requestParamsNode.get("errMsg").asText());
    }

    @Transactional
    @Rollback
    @Test
    public void handleLimsPostResultTest()
        throws IOException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {

        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
        mockRequest.setParameter("test", "1");
        // Mockito mock(LimsService.class).

    }


}
