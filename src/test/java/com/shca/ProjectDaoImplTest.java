package com.shca;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.LocalTime;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.shca.config.ApplicationConfiguration;
import com.shca.dao.daoimpl.ProjectDaoImpl;
import com.shca.entity.Project;
import com.shca.entity.enums.ProjectStatusEnum;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
public class ProjectDaoImplTest {

    @Autowired
    private ProjectDaoImpl projectDao;

    @Test
    public void selectByIdTest() {
        Project result = projectDao.selectById(32);
        assertNull(result);
        result = projectDao.selectById(39);
        assertNotNull(result);
        assertEquals(39, result.getId());
        assertEquals("11221848", result.getProjectSn());
        assertEquals("B2001223", result.getProjectName());
        assertEquals("CLEARED", result.getStatus().toString());
        assertEquals("2020-11-22T18:48:53", result.getCreateTime().toString());
        assertEquals(5, result.getPriority());
    }

    @Test
    public void insertTest() {
        int projectId = 32;
        Project testProject = projectDao.selectById(projectId);
        assertNull(testProject);
        long result = projectDao.insert(testProject);
        assertEquals(0, result);
        projectId = 39;
        testProject = projectDao.selectById(projectId);
        assertNotNull(testProject);
        int originPriority = testProject.getPriority();
        testProject.setPriority(100);
        result = projectDao.update(testProject);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        testProject = projectDao.selectById(projectId);
        assertEquals(100, testProject.getPriority());
        testProject.setPriority(originPriority);
        result = projectDao.update(testProject);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        assertEquals(originPriority, testProject.getPriority());
        testProject.setProjectSn(LocalTime.now().toString());
        result = projectDao.insert(testProject);
        assertNotEquals(0, result);
        assertNotEquals(-1, result);
        testProject = projectDao.selectById(result);
        assertNotNull(testProject);
        projectDao.deleteById(result);
        testProject = projectDao.selectById(result);
        assertNull(testProject);
    }

    @Test
    public void getProjectListByStatusTest() {
        List<Project> resultList = projectDao.getProjectListByStatus(ProjectStatusEnum.CLEARED);
        assertEquals(60, resultList.size());
    }
}
