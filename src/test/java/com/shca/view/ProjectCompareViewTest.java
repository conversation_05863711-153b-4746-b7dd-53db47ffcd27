/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-17 22:47:41
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-18 10:34:38
 * @FilePath: /analysis_engine/src/test/java/com/shca/view/ProjectCompareViewTest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.view;
import com.shca.Pipeline;
import com.shca.config.JdbcConfig;
import com.shca.module.cancer.service.support.QcSummaryConfigService;
import com.ywang.sql.support.Db;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {JdbcConfig.class})
public class ProjectCompareViewTest {
    @Resource Db db;
    @Test
    public void cancer_type_enum_test() {
        System.out.println("ProjectCompareViewTest starting ...");
        System.out.println("db object is -> " + db.getClass().getName());
        String sql = "SELECT * FROM shca_project_project where project_name like 'DZ%' limit 1";
        // // List<Map<String, Object>> projectList = jdbcTemplate.queryForList(sql);
        List<Map<String, Object>> projectList = db.select(sql);
        String cancerKindString = (String) projectList.get(0).get("cancer_kind");
        System.out.println("cancerKindString is -> " + cancerKindString);
        // Pipeline pipeline =  Pipeline.valueOf(cancerKindString);
        Pipeline parseResult = Pipeline.parse(cancerKindString);
        String fullName = parseResult.getFullName();
        System.out.println("fullName is -> " + fullName);
        // Assertions.assertEquals("BZ", cancerKindString);
        // Assertions.assertEquals("BZ", pipeline.getFullName());
    }

}
