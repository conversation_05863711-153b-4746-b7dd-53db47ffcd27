package com.shca;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shca.config.ApplicationConfiguration;
import com.shca.cromwell.CromwellClientNew;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.cromwell.service.CancerFileService;
import com.shca.cromwell.service.CromwellService;
import com.shca.cromwell.service.CromwellServiceSupport;
import com.shca.cromwell.service.FileTransferService;
import com.shca.cromwell.service.GenericService;
import com.shca.cromwell.service.MutualService;
import com.shca.cromwell.service.SummaryService;
import com.shca.cromwell.service.UploadService;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.shca.config.SystemConfigService;

// import mockwebserver3.MockResponse;
// import mockwebserver3.MockWebServer;

@Transactional
@Rollback
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
public class CromwellServiceTest {
    @Resource JdbcTemplate jdbcTemplate;
    @Resource CromwellService cromwellService;
    @Resource CromwellServiceSupport support;
    @Resource FileTransferService transfer;
    @Resource CancerFileService fileService;
    @Resource GenericService genericService;
    @Resource MutualService mutualService;
    @Resource SummaryService summaryService;
    @Resource UploadService uploadService;
    @Resource SystemConfigService config;
    @Resource DataSource dataSource;
    @Resource RestTemplate restTemplate;
    @Resource ObjectMapper objectMapper;
    @Resource CromwellClientNew client;

    public static Map<String, Object> project;
    public static Map<String, Object> sample;
    public static Map<String, Object> peixiSample;
    // public static  MockRestServiceServer mockServer;
    // private MockWebServer mockWebServer;


    @BeforeAll
    public void init() throws IOException {
        String projectSql = "SELECT * FROM shca_project_project WHERE status='finished' ORDER BY id DESC LIMIT 1";
        CromwellServiceTest.project = jdbcTemplate.queryForMap(projectSql);
        String sampleSql = "SELECT * FROM shca_project_cancer ORDER BY id DESC LIMIT 1";
        CromwellServiceTest.sample = jdbcTemplate.queryForMap(sampleSql);
        String peixiSampleSql = "SELECT * FROM shca_project_cancer WHERE sample_name like '%J%' ORDER BY id DESC LIMIT 1";
        CromwellServiceTest.peixiSample = jdbcTemplate.queryForMap(peixiSampleSql);
        // RestGatewaySupport gateway = new RestGatewaySupport();
        // gateway.setRestTemplate(restTemplate);
        // this.mockServer = MockRestServiceServer.createServer(gateway);
        // mockServer = MockRestServiceServer.createServer(restTemplate);
        // mockWebServer = new MockWebServer();
        // mockWebServer.start( 8089);
        // mockWebServer.url("/api/worflows/v1");
    }

    // private Object invokeReflectMethod(String methodName, Object... args) {
    //     return ReflectionTestUtils.invokeMethod(, methodName, args);
    // }

    @Test
    public void fileTransferTest() {
        assertFalse((Boolean)ReflectionTestUtils.invokeMethod(transfer, "ifTransferTaskBusy"));
        // Assertions.assertEquals(1, transfer.ifTransferTaskBusy());
    }

    @Test
    public void configTest() {
        System.out.println(config.getValue("cromwell.max_workflow_count"));
        int maxCapacityOfJobQueue = Integer.parseInt(config.getValue("cromwell.max_workflow_count"));
        Assertions.assertEquals(25, maxCapacityOfJobQueue);
    }

    @Test
    public void _getAvailableSlotsNumTest() {
        Map<String, Integer> jobqueueStatus = support.getAvailableSlotsNum(config);
        assertEquals(25 , jobqueueStatus.get("maxCapacityOfJobQueue"));
        assertEquals(0, jobqueueStatus.get("currentRunningJobCnt"));
        assertEquals(25, jobqueueStatus.get("availableSlotsNum"));
        // config.remove("cromwell.max_workflow_count"); // SystemConfigService doesn't have remove method
        // jobqueueStatus = support.getAvailableSlotsNum(config);
        // assertEquals(35 , jobqueueStatus.get("maxCapacityOfJobQueue"));
    }


    @Test
    public void _getWorkflowOptionJsonTest() {
        Map<String, Object> cromwellConfig = support.getCromwellConfig("breast_cancer", '1');
        File workflowOptionsJson = support.getWorkflowOptionJson(config, cromwellConfig);
        assertEquals(null, workflowOptionsJson);
    }

    @Test
    public void getTransferFileProjectByIdTest() {
        Map<String, Object> result = ReflectionTestUtils.invokeMethod(transfer, "getTransferFileProjectBySn", new Object[]{"2024012204"});
        assertNull(result);
    }

    @Test
    public void createOrUpdateFileRecordTest() {
        Map<String, Object> project = new HashMap<>();
        project.put("id", 36);
        project.put("project_sn", 11111639);
        project.put("project_no", 11111639);
        project.put("project_name", "B2001174");
        int returnId = (int)ReflectionTestUtils.invokeMethod(transfer,"createOrUpdateTransferFileRecord",
            new Object[] {
                project,
                "bbd7af5b-5196-4036-ba1c-4b8aafb6caef",
                "create"
            });
        assertEquals(1, returnId);
    }

    @Test
    public void _getAvailableFastqsToCreateSampleTest() {
        Map<String, List<Map<String, Object>>>  result =
            ReflectionTestUtils.invokeMethod(fileService, "getDoubleReadyStatusFastqs");
        assertEquals(2, result.size());
        // System.out.println(result);
        ReflectionTestUtils.invokeMethod(fileService, "createCancerRecordByCancerFileRecord");
        result = ReflectionTestUtils.invokeMethod(fileService, "getAvailableFastqsToCreateSample");
        assertEquals(0, result.size());
    }

    @Test
    public void fileTransferTaskCountTest() {
        int result = (int)ReflectionTestUtils.invokeMethod(transfer, "fileTransferTaskCount", new Object[]{24});
        assertEquals(1, result);
    }

    @Test
    public void createTransferJsonFileWithProjectMapTest() {
        File result =
            ReflectionTestUtils.invokeMethod(transfer, "createTransferJsonFileWithProjectMap", new Object[]{project});
        assertNotNull( result );
        System.out.println("\033[0;32m" + result.getAbsolutePath() + "\033[0m");
        assertTrue(result.getAbsolutePath().contains("CZ2203848/fileTransfer_CZ2203848_"));
        assertTrue(result.getName().endsWith("json"));
        //     "/mydata/shca/projects/CZ2203908/fileTransfer_CZ2203908_202311131749.json",
    }

    // @Test
    // public void createVcfTransferJsonFileWithProjectMapTest() {
    //     File result = support.createVcfTransferJsonFileWithProjectMap(project);
    //     assertNull(result);
    // }

    @Test
    public void rawdataReadyTest() {
        boolean resultR1 =
            ReflectionTestUtils.invokeMethod(genericService, "rawdataReady", new Object[]{sample, "R1"});
        boolean resultR2 =
            ReflectionTestUtils.invokeMethod(genericService, "rawdataReady", new Object[]{sample, "R2"});
        boolean resultR3 =
            ReflectionTestUtils.invokeMethod(genericService, "rawdataReady", new Object[]{sample, "R3"});
        assertTrue(resultR1);
        assertTrue(resultR2);
        assertFalse(resultR3);
    }

    @Test
    public void getRawdataStorePathTest() {
        String result = support.getRawdataStorePath(sample, "R1");
        assertEquals(
            "/mydata/shca/rawdata/CZ2203654S01/R22012011-zhoupu02173-CZ2203654S01_combined_R1.fastq.gz",
            result);
        result = support.getRawdataStorePath(sample, "R2");
        assertEquals(
            "/mydata/shca/rawdata/CZ2203654S01/R22012011-zhoupu02173-CZ2203654S01_combined_R2.fastq.gz",
            result);

        result = support.getRawdataStorePath("abcd", "R2");
        assertNull(result);
        result = support.getRawdataStorePath("", "R2");
        assertNull(result);
        // result = cromwellService.getRawdataStorePath((String)null, "R2");
    }

    @Test
    public void getReadySamplesForGenericTest() {
        List<Map<String, Object>> result =
            ReflectionTestUtils.invokeMethod(genericService, "getReadySamplesForGeneric");
        assertEquals(21, result.size());
        assertTrue(
            result.stream()
                .map( item -> item.get("sample_name"))
                .anyMatch(name -> "BZ2203636B01".equals(name)));
        ;
    }

    @Test
    public void getProcessServiceTest() {
        ProcessService result = support.getProcessService(sample);
        assertNotNull(result);
        assertEquals(
            "com.shca.module.cancer.service.impl.NormalProcessServiceImpl",
            result.getClass().getName());
        result = support.getProcessService(peixiSample);
        assertEquals(
            "com.shca.module.cancer.service.impl.PeixiProcessServiceImpl",
            result.getClass().getName());
    }

    @Test
    public void updateSampleStatusToRunningStep0Test() {
        String originalStatus = (String)sample.get("status");
        ReflectionTestUtils.invokeMethod(genericService, "updateSampleStatusToRunningStep0", new Object[]{sample});
        // support.updateSampleStatusToRunningStep0(sample);
        Map<String ,Object> result =jdbcTemplate.queryForMap(
            "SELECT * FROM shca_project_cancer WHERE id=? ",
            new Object[] {sample.get("id")});
        assertNotNull(result);
        assertEquals("running_step0", (String)result.get("status"));
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET status=? WHERE id=? ",
            new Object[] { originalStatus, sample.get("id") });
    }

    @Test
    public void getCromwellConfigTest() {
        Map<String, Object> step0Result = support.getCromwellConfig("breast_cancer", '0');
        assertEquals(13, step0Result.size());
        assertEquals(24, step0Result.get("total_run_step"));
    }

    @Test
    public void updateProjectStatus2RunningStep1Test() {
        Map<String, Object> cromwellConfig = support.getCromwellConfig(
            (String)project.get("cancer_kind"), '0');
        String originalStatus = (String)project.get("status");
        ReflectionTestUtils.invokeMethod(mutualService, "updateProjectStatus2RunningStep1", new Object[]{project, new WorkflowsResponse(), cromwellConfig});
        // support.updateProjectStatus2RunningStep1();
        Map<String ,Object> result =jdbcTemplate.queryForMap(
            "SELECT * FROM shca_project_project WHERE id=? ",
            new Object[] {project.get("id")});
        assertEquals("running_step1", (String)result.get("status"));
        jdbcTemplate.update(
            "UPDATE shca_project_project SET status=? WHERE id=? ",
            new Object[] { originalStatus, project.get("id") });
    }

    @Test public void getWorkFlowStatusByWorkFlowIdTest() {
        String result =
            ReflectionTestUtils.invokeMethod(support, "getWorkFlowStatusByWorkFlowId", new Object[] {"8ac19c24-c1df-4d5b-9d67-88bdad0cf3aa"});
        assertEquals("Failed", result);
        result =
            ReflectionTestUtils.invokeMethod(support, "getWorkFlowStatusByWorkFlowId", new Object[] {"4cf946e8-cf1c-4dc9-8d09-bd34dc829255"});
        assertEquals("Succeeded", result);
         result =
            ReflectionTestUtils.invokeMethod(support, "getWorkFlowStatusByWorkFlowId", new Object[] {"e8f3299d-ae75-435b-bbff-b316c15efaf8"});
        assertEquals("Running", result);

    }
    @Test
    public void updateSampleTaskStatusByCromwellTest() {
        String originCromwellId = (String)sample.get("cromwell_workflow_id_0");
        String originStatus = (String)sample.get("status");
        long sampleId = (long) sample.get("id");
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET cromwell_workflow_id_0=? WHERE id=?", new Object[] {
                "8f01e3d0-6e68-4f8f-9bc3-9498de698778",
                sampleId});

        ReflectionTestUtils.invokeMethod(fileService, "updateSampleTaskStatusByCromwell");
        // support.updateSampleTaskStatusByCromwell();
        String status = jdbcTemplate.queryForObject(
            "SELECT status FROM shca_project_cancer WHERE id=?",
            new Object[] { sampleId},
            String.class);

        assertEquals("failed", status);
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET status=?, cromwell_workflow_id_0=? WHERE id=?",
            new Object[] {originStatus, originCromwellId, sampleId });
    }

    @Test
    public void updatePriorityTest() {
        SimpleJdbcInsert simpleJdbcInsert = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_project_priority")
            .usingGeneratedKeyColumns("id");
        Map<String, Object> priorityMap = new HashMap<>();
        priorityMap.put("project_name", project.get("project_name"));
        priorityMap.put("priority", (int)project.get("priority")+10);
        priorityMap.put("file", "/path/to/file/...");
        priorityMap.put("create_time", LocalDateTime.now());
        priorityMap.put("create_username", "test");
        priorityMap.put("update_time", LocalDateTime.now());
        priorityMap.put("update_username", "test");

        int returnId =  simpleJdbcInsert.execute(priorityMap);
        cromwellService.updatePriority();
        int newpriority = jdbcTemplate.queryForObject(
            "SELECT priority FROM shca_project_project WHERE id=? ",
            new Object[] {project.get("id")}, Integer.class);
        jdbcTemplate.update(
            "DELETE FROM shca_project_project_priority WHERE id=?",
            new Object[] { returnId });
        // assertEquals(5, project.get("priority"));
        assertEquals((int)project.get("priority")+10, newpriority);
    }

    @Test
    public void getReadyForMutualProjectsTest() {
        List<Map<String, Object>> result =
            ReflectionTestUtils.invokeMethod(mutualService, "getReadyForMutualProjects");
        assertEquals(6, result.size());
        Map<String, Object> project = jdbcTemplate.queryForMap(
            "SELECT * FROM shca_project_project WHERE status='wait_sample' LIMIT 1");
        assertFalse(project.isEmpty());
        System.out.println(project.get("project_name"));
        System.out.println(jdbcTemplate.getClass().getName());

        Map<String, Object> normalSample = jdbcTemplate.queryForMap(
            "SELECT id, status FROM shca_project_cancer WHERE sample_name=? AND status not in (?,?,?)",
            new Object[] {project.get("sample_name_0"), "cleared", "failed", "aborted" });
        Map<String, Object> tumorSample = jdbcTemplate.queryForMap(
            "SELECT id, status FROM shca_project_cancer WHERE sample_name=? AND status not in (?,?,?)",
            new Object[] {project.get("sample_name_1"), "cleared", "failed", "aborted" });
        assertNotNull(tumorSample);
        assertFalse(CollectionUtils.isEmpty(tumorSample));
        System.out.println(normalSample.get("id"));
        System.out.println(tumorSample.get("id"));
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET status='finished' WHERE (id=? OR id=?) ",
            new Object[] {normalSample.get("id"), tumorSample.get("id")});
        result = ReflectionTestUtils.invokeMethod(mutualService, "getReadyForMutualProjects");
        assertEquals(7, result.size());
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET status=? WHERE id=? ",
            new Object[] {normalSample.get("status"),normalSample.get("id")});
        jdbcTemplate.update(
            "UPDATE shca_project_cancer SET status=? WHERE id=? ",
            new Object[] {tumorSample.get("status"),tumorSample.get("id")});
        result = ReflectionTestUtils.invokeMethod(mutualService, "getReadyForMutualProjects");
        assertEquals(6, result.size());
    }

    @Test
    public void getRunningWorkFlowCountFromCromwellTest() {
        int result = support.getRunningWorkFlowCountFromCromwell();
        assertEquals(0, result);
        jdbcTemplate.update("UPDATE cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY SET WORKFLOW_NAME='generalProcess' WHERE WORKFLOW_STATUS='Running'");
        result = support.getRunningWorkFlowCountFromCromwell();
        assertEquals(1, result);
        jdbcTemplate.update("UPDATE cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY SET WORKFLOW_NAME='fileTransfer' WHERE WORKFLOW_STATUS='Running'");
    }

    @Test
    public void adjustProjectStatusTest() {

    }

    @Test
    public void getWorkFlowStatusByWorkFlowId() {
        String result =
            support.getWorkFlowStatusByWorkFlowId((String)project.get("cromwell_workflow_id_0"));
        assertEquals("Failed", result);
    }

    @Test
    public void launchSummaryTaskTest() throws JsonProcessingException, URISyntaxException {

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("id", "89c4849e-86b5-441a-bee5-c63f9d8327d2");
        mockResult.put("status", "Submitted");

        // mockWebServer.enqueue(
        //     new MockResponse()
        //         .setResponseCode(200)
        //         .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        //         .setBody(objectMapper.writeValueAsString(mockResult))
        // );
        config.setValue("workspace.project_path",  "/Users/<USER>/analysis_engine/src/main/resources/test_resources/");

        Map<String, Object> originSource = jdbcTemplate.queryForMap(
            "SELECT id, cromwell_workflow_source FROM shca_cromwell_config "
            + " WHERE cancer_kind=? AND script_step=? ORDER BY id DESC",
            new Object[] { project.get("cancer_kind"), '2'});

        jdbcTemplate.update(
            "UPDATE shca_cromwell_config SET cromwell_workflow_source=? WHERE id=?",
            new Object[] {
                "/Users/<USER>/analysis_engine/src/main/resources/test_resources/generalTemplate.wdl",
                originSource.get("id")
            }
        );

        summaryService.launchSummaryTask(
            project,
            (String)project.get("sample_name_0"),
            "/Users/<USER>/analysis_engine/src/main/resources/test_resources/*********-zhoupu123456-CZ2203848B01_combined_R1.fastq.gz",
            "/Users/<USER>/analysis_engine/src/main/resources/test_resources/*********-zhoupu123456-CZ2203848B01_combined_R2.fastq.gz"
        );

        assertNotNull((String)project.get("cromwell_workflow_id_1"));
        assertEquals(
            "Succeeded",
            (String)project.get("cromwell_workflow_status_1"));

        jdbcTemplate.update(
            "UPDATE shca_cromwell_config SET cromwell_workflow_source=? WHERE id=? ",
            new Object[] {
                originSource.get("cromwell_workflow_source"),
                originSource.get("id")
            }
        );

    }

    @Test
    public void updateSampleSummaryStatusByCromwellTest() {

    }

    @Test
    @SuppressWarnings("unchecked")
    public void createQcSummaryEntryByTxtFileTest() {
        File result = summaryService.createQcSummaryEntryByTxtFile(project, "CZ2203848B01");
        Map<String, QcSummaryService> qcSummaryServiceMap =
            (Map<String, QcSummaryService>) ReflectionTestUtils.getField(summaryService, "qcSummaryServiceMap");
        assertNotNull(qcSummaryServiceMap);
        assertNotNull(result);
        Map<String,String> references = qcSummaryServiceMap.get(project.get("cancer_kind")).getReferences();
        // QcSummaryService qcSummaryService = (QcSummaryService) ReflectionTestUtils.getField(summaryService, "qcSummaryService");
        QcSummaryService qcSummaryService = qcSummaryServiceMap.get((String) project.get("cancer_kind"));
        List<QcSummary> qcSummaryList = (List<QcSummary>) qcSummaryService.qcSummary(result);
        assertEquals("TotalReads", qcSummaryList.get(0).getName());
        assertEquals("14739444", qcSummaryList.get(0).getValue());
        assertEquals(references.get("TotalReads"), qcSummaryList.get(0).getReference());
        assertNull(qcSummaryList.get(0).getQualified());
        assertEquals("Q20_R1", qcSummaryList.get(1).getName());
        assertEquals("97.98", qcSummaryList.get(1).getValue());
        assertEquals(">=" + references.get("Q20_R1"), qcSummaryList.get(1).getReference());
        assertTrue(qcSummaryList.get(1).getQualified());
    }

    @Test
    public void getConcentrationArrayTest() {
        String[] result = null;
        try {
            result = uploadService.getConcentrationArray(project);
        } catch (IOException e) {
            e.printStackTrace();
        }

        assertNotNull(result);
        assertEquals("166.124", result[0]);
        assertEquals("27.29", result[1]);
    }
}