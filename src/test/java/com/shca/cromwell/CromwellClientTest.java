package com.shca.cromwell;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.config.ApplicationConfiguration;


@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ApplicationConfiguration.class})
@TestInstance(Lifecycle.PER_CLASS)
// @WebAppConfiguration
public class CromwellClientTest {

    private final String url = "http://localhost:8899";
    private final String version = "v1";

    @Autowired
    private CromwellClientNew cromwellClient;

    @Autowired
    ObjectMapper mapper;

    @Autowired
    RestTemplate restTemplate;

    @BeforeAll
    public void init() {
        System.out.println("test initing ...");
        cromwellClient.setUrl(url);
        cromwellClient.setVersion(version);
    }

    @Test
    public void cromwellClientNew_getSend_Test() throws IOException {
        ResponseEntity<String> response = cromwellClient.getSend(
            "/api/workflows/", "89c4849e-86b5-441a-bee5-c63f9d8327d2", "status");
        System.out.println(response.getBody());
        assertNotNull(response);
        ObjectNode objectNode = (ObjectNode)mapper.readTree(response.getBody().toString());
        assertEquals("89c4849e-86b5-441a-bee5-c63f9d8327d2", objectNode.get("id").asText());

    }

    @Test
    public void cromwellClientNew_postMultipart_Test() throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.add("workflowSource",
            new FileSystemResource("/Users/<USER>/analysis_engine/src/main/resources/test_resources/generalTemplate.wdl"));
        paramsMap.add("workflowInputs",
            new FileSystemResource("/Users/<USER>/analysis_engine/src/main/resources/test_resources/*********/*********B01/summary_*********_202311202053_pretty.json"));

        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(paramsMap, headers);
        // ResponseEntity<String> response = restTemplate.postForEntity("http://localhost:8899/api/workflows/v1", request,String.class);
        ResponseEntity<String> response = cromwellClient.postMultipart("/api/workflows/", request);
        System.out.println(response.getBody());
        assertNotNull(response);
        ObjectNode objectNode = (ObjectNode)mapper.readTree(response.getBody().toString());
        assertEquals("Submitted", objectNode.get("status").asText());
    }

    @Test
    public void cromwellClientNew_postSend_Test() throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        List<String> paramList = new LinkedList<>();
        ObjectNode param1 = mapper.createObjectNode();
        ObjectNode param2 = mapper.createObjectNode();
        param1.put("status", "Succeeded");
        param2.put("status", "Failed");
        paramList.add(param1.toString());
        paramList.add(param2.toString());
        System.out.println(paramList);
        // HttpEntity<String> request = new HttpEntity<>("[{\"status\":\"Succeeded\"},{\"status\":\"Failed\"}]", headers);
        HttpEntity<String> request = new HttpEntity<>(paramList.toString(), headers);
        ResponseEntity<String>  response = restTemplate.postForEntity("http://localhost:8899//api/workflows/v1/query", request, String.class);
        System.out.println(response.getBody());
        assertNotNull(response);
        ObjectNode objectNode = (ObjectNode)mapper.readTree(response.getBody().toString());
        assertEquals(16, objectNode.get("results").size());
    }


}
