[{"description": "mock lims", "request": {"uri": "/rest/RestfulService/informationCapture", "method": "post"}, "response": {"success": "true", "analysisTaskResult": [{"code": [{"PZ2203130B01": ["PZ2203130B01"]}, {"PZ2203130F01": ["PZ2203130F01"]}], "identification": "null,null", "productId": "PZ2203130", "runningNumber": "220513005", "concentration": "72.313,69.0", "identityCard": "320911196206204923", "reanalysis": "", "state": "0,0", "productMask": "280400000004"}, {"code": [{"DZ2203489B01": ["DZ2203489B01"]}, {"DZ2203489F02": ["DZ2203489F02"]}], "identification": "null,null", "productId": "DZ2203489", "runningNumber": "220513011", "concentration": "251.423,41.0", "identityCard": "342601196604140642", "reanalysis": "", "state": "1,1", "productMask": "280600000009"}]}}]