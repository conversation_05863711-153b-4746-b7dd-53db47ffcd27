package com.shca.http;

/**
 * Modern replacement for com.ywang.http.SessionUser
 */
public interface SessionUser {
    
    /**
     * Get the username
     */
    String getUserName();
    
    /**
     * Get the user ID
     */
    String getUserId();
    
    /**
     * Get the organization code
     */
    String getOrgCode();
    
    /**
     * Get the role code
     */
    String getRoleCode();

    boolean hasRole(String roleCode);
}
