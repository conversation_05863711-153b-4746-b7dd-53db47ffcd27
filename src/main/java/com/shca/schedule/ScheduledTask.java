package com.shca.schedule;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.shca.module.ftp.service.FtpMonitorByLog;

@Component
@EnableScheduling
public class ScheduledTask {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ScheduledTask.class);
    @Resource private FtpMonitorByLog ftpMonitorByLog;

    @Scheduled(cron = "* */1 * * * *")
    private void ftpLogHandler() {
        log.info("FTP log handler is running...");
        ftpMonitorByLog.sync();
    }

}
