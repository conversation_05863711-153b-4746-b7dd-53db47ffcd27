package com.shca.schedule;

import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

// import com.shca.module.ftp.service.FtpMonitorByLog; // Disabled during migration

/**
 * Scheduled Task Component
 *
 * Note: FTP monitoring task is temporarily disabled during migration
 * as it requires extensive refactoring of FtpLogSupport class
 */
@Component
@EnableScheduling
public class ScheduledTask {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ScheduledTask.class);
    // @Resource private FtpMonitorByLog ftpMonitorByLog; // Disabled during migration

    /**
     * FTP log handler - temporarily disabled during migration
     * This task requires FtpMonitorByLog which depends on legacy Db class
     */
    // @Scheduled(cron = "* */1 * * * *")
    // private void ftpLogHandler() {
    //     log.info("FTP log handler is running...");
    //     ftpMonitorByLog.sync();
    // }

}
