package com.shca.file.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.LongCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.util.*;


public class FileService {

    private final static Logger LOGGER = Logger.getLogger( FileService.class);

    @Autowired
    private Db db;

    private CromwellClient client;
    public void setClient(CromwellClient client) {
        this.client = client;
    }


    public Map create(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql,id);
        String sample_name_0 = (String) map.get("sample_name_0");
        String sample_name_1 = (String) map.get("sample_name_1");
        String project_name = (String) map.get("project_name");

        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        if(!workflowSource.exists()){
            ret.put("errcode","1000");
            ret.put("errmsg","fileTransfer.wdl未找到");
            return ret;
        }
        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer.json");

        JSONObject obj = new JSONObject();
        obj.put("fileTransfer.ACTION_FT","create");
        obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name);
        obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");

        JSONArray array = new JSONArray();
        if(StringUtils.hasLength(sample_name_0)) {
            File bai = new File("/mydata/shca/samples/" + sample_name_0 + "/gatk/variation/" + sample_name_0 + "_recal.bai");
            if(bai.exists()){
                array.add(bai.getAbsolutePath());
            } else {
                ret.put("errcode","3000");
                ret.put("errmsg",sample_name_0+"_recal.bai未找到");
                return ret;
            }
            File bam = new  File("/mydata/shca/samples/" + sample_name_0 + "/gatk/variation/" + sample_name_0 + "_recal.bam");
            if(bam.exists()){
                array.add(bam.getAbsolutePath());
            } else {
                ret.put("errcode","3000");
                ret.put("errmsg",sample_name_0+"_recal.bam未找到");
                return ret;
            }

            File bencov = new  File("/mydata/shca/samples/" + sample_name_0 + "/capture/beds/" + sample_name_0 + ".bedcov.txt");
            if(bencov.exists()){
                array.add(bencov.getAbsolutePath());
            }
            File bencov_average = new  File("/mydata/shca/samples/" + sample_name_0 + "/capture/beds/" + sample_name_0 + ".bedcov.average.txt");
            if(bencov_average.exists()){
                array.add(bencov_average.getAbsolutePath());
            }
        }
        if(StringUtils.hasLength(sample_name_1)) {
            File bai = new File("/mydata/shca/samples/" + sample_name_1 + "/gatk/variation/" + sample_name_1 + "_recal.bai");
            if(bai.exists()){
                array.add(bai.getAbsolutePath());
            } else {
                ret.put("errcode","3000");
                ret.put("errmsg",sample_name_1+"_recal.bam未找到");
                return ret;
            }
            File bam = new  File("/mydata/shca/samples/" + sample_name_1 + "/gatk/variation/" + sample_name_1 + "_recal.bam");
            if(bam.exists()){
                array.add(bam.getAbsolutePath());
            } else {
                ret.put("errcode","3000");
                ret.put("errmsg",sample_name_1+"_recal.bam未找到");
                return ret;
            }

            File bencov = new  File("/mydata/shca/samples/" + sample_name_1 + "/capture/beds/" + sample_name_1 + ".bedcov.txt");
            if(bencov.exists()){
                array.add(bencov.getAbsolutePath());
            }
            File bencov_average = new  File("/mydata/shca/samples/" + sample_name_1 + "/capture/beds/" + sample_name_1 + ".bedcov.average.txt");
            if(bencov_average.exists()){
                array.add(bencov_average.getAbsolutePath());
            }
        }
        obj.put("fileTransfer.fileList_FT",array);

        WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

        WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));

        String sql2 = "update shca_project_project set cromwell_workflow_id_2 = ?,cromwell_workflow_type_2 = ? where id = ?  ";
        db.update(sql2,response.getId(),"create",id);

        ret.put("errcode","0");
        ret.put("workflowId",response.getId());
        return ret;
    }


    public Map create2(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql,id);
        String sample_name_0 = (String) map.get("sample_name_0");
        String sample_name_1 = (String) map.get("sample_name_1");
        String project_name = (String) map.get("project_name");

        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        if(!workflowSource.exists()){
            ret.put("errcode","1000");
            ret.put("errmsg","fileTransfer.wdl未找到");
            return ret;
        }
        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer2.json");

        JSONObject obj = new JSONObject();
        obj.put("fileTransfer.ACTION_FT","create");
        obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name+"/fileForUpload");
        obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");

        JSONArray array = new JSONArray();
        File filesForUpload = new File("/mydata/shca/projects/"+project_name+"/filesForUpload");
        if(filesForUpload.exists()){
            File[] files = filesForUpload.listFiles();
            for(int i=0;i<files.length;i++){
                File file = files[i];
                array.add(file.getAbsolutePath());
            }
        }
        obj.put("fileTransfer.fileList_FT",array);

        WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

        WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));

        String sql2 = "update shca_project_project set cromwell_workflow_id_2_2 = ? where id = ?  ";
        db.update(sql2,response.getId() , id);

        ret.put("errcode","0");
        ret.put("workflowId",response.getId());
        return ret;
    }

    public Map create3(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql,id);
        String sample_name_0 = (String) map.get("sample_name_0");
        String sample_name_1 = (String) map.get("sample_name_1");
        String project_name = (String) map.get("project_name");

        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        if(!workflowSource.exists()){
            ret.put("errcode","1000");
            ret.put("errmsg","fileTransfer.wdl未找到");
            return ret;
        }
        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer3.json");

        JSONObject obj = new JSONObject();
        obj.put("fileTransfer.ACTION_FT","create");
        obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name+"/sv");
        obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");

        JSONArray array = new JSONArray();
        File filesForUpload = new File("/mydata/shca/projects/"+project_name+"/sv");
        if(filesForUpload.exists()){
            File[] files = filesForUpload.listFiles();
            for(int i=0;i<files.length;i++){
                File file = files[i];
                if(file.isDirectory()){
                    File[] files2 = file.listFiles();
                    for(int j=0;j<files2.length;j++){
                        File file2 = files2[j];
                        if(file2.isFile()){
                            array.add(file2.getAbsolutePath());
                        }
                    }
                } else if(file.isFile()) {
                    array.add(file.getAbsolutePath());
                } else {

                }

            }
        } else {
            String sql2 = "update shca_project_project set cromwell_workflow_id_2_3 = cromwell_workflow_id_2_2 where id = ?  ";
            db.update(sql2, id);

            ret.put("errcode","0");
            ret.put("errmsg","没有找到sv");
            return ret;
        }
        obj.put("fileTransfer.fileList_FT",array);

        WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

        WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));

        String sql2 = "update shca_project_project set cromwell_workflow_id_2_3 = ? where id = ?  ";
        db.update(sql2,response.getId() , id);

        ret.put("errcode","0");
        ret.put("workflowId",response.getId());
        return ret;
    }

    public Map delete(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql,id);
        String project_name = (String) map.get("project_name");

        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer.json");

        File dir = new File("/CloudSvr01/analysisSourceFiles/"+project_name);
        if(dir.exists()){
            JSONObject obj = new JSONObject();
            obj.put("fileTransfer.ACTION_FT","remove");
            obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name);
            obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");
            JSONArray array = new JSONArray();
            obj.put("fileTransfer.fileList_FT",array);

            WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));


            String sql2 = "update shca_project_project set cromwell_workflow_id_2 = ?,cromwell_workflow_type_2 = ? where id = ?  ";
            db.update(sql2,response.getId(),"remove",id);
            ret.put("workflowId",response.getId());
        } else {
            String sql2 = "update shca_project_project set cromwell_workflow_id_2 = null,cromwell_workflow_type_2 = ?,cromwell_workflow_status_2=? where id = ?  ";
            db.update(sql2,"remove","未找到相应目录",id);
        }
        ret.put("errcode","0");
        return ret;
    }

    public void createTask() {
        Calendar now = Calendar.getInstance();
        now.add(Calendar.DAY_OF_MONTH,-90);
        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        now.set(Calendar.SECOND,0);

        create(now.getTime());
    }

    public void createTask1() {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_MONTH,1);
        now.set(Calendar.YEAR,2021);
        now.set(Calendar.MONTH,4);

        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        now.set(Calendar.SECOND,0);


        create(now.getTime());
    }

    public void createTask2() {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_MONTH,1);
        now.set(Calendar.YEAR,2021);
        now.set(Calendar.MONTH,4);

        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        now.set(Calendar.SECOND,0);

        create2(now.getTime());
    }

    public void createTask3() {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_MONTH,1);
        now.set(Calendar.YEAR,2021);
        now.set(Calendar.MONTH,4);

        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        now.set(Calendar.SECOND,0);

        create3(now.getTime());
    }

    public Map create(@MethodParam(name="now")Date now) {
        LOGGER.info("FileService:create1 start" );
        String sql = "select count(0) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY where WORKFLOW_STATUS = ?";

        Long cnt = db.selectUnique(sql, new LongCallback(),"Running");
        if(cnt==0) {
            List<Map<String, Object>> ids = db.select(
                    "select id,project_name from v_shca_project_file where create_time >=? and  workflow_id is null and upload_result LIKE '{\"succ\":true%' order by id desc limit 1", now);

            for (Map<String, Object> map : ids) {
                Long id = (Long) map.get("id");
                Map<String, Object> ret = create(id);
                LOGGER.info(id + ": " + ret);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",0);
        ret.put("errmsg","order by id asc limit 1 执行成功");
        return ret;
    }


    public Map create2(@MethodParam(name="now")Date now) {
        LOGGER.info("FileService:create2 start" );
        /*String sql = "select count(0) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY where WORKFLOW_STATUS = ?";

        Long cnt = db.selectUnique(sql, new LongCallback(),"Running");
        if(cnt==0) {*/
            List<Map<String, Object>> ids = db.select(
                    "select id,project_name from v_shca_project_file where create_time >=? and workflow_type = ? and workflow_status = ?  and workflow_id2 is null order by id desc limit 1", now,"create","Succeeded");

            for (Map<String, Object> map : ids) {
                Long id = (Long) map.get("id");
                Map<String, Object> ret = create2(id);
                LOGGER.info(id + ": " + ret);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        /*}*/
        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",0);
        ret.put("errmsg","create2:order by id asc limit 1 执行成功");
        return ret;
    }

    public Map create3(@MethodParam(name="now")Date now) {
        LOGGER.info("FileService:create3 start" );
            List<Map<String, Object>> ids = db.select(
                    "select id,project_name from v_shca_project_file where create_time >=? and workflow_type = ? and  workflow_status2 = ? and  workflow_id3 is null order by id desc limit 1", now,"create","Succeeded");

            for (Map<String, Object> map : ids) {
                Long id = (Long) map.get("id");
                Map<String, Object> ret = create3(id);
                LOGGER.info(id + ": " + ret);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",0);
        ret.put("errmsg","create3:order by id asc limit 1 执行成功");
        return ret;
    }


    public void deleteTask() {

        Calendar now = Calendar.getInstance();
        now.add(Calendar.DAY_OF_MONTH,-90);
        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        now.set(Calendar.SECOND,0);

        /**
         * upload_time 超过三个月的，create成功的记录
         */

        String sql = "select count(0) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY where WORKFLOW_STATUS = ?";

        Long cnt = db.selectUnique(sql, new LongCallback(),"Running");

        if(cnt==0) {
            List<Map<String, Object>> ids = db.select(
                    "select id,project_name from v_shca_project_file where create_time < ? and workflow_id is not null and workflow_type = ? and workflow_status = ? ",  now, "create", "Succeeded");

            for (Map<String, Object> map : ids) {
                Long id = (Long) map.get("id");
                Map<String, Object> ret = delete(id);
                LOGGER.info(id + ": " + ret);
            }
        }
    }

    private void WriteStringToFile(String filePath,String str) {
        try {
            File file = new File(filePath);
            PrintStream ps = new PrintStream(new FileOutputStream(file));
            ps.println(str);// 往文件里写入字符串
            //ps.append(str);// 在已有的基础上添加字符串
        } catch (FileNotFoundException e) {

            e.printStackTrace();
        }
    }

}
