package com.shca.file.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.LongCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.util.*;


public class FileService {

    private final static Logger LOGGER = Logger.getLogger( FileService.class);

    @Autowired
    private Db db;

    private CromwellClient client;

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    // 根据id创建备份map
    public Map create(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "SELECT id, project_name, sample_name_0, sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql, id);
        String sample_name_0 = (String) map.get("sample_name_0");
        String sample_name_1 = (String) map.get("sample_name_1");
        String project_name = (String) map.get("project_name");

        // 重构成getWdlFile方法
        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        if(!workflowSource.exists()){
            ret.put("errcode","1000");
            ret.put("errmsg","fileTransfer.wdl未找到");
            return ret;
        }

        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer3.json");

        JSONObject obj = new JSONObject();
        obj.put("fileTransfer.ACTION_FT","create");
        obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name+"/sv");
        obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");

        JSONArray array = new JSONArray();
        File filesForUpload = new File("/mydata/shca/projects/"+project_name+"/sv");
        if(filesForUpload.exists()){
            File[] files = filesForUpload.listFiles();
            for(int i=0;i<files.length;i++){
                File file = files[i];
                if(file.isDirectory()){
                    File[] files2 = file.listFiles();
                    for(int j=0;j<files2.length;j++){
                        File file2 = files2[j];
                        if(file2.isFile()){
                            array.add(file2.getAbsolutePath());
                        }
                    }
                } else if(file.isFile()) {
                    array.add(file.getAbsolutePath());
                } else {

                }

            }
        } else {
            String sql2 = "update shca_project_project set cromwell_workflow_id_2_3 = cromwell_workflow_id_2_2 where id = ?  ";
            db.update(sql2, id);

            ret.put("errcode","0");
            ret.put("errmsg","没有找到sv");
            return ret;
        }
        obj.put("fileTransfer.fileList_FT",array);

        WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

        WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));

        String sql2 = "update shca_project_project set cromwell_workflow_id_2_3 = ? where id = ?  ";
        db.update(sql2,response.getId() , id);

        ret.put("errcode","0");
        ret.put("workflowId",response.getId());
        return ret;
    }

    public Map<String, Object> delete(@MethodParam(name="id")Long id) {
        Map<String,Object> ret = new HashMap<>();

        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where id = ? ";
        Map<String,Object> map = db.selectUnique(sql,id);
        String project_name = (String) map.get("project_name");

        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        File prettyJson = new File("/mydata/geneAnalysisPipeline/fileTransfer.json");

        File dir = new File("/CloudSvr01/analysisSourceFiles/"+project_name);
        if(dir.exists()){
            JSONObject obj = new JSONObject();
            obj.put("fileTransfer.ACTION_FT","remove");
            obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/"+project_name);
            obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");
            JSONArray array = new JSONArray();
            obj.put("fileTransfer.fileList_FT",array);

            WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));


            String sql2 = "update shca_project_project set cromwell_workflow_id_2 = ?,cromwell_workflow_type_2 = ? where id = ?  ";
            db.update(sql2,response.getId(),"remove",id);
            ret.put("workflowId",response.getId());
        } else {
            String sql2 = "update shca_project_project set cromwell_workflow_id_2 = null,cromwell_workflow_type_2 = ?,cromwell_workflow_status_2=? where id = ?  ";
            db.update(sql2,"remove","未找到相应目录",id);
        }
        ret.put("errcode","0");
        return ret;
    }

//调整
    private void WriteStringToFile(String filePath,String str) {
        try {
            File file = new File(filePath);
            PrintStream ps = new PrintStream(new FileOutputStream(file));
            ps.println(str);// 往文件里写入字符串
            //ps.append(str);// 在已有的基础上添加字符串
            ps.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

}
