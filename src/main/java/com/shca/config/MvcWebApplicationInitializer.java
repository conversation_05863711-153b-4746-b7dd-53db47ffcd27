/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-09 21:45:31
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 20:44:15
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/MyWebApplicationInitializer.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// package com.shca.config;

// import javax.servlet.Filter;

// import org.springframework.web.filter.HiddenHttpMethodFilter;
// import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;


// public class MvcWebApplicationInitializer extends AbstractAnnotationConfigDispatcherServletInitializer {

// 	@Override
// 	protected Class<?>[] getRootConfigClasses() {
// 		return null;
// 	}

// 	@Override
// 	protected Class<?>[] getServletConfigClasses() {
// 		return new Class[] { ApplicationConfiguration.class };
// 	}

// 	@Override
// 	protected String[] getServletMappings() {
// 		return new String[] { "/" };
// 	}

// 	@Override
// 	protected Filter[] getServletFilters() {
// 		return new Filter[] { new HiddenHttpMethodFilter() };
// 	}

// }