package com.shca.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * Scheduling Configuration
 * 
 * Replaces the Quartz-based scheduling from XML configuration
 * with Spring Boot's @Scheduled annotation
 */
@Configuration
public class SchedulingConfiguration {

    /**
     * Scheduled Tasks Component
     */
    @Component
    public static class ScheduledTasks {

        // @Autowired
        // private CromwellService3 cromwellService; // Disabled during migration

        // @Autowired
        // private ReportService reportService; // Disabled during migration

        /**
         * Cromwell service task - runs every 2 minutes
         * Replaces: cronExpression="0 0/2 * * * ?"
         * Note: Disabled during migration due to Db dependency
         */
        /*
        @Scheduled(fixedRate = 120000) // 2 minutes in milliseconds
        public void cromwellServiceTask() {
            try {
                cromwellService.task();
            } catch (Exception e) {
                // Log error but don't stop the scheduler
                System.err.println("Error in cromwell service task: " + e.getMessage());
                e.printStackTrace();
            }
        }
        */

        /**
         * FTP monitor service task - runs every 1 minute
         * Replaces: cronExpression="0 0/1 * * * ?"
         * 
         * Note: FTP monitor is commented out in original XML, 
         * so this is also commented out
         */
        /*
        @Scheduled(fixedRate = 60000) // 1 minute in milliseconds
        public void ftpServiceTask() {
            try {
                // ftpMonitor.sync();
            } catch (Exception e) {
                System.err.println("Error in FTP service task: " + e.getMessage());
                e.printStackTrace();
            }
        }
        */

        /**
         * LIMS service task - runs every 10 minutes
         * Replaces: cronExpression="0 0/10 * * * ?"
         * Note: LimsService2 is not available, task disabled
         */
        /*
        @Scheduled(fixedRate = 600000) // 10 minutes in milliseconds
        public void limsServiceTask() {
            try {
                // limsService.sync(); // TODO: Implement when LimsService2 is available
            } catch (Exception e) {
                System.err.println("Error in LIMS service task: " + e.getMessage());
                e.printStackTrace();
            }
        }
        */

        /**
         * Report service task - runs every 5 minutes
         * Replaces: cronExpression="0 0/5 * * * ?"
         * Note: Disabled during migration due to Db dependency
         */
        /*
        @Scheduled(fixedRate = 300000) // 5 minutes in milliseconds
        public void reportServiceTask() {
            try {
                reportService.sync();
            } catch (Exception e) {
                System.err.println("Error in report service task: " + e.getMessage());
                e.printStackTrace();
            }
        }
        */
    }
}
