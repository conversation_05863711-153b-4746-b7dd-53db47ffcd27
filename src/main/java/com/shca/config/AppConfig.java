/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-16 23:00:01
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2025-01-02 18:32:26
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/AppConfig.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.config;

import java.util.regex.Pattern;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.service.CromwellService;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.impl.NormalProcessServiceImpl;
import com.shca.module.cancer.service.impl.PeixiProcessServiceImpl;
import com.shca.module.ftp.service.FtpMonitorByLog;
import com.ywang.sql.support.Db;

@Configuration
@ComponentScan(basePackages = "com.shca")
// @ComponentScan(basePackages = "com.shca.config")
public class AppConfig {

    @PostConstruct
    public void init() {
        System.out.println("app config initialized");
    }

    @Bean
    public RestTemplate getRestTemplate() {
        System.out.println("getRestTemplate");
        return new RestTemplate(getClientHttpRequestFactory());
    }

    private ClientHttpRequestFactory getClientHttpRequestFactory() {
        int timeout = 5000;
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setConnectTimeout(timeout);
        return clientHttpRequestFactory;
    }

    // @Bean
    // public ObjectMapper getMapper() {
    //     System.out.println("getMapper");
    //     return new ObjectMapper();
    // }

    // private ClientHttpRequestFactory getClientHttpRequestFactory() {
    // int timeout = 5000;
    // RequestConfig config = RequestConfig.custom()
    // .setConnectTimeout(timeout)
    // .setConnectionRequestTimeout(timeout)
    // .setSocketTimeout(timeout)
    // .build();
    // CloseableHttpClient client = HttpClientBuilder
    // .create()
    // .setDefaultRequestConfig(config)
    // .build();
    // return new HttpComponentsClientHttpRequestFactory(client);
    // }

    // @Bean
    public ProcessService normalProcessService() {
        return new NormalProcessServiceImpl();
    }

    // @Bean
    public ProcessService peixiProcessService() {
        return new PeixiProcessServiceImpl();
    }

    // @Bean
    public CromwellService getCromwellService() {
        return new CromwellService();
    }

    // @Bean
    // public DataSourceTransactionManager getTransactionManager(DataSource
    // datasource) {
    // return new DataSourceTransactionManager(datasource);
    // }

    // @Bean
    public CromwellClient cromwellClient() {
        return new CromwellClient("http://***************:8899", "v1");
    }

    @Bean
    public FtpMonitorByLog getFtpMonitorByLog(@Autowired Db db) {
        Pattern sampleName_Pattern = Pattern.compile(
                "(AJ|BJ|B|BZ|C|CZ|G|GZ|H|HZ|P|PC|OH|PZ|GI|UT|LA|LB|LC|CJ|HJ|GJ|IJ|PJ|DJ|AZ|DZ|NZ|NJ|SZ|SJ|PM|BR|BA|BH|CR|CH|GH|IH|HH|AH|PR|BC|FZ|LC|LF)[0-9]{7}(B|K|S|F|C)[0-9]{2}");
        Pattern R_Pattern = Pattern.compile("(R(1|2))\\.");
        Pattern NOVA_Pattern = Pattern.compile("[0-9]+");
        FtpMonitorByLog result = new FtpMonitorByLog(sampleName_Pattern, R_Pattern, NOVA_Pattern);
        result.setDb(db);
        result.setThreshold(36000);
        return result;
    }

}