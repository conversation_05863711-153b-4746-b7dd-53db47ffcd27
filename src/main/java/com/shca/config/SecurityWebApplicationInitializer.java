/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-15 12:52:57
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 15:33:15
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/SecurityConfigInitializer.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

package com.shca.config;
import org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer;
import org.springframework.security.web.session.HttpSessionEventPublisher;

/**
 * We customize {@link AbstractSecurityWebApplicationInitializer} to enable the
 * {@link HttpSessionEventPublisher}.
 *
 * <AUTHOR> <PERSON>ch
 */
public class SecurityWebApplicationInitializer extends AbstractSecurityWebApplicationInitializer {

	@Override
	protected boolean enableHttpSessionEventPublisher() {
		return true;
	}

}

