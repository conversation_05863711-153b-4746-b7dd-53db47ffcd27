/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-16 23:00:01
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2025-01-02 18:22:55
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/AppConfig.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shca.cromwell.CromwellClient;

@Configuration
public class EnvironConfig {

    @Bean
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }

    @Bean
    public ObjectMapper getMapper() {
        return new ObjectMapper();
    }

    @Bean
    public CromwellClient cromwellClient() {
        return new CromwellClient("http://***************:8899", "v1");
    }

    /**
     * FtpMonitorByLog bean - temporarily disabled during migration
     * This service requires extensive refactoring of FtpLogSupport class
     * to work with the new DbService instead of legacy Db class
     */
    /*
    @Bean
    public FtpMonitorByLog getFtpMonitorByLog() {
        Pattern sampleName_Pattern = Pattern.compile(
                "(AJ|BJ|B|BZ|C|CZ|G|GZ|H|HZ|P|PC|OH|PZ|GI|UT|LA|LB|LC|CJ|HJ|GJ|IJ|PJ|DJ|AZ|DZ|NZ|NJ|SZ|SJ|PM|BR|BA|BH|CR|CH|GH|IH|HH|AH|PR)[0-9]{7}(B|K|S|F|C)[0-9]{2}");
        Pattern R_Pattern = Pattern.compile("(R(1|2))\\.");
        Pattern NOVA_Pattern = Pattern.compile("[0-9]+");
        FtpMonitorByLog result = new FtpMonitorByLog(sampleName_Pattern, R_Pattern, NOVA_Pattern);
        // result.setDb(db);
        result.setThreshold(36000);
        return result;
    }
    */

}