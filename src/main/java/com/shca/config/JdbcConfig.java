/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 16:16:01
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-09 23:48:04
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/JdbcConfig.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import com.shca.support.DbSysConfigManager;
import com.shca.support.SysConfigManager;
import com.shca.util.JdbcTemplateEnhance;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.TranscationStatememt;
import com.ywang.sql.support.adapter.MySQLPageStatement;

@Configuration
// @ComponentScan(basePackages = "com.shca.config")
public class JdbcConfig {
    @Bean
    public SysConfigManager getConfig() {
        new DbSysConfigManager(jdbcTemplate(dataSource()));
        return DbSysConfigManager.getInstance();
        // return SysConfigManager.getInstance();
    }

    // @Bean
    // public com.ywang.module.sys.service.SysConfigManager getOldConfig(@Autowired Db db) {
    //     new com.ywang.module.sys.service.support.DbSysConfigManager(db);
    //     return com.ywang.module.sys.service.support.DbSysConfigManager.getInstance();

    // }

    @Bean
    public DataSource dataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl("**********************************************************************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        // dataSource.setPassword("password");
        return dataSource;
    }

    @Bean("jdbcTemplate")
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        System.out.println("hello jdbcTemplate bean created.");
        // return new JdbcTemplate(dataSource);
        return new JdbcTemplateEnhance(dataSource);
    }

    // @SuppressWarnings("")
    @Bean
    public DataSourceTransactionManager getDataSourceTransactionManager(@Autowired DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public TranscationStatememt getTransactionStmt(@Autowired DataSource dataSource) {
        return new TranscationStatememt(dataSource);
    }

    @Bean
    public MySQLPageStatement getPageStmt(@Autowired TranscationStatememt transactionStmt) {
        return new MySQLPageStatement(transactionStmt);
    }

    @Bean
    @Qualifier("db")
    public Db getDb(@Autowired MySQLPageStatement pageStmt) {
        return new Db(pageStmt);
    }
}
