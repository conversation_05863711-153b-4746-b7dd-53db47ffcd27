package com.shca.config;

import javax.sql.DataSource;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.shca.support.DbSysConfigManager;
import com.shca.support.SysConfigManager;
import com.shca.util.JdbcTemplateEnhance;

/**
 * Database Configuration
 *
 * Spring Boot will auto-configure the DataSource using Druid starter
 * This class provides additional beans that depend on the DataSource
 */
@Configuration
@EnableTransactionManagement
public class JdbcConfig {

    /**
     * System configuration manager
     */
    @Bean
    public SysConfigManager dbSysConfigManager() {
        return new DbSysConfigManager();
    }

    /**
     * Enhanced JdbcTemplate with additional utility methods
     * Spring Boot will auto-configure the DataSource, so we don't need to define it manually
     */
    @Bean
    @ConditionalOnMissingBean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplateEnhance(dataSource);
    }

    /**
     * JdbcTemplateEnhance bean for controllers that need the enhanced functionality
     */
    @Bean
    public JdbcTemplateEnhance jdbcTemplateEnhance(DataSource dataSource) {
        return new JdbcTemplateEnhance(dataSource);
    }
}
