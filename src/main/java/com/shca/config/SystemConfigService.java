package com.shca.config;

import com.shca.util.DbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Modern Spring Boot replacement for SysConfigManager
 * Loads configuration from database table comm_sys_config
 */
@Service
public class SystemConfigService {

    @Autowired
    private DbService dbService;

    private final Map<String, Map<String, Object>> configs = new ConcurrentHashMap<>();

    @PostConstruct
    public void refresh() {
        this.configs.clear();

        List<Map<String, Object>> configList = dbService.select(
            "SELECT id, config_name AS name, config_value AS value, group_name AS group_name FROM comm_sys_config ORDER BY sort"
        );

        if (configList.isEmpty()) {
            System.out.println("No config found in database");
        }

        // Convert list to map with config_name as key
        for (Map<String, Object> config : configList) {
            String name = (String) config.get("name");
            if (name != null) {
                this.configs.put(name, config);
            }
        }

        // Set OS name parameter
        setValue("param.os_name", System.getProperty("os.name"));
    }

    public String getValue(String key) {
        return getValue(key, null);
    }

    public String getValue(String key, String defValue) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }

        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return defValue;
        }

        Object value = config.get("value");
        if (value == null) {
            return defValue;
        }

        return value.toString();
    }

    public void setValue(String key, String value) {
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return;
        }

        dbService.update(
            "UPDATE comm_sys_config SET config_value=?, update_time=? WHERE id=?",
            value, new Date(), config.get("id")
        );

        config.put("value", value);
    }

    public Map<String, Object> getProperty(String key) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }

        Map<String, Object> config = configs.get(key);
        return config != null ? Collections.unmodifiableMap(config) : null;
    }

    public List<Map<String, Object>> getProperties(String group) {
        if (!StringUtils.hasLength(group)) {
            throw new IllegalArgumentException("group cannot be null or empty");
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> config : this.configs.values()) {
            if (group.equals(config.get("group_name"))) {
                result.add(config);
            }
        }

        return Collections.unmodifiableList(result);
    }

    public Set<String> getAllKeys() {
        return Collections.unmodifiableSet(configs.keySet());
    }

    public boolean hasKey(String key) {
        return configs.containsKey(key);
    }
}