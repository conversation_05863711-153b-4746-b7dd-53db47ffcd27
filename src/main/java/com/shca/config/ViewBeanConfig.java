/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-15 16:26:32
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 16:22:28
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/ShcaConfig.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.config;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.shca.cromwell.CromwellClient;
import com.shca.module.ftp.view.FtpFileView;
import com.shca.module.project.view.CancerFileView;
import com.shca.module.project.view.CancerView;
import com.shca.module.project.view.ProjectCompareView;
import com.shca.module.project.view.ProjectReportView;
import com.shca.module.project.view.ProjectSuitabilityView;
import com.shca.module.project.view.ProjectUploadView;
import com.shca.module.project.view.ProjectView;
import com.shca.module.project.view.TestView;
import com.shca.module.system.view.ConfigView;
import com.shca.module.system.view.CromwellView;
import com.shca.view.ShcaIndexView;

@Configuration
public class ViewBeanConfig {

    @Resource ApplicationContext context;
    @Resource @Qualifier("cromwellClient") CromwellClient client;

    @Bean("view-index")
    public ShcaIndexView shcaIndexView() {
        return new ShcaIndexView();
    }

    @Bean("view-system.cromwell")
    public CromwellView getCromwellView() {
        return new CromwellView();
    }

    @Bean("view-project.cancer")
    public CancerView getCancerView() {
        return new CancerView();
    }

    @Bean("view-project.cancerfile")
    public CancerFileView getCancerFileView() {
        return new CancerFileView();
    }

    @Bean("view-project.project")
    public ProjectView getProjectView() {
        return new ProjectView();
    }

    @Bean("view-project.suitability")
    public ProjectSuitabilityView projectSuitabilityView() {
        ProjectSuitabilityView view = new
            ProjectSuitabilityView("/myTools/geneAnalysisPipeline/CreateBatchBvskSuitability.wdl");
        return view;
    }

    @Bean("view-project.compare")
    public ProjectCompareView projectCompareView() {
        return new ProjectCompareView();
    }

    @Bean("view-project.report")
    public ProjectReportView projectReportView() {
        return new ProjectReportView();
    }

    @Bean("view-project.upload")
    public ProjectUploadView projectUploadView() {
        return new ProjectUploadView();
    }

    @Bean
    public TestView testView() {
        TestView view = new TestView();
        return view;
    }

    @Bean("view-system.config")
    public ConfigView configView() {
        return new ConfigView();
    }

    @Bean("view-ftp.file")
    public FtpFileView ftpFileView() {
        FtpFileView view = new FtpFileView();
        return view;
    }
}
