package com.shca.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableScheduling;

import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Main Application Configuration
 * Replaces the XML-based Spring configuration
 */
@Configuration
@EnableScheduling
public class ApplicationConfiguration implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/", "file:web/");
        registry.addResourceHandler("/plugin/**")
                .addResourceLocations("file:web/plugin/");
        registry.addResourceHandler("/shca/**")
                .addResourceLocations("file:web/shca/");
    }

    /**
     * Custom application properties configuration
     */
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "app")
    public AppProperties appProperties() {
        return new AppProperties();
    }

    /**
     * Application properties holder
     */
    public static class AppProperties {
        private CromwellProperties cromwell = new CromwellProperties();
        private LimsProperties lims = new LimsProperties();
        private ReportProperties report = new ReportProperties();

        // Getters and setters
        public CromwellProperties getCromwell() { return cromwell; }
        public void setCromwell(CromwellProperties cromwell) { this.cromwell = cromwell; }
        public LimsProperties getLims() { return lims; }
        public void setLims(LimsProperties lims) { this.lims = lims; }
        public ReportProperties getReport() { return report; }
        public void setReport(ReportProperties report) { this.report = report; }

        public static class CromwellProperties {
            private String url = "http://0.0.0.0:8899";
            private String version = "v1";

            public String getUrl() { return url; }
            public void setUrl(String url) { this.url = url; }
            public String getVersion() { return version; }
            public void setVersion(String version) { this.version = version; }
        }

        public static class LimsProperties {
            private String projectListUrl;
            private String uploadQcSummaryUrl;

            public String getProjectListUrl() { return projectListUrl; }
            public void setProjectListUrl(String projectListUrl) { this.projectListUrl = projectListUrl; }
            public String getUploadQcSummaryUrl() { return uploadQcSummaryUrl; }
            public void setUploadQcSummaryUrl(String uploadQcSummaryUrl) { this.uploadQcSummaryUrl = uploadQcSummaryUrl; }
        }

        public static class ReportProperties {
            private String queryUrl;
            private String uploadUrl;

            public String getQueryUrl() { return queryUrl; }
            public void setQueryUrl(String queryUrl) { this.queryUrl = queryUrl; }
            public String getUploadUrl() { return uploadUrl; }
            public void setUploadUrl(String uploadUrl) { this.uploadUrl = uploadUrl; }
        }
    }
}
