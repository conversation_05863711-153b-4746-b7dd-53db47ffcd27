/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-17 16:29:09
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-18 11:26:02
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/ApplicationConfiguration.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackages = "com.shca")
// @ImportResource("classpath:shca.xml")
public class ApplicationConfiguration {
}
