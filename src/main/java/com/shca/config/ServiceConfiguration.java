package com.shca.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.service.CromwellService3;
import com.shca.file.service.FileService;
import com.shca.report.service.ReportService;
import com.shca.util.JdbcTemplateEnhance;

import javax.sql.DataSource;

/**
 * Service Configuration
 * 
 * Replaces the service bean definitions from XML configuration
 */
@Configuration
public class ServiceConfiguration {

    @Autowired
    private ApplicationConfiguration.AppProperties appProperties;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplateEnhance jdbcTemplate;

    /**
     * Cromwell Client Configuration
     */
    @Bean
    public CromwellClient cromwellClient() {
        return new CromwellClient(
            appProperties.getCromwell().getUrl(),
            appProperties.getCromwell().getVersion()
        );
    }

    /**
     * Cromwell Service Configuration
     * Note: Service configuration will be handled by component scanning
     * The services will be auto-configured as Spring components
     */
    // Services will be auto-discovered through @Service annotation
    // and configured through @Autowired dependencies
}
