package com.shca.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.shca.cromwell.CromwellClient;

/**
 * Service Configuration
 * 
 * Replaces the service bean definitions from XML configuration
 */
@Configuration
public class ServiceConfiguration {

    @Autowired
    private ApplicationConfiguration.AppProperties appProperties;

    // CromwellClient bean is already defined in EnvironConfig

    /**
     * Cromwell Service Configuration
     * Note: Service configuration will be handled by component scanning
     * The services will be auto-configured as Spring components
     */
    // Services will be auto-discovered through @Service annotation
    // and configured through @Autowired dependencies
}
