/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-10 13:56:01
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 16:24:58
 * @FilePath: /analysis_engine/src/main/java/com/shca/config/SpringWebInitializer.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// /*
//  * @Author: sneaker <EMAIL>
//  * @Date: 2024-11-10 13:56:01
//  * @LastEditors: sneaker <EMAIL>
//  * @LastEditTime: 2024-11-17 15:40:51
//  * @FilePath: /analysis_engine/src/main/java/com/shca/config/SpringWebInitializer.java
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
package com.shca.config;


import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.AnnotationConfigWebApplicationContext;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.HiddenHttpMethodFilter;
import org.springframework.web.servlet.support.AbstractDispatcherServletInitializer;

public class SpringWebInitializer extends AbstractDispatcherServletInitializer {

    @Override
    protected String[] getServletMappings() {
        return new String[] { "/" };
    }

    @Override
    protected WebApplicationContext createServletApplicationContext() {
		AnnotationConfigWebApplicationContext context = new AnnotationConfigWebApplicationContext();
		context.register(ApplicationConfiguration.class);
        return context;
    }

    @Override
    protected WebApplicationContext createRootApplicationContext() {
        return null;
    }

    @Override
    protected jakarta.servlet.Filter[] getServletFilters() {
        // Legacy filters disabled during migration - Spring Boot handles these
        // DelegatingFilterProxy rbacDelegatingFilter = new DelegatingFilterProxy();
        // rbacDelegatingFilter.setTargetBeanName("rbacFilter");
        // DelegatingFilterProxy controllerDelegatingFilter = new DelegatingFilterProxy();
        // controllerDelegatingFilter.setTargetBeanName("controllerFilter");

        return new jakarta.servlet.Filter[] {
			new CharacterEncodingFilter(),
            // rbacDelegatingFilter, // Disabled - will be replaced with Spring Security
            // controllerDelegatingFilter, // Disabled
            new HiddenHttpMethodFilter()};
    }
}
