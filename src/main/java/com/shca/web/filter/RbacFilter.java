package com.shca.web.filter;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import com.ywang.http.SessionUser;
import com.ywang.module.rbac.dao.PrivilegeCallback;
import com.ywang.module.rbac.vo.AbstractPrivilege;
import com.ywang.module.rbac.vo.NoUserPrivilege;
import com.ywang.sql.support.Db;

@Component("rbacFilter")
public class RbacFilter extends GenericFilterBean {
    private final static Logger log = LoggerFactory.getLogger(RbacFilter.class);
    // private final static String SIMPLE_MODE_PARAM_NAME = "simple_mode";
    private final String actionType = ".htm";
    private static AbstractPrivilege privilegeChain;
    private static final String JSON_SUFFIX = ".json";
    private static final String SESSION_USER_KEY = "__sessionUser";
    @Resource private Db db;
    // @Resource private JdbcTemplate jdbcTemplate;
    private boolean simpleMode = true;

    @PostConstruct
    @SuppressWarnings("unused")
    private void getInitPrivileges() {
        log.info("RbacFilter getting privileges...");
        if (db == null) {
            log.error("ERROR -------------> RbacFilter Error: db is null.");
        }
        String privilegeSql = "SELECT url, has_login, role_code, type " +
        " FROM comm_rbac_privilege ORDER BY sort ";
        try {
            List<AbstractPrivilege> privileges = db.select(privilegeSql, new PrivilegeCallback());
            AbstractPrivilege father = null;
            for (AbstractPrivilege privilege : privileges) {
                if (father == null) {
                    privilegeChain = privilege;
                } else {
                    father.setPrivilegeChain(privilege);
                }
                father = privilege;
            }
            if (father != null) {
                father.setPrivilegeChain(new NoUserPrivilege());
            }
            log.info("RbacFilter getting privileges Completed.");
        } catch(Exception e){
            log.error("RbacFilter Error: ", e);
        }
    }

    @Override
    public void initFilterBean() throws ServletException {
    // public void init(FilterConfig config) throws ServletException {
            log.info("RbacFilter init...");
            // String simpleMode = config.getInitParameter(SIMPLE_MODE_PARAM_NAME);
            // if (StringUtils.hasLength(simpleMode)) {
            //     this.simpleMode = ConvertUtils.bool(simpleMode);
            // }
            this.simpleMode = true;
            log.info("RbacFilter simpleMode: " + this.simpleMode);
            // db = Context.getBean(Db.class, Constant.DEFAULT_DB_BEAN_NAME);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;

        String path = req.getServletPath();
        String url = "";
        if (path.startsWith("/")) {
            url = path.substring(1);
        }

        if (!path.endsWith(actionType) && !path.endsWith(JSON_SUFFIX) && !"/".equals(path)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            SessionUser user = (SessionUser) req.getSession().getAttribute(SESSION_USER_KEY);
            if (url.endsWith(actionType)) {
                url = url.substring(0, url.length() - actionType.length());
            }
            if (url.endsWith(JSON_SUFFIX)) {
                url = url.substring(0, url.length() - JSON_SUFFIX.length());
            }

            Map<String, Object> ret = new HashMap<>();
            boolean hasPrivilege = privilegeChain.hasPrivilege(url, user, ret);
            log.info("权限认证[" + path + "][" + user + "]: " + hasPrivilege);
            if (hasPrivilege) {
                chain.doFilter(request, response);
                return;
            }

            if (path.endsWith(JSON_SUFFIX)) {
                request.setAttribute("json", "{message:'权限不足'}");
                request.getRequestDispatcher("/comm/json.json.jsp").forward(request, response);
                return;
            }

            request.getRequestDispatcher("/shca/login.jsp").forward(request, response);

        } catch (IOException | ServletException e) {
            log.error(e.getMessage(), e);
        }
    }

}