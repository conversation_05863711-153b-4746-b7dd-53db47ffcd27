package com.shca.web.filter;

import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.Context;
import com.ywang.http.SessionUser;
import com.ywang.module.rbac.dao.PrivilegeCallback;
import com.ywang.module.rbac.vo.AbstractPrivilege;
import com.ywang.module.rbac.vo.NoUserPrivilege;
import com.ywang.sql.support.Db;
import com.ywang.utils.ConvertUtils;
import com.ywang.utils.LogUtils;
import com.ywang.utils.StringUtils;
import org.apache.log4j.Logger;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class RbacFilter implements Filter {

    private final static Logger LOGGER = Logger.getLogger( RbacFilter.class);

    private final static String SIMPLE_MODE_PARAM_NAME = "simple_mode";

    private AbstractPrivilege privilegeChain;

    private Db db;

    private boolean simpleMode = true;

    public void init(FilterConfig config) throws ServletException {
        try {
            LOGGER.info("RbacFilter init...");

            String simpleMode = config.getInitParameter(SIMPLE_MODE_PARAM_NAME);
            if (StringUtils.hasLength(simpleMode)) {
                this.simpleMode = ConvertUtils.bool(simpleMode);
            }


            db = Context.getBean(Db.class, Constant.DEFAULT_DB_BEAN_NAME);

            List<AbstractPrivilege> privileges =
                    db.select( "select url,has_login,role_code,type " +
                            " from comm_rbac_privilege a order by a.sort ", new PrivilegeCallback());
            AbstractPrivilege father = null;
            for (AbstractPrivilege privilege : privileges) {
                if (father == null) {
                    privilegeChain = privilege;
                } else {
                    father.setPrivilegeChain(privilege);
                }
                father = privilege;
            }
            father.setPrivilegeChain( new NoUserPrivilege());

            LOGGER.info( "RbacFilter Completed.");
        } catch (Exception e) {
            LOGGER.error( "RbacFilter Error: ");
            LOGGER.error( LogUtils.toString(e));
        }
    }


    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        String actionType = (String) req.getServletContext().getAttribute(Constant.ACTION_TYPE);


        String path = req.getServletPath();
        String url = "";
        if (path.startsWith("/")) {
            url = path.substring(1);
        }

        if (!path.endsWith(actionType) && !path.endsWith(Constant.JSON_SUFFIX) && !"/".equals(path)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            SessionUser user = (SessionUser) req.getSession().getAttribute(Constant.SESSION_USER_KEY);
            if (url.endsWith(actionType)) {
                url = url.substring(0, url.length() - actionType.length());
            }
            if (url.endsWith(Constant.JSON_SUFFIX)) {
                url = url.substring(0, url.length() - Constant.JSON_SUFFIX.length());
            }

            Map<String, Object> ret = new HashMap<>();
            boolean hasPrivilege = privilegeChain.hasPrivilege(url, user, ret);
            AbstractPrivilege privilege = null;


            LOGGER.info( "权限认证[" + path + "][" + user + "]: " + hasPrivilege);
            if (hasPrivilege) {
                chain.doFilter(request, response);
                return;
            }

            if (path.endsWith(Constant.JSON_SUFFIX)) {
                request.setAttribute("json", "{message:'权限不足'}");
                request.getRequestDispatcher( "/comm/json.json.jsp").forward( request, response);
                return;
            }

            request.getRequestDispatcher( "/shca/login.jsp").forward( request, response);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void destroy() {
    }

}