/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-13 00:11:42
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-14 15:34:29
 * @FilePath: /analysis_engine/src/main/java/com/shca/web/filter/FilterInjection.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.web.filter;

import javax.servlet.FilterRegistration;
import javax.servlet.ServletContext;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.Filter;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.web.context.ServletContextAware;
import org.springframework.web.filter.DelegatingFilterProxy;

// @Component
public class FilterInjection implements ApplicationListener<ContextRefreshedEvent>, ServletContextAware {

    private static ServletContext servletContext;
    // @Autowired GenericFilterBean rbacFilter;
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        System.out.println("######### Filter injection started");
        ApplicationContext applicationContext = event.getApplicationContext();
        Map<String, Filter> filterMap = applicationContext.getBeansOfType(Filter.class);
        for (Entry<String, Filter> entry : filterMap.entrySet()) {
            System.out.println("Filter name: " + entry.getKey() + " Filter value: " + entry.getValue()) ;
        }
        DelegatingFilterProxy delegateFilterProxy = (DelegatingFilterProxy) servletContext.getFilterRegistration("rbacFilter");
        delegateFilterProxy.setTargetBeanName("rbacFilter");

                // FilterRegistration.Dynamic rbacFilterRegistration = servletContext.addFilter("rbacFilter", (Filter)new RbacFilter());
                // rbacFilterRegistration.addMappingForServletNames(null, false, "dispatcher");
                // rbacFilterRegistration.addMappingForUrlPatterns(null, true, "/*");
                // Map<String, ? extends FilterRegistration> registryEntries = servletContext.getFilterRegistrations();

        for ( Entry<String, ? extends FilterRegistration> entry : servletContext.getFilterRegistrations().entrySet()) {
            System.out.println(entry.getKey() + " : " + entry.getValue());
        }
        System.out.println("####### end of filter injection");
}

    @Override
    public void setServletContext(ServletContext servletContext) {
        FilterInjection.servletContext = servletContext;
    }
}