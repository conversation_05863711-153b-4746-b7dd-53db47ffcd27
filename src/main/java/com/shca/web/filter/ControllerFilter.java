package com.shca.web.filter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shca.entity.UploadFile;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultDispatcher;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.http.support.MultipartHttpServletRequest;

// @Component("controllerFilter") // Disabled during migration - replaced by Spring Boot auto-configuration
// Entire class disabled due to servlet API compatibility issues
/*
public class ControllerFilter extends GenericFilterBean {

    private static final Logger log = LoggerFactory.getLogger(ControllerFilter.class);
    private final static String DEFAULT_ACTION_TYPE = ".htm";
    private static final String ACTION_TYPE = "action_type";
    private static final String JSON_SUFFIX = ".json";
    private final String actionType = DEFAULT_ACTION_TYPE;
    private static ServletContext servletContext;
    @Resource private ApplicationContext context;
    @Resource private ObjectMapper objectMapper;

    @Override
    public void initFilterBean() throws ServletException {
        // 1. 动态请求后缀名
        log.info("#######  SpringControllerFilter initFilterBean, actionType: " + this.actionType);
        try {
            ControllerFilter.servletContext = getServletContext();
            if (Objects.nonNull(ControllerFilter.servletContext)) {
                servletContext.setAttribute(ACTION_TYPE, this.actionType);
            }
        } catch (Exception e) { // 忽略异常
            log.info("get servletContext error");
            log.error(e.getMessage());
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        // BufferedReader reader = new BufferedReader(new
        // InputStreamReader(req.getInputStream()));
        // String s = null;
        // while((s = reader.readLine()) != null) {
        // System.out.println(s);
        // }
        String path = req.getServletPath();
        /**
         * 1. 判断动态请求连接
         * 1.1 不以actionType结尾的请求
         * 1.2 不以.json结尾的请求
         * 1.3 不以"/"请求
         *
         * 以上三种请求连接均属于静态连接
         */

        // System.out.println( req.getSession().getClass() + ": " +
        // req.getSession().getId());
        if (!path.endsWith(actionType) && !path.endsWith(JSON_SUFFIX) && !"/".equals(path) && !path.endsWith(".api")) {
            chain.doFilter(request, response);
            return;
        }
        if (path.startsWith("/service-")) {
            chain.doFilter(request, response);
            return;
        }
        // SessionUser user =
        // (SessionUser)req.getSession().getAttribute(Constant.CONTEXT_SESSIONS_KEY);
        //
        // AccountService accountService = Context.getAccountService();
        //
        // if (!accountService.hasPermission(user, path)) {
        // request.getRequestDispatcher(accountService.getLoginPath()).forward(request,
        // response);;
        // return;
        // }

        // Module comm = (Module) Context.getApplicationContext().getBean("comm");

        // 附件处理
        Map<String, List<Object>> params = new HashMap<>();
        if (ServletFileUpload.isMultipartContent(req)) {
            try {
                FileItemFactory factory = new DiskFileItemFactory();
                ServletFileUpload upload = new ServletFileUpload(factory);
                Iterator<FileItem> it = upload.parseRequest(req).iterator();
                List<String> paramNames = new ArrayList<>();
                while (it.hasNext()) {
                    FileItem item = it.next();
                    List<Object> paramValues = params.get(item.getFieldName());
                    if (CollectionUtils.isEmpty(paramValues)) {
                        paramValues = new ArrayList<>();
                        params.put(item.getFieldName(), paramValues);
                    }
                    if (!item.isFormField()) {
                        if (StringUtils.hasLength(item.getName())) {
                            paramValues.add(new UploadFile(item.getName(), item.get()));
                        }
                    } else {
                        paramNames.add(item.getFieldName());
                        paramValues.add(item.getString(req.getCharacterEncoding()));
                    }
                }
                Map<String, String[]> _params = new HashMap<>();
                for (String paramName : paramNames) {
                    _params.put(paramName, (String[]) params.get(paramName).toArray());
                }
                req = new MultipartHttpServletRequest(_params, req);
            } catch (UnsupportedEncodingException | FileUploadException e) {
                log.error(e.getMessage());
            }
        } else if (path.endsWith(".api")) {
            InputStream is = request.getInputStream();
            StringBuilder json = new StringBuilder(500);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is, "utf-8"));
            String text;
            while ((text = bufferedReader.readLine()) != null) {
                json.append(text);
            }

            // Map<String, Object> _params = JSON.parseObject( json.toString());
            Map<String, Object> _params = objectMapper
                .convertValue(json.toString(), new TypeReference<Map<String, Object>>() {});
            if (_params == null) {
                _params = new HashMap<>();
            }

            params = _params.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                        List<Object> paramValues = new ArrayList<>();
                        if (entry.getValue() instanceof Collection) {
                            paramValues.addAll((Collection<?>) entry.getValue());
                        } else {
                            paramValues.add(entry.getValue());
                        }
                        return paramValues;
                    }));

            // for (Object name : _params.keySet()) {
            //     List paramValues = new ArrayList();
            //     Object value = _params.get(name);

            //     if (value instanceof Collection) {
            //         paramValues.addAll((Collection) value);
            //     } else {
            //         paramValues.add(value);
            //     }

            //     params.put((String) name, paramValues);
            // }

        } else {
            Map<String, String[]> _params = req.getParameterMap();
            for (String name : _params.keySet()) {
                List<Object> paramValues = new ArrayList<>();
                Collections.addAll(paramValues, (String[]) _params.get(name));
                params.put((String) name, paramValues);
            }
        }

        HttpSession httpSession = req.getSession();
        Map<String, Object> session = new HashMap<>();
        for (Object attrName : Collections.list(httpSession.getAttributeNames())) {
            session.put((String) attrName, httpSession.getAttribute((String) attrName));
        }
        ViewParams<Object> viewParams = new ViewParams<>(params, session, req);

        String url = "";
        if (path.startsWith("/")) {
            url = path.substring(1);
        }
        if (url.endsWith(actionType)) {
            url = url.substring(0, url.length() - actionType.length());
        }

        req.setAttribute("__menu_id", url);

        //
        // 3. 业务处理
        try {
            // String strParams = JSON.toJSONString(params);
            String strParams = objectMapper.writeValueAsString(params);

            if (strParams.length() > 100) {
                strParams = strParams.substring(0, 100);
            }

            log.info("Request URL[" + req.getAttribute("__agent_id") + "]: " + req.getServletPath() + ", params: "
                    + strParams + ", user: " + viewParams.getUserName());
            // System.out.println("Request URL: " + req.getServletPath() + ", params: " +
            // StringUtils.toJson( params) + ", user: " + viewParams.getUserName());

            // 3.1 解析URL
            String[] msdr = parseUrl(path);
            // 3.2 获取View对象
            String viewId = (msdr[0] + "." + msdr[1]);
            if (".".equals(viewId)) {
                viewId = "index";
            }
            // 3.3 当Bean不存在时,改为静态请求
            if (!context.containsBean("view-" + viewId)) {
                chain.doFilter(request, response);
            }
            Object view = context.getBean("view-" + viewId);
            Forward forward = null;
            Method method = view.getClass().getMethod(msdr[2], new Class[] { ViewParams.class });
            try {
                forward = (Forward) method.invoke(view, new Object[] { viewParams });
            } catch (InvocationTargetException e) {
                if (e.getTargetException() instanceof UnknownException) {
                    forward = ((UnknownException) e.getTargetException()).getForward();
                } else {
                    throw e;
                }
            }

            // 3.4 JSON跨域请求处理
            if (forward != null) {
                res.addHeader("Access-Control-Allow-Origin", "*");
                forward.forward(new DefaultDispatcher(req, res, actionType));
            }

        } catch (IOException | IllegalAccessException
            | IllegalArgumentException | NoSuchMethodException
            | SecurityException | InvocationTargetException
            | ServletException | BeansException e) {
            log.error(e.getMessage());
            chain.doFilter(request, response);
        }

    }

    /**
     * URL协议(msdr)：module service do result
     */
    private String[] parseUrl(String path) {
        String msdr;
        String defaultPath = "index";
        if (!StringUtils.hasLength(path)) {
            throw new IllegalArgumentException(String.format("web path[%s] must obey msmr1!", path));
        }
        // 默认路径
        path = ("/".equals(path) ? "/" + defaultPath + actionType : path);
        String[] paths = path.split("/");
        if (paths.length > 2) {
            throw new IllegalArgumentException(String.format("web path[%s] must obey msmr2!", path));
        }
        msdr = paths[1];

        String _path = msdr.substring(0, msdr.lastIndexOf("."));

        String[] ret = { "", "", defaultPath, "" };
        String[] arr = _path.split("_");
        switch (arr.length) {
            case 1:
                ret[2] = arr[0];
                break;
            case 2:
                ret[1] = arr[0];
                ret[2] = arr[1];
                break;
            case 4:
                ret[3] = arr[3];
            case 3:
                ret[0] = arr[0];
                ret[1] = arr[1];
                ret[2] = arr[2];
                break;
            default:
                throw new IllegalArgumentException(String.format("web path[%s] must obey msmr3!", path));
        }
        return ret;
    }

}
*/
// End of disabled ControllerFilter class