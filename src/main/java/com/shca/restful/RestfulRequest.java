package com.shca.restful;

import java.util.Map;

/**
 * Modern replacement for com.ywang.restful.RestfulRequest
 */
public interface RestfulRequest<T extends RestfulResponse<?, ?>> {

    /**
     * Get the API URL pattern
     */
    String getApiUrl();

    /**
     * Get request parameters
     */
    Map<String, String> getParams();

    /**
     * Process response and return typed response object
     */
    T response(String json);
}
