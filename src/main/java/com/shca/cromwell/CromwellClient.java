package com.shca.cromwell;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ywang.restful.*;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.HttpUtils;
import com.ywang.utils.LogUtils;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Map;

public class CromwellClient {

    private final static Logger log = Logger.getLogger( CromwellClient.class);
    private final static String CHARSET = "UTF-8";
    private final String url;
    private final String version;

    public CromwellClient(String url, String version) {
        this.url = url;
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public String getVersion() {
        return version;
    }

    public <T extends RestfulResponse> T send(RestfulRequest<T> request) {
        if (request == null) {
            throw new IllegalArgumentException( "request cannot be null");
        }

        for (int i = 0; i < 5; i++) {
            try {
                if (request instanceof RestfulMultipartPostRequest) {
                    return (T) sendMultipartPostRequest( (RestfulMultipartPostRequest)request);
                }
                if (request instanceof RestfulGetRequest) {
                    return (T) sendGetRequest( (RestfulGetRequest)request);
                }
            } catch (Exception e) {
                log.warn( "Catched exception:" + e.getMessage());
            }
        }
        throw new RuntimeException( request.getClass() + " cannot be support");
    }

    private <T extends RestfulResponse> T sendGetRequest(RestfulGetRequest<T> request) {
        T response = null;

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String url = this.url + request.getApiUrl().replace( "{version}", this.version);
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();


            URL _url = new URL( url);
            HttpGet httpGet = new HttpGet( new URI( _url.getProtocol(), null, _url.getHost(), _url.getPort(), _url.getPath(), _url.getQuery(), null));

            // 设置超时、设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpGet.setConfig(requestConfig);

            log.info( "Cromwell Client request: " + url);
            httpResponse = httpClient.execute( httpGet);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), CHARSET));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }
            log.debug( "Cromwell Client response(" + url + "): " + result);

            response = request.response( result);

            return response;
        } catch (IOException | UnsupportedOperationException | URISyntaxException e) {
            log.warn( "Cromwell Client request(" + url + "):\n" + LogUtils.toString( e));
            throw new RuntimeException( "Cromwell Client request(" + url + "): ", e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }

    private <T extends RestfulResponse> T sendMultipartPostRequest(RestfulMultipartPostRequest<T> request) {
        T response = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String url = this.url + request.getApiUrl().replace( "{version}", this.version);
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();

            HttpPost httpPost = new HttpPost( url);
            // 、设置请求和传输超时时间
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
            httpPost.setConfig( requestConfig);

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode( HttpMultipartMode.RFC6532);
            builder.setContentType( ContentType.MULTIPART_FORM_DATA);
            log.info( "Cromwell Client request: " + url);
            if (!CollectionUtils.isEmpty( request.getFiles())) {
                for (String key : request.getFiles().keySet()) {
                    for (File file : request.getFiles().get(key)) {
                        builder.addPart(key, new FileBody(file));
                    }
                }
            }
            HttpEntity multipart = builder.build();
            httpPost.setEntity( multipart);

            httpResponse = httpClient.execute( httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), CHARSET));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }
            log.debug( "Cromwell Client response(" + url + "): " + result);

            response = request.response( result);

            return response;
        } catch (IOException | UnsupportedOperationException e) {
            log.warn( "Cromwell Client request(" + url + "):\n" + LogUtils.toString( e));
            throw new RuntimeException( "Cromwell Client request(" + url + "): ", e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }

    public static void main(String...args) {
        String json = HttpUtils.postRequest( "http://ids.sjtu.edu.cn/classRoom/getByFreeClassroomInfo?roomCode=LGXQ", (Map<String, String>) null);
        JSONObject jsonObject = JSON.parseObject( json);
        JSONArray jsonArray = jsonObject.getJSONObject( "data").getJSONArray( "freeClassRoomList");
        System.out.println( jsonArray.size());
    }
}
