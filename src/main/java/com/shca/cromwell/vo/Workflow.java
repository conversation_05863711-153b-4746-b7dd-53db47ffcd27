package com.shca.cromwell.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by he<PERSON><PERSON><PERSON> on 2020/7/29.
 */
public class Workflow implements Serializable {

    private String id;

    private String name;

    private String status;

    private Date submission;

    private Date start;

    private Date end;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getSubmission() {
        return submission;
    }

    public void setSubmission(Date submission) {
        this.submission = submission;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }
}
