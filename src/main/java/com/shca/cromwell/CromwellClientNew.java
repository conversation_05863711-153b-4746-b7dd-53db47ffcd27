/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 13:26:57
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 20:53:52
 * @FilePath: /analysis_engine/src/main/java/com/shca/cromwell/CromwellClientNew.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.cromwell;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;

// @Component
public class CromwellClientNew {
    @Resource ObjectMapper objectMapper;
    @Resource RestTemplate restTemplate;

    private final static Logger log = LoggerFactory.getLogger( CromwellClientNew.class);
    private String url;
    private String version;

    public CromwellClientNew(String url, String version) {
        this.url = url;
        this.version = version;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public String getVersion() {
        return version;
    }

    public ResponseEntity<String> getSend(String apiPath,  String workflowId, String action) {
        apiPath = apiPath.startsWith("/")? apiPath: "/" + apiPath ;
        apiPath = apiPath.endsWith("/")? apiPath: apiPath + "/";
        String url = this.url +  apiPath + this.version + "/" + workflowId + "/" + action;
        try {
            log.info("send request to url : " + url);
            return restTemplate.getForEntity(url, String.class);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<String> postSend(String apiPath, String workflowId, String action, HttpEntity<String> request) {
        apiPath = apiPath.startsWith("/")? apiPath : "/" + apiPath ;
        apiPath = apiPath.endsWith("/")? apiPath : apiPath + "/";
        workflowId = workflowId==null ? "" : workflowId;
        action = (action == null) ? "" : action;
        String url = this.url +  apiPath + this.version + "/" + workflowId + "/" + action;
        try {
            log.info("post request to url : " + url);
            return restTemplate.postForEntity(url, request,String.class);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<String> postMultipart(String apiPath, HttpEntity<MultiValueMap<String, Object>> request) {
        apiPath = apiPath.startsWith("/")? apiPath: "/" + apiPath ;
        apiPath = apiPath.endsWith("/")? apiPath: apiPath + "/";
        String url = this.url +  apiPath + this.version;
        try {
            log.info("post multipart to url : " + url);
            return restTemplate.postForEntity(url, request,String.class);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }
}
