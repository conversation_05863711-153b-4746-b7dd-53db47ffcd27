package com.shca.cromwell.api;

import com.shca.restful.RestfulRequest;
import com.shca.restful.RestfulResponse;

/**
 * {"id":"73e59f38-1a6e-4c59-aa41-b9faa98f07f5","status":"Submitted"}
 */
public class WorkflowsResponse implements RestfulResponse<String, String> {

    private String id;

    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public RestfulRequest<?>[] getRequests() {
        return null;
    }

    @Override
    public void response(int i, RestfulResponse<?, ?> response) {
    }
}
