package com.shca.cromwell.api;

import com.ywang.restful.RestfulRequest;
import com.ywang.restful.RestfulResponse;

/**
 * {"id":"73e59f38-1a6e-4c59-aa41-b9faa98f07f5","status":"Submitted"}
 */
public class WorkflowsResponse implements RestfulResponse {

    private String id;

    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public RestfulRequest[] getRequests() {
        return null;
    }

    @Override
    public void response(int i, RestfulResponse response) {
    }
}
