/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 21:13:42
 * @FilePath: /analysis_engine/src/main/java/com/shca/cromwell/api/AbortRequest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.cromwell.api;

import com.alibaba.fastjson.JSON;
import com.shca.restful.RestfulMultipartPostRequest;

import java.io.File;
import java.util.Map;


public class AbortRequest implements RestfulMultipartPostRequest<AbortResponse> {

    private static final String API_URL = "/api/workflows/{version}/";
    private final String id;

    public AbortRequest(String id) {
        this.id = id;
    }

    @Override
    public String getApiUrl() {
        return API_URL + id + "/abort";
    }

    @Override
    public Map<String, String> getParams() {
        return null;
    }

    @Override
    public Map<String, File[]> getFiles() {
        return null;
    }

    @Override
    public AbortResponse response(String json) {
        AbortResponse response = JSON.parseObject( json, AbortResponse.class);
        return response;
    }
}
