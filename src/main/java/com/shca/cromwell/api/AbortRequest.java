package com.shca.cromwell.api;

import com.alibaba.fastjson.JSON;
import com.shca.cromwell.CromwellClient;
import com.ywang.restful.RestfulMultipartPostRequest;

import java.io.File;
import java.util.Map;


public class AbortRequest implements RestfulMultipartPostRequest<AbortResponse> {

    private static final String API_URL = "/api/workflows/{version}/";

    private final String id;

    public AbortRequest(String id) {
        this.id = id;
    }

    @Override
    public String getApiUrl() {
        return API_URL + id + "/abort";
    }

    @Override
    public Map<String, String> getParams() {
        return null;
    }

    @Override
    public Map<String, File[]> getFiles() {
        return null;
    }

    @Override
    public AbortResponse response(String json) {
        AbortResponse response = JSON.parseObject( json, AbortResponse.class);
        return response;
    }

    public static void main(String...args) {
        CromwellClient client = new CromwellClient( "http://**************:90", "v1");
        AbortResponse response = client.send( new AbortRequest( "3f0a3912-930f-4129-878c-37db27b10f4a"));

        System.out.println( JSON.toJSONString( response));
    }
}
