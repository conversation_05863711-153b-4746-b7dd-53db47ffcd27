package com.shca.cromwell.api;

import com.alibaba.fastjson.JSON;
import com.shca.cromwell.CromwellClient;
import com.ywang.restful.RestfulGetRequest;


public class QueryRequest implements RestfulGetRequest<QueryResponse> {

    private static final String API_URL = "/api/workflows/{version}/query";

    @Override
    public String getApiUrl() {
        return API_URL;
    }


    @Override
    public QueryResponse response(String json) {
        QueryResponse response = JSON.parseObject( json, QueryResponse.class);
        return response;
    }

    public static void main(String... args) {
        CromwellClient client = new CromwellClient( "http://111.186.58.116:90", "v1");
        QueryResponse response = client.send( new QueryRequest());

        System.out.println(JSON.toJSONString( response));
    }
}
