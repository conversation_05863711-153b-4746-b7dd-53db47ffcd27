/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 21:14:45
 * @FilePath: /analysis_engine/src/main/java/com/shca/cromwell/api/QueryRequest.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.cromwell.api;

import com.alibaba.fastjson.JSON;
import com.ywang.restful.RestfulGetRequest;
public class QueryRequest implements RestfulGetRequest<QueryResponse> {

    private static final String API_URL = "/api/workflows/{version}/query";

    @Override
    public String getApiUrl() {
        return API_URL;
    }


    @Override
    public QueryResponse response(String json) {
        QueryResponse response = JSON.parseObject( json, QueryResponse.class);
        return response;
    }
}
