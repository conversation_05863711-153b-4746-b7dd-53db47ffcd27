package com.shca.cromwell.api;

import com.shca.util.JsonUtils;
import com.shca.restful.RestfulMultipartPostRequest;

import java.io.File;
import java.util.HashMap;
import java.util.Map;


public class WorkflowsRequest implements RestfulMultipartPostRequest<WorkflowsResponse> {

    private static final String API_URL = "/api/workflows/{version}";

    private final File source;

    private final File inputs;

    private final File dependencies;

    private final File options;

    public WorkflowsRequest(File source, File inputs, File dependencies, File options) {
        this.dependencies = dependencies;
        this.inputs = inputs;
        this.source = source;
        this.options = options;
    }

    @Override
    public String getApiUrl() {
        return API_URL;
    }

    @Override
    public Map<String, String> getParams() {
        return null;
    }

    @Override
    public Map<String, File[]> getFiles() {
        Map<String,File[]> files = new HashMap<>();

        files.put( "workflowSource", new File[]{ source});
        files.put( "workflowInputs", new File[]{ inputs});
        if (dependencies != null) {
            files.put("workflowDependencies", new File[]{ dependencies});
        }
        if (options != null) {
            files.put("workflowOptions", new File[]{ options});
        }

        return files;
    }

    @Override
    public WorkflowsResponse response(String json) {
        ///System.out.println( "json: " + json);
        WorkflowsResponse response = JsonUtils.parseObject(json, WorkflowsResponse.class);
        return response;
    }
}
