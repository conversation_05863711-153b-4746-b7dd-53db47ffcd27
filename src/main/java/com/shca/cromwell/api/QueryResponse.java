package com.shca.cromwell.api;

import com.shca.cromwell.vo.Workflow;
import com.shca.restful.RestfulRequest;
import com.shca.restful.RestfulResponse;

import java.util.List;

/**
 * {
 * "results": [
 * {
 * "id": "string",
 * "name": "string",
 * "status": "string",
 * "submission": "2020-07-29T15:14:29.740Z",
 * "start": "2020-07-29T15:14:29.740Z",
 * "end": "2020-07-29T15:14:29.740Z"
 *}],
 * "totalResultsCount": 0
 }
 */
public class QueryResponse implements RestfulResponse<List<Workflow>, Integer> {

    private List<Workflow> results;

    private long totalResultsCount;

    public List<Workflow> getResults() {
        return results;
    }

    public void setResults(List<Workflow> results) {
        this.results = results;
    }

    public long getTotalResultsCount() {
        return totalResultsCount;
    }

    public void setTotalResultsCount(long totalResultsCount) {
        this.totalResultsCount = totalResultsCount;
    }

    @Override
    public RestfulRequest<?>[] getRequests() {
        return null;
    }

    @Override
    public void response(int i, RestfulResponse<?, ?> response) {
    }
}
