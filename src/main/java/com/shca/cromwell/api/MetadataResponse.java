package com.shca.cromwell.api;



import com.shca.cromwell.vo.Call;
import com.shca.cromwell.vo.Workflow;
import com.ywang.restful.RestfulRequest;
import com.ywang.restful.RestfulResponse;

import java.util.List;
import java.util.Map;

public class MetadataResponse implements RestfulResponse {

    private String json;

    private Workflow workflow;

    private Map<String,List<Call>> calls;

    public Workflow getWorkflow() {
        return workflow;
    }

    public void setWorkflow(Workflow workflow) {
        this.workflow = workflow;
    }

    public Map<String, List<Call>> getCalls() {
        return calls;
    }

    public void setCalls(Map<String, List<Call>> calls) {
        this.calls = calls;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    @Override
    public RestfulRequest[] getRequests() {
        return null;
    }

    @Override
    public void response(int i, RestfulResponse response) {
    }


}
