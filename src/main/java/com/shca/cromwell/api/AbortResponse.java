package com.shca.cromwell.api;

import com.ywang.restful.RestfulRequest;
import com.ywang.restful.RestfulResponse;


public class AbortResponse implements RestfulResponse {

    private String status;

    private String message;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public RestfulRequest[] getRequests() {
        return new RestfulRequest[0];
    }

    @Override
    public void response(int i, RestfulResponse response) {

    }
}
