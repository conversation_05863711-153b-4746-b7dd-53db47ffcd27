package com.shca.cromwell.api;

import com.shca.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.cromwell.vo.Call;
import com.shca.cromwell.vo.Workflow;
import com.shca.restful.RestfulGetRequest;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class MetadataRequest implements RestfulGetRequest<MetadataResponse> {

    private static final String API_URL = "/api/workflows/{version}/";

    private final String id;

    public MetadataRequest(String id) {
        this.id = id;
    }

    @Override
    public String getApiUrl() {
        return API_URL + id + "/metadata";
    }

    @Override
    public Map<String, String> getParams() {
        return null;
    }

    @Override
    public MetadataResponse response(String json) {
        try {
            //System.out.println("MetadataRequest.java MetadataResponse "+json);
            ObjectNode jsonObject = (ObjectNode) JsonUtils.getObjectMapper().readTree(json);

            MetadataResponse response = new MetadataResponse();
            response.setJson(json);

            Workflow workflow = new Workflow();
            workflow.setId(getStringValue(jsonObject, "id"));
            workflow.setName(getStringValue(jsonObject, "workflowName"));
            workflow.setStatus(getStringValue(jsonObject, "status"));
            workflow.setSubmission(getDateValue(jsonObject, "submission"));
            workflow.setStart(getDateValue(jsonObject, "start"));
            workflow.setEnd(getDateValue(jsonObject, "end"));
            response.setWorkflow(workflow);

            ObjectNode calls = (ObjectNode) jsonObject.get("calls");
            Map<String,List<Call>> callMap = new HashMap<>();
            if (calls != null) {
                calls.fieldNames().forEachRemaining(key -> {
                    ArrayNode callJSONArray = (ArrayNode) calls.get(key);
                    List<Call> callList = new ArrayList<>();

                    for (int i = 0; i < callJSONArray.size(); i++) {
                        Call call = new Call();
                        ObjectNode callJsonObject = (ObjectNode) callJSONArray.get(i);

                        call.setJobId(getLongValue(callJsonObject, "jobId"));
                        call.setCommandLine(getStringValue(callJsonObject, "commandLine"));
                        call.setBackend(getStringValue(callJsonObject, "backend"));
                        call.setReturnCode(getIntegerValue(callJsonObject, "returnCode"));
                        call.setStart(getDateValue(callJsonObject, "start"));
                        call.setEnd(getDateValue(callJsonObject, "end"));
                        call.setExecutionStatus(getStringValue(callJsonObject, "executionStatus"));
                        call.setBackendStatus(getStringValue(callJsonObject, "backendStatus"));
                        call.setStdout(getStringValue(callJsonObject, "stdout"));
                        call.setStderr(getStringValue(callJsonObject, "stderr"));

                        if(callJsonObject.has("executionEvents") && !callJsonObject.get("executionEvents").isNull()) {
                            List<Map<String,Object>> events = new ArrayList<>();
                            ArrayNode executionEvents = (ArrayNode) callJsonObject.get("executionEvents");
                            for (int j = 0; j < executionEvents.size(); j++) {
                                ObjectNode obj = (ObjectNode) executionEvents.get(j);
                                Map<String,Object> tempMap = new HashMap<>();
                                tempMap.put("description", getStringValue(obj, "description"));
                                tempMap.put("startTime", getDateValue(obj, "startTime"));
                                tempMap.put("endTime", getDateValue(obj, "endTime"));
                                events.add(tempMap);
                            }
                            call.setExecutionEvents(events);
                        }
                        if(callJsonObject.has("subWorkflowId") && !callJsonObject.get("subWorkflowId").isNull()) {
                            call.setSubWorkflowId(getStringValue(callJsonObject, "subWorkflowId"));
                        }

                        callList.add(call);
                    }
                    callMap.put(key, callList);
                });
            }
            response.setCalls(callMap);

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse metadata response", e);
        }
    }

    private String getStringValue(ObjectNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asText() : null;
    }

    private Long getLongValue(ObjectNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asLong() : null;
    }

    private Integer getIntegerValue(ObjectNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asInt() : null;
    }

    private Date getDateValue(ObjectNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        if (field == null || field.isNull()) {
            return null;
        }
        try {
            // Assuming ISO date format, adjust as needed
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            return sdf.parse(field.asText());
        } catch (ParseException e) {
            return null;
        }
    }
}
