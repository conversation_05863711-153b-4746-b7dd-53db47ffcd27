package com.shca.cromwell.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.vo.Call;
import com.shca.cromwell.vo.Workflow;
import com.ywang.restful.RestfulGetRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class MetadataRequest implements RestfulGetRequest<MetadataResponse> {

    private static final String API_URL = "/api/workflows/{version}/";

    private final String id;

    public MetadataRequest(String id) {
        this.id = id;
    }

    @Override
    public String getApiUrl() {
        return API_URL + id + "/metadata";
    }

    @Override
    public MetadataResponse response(String json) {
        //System.out.println("MetadataRequest.java MetadataResponse "+json);
        JSONObject jsonObject = JSON.parseObject( json);


        MetadataResponse response = new MetadataResponse();
        response.setJson( json);

        Workflow workflow = new Workflow();
        workflow.setId( jsonObject.getString( "id"));
        workflow.setName( jsonObject.getString( "workflowName"));
        workflow.setStatus( jsonObject.getString( "status"));
        workflow.setSubmission( jsonObject.getDate( "submission"));
        workflow.setStart( jsonObject.getDate( "start"));
        workflow.setEnd( jsonObject.getDate( "end"));
        response.setWorkflow( workflow);

        JSONObject calls = jsonObject.getJSONObject( "calls");
        Map<String,List<Call>> callMap = new HashMap<>();
        if (calls != null) {
            for (String key : calls.keySet()) {
                JSONArray callJSONArray = calls.getJSONArray(key);
                List<Call> callList = new ArrayList<>();

                for (int i = 0; i < callJSONArray.size(); i++) {
                    Call call = new Call();
                    JSONObject callJsonObject = callJSONArray.getJSONObject(i);

                    call.setJobId(callJsonObject.getLong("jobId"));
                    call.setCommandLine(callJsonObject.getString("commandLine"));
                    call.setBackend(callJsonObject.getString("backend"));
                    call.setReturnCode(callJsonObject.getInteger("returnCode"));
                    call.setStart(callJsonObject.getDate("start"));
                    call.setEnd(callJsonObject.getDate("end"));
                    call.setExecutionStatus(callJsonObject.getString("executionStatus"));
                    call.setBackendStatus(callJsonObject.getString("backendStatus"));
                    call.setStdout(callJsonObject.getString("stdout"));
                    call.setStderr(callJsonObject.getString("stderr"));

                    call.setStderr(callJsonObject.getString("stderr"));
                    if(callJsonObject.get("executionEvents")!=null) {
                        List<Map<String,Object>> events = new ArrayList<>();
                        for (int j = 0; j < callJsonObject.getJSONArray("executionEvents").size(); j++) {
                            JSONObject obj = callJsonObject.getJSONArray("executionEvents").getJSONObject(j);
                            Map<String,Object> tempMap = new HashMap<>();
                            tempMap.put("description", obj.getString("description"));
                            tempMap.put("startTime", obj.getDate("startTime"));
                            tempMap.put("endTime", obj.getDate("endTime"));
                            events.add(tempMap);
                        }
                        call.setExecutionEvents(events);
                    }
                    if(callJsonObject.get("subWorkflowId")!=null) {
                        call.setSubWorkflowId(callJsonObject.getString("subWorkflowId"));
                    }

                    callList.add(call);
                }
                callMap.put(key, callList);
            }
        }
        response.setCalls(callMap);

        return response;
    }

    public static void main(String... args) {
        CromwellClient client = new CromwellClient( "http://**************:90", "v1");
        MetadataResponse response = client.send( new MetadataRequest( "e04f8bcd-fad4-4501-a011-a935756137e5"));

        System.out.println( JSON.toJSONString( response.getWorkflow()));
    }
}
