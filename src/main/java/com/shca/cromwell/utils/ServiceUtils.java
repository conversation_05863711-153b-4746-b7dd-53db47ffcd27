package com.shca.cromwell.utils;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.ConvertUtils;

public class ServiceUtils {

    @Resource private static Db db;
    public static List<Map<String, Object>> cromwellPipelineConfig;


    public static File readOptions(File source) {
        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
        if (!options.exists()) {
            options = new File(source.getParentFile().getParentFile().getParentFile().getAbsolutePath()
                    + "/workflow_options.json");
        }
        if (!options.exists()) {
            options = null;
        }
        return options;
    }

    public static List<Map<String, Object>> selectUploadTasks() {
        return db.select("SELECT * from shca_project_project where status='running_step3' ");
    }

    public static List<Map<String, Object>> selectQcSummaryTasks() {
        return db.select("SELECT * from shca_project_project where status='running_step2' ");
    }

    public static List<Map<String, Object>> selectMutualTasks() {
        return db.select("SELECT * from shca_project_project where status='wait_running' ORDER BY priority, create_time DESC ");
    }

    public static List<Map<String, Object>> selectGenericTasks() {
        return db.select("SELECT * from shca_project_cancer WHERE status='wait_running' ORDER BY priority, create_time DESC ");
    }

    public static List<Map<String, Object>> selectFileTransferTasks() {
        // how to get the correct status
        return db.select("SELECT * from shca_project_project WHERE status='running_step4' ORDER BY priority, create_time DESC ");
    }

    public static void prioritySync() {
        // set the project priority along with the priority table
        db.update(
                "UPDATE shca_project_project a set priority=ifnull((SELECT priority FROM shca_project_project_priority b where b.project_name=a.project_name), priority) ");
        // set the samples priority along with the project priority
        db.update(
                "UPDATE shca_project_cancer a set priority=ifnull((select max(priority) from shca_project_project b where b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name), priority) ");
    }

    // public static Map<String, Object> _query() {
    //     return ;

    // }

    public static Map<String, Integer> _getAvailableSlotsNum(SysConfigManager config) {
        int maxCapacityOfJobQueue = ConvertUtils.integer(config.getValue("cromwell.max_workflow_count"), 12);

        int currentRunningJobNum = db.selectUnique(
                "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                        " WHERE WORKFLOW_NAME=? AND  WORKFLOW_STATUS not in (?,?,?)",
                new IntegerCallback(), "generalProcess", "Succeeded", "Failed", "Aborted");

        Map<String, Integer> jobQueueStatus = new HashMap<>();
        jobQueueStatus.put("maxCapacityOfJobQueue", maxCapacityOfJobQueue);
        jobQueueStatus.put("currentRunningJobNum", currentRunningJobNum);
        jobQueueStatus.put("availableSlotsNum", maxCapacityOfJobQueue - currentRunningJobNum);

        return jobQueueStatus;

    }

    public static List<Map<String, Object>> getCromwellPipelineConfig() {
        return cromwellPipelineConfig;
    }

    public static void setCromwellPipelineConfig(List<Map<String, Object>> cromwellPipelineConfig) {
        ServiceUtils.cromwellPipelineConfig = cromwellPipelineConfig;
    }

    public static void setCromwellPipelineConfig() {
        ServiceUtils.cromwellPipelineConfig = db.select("SELECT * FROM shca_project_cancer_pipeline_config");
    }

    public File _getWorkflowOptionJson(SysConfigManager config, Map<String, Object> cromwellConfig) {
        // get options json file, now this file is no longer exist
        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
        File optionsWithWDL = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json"); // 不存在这个配置文件
        File optionsWithConfig = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");

        if (optionsWithWDL.isFile()) {
            return optionsWithWDL;
        }

        if (optionsWithConfig.isFile()) {
            return optionsWithConfig;
        }
        throw new RuntimeException("workflow_options.json not found");
    }
}
