package com.shca.cromwell.service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.support.SysConfigManager;
import com.shca.util.CmdOperation;
import com.shca.reflect.MethodParam;

@Service
public class FileTransferService {
    @Resource private JdbcTemplate jdbcTemplate;
    @Resource private CromwellClient client;
    @Resource private DataSource dataSource;
    @Resource private SysConfigManager config;
    @Resource private ObjectMapper objectMapper;
    @Resource CromwellServiceSupport support;
    private static final Logger log = Logger.getLogger(FileTransferService.class);

    public void launch() {
        log.info("check and do file transfer process");
        if (!ifTransferTaskBusy()) {
            launchFileTransferTask(1);
        }
    }

    private void launchFileTransferTask(int taskNum) {
        String streamSql = "SELECT id, project_name, project_sn, cancer_kind, sample_name_0,sample_name_1 "
                        + "FROM v_shca_project_project "
                        + "WHERE is_filing=? AND status=? AND cromwell_workflow_id_2 is null ORDER BY priority DESC, create_time ASC " ;

        List<Map<String, Object>> streamProjects = jdbcTemplate.queryForList(
            streamSql,
            new Object[]{'0', "finished"});

        log.info("Projects count to be transfered is : " + streamProjects.size());
        streamProjects.stream()
            .filter( proj -> !proj.isEmpty())
            .limit(taskNum)
            .forEach( proj -> {
                try{
                    _fileTransfer(proj);
                } catch (Exception e) {
                    log.error("file transfer error", e);
            }});

        streamProjects.stream()
            .filter( proj -> "peixirna".equals((String)proj.get("cancer_kind")))
            .forEach(proj -> {
                try{
                    _fileTransfer(proj);
                } catch (Exception e) {
                    log.error("file transfer peixi error", e);
            }});
    }
    public Map<String, Object> fileTransfer(
        @MethodParam(name = "project_no") String projectSn)
        throws Exception {

        log.info("check and do file transfer process");
        Map<String, Object> ret = new HashMap<>();
        if (StringUtils.hasLength(projectSn)) {
            ret.put("errcode", 1001);
            ret.put("errmsg", "项目输入错误");
            return ret;
        }
        Map<String, Object>  projectMap = getTransferFileProjectBySn(projectSn);
        if (CollectionUtils.isEmpty(projectMap)) {
            ret.put("errcode", 1002);
            ret.put("errmsg", "未找到项目");
            return ret;
        }
        try {
            _fileTransfer(projectMap);
        } catch (Exception e) {
            log.info("sub file transfer error", e);
        }
        ret.put("errcode", 0);
        ret.put("errmsg", "同步任务已提交");
        return ret;
    }

    private void _fileTransfer(Map<String, Object> projectMap)
        throws Exception {

        File workflowSource = new File("/myTools/geneAnalysisPipeline/AutoFileTransfer-ver10.wdl");
        if (!workflowSource.exists()) {
            return;
        }

        File prettyJson = createTransferJsonFileWithProjectMap(projectMap);
        WorkflowsResponse response0 = client.send(new WorkflowsRequest(
            workflowSource, prettyJson, null, null));

        // ret.put("response0", response0);
        createOrUpdateTransferFileRecord(projectMap,response0.getId(),"create");
    }

    private Map<String, Object> getTransferFileProjectBySn(String projectSn) {
        String transferFileSql = "SELECT * FROM shca_project_project_transfer_file "
            + " WHERE project_sn = ?";
        try{
            Map<String,Object> transferFileMap = jdbcTemplate.queryForMap(
            transferFileSql, new Object[]{ projectSn });
            return transferFileMap;
        } catch (DataAccessException e) {
            return new HashMap<>();
        }
    }

    private int createOrUpdateTransferFileRecord(
        Map<String, Object> project,
        String cromwell_workflow_id,
        String operType ){

        int result = 0;
        String projectSn = (String)project.get("project_sn");
        if (!StringUtils.hasLength(projectSn)) {
            return result;
        }

        Map<String, Object> transferFileMap = getTransferFileProjectBySn(projectSn);
        if(transferFileMap.isEmpty()){
            transferFileMap = new HashMap<>();
            transferFileMap.put("project_id", project.get( "id"));
            transferFileMap.put("project_sn", project.get( "project_sn"));
            transferFileMap.put("project_no", project.get( "project_no"));
            transferFileMap.put("project_name", project.get( "project_name"));
            transferFileMap.put("cromwell_workflow_id", cromwell_workflow_id);
            transferFileMap.put("cromwell_workflow_type", operType);
            transferFileMap.put("update_time", LocalDateTime.now());

            SimpleJdbcInsert simpleJdbcInsert = new SimpleJdbcInsert(dataSource)
                                        .withTableName("shca_project_project_transfer_file");

            result = (int) simpleJdbcInsert.execute(transferFileMap);
        } else {
            String countSql = "UPDATE shca_project_project_transfer_file "
                + " SET cromwell_workflow_id=?, cromwell_workflow_type=? "
                + " WHERE project_sn=?";
            result = jdbcTemplate.update(
                countSql,
                new Object[]{ cromwell_workflow_id, operType, projectSn});

        }
        return result;
    }

    public File createTransferJsonFileWithProjectMap(Map<String, Object> projectMap) {

        String projectName = (String) projectMap.get("project_name");
        String sampleName0 = (String) projectMap.get("sample_name_0");
        String sampleName1 = (String) projectMap.get("sample_name_1");
        String cancerName = (String) projectMap.get("cancer_kind");
        cancerName = support.cancerNameMapper(cancerName);
        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("fileTransfer.ACTION_WF", "create");
        File targetDir = getTargetTransferDir(projectName);
        objectNode.put("fileTransfer.targetPath_WF", targetDir.getAbsolutePath());
        objectNode.put("fileTransfer.cancerName_WF", cancerName);
        // if (cancerName.startsWith("peixi") || cancerName.endsWith("peixi")) {
        if (support.ifNormalProject(projectMap)) {
            objectNode.put("fileTransfer.sampleName_WF", "");
            objectNode.put("fileTransfer.normalSampleName_WF", sampleName0);
            objectNode.put("fileTransfer.normalSampleName_WF", sampleName1);
        } else {
            objectNode.put("fileTransfer.sampleName_WF", sampleName0);
            objectNode.put("fileTransfer.normalSampleName_WF", "");
            objectNode.put("fileTransfer.tumorSampleName_WF", "");
        }

        objectNode.put("fileTransfer.projectName_WF", projectName);
        objectNode.put("fileTransfer.homePath_WF", config.getValue("workspace.home_path"));

        String projectDir = config.getValue("workspace.project_path") + "/" + projectName;
        String dateSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        File result = new File(projectDir + "/fileTransfer_" + projectName + "_" + dateSuffix + ".json");
        try {
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(result, objectNode);
        } catch (JsonGenerationException e) {
            log.error("JsonGenerationException", e);
        } catch (JsonMappingException e) {
            log.error("JsonMappingException", e);
        } catch (IOException e) {
            log.error("IOException", e);
        }
        return result;
    }

    private File getTargetTransferDir(String projectName) {
        File targetDir = new File("/CloudSvr01/analysisSourceFiles/" + projectName);
        if (targetDir.exists()) {
            CmdOperation.ShellCmd("rm -rf " + targetDir.getAbsolutePath());
        }
        return targetDir;
    }

    // TODO to be implemented later
    // public File createVcfTransferJsonFileWithProjectMap( Map<String, Object> projectMap) {
    //     String projectSn = (String) projectMap.get("project_sn");
    //     String projectName = (String) projectMap.get("project_name");
    //     String cancerKind = (String) projectMap.get("cancer_kind");
    //     String normalSampleName = (String) projectMap.get("sample_name_0");
    //     String samplePath = config.getValue("workspace.sample_path");
    //     String projectPath = config.getValue("workspace.project_path");
    //     ObjectNode nodeVcf = objectMapper.createObjectNode();
    //     ArrayNode vcfFolderArray = objectMapper.createArrayNode();
    //     File targetVcfDir = new File("/CloudSvr01/analysisSourceFiles/" + "VCFsOfError" + projectSn +  "_"
    //             + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
    //     if (targetVcfDir.exists()) {
    //         CmdOperation.ShellCmd("rm -rf " + targetVcfDir.getAbsolutePath());
    //     }
    //     nodeVcf.put("fileTransfer.ACTION_WF", "create");
    //     nodeVcf.put("fileTransfer.targetPath_WF", targetVcfDir.getAbsolutePath());
    //     nodeVcf.put("fileTransfer.cancerName_WF", cancerKind);
    //     File sampleBVcf = new File(samplePath + normalSampleName + "/gatk/vcf");
    //     if( sampleBVcf.exists() ) {
    //             vcfFolderArray.add(sampleBVcf.getAbsolutePath()) ;
    //     }
    //     if (support.ifNormalProject(projectMap)) {
    //         String tumorSampleName = (String) projectMap.get("sample_name_1");
    //         File sampleKVcf = new File(samplePath + tumorSampleName + "/gatk/vcf");
    //         if( sampleKVcf.exists() ) {
    //             vcfFolderArray.add(sampleKVcf.getAbsolutePath()) ;
    //         }
    //     }

    //     if (vcfFolderArray.hasNonNull(0)) {
    //         nodeVcf.set("fileTransfer.customFileList", vcfFolderArray);
    //         File prettyJsonVcf = new File( projectPath + "/VcfFileTransfer_" + projectName + "_"
    //             + ConvertUtils.date2Str(new Date(), "yyyyMMddHHmm") + ".json");
    //         try {
    //             objectMapper.writerWithDefaultPrettyPrinter().writeValue(prettyJsonVcf, nodeVcf);
    //         } catch (Exception e) {
    //             e.printStackTrace();
    //         }
    //         return prettyJsonVcf;
    //     }
    //     return null;
    // }

    private boolean ifTransferTaskBusy() {
        int fileTransferCnt = fileTransferTaskCount(24);
        return (fileTransferCnt > 0);
    }

    private int fileTransferTaskCount(int hours) {
        // String findRunningFileTransferSql = "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
        //      + "WHERE WORKFLOW_NAME=? AND WORKFLOW_STATUS=? ";
        String findRunningFileTransferSql = "select count(*) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
                + " where WORKFLOW_NAME=? and WORKFLOW_STATUS=? and START_TIMESTAMP >= ?";
        @SuppressWarnings("deprecation")
        int fileTransferCnt = jdbcTemplate.queryForObject(
            findRunningFileTransferSql,
            new Object[] {"fileTransfer", "Running", LocalDateTime.now().minusHours(hours)},
            (resulSet, i) -> resulSet.getInt(i));

        return fileTransferCnt;
    }
}
