package com.shca.cromwell.service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.shca.Pipeline;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.shca.config.SystemConfigService;

@Service
public class SummaryService {
    @Resource private JdbcTemplate jdbcTemplate;
    @Resource private CromwellServiceSupport support;
    @Resource private SystemConfigService config;

    private static final Logger log = Logger.getLogger(SummaryService.class);
    public void launch() {
        log.info("check and do summary process");
        List<Map<String,Object>> projectsToSummary = getProjectToSummary();
        log.info("summary-process[" + projectsToSummary.size() + "]...");
        // prepare summary
        projectsToSummary.stream().forEach(item -> {
            item.put("rawDataR1_0", support.getRawdataStorePath((String)item.get("sample_name_0"), "R1"));
            item.put("rawDataR1_0", support.getRawdataStorePath((String)item.get("sample_name_0"), "R2"));
            if (support.ifNormalProject(item)) {
                item.put("rawDataR1_1", support.getRawdataStorePath((String)item.get("sample_name_1"), "R1"));
                item.put("rawDataR1_1", support.getRawdataStorePath((String)item.get("sample_name_1"), "R2"));
            }
        });

        projectsToSummary.stream()
            .filter(project -> "finished_step1".equals(project.get("status")))
            .forEach( project -> {
                // update project status to confirm if mutual process is finished.
                launchSummaryTask( project,
                    (String)project.get("sample_name_0"),(String)project.get("rawDataR1_0"),(String)project.get("rawDataR2_0"));

                if (support.ifNormalProject(project)) {
                    launchSummaryTask( project,
                        (String)project.get("sample_name_1"), (String)project.get("rawDataR1_1"), (String)project.get("rawDataR2_1"));

            }
            // updateProjectStatusFromFinishedStep1ToRunningStep2(project);
        });
        updateSampleSummaryStatusByCromwell();
        upateProjectStatusByCromwell();

        getReadyCheckQcProject().stream().forEach(project -> {
            createQcSummaryEntryByTxtFile(project, (String)project.get("sample_name_0"));
            if (support.ifNormalProject(project)) {
                createQcSummaryEntryByTxtFile(project, (String)project.get("sample_name_1"));
            }
        });
    }

    private List<Map<String, Object>> getProjectToSummary() {
        String readyToSummarySql = "SELECT * FROM shca_project_project WHERE status='running_step2' ";
        return jdbcTemplate.queryForList(readyToSummarySql);
    }

    public void launchSummaryTask(Map<String, Object> projectMap,
        String sampleName, String rawdataR1, String rawdataR2) {
        // Map<String, Object> result = new HashMap<>();
        log.info(
            "summary-process[" + projectMap.get("project_sn") + "][" + projectMap.get("project_name")
            + "][" + sampleName + "]: start workflow...");
        Map<String, Object> cromwellConfig = support.getCromwellConfig((String)projectMap.get("cancer_kind"), '2');
        ProcessService processService = support.getProcessService(projectMap);
        if (cromwellConfig == null || processService == null) {
            return ;
        }
        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
        File workflowOptionsJson = support.getWorkflowOptionJson(config, cromwellConfig);
        try {
            WorkflowsResponse response = processService.summary(
                (String) projectMap.get("project_sn"),
                (String) projectMap.get("project_name"),
                sampleName,
                new File(rawdataR1),
                new File(rawdataR2),
                (String) cromwellConfig.get("cromwell_workflow_inputs"),
                source,
                new File((String) cromwellConfig.get("cromwell_workflow_dependencies")),
                workflowOptionsJson);

            // update cancer table status of cromwell_workflow_1
            String updateSampleSql = "UPDATE shca_project_cancer "
                + " SET cromwell_workflow_id_1=?, cromwell_workflow_status_1=?, cancer_script_1=? "
                + " ,update_username=?,update_time=? WHERE sample_name=? ";
            jdbcTemplate.update(
                updateSampleSql,
                new Object[] {
                    response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"),
                    "CromwellService.summary", LocalDateTime.now(), sampleName });

            // result.put("project_sn", projectMap.get("project_sn"));
            // result.put("project_name", projectMap.get("project_name"));
            // result.put("sample_name", sampleName);
            // result.put("cancer_script_1", cromwellConfig.get("cancer_script"));
            // result.put("cromwell_workflow_id_1", response.getId());
            // result.put("cromwell_workflow_status_1", response.getStatus());
        } catch (DataAccessException e) {
            log.error("summary-process[" + projectMap.get("project_sn") + "][" + projectMap.get("project_name"), e);
        }
    }

    private void upateProjectStatusByCromwell() {
            // update running_step2 status by cromwell
        String selectRunningStep2ProjectsSql = "SELECT * FROM shca_project_project "
            + " WHERE status='running_step2' ORDER BY priority DESC ";
        List<Map<String, Object>> selectRunningStep2Projects = jdbcTemplate.queryForList(
            selectRunningStep2ProjectsSql);

        String updateProjectSql = "UPDATE shca_project_project SET status=?, update_username=?, update_time=?"
                                + " WHERE id=? ";
        selectRunningStep2Projects.stream()
            .filter( project -> ("Failed".equals(project.get("workflow_status_0")) || "Failed".equals(project.get("workflow_status_1"))))
            .forEach( project -> {
                jdbcTemplate.update(
                updateProjectSql, new Object[]{ "failed", "CromwellService.summary", LocalDateTime.now(), project.get("id")});
            });
        selectRunningStep2Projects.stream()
            .filter( project -> ("Aborted".equals(project.get("workflow_status_0")) || "Aborted".equals(project.get("workflow_status_1"))))
            .forEach( project -> {
                jdbcTemplate.update(
                updateProjectSql, new Object[]{ "aborted", "CromwellService.summary", LocalDateTime.now(), project.get("id")});
            });
        // List<Map<String, Object>> readyCheckQcProjects =
        selectRunningStep2Projects.stream()
            .filter(project -> "Succeeded".equals(project.get("workflow_status_0")))
            .filter(project->("Succeeded".equals(project.get("workflow_status_1")) || project.get("sample_name_1") == null))
            .forEach(project ->{
                updateProjectStatusFromFinishedStep2ToRunningStep3(project);
            });
    }
    private void updateProjectStatusFromFinishedStep2ToRunningStep3(Map<String, Object> projectMap) {
        String updateProjectSql = "UPDATE shca_project_project SET status=?, update_username=?, update_time=?"
                                + " WHERE status='running_step2' AND id=? ";
        jdbcTemplate.update(
            updateProjectSql,
            new Object[]{ "running_step3", "CromwellService.summary", LocalDateTime.now(), projectMap.get("id")});
    }

    public File createQcSummaryEntryByTxtFile(
        Map<String,Object> projectMap,
        String sampleName ) {

        Pipeline pipeline = Pipeline.parse((String)projectMap.get("cancer_kind"));

        QcSummaryService qcSummaryService = pipeline.getQcSummaryService();
        File qcSummaryFile = new File(
            config.getValue("workspace.project_path") + "/" + projectMap.get("project_name")
            + "/qc_summary/" + sampleName + "_summary.txt");
        if (qcSummaryService == null) {
            return qcSummaryFile;
        }
        try {
            List<QcSummary> qcSummaries = qcSummaryService.qcSummary(qcSummaryFile);
            insertQcSummaryFileToDb(qcSummaries, projectMap, sampleName);
        } catch (Exception e) {
            log.error("createQcSummaryEntryByTxtFile error", e);
        }
        return qcSummaryFile;
    }

    private void insertQcSummaryFileToDb(List<QcSummary> qcSummaries, Map<String,Object> projectMap,
        String sampleName) {

        String clearSql = "DELETE FROM shca_project_project_qcsummary "
            + "WHERE project_sn=? AND sample_name=? ";
        jdbcTemplate.update(
            clearSql,
            new Object[] {projectMap.get("project_sn"), sampleName});

        String insertSql = "INSERT INTO shca_project_project_qcsummary "
            + "(project_sn, project_no, sample_name, qc_name, qc_value, qc_reference, qc_qualified, "
            + "create_username, create_time, update_time, update_username) VALUES (?,?,?,?,?,?,?,?,?,?,?)";

        qcSummaries.stream().forEach( item -> {
            jdbcTemplate.update(
                insertSql,
                new Object[]{
                    projectMap.get("project_sn"),
                    projectMap.get("project_no"),
                    sampleName,
                    item.getName(), item.getValue(),
                    item.getReference(), item.getQualified(),
                    "CromwellService.summary",
                    LocalDateTime.now(), LocalDateTime.now(), "CromwellService.summary"});
        });
    }

    private void updateSampleSummaryStatusByCromwell() {
        String selectCancerSql = " SELECT id, cromwell_workflow_id_1 "
                                + " FROM shca_project_cancer "
                                + " WHERE cromwell_workflow_status_1 not in (?,?,?)" ;
        List<Map<String, Object>> sampleList = jdbcTemplate.queryForList(
            selectCancerSql,
            new Object[]{"Succeeded", "Failed", "Aborted"});

        String updateWorkflowStatusSql = "UPDATE shca_project_cancer "
            + " SET cromwell_workflow_status_1=? "
            + " update_username=?, update_time=? "
            + " WHERE id=? ";
        sampleList.stream().forEach(sample -> {
            String workflowStatus = support.getWorkFlowStatusByWorkFlowId((String)sample.get("cromwell_workflow_id_1"));
            jdbcTemplate.update(
                updateWorkflowStatusSql,
                new Object[]{ workflowStatus, "CromwellService.summary",
                    LocalDateTime.now(), sample.get("id") });
        });
    }

    private List<Map<String, Object>> getReadyCheckQcProject() {
        String readyToSummarySql = "SELECT * FROM shca_project_project WHERE status='running_step3' ";
        return jdbcTemplate.queryForList(readyToSummarySql);
    }
}
