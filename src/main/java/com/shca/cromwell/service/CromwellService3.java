package com.shca.cromwell.service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import jakarta.annotation.Resource;
import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.shca.support.SysConfigManager;
import com.shca.util.ConvertUtils;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.Cmd;
import com.ywang.utils.DbUtils;
import com.ywang.utils.LogUtils;

public class CromwellService3 {

    @Resource private CromwellClient client;
    @Resource private Db db;
    @Resource private SysConfigManager config;

    private Map<String, ProcessService> processServiceMap;
    private Map<String, QcSummaryService> qcSummaryServiceMap;
    // private List<Map<String, Object>> queue = new ArrayList<>();
    private static final Logger LOGGER = Logger.getLogger(CromwellService3.class);

    public CromwellService3(DataSource dataSource) {
    }

    public CromwellService3(String s) {
    }

    public void setProcessServiceMap(Map<String, ProcessService> processServiceMap) {
        this.processServiceMap = processServiceMap;
    }

    public void setQcSummaryServiceMap(Map<String, QcSummaryService> qcSummaryServiceMap) {
        this.qcSummaryServiceMap = qcSummaryServiceMap;
    }

    // public void setClient(CromwellClient client) {
    //     this.client = client;
    // }

    public void task() {
        try {
            LOGGER.info("CromwellService3 task() starting ...");
            LOGGER.info("check and do file transfer process");
            fileTransfer();

            LOGGER.info("check and do mutual mutual process");
            Map<String, Object> ret = mutual();
            LOGGER.info("mutual return result is : " + ret.toString());

            // make sure do mutual and other process first
            if (ret.get("continue") != null && (boolean) ret.get("continue")) {
                LOGGER.info("check and do generic process");
                generic();
            }

            LOGGER.info("check and do summary process");
            summary();
            LOGGER.info("check and do upload process");
            upload();

            LOGGER.info("CromwellService finished");
        } catch (Exception e) {
            LOGGER.error("CromwellService task() error", e);
        }
    }

    public void fileTransfer() {

        String streamSql = "SELECT id, project_name, project_sn, sample_name_0,sample_name_1, cancer_kind " +
            "FROM v_shca_project_project " +
            "WHERE is_filing=? and status=? and cromwell_workflow_id_2 is null ORDER BY priority DESC, create_time ASC " ;
        List<Map<String, Object>> streamProjects = db.select(streamSql, '0', "finished");
        LOGGER.info("Projects count to be transfered is : " + streamProjects.size());

        // WORKFLOW_NAME, WORKFLOW_STATUS
        String findRunningFileTransferSql = "select count(*) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                                            "where WORKFLOW_NAME=? and WORKFLOW_STATUS=? and START_TIMESTAMP >= ?";
        int fileTransferCnt = db.selectUnique(
            findRunningFileTransferSql,
            new IntegerCallback(), "fileTransfer", "Running", LocalDateTime.now().minusHours(24)
        );

        LOGGER.info("FileTransfer running task count is : " + fileTransferCnt);
        if (fileTransferCnt > 0) {
            return ;
        }

        if (!streamProjects.isEmpty()) {
            LOGGER.info("starting file transfer project no is : " + (String)streamProjects.get(0).get("project_sn"));
            // fileTransfer((String)(streamProjects.get(0).get("project_sn")));
            try {
                int index =0;
                Map<String, Object> project = streamProjects.get(index);
                _fileTransfer(project);
                while ("peixirna".equals(project.get("cancer_kind")) && index < 10) {
                    index += 1;
                    project = streamProjects.get(index);
                    _fileTransfer(project);
                }
            } catch (Exception e) {
                LOGGER.error("fileTransfer error", e);
            }
        }
    }

    // http://172.100.125.149:8080/shca/service-cromwellService_fileTransfer.json?project_no=
    public Map<String, Object> fileTransfer(@MethodParam(name = "project_no") String projectNo) {
        Map<String, Object> ret = new HashMap<>();
        LOGGER.info("entering fileTransfer method, fileTransfer project no is : " + projectNo);
        // int count = db.selectUnique( "select count(*) from v_shca_project_project a " +
        //         " inner join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_2=b.WORKFLOW_EXECUTION_UUID " +
        //         " where b.workflow_status not in (?,?,?)", new IntegerCallback(), "Succeeded", "Failed", "Aborted");

        // sneaker@20220827  add project_sn into the project Object, so can be used in QcSummaryHandler.isQualified()
        Map<String, Object> project = db.selectUnique("select id, project_name, project_sn, sample_name_0, sample_name_1, cancer_kind " +
                " FROM v_shca_project_project " +
                " where project_sn=? and create_time >=? and status=? and is_filing=? order by priority desc",
                projectNo, ConvertUtils.str2Date("2021-08-01"), "finished", '0');

        LOGGER.info("Project is : " + project);
        if (CollectionUtils.isEmpty(project)) {
            ret.put("errcode", 2000);
            ret.put("errmsg", "项目未匹配到");
            return ret;
        }

        try {
            ret = _fileTransfer(project);
            ret.put("project", project);
        } catch (Exception e){
            LOGGER.error("_fileTransfer error", e);
            _createOrUpdateFileRecord(project, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")), "create_error");
            ret.put("errcode", 9998);
            ret.put("errmsg", e.getMessage());
            return ret;
        }
        return ret;
    }

    private Map<String, Object> _fileTransfer(Map<String, Object> project) throws Exception {

        LOGGER.info("fileTransfer[0]: " + project.toString());
        String projectName = (String) project.get("project_name");
        String sampleName0 = (String) project.get("sample_name_0");
        String sampleName1 = (String) project.get("sample_name_1");
        // String projectSn = (String) project.get("project_sn");
        String cancerName = (String) project.get("cancer_kind");
        if ("breast_cancer".equals(cancerName)) {
            cancerName = "breast";
        }
        if ("colorectal_cancer".equals(cancerName)) {
            cancerName = "colon";
        }
        if ("ctDNA_pan-cancer".equals(cancerName)) {
            cancerName = "ctDNA";
        }

        Map<String, Object> ret = new HashMap<>();

        // File workflowSource = new File("/myTools/geneAnalysisPipeline/fileTransfer-ver1125.wdl");
        // File workflowSource = new File("/myTools/geneAnalysisPipeline/templateFlows/SimpleFileTransfer.wdl");
        File workflowSource = new File("/myTools/geneAnalysisPipeline/templateFlows/AutoFileTransfer-ver10.wdl");
        if (!workflowSource.exists()) {
            ret.put("errcode", 10);
            ret.put("errmsg", "WDL文件不存在: " + workflowSource.getAbsolutePath());
            return ret;
        }
        try {
            JSONObject simpleTransferObject = new JSONObject();
            simpleTransferObject.put("fileTransfer.ACTION_WF", "create");
            File targetDir = new File("/CloudSvr01/analysisSourceFiles/" + projectName);
            if (targetDir.exists()) {
                Cmd.exec("rm -rf " + targetDir.getAbsolutePath());
            }
            simpleTransferObject.put("fileTransfer.targetPath_WF", targetDir.getAbsolutePath());

            // SysConfigManager config = SysConfigManager.getInstance();
            // String sampleDir = config.getValue("workspace.sample_path") + "/";
            String projectDir = config.getValue("workspace.project_path") + "/" + projectName;
            String homePath = config.getValue("workspace.home_path");
            LOGGER.info("homepath -> "+ homePath);
            LOGGER.info("project dir -> " + projectDir);
            simpleTransferObject.put("fileTransfer.homePath_WF", homePath);
            simpleTransferObject.put("fileTransfer.projectName_WF", projectName);
            simpleTransferObject.put("fileTransfer.cancerName_WF", cancerName);
            if (cancerName.endsWith("peixi") || cancerName.startsWith("peixi")) {
                simpleTransferObject.put("fileTransfer.sampleName_WF", sampleName0);
                simpleTransferObject.put("fileTransfer.normalSampleName_WF", "");
                simpleTransferObject.put("fileTransfer.tumorSampleName_WF", "");
            } else {
                simpleTransferObject.put("fileTransfer.sampleName_WF", "");
                simpleTransferObject.put("fileTransfer.normalSampleName_WF", sampleName0);
                simpleTransferObject.put("fileTransfer.tumorSampleName_WF", sampleName1);
            }

            String dateSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
            File prettyJson0 = new File(projectDir + "/fileTransfer_" + projectName + "_" + dateSuffix + ".json");
            try (BufferedWriter prettyWriter0 = new BufferedWriter(new FileWriter(prettyJson0))) {
                prettyWriter0.write(JSON.toJSONString(simpleTransferObject, SerializerFeature.PrettyFormat).replaceAll("\\t", "    "));
            }
            WorkflowsResponse response0 = client.send(new WorkflowsRequest(workflowSource, prettyJson0, null, null));
            ret.put("response0", response0);
            _createOrUpdateFileRecord(project, response0.getId(),"create");
        } catch (IOException e) {
            LOGGER.error("_fileTransfer error", e);
            return ret;
        }
        ret.put("errcode", 0);
        ret.put("errmsg", "ok");

        return ret;
    }

    public Map<String, Object> generic() {
        Map<String, Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "单样本分析错误: ";

        // _prioritySync();

        try {
            // errCode = 1010;
            // 9000 is too randomized, and not reasonable
            // also with 'count(distinct project_name)', I guess it will always be 1, what's
            // the purpose of that?
            List<Map<String, Object>> cancers = db.select("SELECT id, sample_name, cancer_kind " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR1 "
                    +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR2 "
                    +
                    " ,(( select count( DISTINCT project_name ) from shca_project_project b where (b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name) and status=? )* 9000 + a.priority) _priority "
                    +
                    " FROM shca_project_cancer a where status=? order by _priority desc ", "R1", "successed", "R2",
                    "successed", "wait_sample", "wait_running");

            LOGGER.info("generic-process[" + (cancers.size()) + "]...");
            ret.put("total", cancers.size());

            Map<String, Integer> jobQueueStatus = _getAvailableSlotsNum(config);
            int availableSlotsNum = jobQueueStatus.get("availableSlotsNum");
            int jobNumsToLanuch = cancers.size() < availableSlotsNum ? cancers.size() : availableSlotsNum;
            LOGGER.info("generic-process, job to launch is [" + jobNumsToLanuch + "]");
            ret.put("current", jobQueueStatus.get("currentRunningJobNum"));
            ret.put("max", jobQueueStatus.get("maxCapacityOfJobQueue"));

            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> errorList = new ArrayList<>();
            ret.put("success", successList);
            ret.put("errors", errorList);

            for (int i = 0; i < jobNumsToLanuch; i++) {
                Map<String, Object> cancer = cancers.get(i);
                try {

                    String sampleName = (String) cancer.get("sample_name");
                    String cancerKind = (String) cancer.get("cancer_kind");
                    LOGGER.info("generic-process, -> sampleName [" + sampleName + "]");
                    LOGGER.info("generic-process, -> sampleName [" + cancerKind + "]");
                    Map<String, Object> cromwellConfig = db.selectUnique(
                            "SELECT cancer_script, cromwell_workflow_source, cromwell_workflow_dependencies, cromwell_workflow_inputs "
                                    +
                                    " FROM shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc",
                            cancerKind, true, 0);  // script_step ???   auto_script is 1   cromwellConfig actually is the cromwell status of some sample

                    ProcessService processService = processServiceMap.get(cancerKind);
                    LOGGER.info("generic-process, -> processServicey [" + processService.getClass().getSimpleName() + "]");

                    if (Objects.nonNull(cromwellConfig)) {
                        LOGGER.info("generic-process[" + sampleName + "]: start workflow...");
                        File rawDataR1 = new File((String) cancer.get("rawDataR1"));
                        File rawDataR2 = new File((String) cancer.get("rawDataR2"));
                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));  // path of generalTemplate.wdl
                        File workflowOptionsJson = _getWorkflowOptionJson(config, cromwellConfig);

                        WorkflowsResponse response = processService.generic(
                                sampleName, rawDataR1, rawDataR2,
                                (String) cromwellConfig.get("cromwell_workflow_inputs"),
                                source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")),
                                workflowOptionsJson);

                        cancer.put("cancer_script_0", cromwellConfig.get("cancer_script"));
                        cancer.put("cromwell_workflow_id_0", response.getId());
                        cancer.put("cromwell_workflow_status_0", response.getStatus());

                        db.update(
                                "update shca_project_cancer set status=?,cromwell_workflow_id_0=?,cromwell_workflow_status_0=? "
                                        +
                                        " ,cancer_script_0=?,update_username=?,update_time=? where id=? ",
                                "running_step0", response.getId(), response.getStatus(),
                                cromwellConfig.get("cancer_script"), "CromwellService.generic", now, cancer.get("id"));

                        successList.add(cancer);
                        LOGGER.info("[" + sampleName + "] start workflow - " + response.getId());

                    }
                } catch (Exception e) {
                    errorList.add(cancer);
                    LOGGER.error("generic-process: " + LogUtils.toString(e));
                }
            }

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");
            return ret;
        } catch (Exception e) {
            LOGGER.error("generic-process: " + LogUtils.toString(e));

            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());

            return ret;
        }
    }

    public Map<String, Object> mutual() {
        Map<String, Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "对比分析错误: ";

        try {
            List<Map<String, Object>> cancers = db.select(
                    "SELECT id, cromwell_workflow_id_0 FROM shca_project_cancer WHERE status=? ", "running_step0");

            LOGGER.info("generic-process[" + cancers.size() + "]: workflow status update...");

            for (Map<String, Object> cancer : cancers) {
                try {
                    String workflowId = (String) cancer.get("cromwell_workflow_id_0");
                    String workflowStatus = db
                            .selectUnique("SELECT WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                                    " WHERE WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                    if (StringUtils.hasLength(workflowStatus)) {
                        continue;
                    }

                    String status = "running_step0";
                    if ("Succeeded".equals(workflowStatus)) {
                        status = "finished";
                    }
                    if ("Failed".equals(workflowStatus)) {
                        status = "failed";
                    }
                    if ("Aborted".equals(workflowStatus)) {
                        status = "aborted";
                    }

                    db.update(
                            "UPDATE shca_project_cancer set cromwell_workflow_status_0=?,status=?,update_username=?,update_time=? WHERE id=? ",
                            workflowStatus, status, "CromwellService3.mutual", now, cancer.get("id"));
                } catch (Exception e) {
                    LOGGER.error("mutual-process: " + LogUtils.toString(e));
                }
            }

            db.update(  // priority update should be added into the quartz list.
                    "UPDATE shca_project_project a set priority=ifnull((select priority from shca_project_project_priority b where b.project_name=a.project_name), priority) ");

            List<Map<String, Object>> projects = db.select("SELECT a.id, a.project_sn, a.project_no, a.project_name, " +
                    " a.sample_name_0, a.sample_name_1, a.cancer_kind, a.cancer_script_0 " +
                    " FROM shca_project_project a " +
                    " INNER JOIN shca_project_cancer b ON a.sample_name_0=b.sample_name " +
                    " LEFT JOIN shca_project_cancer c ON a.sample_name_1=c.sample_name " +
                    " WHERE a.status=? and b.status=? and (a.sample_name_1 is null or c.status=?) order by a.priority desc ",
                    "wait_sample", "finished", "finished");

            LOGGER.info("mutual-process[" + (projects.size()) + "]...");
            ret.put("total", projects.size());

            // SysConfigManager config = SysConfigManager.getInstance();
            int max = ConvertUtils.integer(config.getValue("cromwell.max_workflow_count"), 40);
            int current = db.selectUnique("select count(*) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                    " where WORKFLOW_NAME=? and  WORKFLOW_STATUS not in (?,?,?)", new IntegerCallback(),
                    "generalProcess", "Succeeded", "Failed", "Aborted");

            ret.put("current", current);
            ret.put("max", max);

            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> errorList = new ArrayList<>();
            ret.put("success", successList);
            ret.put("errors", errorList);
            ret.put("continue", false);

            max = max - current;
            if (max > projects.size()) {
                max = projects.size();
                ret.put("continue", true);
            }

            for (int i = 0; i < max; i++) {
                Map<String, Object> project = projects.get(i);
                try {
                    String cancerKind = (String) project.get("cancer_kind");

                    Map<String, Object> cromwellConfig;
                    if (StringUtils.hasLength((String) project.get("cancer_script_0"))) {
                        cromwellConfig = db.selectUnique(
                                "SELECT cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs "
                                    +
                                    " FROM shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc",
                                cancerKind, true, 1);
                    } else {
                        cromwellConfig = db.selectUnique(
                                "SELECT cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs "
                                    +
                                    " FROM shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ",
                                cancerKind, project.get("cancer_script_0"), 1);
                    }
                    if (Objects.isNull(cromwellConfig))  {
                        throw new Exception("cromwell Config is null");
                    }

                    ProcessService processService = processServiceMap.get(cancerKind);

                    if (cromwellConfig != null && processService != null) {
                        LOGGER.info("mutect-process[" + project.get("project_sn") + "][" + project.get("project_name")
                                + "]: start workflow...");

                        File workflowOptionsJson = _getWorkflowOptionJson(config, cromwellConfig);
                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));

                        WorkflowsResponse response = processService.mutual((String) project.get("project_sn"),
                                (String) project.get("project_name"), (String) project.get("sample_name_0"),
                                (String) project.get("sample_name_1"),
                                (String) cromwellConfig.get("cromwell_workflow_inputs"), source,
                                new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), workflowOptionsJson);

                        project.put("cancer_script_0", cromwellConfig.get("cancer_script"));
                        project.put("cromwell_workflow_id_0", response.getId());
                        project.put("cromwell_workflow_status_0", response.getStatus());

                        db.update(
                                "UPDATE shca_project_project SET status=?, cromwell_workflow_id_0=?, cromwell_workflow_status_0=?"
                                        +
                                        " ,cancer_script_0=?, update_username=?, update_time=? WHERE id=? ",
                                "running_step1", response.getId(), response.getStatus(),
                                cromwellConfig.get("cancer_script"), "CromwellService2.mutual", now, project.get("id"));

                        LOGGER.info("mutect-process[" + project.get("project_sn") + "][" + project.get("project_name")
                                + "]: start workflow - " + response.getId());

                        successList.add(project);
                    } else {
                        LOGGER.error("mutect-process[" + project.get("project_sn") + "][" + project.get("project_name")
                                + "]: cromwell config is null");
                    }
                } catch (Exception e) {
                    errorList.add(project);
                    LOGGER.error("mutect-process: " + LogUtils.toString(e));
                }
            }

            // 修复异常状态
            // @sneaker  修复sample分析没有cromwel workflow id的时候，project状态已经变成running_step2了，重设回running_step1
            // 但为何会出现这个异常状态呢？？？
            db.update("UPDATE shca_project_project SET status=?" +
                    " WHERE ( sample_name_0 in (select sample_name from shca_project_cancer where cromwell_workflow_id_1 is null) " +
                    " or sample_name_1 in (select sample_name from shca_project_cancer where cromwell_workflow_id_1 is null) ) " +
                    " and status=?", "running_step1", "running_step2");

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error("mutect-process: " + LogUtils.toString(e));
            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    public Map<String, Object> summary() {
        Map<String, Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "评估报告分析错误: ";

        try {
            List<Map<String, Object>> projects = db.select(
                    "SELECT a.id, a.cromwell_workflow_id_0, a.sample_name_0, a.sample_name_1 " +
                        " , a.project_sn, a.project_no, a.project_name, a.cancer_kind " +
                        " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR1_0 "
                        +
                        " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR2_0 "
                        +
                        " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR1_1 "
                        +
                        " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR2_1 "
                        +
                        " FROM shca_project_project a  " +
                        " WHERE a.status=? ",
                    "R1", "successed", "R2", "successed", "R1", "successed", "R2", "successed", "running_step1");

            LOGGER.info("summary-process[" + projects.size() + "]...");

            // SysConfigManager config = SysConfigManager.getInstance();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> errorList = new ArrayList<>();
            ret.put("success", successList);
            ret.put("errors", errorList);

            for (Map<String, Object> project : projects) {
                try {
                    String workflowId = (String) project.get("cromwell_workflow_id_0");
                    String workflowStatus = db
                            .selectUnique("select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                                    " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                    if (StringUtils.hasLength(workflowStatus)) {
                        continue;
                    }

                    String status = "running_step1";
                    if ("Succeeded".equals(workflowStatus)) {
                        status = "running_step2";
                        Map<String, Object> cromwellConfig = db.selectUnique(
                                "select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs "
                                        +
                                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc",
                                project.get("cancer_kind"), true, 2);

                        ProcessService processService = processServiceMap.get((String) project.get("cancer_kind"));

                        if (cromwellConfig != null && processService != null) {
                            LOGGER.info(
                                    "summary-process[" + project.get("project_sn") + "][" + project.get("project_name")
                                            + "][" + project.get("sample_name_0") + "]: start workflow...");

                            File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                            File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                            if (!options.exists()) {
                                options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                            }
                            if (!options.exists()) {
                                options = null;
                            }

                            WorkflowsResponse response = processService.summary((String) project.get("project_sn"),
                                    (String) project.get("project_name"), (String) project.get("sample_name_0"),
                                    new File((String) project.get("rawDataR1_0")),
                                    new File((String) project.get("rawDataR2_0")),
                                    (String) cromwellConfig.get("cromwell_workflow_inputs"), source,
                                    new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                            Map<String, Object> cancer = new HashMap<>();
                            cancer.put("project_sn", project.get("project_sn"));
                            cancer.put("project_name", project.get("project_name"));
                            cancer.put("sample_name", project.get("sample_name_0"));
                            cancer.put("cancer_script_1", cromwellConfig.get("cancer_script"));
                            cancer.put("cromwell_workflow_id_1", response.getId());
                            cancer.put("cromwell_workflow_status_1", response.getStatus());
                            successList.add(cancer);

                            db.update(
                                    "update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_workflow_status_1=?,cancer_script_1=?"
                                            +
                                            " ,update_username=?,update_time=? where sample_name=? ",
                                    response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"),
                                    "CromwellService3.summary", now, project.get("sample_name_0"));

                            LOGGER.info("summary-process[" + project.get("project_sn") + "]["
                                    + project.get("project_name") + "][" + project.get("sample_name_0")
                                    + "]: start workflow - " + response.getId());

                            if (StringUtils.hasLength((String) project.get("sample_name_1"))) { // 单样本兼容
                                LOGGER.info("summary-process[" + project.get("project_sn") + "]["
                                        + project.get("project_name") + "][" + project.get("sample_name_1")
                                        + "]: start workflow...");

                                response = processService.summary((String) project.get("project_sn"),
                                        (String) project.get("project_name"), (String) project.get("sample_name_1"),
                                        new File((String) project.get("rawDataR1_1")),
                                        new File((String) project.get("rawDataR2_1")),
                                        (String) cromwellConfig.get("cromwell_workflow_inputs"), source,
                                        new File((String) cromwellConfig.get("cromwell_workflow_dependencies")),
                                        options);
                                cancer = new HashMap<>();
                                cancer.put("project_sn", project.get("project_sn"));
                                cancer.put("project_name", project.get("project_name"));
                                cancer.put("sample_name", project.get("sample_name_1"));
                                cancer.put("cancer_script_1", cromwellConfig.get("cancer_script"));
                                cancer.put("cromwell_workflow_id_1", response.getId());
                                cancer.put("cromwell_workflow_status_1", response.getStatus());
                                successList.add(cancer);

                                db.update(
                                        "update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_workflow_status_1=?,cancer_script_1=?"
                                                +
                                                " ,update_username=?,update_time=? where sample_name=? ",
                                        response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"),
                                        "CromwellService3.summary", now, project.get("sample_name_1"));
                                LOGGER.info("summary-process[" + project.get("project_sn") + "]["
                                        + project.get("project_name") + "][" + project.get("sample_name_1")
                                        + "]: start workflow - " + response.getId());
                            }
                        }
                    }

                    if ("Failed".equals(workflowStatus)) {
                        status = "failed";
                    }
                    if ("Aborted".equals(workflowStatus)) {
                        status = "aborted";
                    }

                    db.update(
                            "update shca_project_project set cromwell_workflow_status_0=?,status=?,update_username=?,update_time=? where id=? ",
                            workflowStatus, status, "CromwellService3.summary", now, project.get("id"));
                } catch (Exception e) {
                    errorList.add(project);
                    LOGGER.error("summary-process: " + LogUtils.toString(e));
                }
            }

            List<Map<String, Object>> cancers = db.select(
                    "select id,cromwell_workflow_id_1 from shca_project_cancer where cromwell_workflow_status_1 not in (?,?,?) ",
                    "Succeeded", "Failed", "Aborted");
            LOGGER.info("summary-process[" + cancers.size() + "]: workflow status update...");
            for (Map<String, Object> cancer : cancers) {
                try {
                    // String workflowId = (String) cancer.get("cromwell_workflow_id_1");
                    // String workflowStatus = db.selectUnique( "select WORKFLOW_STATUS from
                    // cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                    // " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                    db.update(
                            "update shca_project_cancer set cromwell_workflow_status_1=ifnull((select WORKFLOW_STATUS " +
                            "from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                            "where WORKFLOW_EXECUTION_UUID=cromwell_workflow_id_1),cromwell_workflow_status_1),update_username=?,update_time=? where id=? ",
                            "CromwellService3.summary", now, cancer.get("id"));
                } catch (Exception e) {
                    LOGGER.error("summary-process: " + LogUtils.toString(e));
                }
            }

            List<Map<String, Object>> successListForQcSummary = new ArrayList<>();
            List<Map<String, Object>> errorListForQcSummary = new ArrayList<>();
            ret.put("qc_success", successListForQcSummary);
            ret.put("qc_errors", errorListForQcSummary);

            projects = db.select(
                    "select a.id,b.cromwell_workflow_status_1 workflow_status_0,c.cromwell_workflow_status_1 workflow_status_1,a.cancer_kind "
                            +
                            " ,sample_name_0,sample_name_1,a.project_no,a.project_sn,a.project_name " +
                            " from shca_project_project a " +
                            " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                            " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                            " where a.status=?",
                    "running_step2");
            for (Map<String, Object> project : projects) {
                try {
                    if ("Succeeded".equals(project.get("workflow_status_0"))
                            && ("Succeeded".equals(project.get("workflow_status_1"))
                                    || project.get("sample_name_1") == null)) {

                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?",
                                "running_step3", "CromwellService2.summary", now, project.get("id"));

                        QcSummaryService qcSummaryService = qcSummaryServiceMap
                                .get((String) project.get("cancer_kind"));
                        if (qcSummaryService != null) {
                            File qcSummaryFile = new File(
                                    config.getValue("workspace.project_path") + "/" + project.get("project_name")
                                            + "/qc_summary/" + project.get("sample_name_0") + "_summary.txt");
                            List<QcSummary> qcSummaries = qcSummaryService.qcSummary(qcSummaryFile);
                            db.delete("delete from shca_project_project_qcsummary where project_sn=? and sample_name=?",
                                    project.get("project_sn"), project.get("sample_name_0"));
                            for (QcSummary qcSummary : qcSummaries) {
                                db.insert(
                                        "insert into shca_project_project_qcsummary(project_sn,project_no,sample_name,qc_name,qc_value,qc_reference,qc_qualified,create_username,create_time,update_time,update_username)"
                                                +
                                                " values(?,?,?,?,?,?,?,?,?,?,?)",
                                        project.get("project_sn"), project.get("project_no"),
                                        project.get("sample_name_0"), qcSummary.getName(), qcSummary.getValue(),
                                        qcSummary.getReference(), qcSummary.getQualified(), "CromwellService2.summary",
                                        now, now, "CromwellService2.summary");
                            }

                            Map<String, Object> cancer = new HashMap<>();
                            cancer.put("project_sn", project.get("project_sn"));
                            cancer.put("project_name", project.get("project_name"));
                            cancer.put("sample_name", project.get("sample_name_0"));
                            successListForQcSummary.add(cancer);

                            if (StringUtils.hasLength((String) project.get("sample_name_1"))) { // 单样本兼容
                                qcSummaryFile = new File(
                                        config.getValue("workspace.project_path") + "/" + project.get("project_name")
                                                + "/qc_summary/" + project.get("sample_name_1") + "_summary.txt");
                                qcSummaries = qcSummaryService.qcSummary(qcSummaryFile);
                                db.delete(
                                        "delete from shca_project_project_qcsummary where project_sn=? and sample_name=?",
                                        project.get("project_sn"), project.get("sample_name_1"));
                                for (QcSummary qcSummary : qcSummaries) {
                                    db.insert(
                                            "insert into shca_project_project_qcsummary(project_sn,project_no,sample_name,qc_name,qc_value,qc_reference,qc_qualified,create_username,create_time,update_time,update_username)"
                                                    +
                                                    " values(?,?,?,?,?,?,?,?,?,?,?)",
                                            project.get("project_sn"), project.get("project_no"),
                                            project.get("sample_name_1"), qcSummary.getName(), qcSummary.getValue(),
                                            qcSummary.getReference(), qcSummary.getQualified(),
                                            "CromwellService2.summary", now, now, "CromwellService2.summary");
                                }

                                cancer = new HashMap<>();
                                cancer.put("project_sn", project.get("project_sn"));
                                cancer.put("project_name", project.get("project_name"));
                                cancer.put("sample_name", project.get("sample_name_1"));
                                successListForQcSummary.add(cancer);
                            }
                        }
                    }

                    if ("Failed".equals(project.get("workflow_status_0"))
                            || "Failed".equals(project.get("workflow_status_1"))) {
                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?",
                                "failed", "CromwellService2.summary", now, project.get("id"));
                    }
                    if ("Aborted".equals(project.get("workflow_status_0"))
                            || "Aborted".equals(project.get("workflow_status_1"))) {
                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?",
                                "aborted", "CromwellService2.summary", now, project.get("id"));
                    }
                } catch (Exception e) {
                    errorListForQcSummary.add(project);
                    LOGGER.error("summary-process: " + LogUtils.toString(e));
                }
            }

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error("summary-process: " + LogUtils.toString(e));

            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());

            return ret;
        }
    }

    public Map<String, Object> upload() {
        Map<String, Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "上传报告分析错误: ";

        try {
            List<Map<String, Object>> projects = db.select(
                    "select a.id,a.project_no,a.project_name,a.sample_name_0,a.sample_name_1,a.cancer_kind,a.cancer_script_0 "
                            +
                            " ,a.project_sn,a.cancer_script_1,lims " +
                            " from shca_project_project a " +
                            " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                            " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                            " where a.status=? ",
                    "running_step3");
            LOGGER.info("upload-process[" + projects.size() + "]... ");
            ret.put("total", projects.size());

            // SysConfigManager config = SysConfigManager.getInstance();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> errorList = new ArrayList<>();
            ret.put("success", successList);
            ret.put("errors", errorList);

            for (Map<String, Object> project : projects) {
                try {
                    String cancerKind = (String) project.get("cancer_kind");

                    Map<String, Object> cromwellConfig;
                    if (StringUtils.hasLength((String) project.get("cancer_script_1"))) {
                        cromwellConfig = db.selectUnique(
                                "select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs "
                                        +
                                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc",
                                cancerKind, true, 3);
                    } else {
                        cromwellConfig = db.selectUnique(
                                "select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs "
                                        +
                                        " from shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ",
                                cancerKind, project.get("cancer_script_1"), 3);
                    }

                    if (Objects.isNull(cromwellConfig)) {
                        throw new Exception("cromwell config is null");
                    }

                    ProcessService processService = processServiceMap.get(cancerKind);
                    if (cromwellConfig != null && processService != null) {
                        LOGGER.info("upload-process[" + project.get("project_no") + "][" + project.get("project_name")
                                + "]: start workflow...");
                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                        if (!options.exists()) {
                            options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                        }
                        if (!options.exists()) {
                            options = null;
                        }

                        /**
                         *
                         * 2021-01-21 添加纯化质检浓度值
                         * {"code":{"BZ2003899B01":["BZ2003899B01"],"BZ2003899K01":["BZ2003899K01"]},"identification":"null,null","productId":"BZ2003899","runningNumber":"201123001","concentration":"58.0,55.0","identityCard":"","state":"0,0","productMask":"BZ510"}
                         */
                        String lims = (String) project.get("lims");
                        String workflowInputs = (String) cromwellConfig.get("cromwell_workflow_inputs");
                        if (StringUtils.hasLength(lims)) {
                            JSONObject jsonObject = JSON.parseObject(workflowInputs);
                            JSONObject limsJson = JSONObject.parseObject(lims);
                            String concentration = limsJson.getString("concentration");
                            String[] array = concentration.split(",");

                            jsonObject.put("geneFileUpload.Bconc_WF", ConvertUtils.dou(array[0]));
                            if (array.length == 2) {
                                jsonObject.put("geneFileUpload.Kconc_WF", ConvertUtils.dou(array[1]));
                            }

                            workflowInputs = jsonObject.toJSONString();
                        }

                        WorkflowsResponse response = processService.upload((String) project.get("project_sn"),
                                (String) project.get("project_name"), (String) project.get("sample_name_0"),
                                (String) project.get("sample_name_1"), workflowInputs, source,
                                new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);
                        project.put("cancer_script_1", cromwellConfig.get("cancer_script"));
                        project.put("cromwell_workflow_id_1", response.getId());
                        project.put("cromwell_workflow_status_1", response.getStatus());

                        db.update(
                                "update shca_project_project set status=?,cromwell_workflow_id_1=?,cromwell_workflow_status_1=?"
                                        +
                                        " ,cancer_script_1=?,update_username=?,update_time=? where id=? ",
                                "running_step4", response.getId(), response.getStatus(),
                                cromwellConfig.get("cancer_script"), "CromwellService2.upload", now, project.get("id"));

                        LOGGER.info("upload-process[" + project.get("project_no") + "][" + project.get("project_name")
                                + "]: start workflow - " + response.getId());
                    } else {
                        LOGGER.error("upload-process[" + project.get("project_sn") + "][" + project.get("project_name")
                                + "]: cromwell config is null or process service is null");
                    }
                } catch (Exception e) {
                    errorList.add(project);
                    LOGGER.error("mutect-process: " + LogUtils.toString(e));
                }
            }

            projects = db.select("select a.id,a.project_no,a.cancer_kind,a.cromwell_workflow_id_1 " +
                    " ,a.project_sn,a.cancer_script_1 " +
                    " from shca_project_project a " +
                    " where a.status=?", "running_step4");
            LOGGER.info("upload-process[" + projects.size() + "]: workflow status update...");
            for (Map<String, Object> project : projects) {
                String workflowId = (String) project.get("cromwell_workflow_id_1");
                String workflowStatus = db
                        .selectUnique("select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                                " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                if (StringUtils.hasLength(workflowStatus)) {
                    continue;
                }

                String status = "running_step4";
                if ("Succeeded".equals(workflowStatus)) {
                    status = "finished";
                }
                if ("Failed".equals(workflowStatus)) {
                    status = "failed";
                }
                if ("Aborted".equals(workflowStatus)) {
                    status = "aborted";
                }

                QcSummaryService qcSummaryService = qcSummaryServiceMap.get((String) project.get("cancer_kind"));
                File projectDir = new File(
                        config.getValue("workspace.project_path") + "/" + project.get("project_name"));
                qcSummaryService.backup(projectDir);

                db.update(
                        "update shca_project_project set status=?, cromwell_workflow_status_1=?,update_username=?,update_time=? where id=? ",
                        status, workflowStatus, "CromwellService3.upload", now, project.get("id"));
            }

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error("mutect-process: " + LogUtils.toString(e));

            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());

            return ret;
        }
    }

    private Map<String, Integer> _getAvailableSlotsNum(SysConfigManager config) {
            int maxCapacityOfJobQueue = ConvertUtils.integer(config.getValue("cromwell.max_workflow_count"), 35);

        int currentRunningJobNum = db.selectUnique(
                "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                        " WHERE WORKFLOW_NAME=? AND  WORKFLOW_STATUS not in (?,?,?)",
                new IntegerCallback(), "generalProcess", "Succeeded", "Failed", "Aborted");

        Map<String, Integer> jobQueueStatus = new HashMap<>();
        jobQueueStatus.put("maxCapacityOfJobQueue", maxCapacityOfJobQueue);
        jobQueueStatus.put("currentRunningJobNum", currentRunningJobNum);
        jobQueueStatus.put("availableSlotsNum", maxCapacityOfJobQueue - currentRunningJobNum);

        return jobQueueStatus;

    }

    private File _getWorkflowOptionJson(SysConfigManager config,
            Map<String, Object> cromwellConfig) {

            // get options json file, now this file is no longer exist
            File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
            // File workflowOptionsJson;
            File optionsWithWDL = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json"); // 不存在这个配置文件
            File optionsWithConfig = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");

        if (optionsWithWDL.isFile()) {
            return optionsWithWDL;
        }
        if (optionsWithConfig.isFile()) {
            return optionsWithConfig;
        }
        throw new IllegalArgumentException("workflow_options.json not found");
    }

    private long _createOrUpdateFileRecord( Map<String, Object> project,
        String cromwell_workflow_id, String operType){

        Map<String,Object> transferFileMap = db.selectUnique("SELECT * FROM shca_project_project_transfer_file " +
            " WHERE project_id = ?"
            ,project.get( "id"));
        if(CollectionUtils.isEmpty(transferFileMap)){
            transferFileMap = new HashMap<>();
            transferFileMap.put("project_id",project.get( "id"));
            transferFileMap.put("project_sn",project.get( "project_sn"));
            transferFileMap.put("project_no",project.get( "project_no"));
            transferFileMap.put("project_name",project.get( "project_name"));
            transferFileMap.put("cromwell_workflow_id",cromwell_workflow_id);
            transferFileMap.put("cromwell_workflow_type",operType);

            List<Object> paramsList = new ArrayList<>();
            String sql = DbUtils.insertSql("shca_project_project_transfer_file",transferFileMap,paramsList);
            long id = db.insert(sql,paramsList.toArray(new Object[0]));
            return id;
        } else {
            int cnt = db.update( "update shca_project_project_transfer_file set cromwell_workflow_id=?,cromwell_workflow_type=? where project_id=? "
                    ,cromwell_workflow_id, operType, project.get( "id"));
            return cnt;
        }

    }
    // END closure of class
}
