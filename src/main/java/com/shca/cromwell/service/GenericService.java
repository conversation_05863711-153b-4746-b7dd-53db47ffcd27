package com.shca.cromwell.service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.config.SystemConfigService;

@Service
public class GenericService {
    @Resource JdbcTemplate jdbcTemplate;
    @Resource CromwellServiceSupport support;
    @Resource SystemConfigService config;
    @Resource CancerFileService cancerFileService;

    private static final Logger log = Logger.getLogger(GenericService.class);
    public void launch() {
        log.info("check and do generic process");
        cancerFileService.prepare();
        List<Map<String, Object>> readySampleList = getReadySamplesForGeneric();

        Map<String, Integer> jobQueueStatus = support.getAvailableSlotsNum(config);
        int availableSlotsNum = jobQueueStatus.get("availableSlotsNum");
        if (availableSlotsNum > 0) {
            readySampleList.stream()
                .limit(availableSlotsNum)
                .forEach(sample -> {
                    boolean result = launchGenericTask(sample);
                    if (result) {
                        updateSampleStatusToRunningStep0(sample);
                        // successList.add(sample);
                    } else {
                        // errorList.add(sample);
                        log.error("generic-process: " + sample.get("project_name"));
                    }
                    updateGenericStatusByCromwell();
                });
        }
    }

    public List<Map<String, Object>> getReadySamplesForGeneric() {
        String selectWaitRunningSamplesSql = "SELECT id, sample_name, cancer_kind, priority "
            + " FROM shca_project_cancer WHERE status=? ";
        List<Map<String, Object>> waitRunningSampleList = jdbcTemplate.queryForList(
            selectWaitRunningSamplesSql,
            new Object[] {"wait_running"});
        Comparator<Map<String, Object>> priorityComparator =
            (item1, item2) -> ((Integer)item1.get("priority")).compareTo((Integer)item2.get("priority"));
        List<Map<String, Object>>  readySampleList = waitRunningSampleList.stream()
            .filter(mapItem -> rawdataReady(mapItem, "R1"))
            .filter(mapItem -> rawdataReady(mapItem, "R2"))
            .filter(item -> support.getRawdataStorePath(item, "R1")!=null)
            .peek(item->item.put("rawDataR1", support.getRawdataStorePath(item, "R1")))
            .filter(item -> support.getRawdataStorePath(item, "R2")!=null)
            .peek(item->item.put("rawDataR2", support.getRawdataStorePath(item, "R2")))
            .sorted(priorityComparator.reversed())
            .collect(Collectors.toList());
        return readySampleList;
    }
    private boolean launchGenericTask(Map<String, Object> sampleMap) {
        String sampleName = (String) sampleMap.get("sample_name");
        String cancerName = (String) sampleMap.get("cancer_kind");
        ProcessService processService = support.getProcessService(sampleMap);
        Map<String, Object> cromwellConfig = getCromwellConfigByCanerName(cancerName);
        if (CollectionUtils.isEmpty(cromwellConfig) ) {
            return false;
        }
        // path of generalTemplate.wdl
        File source = new File( (String) cromwellConfig.get("cromwell_workflow_source"));
        File rawDataR1 = new File( (String) sampleMap.get("rawDataR1"));
        File rawDataR2 = new File( (String) sampleMap.get("rawDataR2"));
        File workflowOptionsJson = support.getWorkflowOptionJson( config, cromwellConfig);
        log.info("generic-process[" + sampleName + "]: start workflow...");
        try {
            WorkflowsResponse response = processService.generic(
                    sampleName, rawDataR1, rawDataR2,
                    (String) cromwellConfig.get("cromwell_workflow_inputs"),
                    source,
                    new File((String) cromwellConfig.get("cromwell_workflow_dependencies")),
                    workflowOptionsJson);
            sampleMap.put("cancer_script_0", cromwellConfig.get("cancer_script"));
            sampleMap.put("cromwell_workflow_id_0", response.getId());
            sampleMap.put("cromwell_workflow_status_0", response.getStatus());
            log.info("[" + sampleName + "] start workflow - " + response.getId());
        } catch (Exception e) {
            log.error("[" + sampleName + "] start workflow - " + e.getMessage());
            return false;
        }
        return true;
    }

    private void updateSampleStatusToRunningStep0(Map<String,Object> sampleMap) {
        String updateSql = "UPDATE shca_project_cancer "
            + " SET status=?, cromwell_workflow_id_0=?, cromwell_workflow_status_0=? "
            + ",cancer_script_0=?, update_username=?, update_time=? "
            + " WHERE id=? ";
        jdbcTemplate.update(
            updateSql,
            new Object[] {
                    "running_step0",
                    sampleMap.get("cromwell_workflow_id_0"),
                    sampleMap.get("cromwell_workflow_status_0"),
                    "general_script",
                    "CromwellService.generic", LocalDateTime.now(), sampleMap.get("id")
            });
    }

    private boolean rawdataReady( Map<String, Object> sampleMap, String rawdata) {
        String selectRawdataBySampleNameSql = "SELECT count(0) "
            + " FROM shca_project_cancer_file "
            + " WHERE sample_name=? AND rawdata=? AND status=? "
            + " ORDER BY create_time DESC LIMIT 1";

        @SuppressWarnings("deprecation")
        int selectCount = jdbcTemplate.queryForObject(
            selectRawdataBySampleNameSql,
            new Object[] { sampleMap.get("sample_name"), rawdata, "successed" },
            Integer.class);
        return (selectCount == 1);
    }

    private void updateGenericStatusByCromwell() {
        String selectRunningSampleSql = "SELECT id, cromwell_workflow_id_0, status "
            + " FROM shca_project_cancer WHERE status=? ";
        List<Map<String, Object>> sampleList = jdbcTemplate.queryForList(
            selectRunningSampleSql,
            new Object[] {"running_step0"});
        String updateSampleSql = "UPDATE shca_project_cancer "
            + " SET cromwell_workflow_status_0=?, status=?, update_username=?, update_time=? "
            + " WHERE id=? ";
        sampleList.stream().forEach(sampleMap -> {
            String workflowId = (String) sampleMap.get("cromwell_workflow_id_0");
            String workflowStatus = support.getWorkFlowStatusByWorkFlowId(workflowId);
            String status = (String) sampleMap.get("status");
            if ("Succeeded".equals(workflowStatus)) {
                status = "finished";
            }
            if ("Failed".equals(workflowStatus)) {
                status = "failed";
            }
            if ("Aborted".equals(workflowStatus)) {
                status = "aborted";
            }

            jdbcTemplate.update(
                updateSampleSql,
                new Object[] {
                    workflowStatus,
                    status,
                    "CromwellService.generic",
                    LocalDateTime.now(),
                    sampleMap.get("id")
                });
        });
    }
    private Map<String, Object> getCromwellConfigByCanerName(String cancerName) {
        String configSql = "SELECT * FROM shca_cromwell_config "
        + " WHERE cancer_kind=? AND script_step='0' ORDER BY id DESC LIMIT 1";
        try {
            Map<String, Object> cromwellConfig = jdbcTemplate.queryForMap(
                configSql,
                new Object[] { cancerName });
            return cromwellConfig;
        } catch (DataAccessException e) {
            log.error("getCromwellConfigByCanerName: " + e.getMessage());
            return null;
        }
    }

}
