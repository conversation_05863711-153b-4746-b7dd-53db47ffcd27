package com.shca.cromwell.service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.config.SystemConfigService;

@Service
public class MutualService {
    @Resource JdbcTemplate jdbcTemplate;
    @Resource SystemConfigService config;
    @Resource CromwellServiceSupport support;
    private static final Logger log = Logger.getLogger(MutualService.class);
    public void launch() {
        log.info("check and do mutual mutual process");
        List<Map<String, Object>> readyProjects = getReadyForMutualProjects();
        log.info("mutual-process[" + (readyProjects.size()) + "]...");
        Map<String, Integer> jobStatusMap = support.getAvailableSlotsNum(config);
        int availableJobSlot = jobStatusMap.get("availableSlotsNum");
        readyProjects.stream()
            .limit(availableJobSlot)
            .forEach( project -> {
                String cancerName = (String) project.get("cancer_kind");
                ProcessService processService = support.getProcessService(project);
                Map<String, Object> cromwellConfig = support.getCromwellConfig(cancerName, '1');
                if (CollectionUtils.isEmpty(cromwellConfig) || processService == null) {
                    // errorList.add(project);
                    return;
                }
                log.info("mutect-process[" + project.get("project_sn") + "][" + project.get("project_name")
                    + "]: start workflow...");
                File workflowOptionsJson = support.getWorkflowOptionJson(config, cromwellConfig);
                File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                try {
                    WorkflowsResponse response = processService.mutual(
                        (String) project.get("project_sn"),
                        (String) project.get("project_name"),
                        (String) project.get("sample_name_0"),
                        (String) project.get("sample_name_1"),
                        (String) cromwellConfig.get("cromwell_workflow_inputs"),
                        source,
                        null,
                        workflowOptionsJson);

                    project.put("cancer_script_0", cromwellConfig.get("cancer_script"));
                    project.put("cromwell_workflow_id_0", response.getId());
                    project.put("cromwell_workflow_status_0", response.getStatus());
                    updateProjectStatus2RunningStep1(project, response, cromwellConfig);
                } catch (Exception e) {
                    e.printStackTrace();
                }

        });
        updateStep1WorkflowStatusByCromwell();
    }

    private List<Map<String, Object>> getReadyForMutualProjects() {
        String selectWaitSampleProjects = "SELECT id, project_sn, project_no, project_name, "
            + "sample_name_0, sample_name_1, cancer_kind, cancer_script_0 "
            + " FROM shca_project_project"
            + " WHERE status='wait_sample' ORDER BY priority DESC ";
        List<Map<String, Object>> waitSampleProjects = jdbcTemplate.queryForList(selectWaitSampleProjects);

        String finishedSamplesSql = "SELECT id, sample_name "
            + " FROM shca_project_cancer WHERE status='finished'";
        List<Map<String, Object>> finishedSamples = jdbcTemplate.queryForList(finishedSamplesSql);
        Set<String> finishedSamplesSet = finishedSamples.stream()
            .map( item->(String)item.get("sample_name"))
            .collect(Collectors.toSet());

        List<Map<String, Object>> readyProjects = waitSampleProjects.stream()
            .filter(item -> finishedSamplesSet.contains(item.get("sample_name_0")))
            .filter(item -> ( item.get("sample_name_1")==null || finishedSamplesSet.contains(item.get("sample_name_1")) ))
            .collect(Collectors.toList());
        return readyProjects;
    }

    public void updateProjectStatus2RunningStep1(Map<String,Object> projectMap,
        WorkflowsResponse response, Map<String, Object> cromwellConfig) {
        String projectUpdateSql = "UPDATE shca_project_project "
                        + " SET status=?, cromwell_workflow_id_0=?, cromwell_workflow_status_0=?"
                        + ",cancer_script_0=?, update_username=?, update_time=? "
                        + " WHERE id=?";
        jdbcTemplate.update(
            projectUpdateSql,
            new Object[] {
                "running_step1",
                response.getId(),
                response.getStatus(),
                cromwellConfig.get("cancer_script"),
                "CromwellService.mutual",
                LocalDateTime.now(),
                projectMap.get("id")});
    }

    private void updateStep1WorkflowStatusByCromwell() {
        List<Map<String, Object>> Step1ProjectList = getStep1Projects();
        Step1ProjectList.stream().forEach( project -> updateStep2WorkflowStatusByCromwell(project));
    }

    private List<Map<String, Object>> getStep1Projects() {
        // List<Map<String, Object>> ret = new ArrayList<>();
        String projectsToSummarySql = "SELECT * FROM shca_project_project WHERE status='running_step1' ";
        return jdbcTemplate.queryForList(projectsToSummarySql);
    }

    private void updateStep2WorkflowStatusByCromwell(Map<String, Object> projectMap) {
        String status = (String) projectMap.get("status");
        if (!StringUtils.hasLength(status) || !"running_step1".equals(status)) {
            return;
        }
        String workflowId = (String) projectMap.get("cromwell_workflow_id_0");
        String workflowStatus = support.getWorkFlowStatusByWorkFlowId(workflowId);
        if ("Succeeded".equals(workflowStatus)) {
            status = "running_step2";
        }
        if ("Failed".equals(workflowStatus)) {
            status = "failed";
        }
        if ("Aborted".equals(workflowStatus)) {
            status = "aborted";
        }
        String updateProjectSql = "UPDATE shca_project_project "
            + " SET cromwell_workflow_status_0=?, status=?, update_username=?, update_time=? "
            + " WHERE id=? ";
        jdbcTemplate.update(
            updateProjectSql,
            new Object[]{
                workflowStatus,
                status,
                "CromwellService.summary",
                LocalDateTime.now(),
                projectMap.get("id")});
    }
}
