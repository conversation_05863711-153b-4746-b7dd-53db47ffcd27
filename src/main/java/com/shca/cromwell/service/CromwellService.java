/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-25 16:58:57
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-18 11:18:46
 * @FilePath: /analysis_engine/src/main/java/com/shca/cromwell/service/CromwellService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.cromwell.service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class CromwellService {

    @Resource private JdbcTemplate jdbcTemplate;
    @Resource private FileTransferService fileTransfer;
    @Resource private GenericService generic;
    @Resource private MutualService mutual;
    @Resource private SummaryService summary;
    @Resource private UploadService upload;

    private static final Logger log = Logger.getLogger(CromwellService.class);

    public void task() {
        BasicConfigurator.configure();
        log.info("CromwellService3 task() starting ...");
        fileTransfer.launch();
        updatePriority();
        mutual.launch();
        adjustProjectStatus();
        generic.launch();
        summary.launch();
        upload.launch();
        log.info("CromwellService finished");
    }
    public void updatePriority() {
        log.info("updating priority from table project_priority to table project ");
        String prioritySql = "SELECT * FROM shca_project_project_priority WHERE 1=1";
        List<Map<String,Object>> priorityList = jdbcTemplate.queryForList(prioritySql);
        priorityList.stream().forEach( map->{
            jdbcTemplate.update(
                "UPDATE shca_project_project SET priority=? WHERE project_name=?",
                new Object[]{
                    map.get("priority"), map.get("project_name")});
        });
    }

    private void adjustProjectStatus() {
        // 修复异常状态
        // adjust status from step2 back to step 1
        String updateStatusProjectSql = "SELECT shca_project_project WHERE status='running_step2' ";
        List<Map<String, Object>> updateStatusProjects = jdbcTemplate.queryForList(updateStatusProjectSql);
        String workflowIdNull = "SELECT id, sample_name "
            + " FROM shca_project_cancer WHERE cromwell_workflow_id_1 is null ";
        Set<String> workflowIdNullRecords = jdbcTemplate.queryForList(workflowIdNull)
            .stream()
            .map( item -> (String)item.get("sample_name"))
            .collect(Collectors.toSet());

        updateStatusProjects.stream()
            .filter( item -> workflowIdNullRecords.contains((String)item.get("sample_name_0"))
                            || workflowIdNullRecords.contains((String)item.get("sample_name_0")))
            .forEach( item -> jdbcTemplate.update(
                "UPDATE shca_project_project SET status='running_step1' WHERE id=? ",
                new Object[]{item.get("id")})
            );
    }

}
