package com.shca.cromwell.service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;

@Service
public class UploadService {
    @Resource private JdbcTemplate jdbcTemplate;
    @Resource private ObjectMapper objectMapper;
    @Resource private CromwellServiceSupport support;

    private static final Logger log = Logger.getLogger(UploadService.class);
    public void launch() {
        List<Map<String, Object>> readyUploadProjects =  getReadyForUploadProjects();
        log.info("upload-process[" + readyUploadProjects.size() + "]... ");
        readyUploadProjects.stream().forEach(this::launchUploadTask);
        updateProjectStatusFromStep4ToFinished();
    } 
    
    private List<Map<String, Object>> getReadyForUploadProjects() {
        String selectRunningStep3Projects = "SELECT * FROM shca_project_project WHERE status=? ";
        List<Map<String, Object>> readyUploadProjects = jdbcTemplate.queryForList(
            selectRunningStep3Projects, 
            new Object[]{ "running_step3" });
        return readyUploadProjects;
    }

    private void launchUploadTask(Map<String, Object> projectMap) {
        String cancerKind = (String) projectMap.get("cancer_kind");
        Map<String, Object> cromwellConfig = support.getCromwellConfig(cancerKind, '3');
        ProcessService processService = support.getProcessService(projectMap);
        if (cromwellConfig == null || processService == null) {
            return;
        }
        log.info("upload-process[" + projectMap.get("project_no") + "][" + projectMap.get("project_name")
                    + "]: start workflow...");
        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
        String workflowInputs = (String) cromwellConfig.get("cromwell_workflow_inputs");
        try {
            workflowInputs = createWorkflowInput(workflowInputs, projectMap);
            WorkflowsResponse response = processService.upload(
                (String) projectMap.get("project_sn"),
                (String) projectMap.get("project_name"), 
                (String) projectMap.get("sample_name_0"),(String) projectMap.get("sample_name_1"), 
                workflowInputs, source,
                new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), null);
                    projectMap.put("cancer_script_1", cromwellConfig.get("cancer_script"));
                    projectMap.put("cromwell_workflow_id_1", response.getId());
                    projectMap.put("cromwell_workflow_status_1", response.getStatus());
            
            // update from step3 to step4
            String updateStep3Sql = "UPDATE shca_project_project " 
                    + " SET status=?, cromwell_workflow_id_1=?, cromwell_workflow_status_1=?, cancer_script_1=?, "
                    + " update_username=?, update_time=? "
                    + " WHERE id=? ";
            jdbcTemplate.update(
                updateStep3Sql, 
                new Object[]{"running_step4", response.getId(), response.getStatus(),
                    cromwellConfig.get("cancer_script"), "CromwellService.upload", 
                    LocalDateTime.now(), projectMap.get("id")});
            
            log.info("upload-process[" + projectMap.get("project_no") + "][" + projectMap.get("project_name")
                    + "]: start workflow - " + response.getId());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String createWorkflowInput(String workflowInputTemplate, Map<String, Object> projectMap) throws IOException {
        ObjectNode inputNode = (ObjectNode) objectMapper.readTree(workflowInputTemplate);
        String[] concentrationArray = getConcentrationArray(projectMap);
        if (concentrationArray.length > 0) {
            inputNode.put("geneFileUpload.Bconc_WF", Double.parseDouble(concentrationArray[0]));
            if (concentrationArray.length == 2) {
                inputNode.put("geneFileUpload.Kconc_WF", Double.parseDouble(concentrationArray[1]));
            }
        }
        return objectMapper.writeValueAsString(inputNode);
    }

    private void updateProjectStatusFromStep4ToFinished() {
        List<Map<String, Object>> runningStep4Projects = jdbcTemplate.queryForList("SELECT * FROM shca_project_project " 
            + " WHERE status='running_step4' ");
        log.info("upload-process[" + runningStep4Projects.size() + "]: workflow status update...");

        runningStep4Projects.stream().forEach( project -> {
            String workflowStatus = support.getWorkFlowStatusByWorkFlowId((String) project.get("cromwell_workflow_id_1"));
            if (!StringUtils.hasLength(workflowStatus) || !"running_step3".equals(workflowStatus)) {
                return;
            }
            String status = (String) project.get("status");
            if ("Succeeded".equals(workflowStatus)) {
                status = "finished";
            }
            if ("Failed".equals(workflowStatus)) {
                status = "failed";
            }
            if ("Aborted".equals(workflowStatus)) {
                status = "aborted";
            }
            String updateSql = "UPDATE shca_project_project SET status=?, cromwell_workflow_status_1=?, "
                            + " update_username=?, update_time=? "
                            + " WHERE id=? ";
            jdbcTemplate.update(
                updateSql, 
                new Object[]{ status, workflowStatus, 
                    "CromwellService3.upload", LocalDateTime.now(), project.get("id") }); 
            
            // if (!status.equals((String) project.get("status"))) {
            //     backupQcSummary(project);
            // }               
        });
    }

    public String[] getConcentrationArray(Map<String,Object> projectMap) throws IOException {
        String[] result = new String[]{};
        String lims = (String) projectMap.get("lims");
        if (lims.isEmpty()) {
            return result;
        }

        JsonNode limsNode = objectMapper.readTree(lims);
        String concentration = limsNode.findValue("concentration").asText();
        if (concentration.isEmpty()) {
            return result;
        }
        return concentration.split(",");
    }
}
