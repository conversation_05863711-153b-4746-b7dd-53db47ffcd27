package com.shca.cromwell.service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.shca.module.cancer.service.ProcessService;
import com.shca.support.DbSysConfigManager;
import com.shca.support.SysConfigManager;
import com.shca.util.DbService;

@Service
public class CromwellServiceSupport {

    @Resource private DbService db;
    // private static CromwellClient client;
    @Resource private JdbcTemplate jdbcTemplate;
    // @Resource
    // @Qualifier("normalProcessService")
    private ProcessService normalProcessService;
    // @Resource
    // @Qualifier("peixiProcessService")
    private ProcessService peixiProcessService;

    // public List<Map<String, Object>> cromwellPipelineConfig = db
    //     .select("SELECT * FROM shca_cromwell_config ");

    public File readOptions(File source) {
        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
        if (!options.exists()) {
            options = new File(source.getParentFile().getParentFile().getParentFile().getAbsolutePath()
                    + "/workflow_options.json");
        }
        if (!options.exists()) {
            options = null;
        }
        return options;
    }

    public List<Map<String, Object>> selectUploadTasks() {
        return db.select("SELECT * from shca_project_project where status='running_step3' ");
    }

    public List<Map<String, Object>> selectQcSummaryTasks() {
        return db.select("SELECT * from shca_project_project where status='running_step2' ");
    }

    public List<Map<String, Object>> selectMutualTasks() {
        return db.select("SELECT * from shca_project_project where status='wait_running' ORDER BY priority, create_time DESC ");
    }

    public List<Map<String, Object>> selectGenericTasks() {
        return db.select("SELECT * from shca_project_cancer WHERE status='wait_running' ORDER BY priority, create_time DESC ");
    }

    public List<Map<String, Object>> selectFileTransferTasks() {
        // how to get the correct status
        return db.select("SELECT * from shca_project_project WHERE status='running_step4' ORDER BY priority, create_time DESC ");
    }

    public void prioritySync() {
        // set the project priority along with the priority table
        db.update(
                "UPDATE shca_project_project a set priority=ifnull((SELECT priority FROM shca_project_project_priority b where b.project_name=a.project_name), priority) ");
        // set the samples priority along with the project priority
        db.update(
                "UPDATE shca_project_cancer a set priority=ifnull((select max(priority) from shca_project_project b where b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name), priority) ");
    }

    // public static Map<String, Object> _query() {
    //     return ;

    // }
    // public Map<String, Integer> _getAvailableSlotsNum(SysConfigManager config) {
    //     int maxCapacityOfJobQueue = Integer.parseInt(config.getValue("cromwell.max_workflow_count"), 40);
    //     // int currentRunningJobNum = db.selectUnique(
    //     //         "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
    //     //                 " WHERE WORKFLOW_NAME=? AND  WORKFLOW_STATUS not in (?,?,?)",
    //     //         new IntegerCallback(), "generalProcess", "Succeeded", "Failed", "Aborted");
    //     String crntRunningJobSql = "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
    //             " WHERE WORKFLOW_NAME=? AND  WORKFLOW_STATUS not in (?,?,?)";
    //     Integer currentRunningJobNum = jdbcTemplate.queryForObject(crntRunningJobSql, new Object[]{"generalProcess", "Succeeded", "Failed", "Aborted"}, Integer.class);

    //     Map<String, Integer> jobQueueStatus = new HashMap<>();
    //     jobQueueStatus.put("maxCapacityOfJobQueue", maxCapacityOfJobQueue);
    //     jobQueueStatus.put("currentRunningJobNum", currentRunningJobNum);
    //     jobQueueStatus.put("availableSlotsNum", maxCapacityOfJobQueue - currentRunningJobNum);

    //     return jobQueueStatus;

    // }

    public boolean ifNormalProject(Map<String, Object> projectMap) {
        String cancerKind = (String) projectMap.get("cancer_kind");
        return !(cancerKind.startsWith("peixi") || cancerKind.endsWith("peixi"));
    }

    public ProcessService getProcessService(Map<String, Object> taskMap) {
        ProcessService result;
        if (ifNormalProject(taskMap)) {
            result = normalProcessService;
        } else {
            result = peixiProcessService;
        }
        return result;
    }

    public boolean sampleExist( String sampleName ) {
        List<Map<String,Object>> cancerRecordList = jdbcTemplate.queryForList(
            "SELECT * FROM shca_project_cancer WHERE sample_name=? ",
            new Object[]{ sampleName });

        cancerRecordList.stream()
            .filter(entry -> !"cleared".equals((String)entry.get("status")))
            .collect(Collectors.toList());

        return !cancerRecordList.isEmpty();
    }

    public File getWorkflowOptionJson(SysConfigManager config, Map<String, Object> cromwellConfig) {
        // get options json file, now this file is no longer exist
        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
        File workflowOptionsJson = null;
        File optionsWithWDL = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json"); // 不存在这个配置文件
        File optionsWithConfig = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");

        if (optionsWithWDL.exists()) {
            workflowOptionsJson = optionsWithWDL;
        }
        if (optionsWithConfig.exists()) {
            workflowOptionsJson = optionsWithConfig;
        }
        return workflowOptionsJson;
    }
    @SuppressWarnings("deprecation")
    public int getRunningWorkFlowCountFromCromwell() {
        String runningWorkflowSql = "SELECT count(0) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
            "WHERE WORKFLOW_NAME='generalProcess' AND  WORKFLOW_STATUS not in (?,?,?)";

        return jdbcTemplate.queryForObject(
            runningWorkflowSql, new Object[]{"Succeeded", "Failed", "Aborted"},
            Integer.class);
    }
    public Map<String, Object> getCromwellConfig(String cancerKind, char scriptStep) {
        String cromwellConfigSql = "SELECT * FROM shca_cromwell_config "
            + " WHERE cancer_kind=? AND script_step=? ORDER BY id DESC";
        Map<String, Object> cromwellConfig = jdbcTemplate.queryForMap(
            cromwellConfigSql,
            new Object[] { cancerKind, scriptStep });
        return cromwellConfig;
    }

    @SuppressWarnings("deprecation")
    public String getWorkFlowStatusByWorkFlowId( String worflowId) {
        String workflowStatusSql = "SELECT WORKFLOW_STATUS "
                + " FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
                + " WHERE WORKFLOW_EXECUTION_UUID=? ";
        String workflowStatus = null;
        try {
            workflowStatus = jdbcTemplate.queryForObject(
                    workflowStatusSql,
                    new Object[]{ worflowId },
                    String.class);
        } catch (DataAccessException e) {
        }
        return workflowStatus;
    }

    public String cancerNameMapper(String cancerName) {
        String result = cancerName;
        if ("breast_cancer".equals(cancerName)) {
            result = "breast";
        }
        if ("colorectal_cancer".equals(cancerName)) {
            result = "colon";
        }
        if ("ctDNA_pan-cancer".equals(cancerName)) {
            result = "ctDNA";
        }
        return result;
    }

    public Map<String, Integer> getAvailableSlotsNum(DbSysConfigManager config) {
        int maxCapacityOfJobQueue = Integer.parseInt(config.getValue("cromwell.max_workflow_count", "35"));
        // String runningWorkflowSql = "SELECT count(0) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
        //     + "WHERE WORKFLOW_NAME='generalProcess' AND  WORKFLOW_STATUS not in (?,?,?)";
        // int currentRunningJobCnt = jdbcTemplate.queryForObject(
        //         runningWorkflowSql, new Object[]{ "Succeeded", "Failed", "Aborted"},
        //         Integer.class);
        int currentRunningJobCnt = getRunningWorkFlowCountFromCromwell();
        Map<String, Integer> jobQueueStatus = new HashMap<>();
        jobQueueStatus.put("maxCapacityOfJobQueue", maxCapacityOfJobQueue);
        jobQueueStatus.put("currentRunningJobCnt", currentRunningJobCnt);
        jobQueueStatus.put("availableSlotsNum", maxCapacityOfJobQueue - currentRunningJobCnt);
        return jobQueueStatus;
    }

    public String getRawdataStorePath(Map<String, Object> sampleMap, String rawdata ) {
        return getRawdataStorePath((String)sampleMap.get("sample_name"), rawdata);
    }

    @SuppressWarnings("deprecation")
    public String getRawdataStorePath(String sampleName, String rawdata ) {
        String selectRawdataBySampleNameSql = "SELECT store_path "
            + " FROM shca_project_cancer_file "
            + " WHERE sample_name=? AND rawdata=? AND status=? "
            + " ORDER BY create_time DESC LIMIT 1";
        if ( sampleName.isEmpty() || !sampleExist(sampleName)) {
            return null;
        }
        return jdbcTemplate.queryForObject(
            selectRawdataBySampleNameSql,
            new Object[] { sampleName, rawdata, "successed" },
            String.class);
    }


    @SuppressWarnings("unused")
    private void updateStepStatusByCromwell(
        Map<String, Object> projectMap, String srcStatus, String targetStatus) {
        String status = (String) projectMap.get("status");
        if (status == null || !status.equals(srcStatus))  {
            return;
        }

        String workflowId = (String) projectMap.get("cromwell_workflow_id_0");
        String workflowStatus = getWorkFlowStatusByWorkFlowId(workflowId);

        if ("Succeeded".equals(workflowStatus)) {
            status = "targetStatus";
        }
        if ("Failed".equals(workflowStatus)) {
            status = "failed";
        }
        if ("Aborted".equals(workflowStatus)) {
            status = "aborted";
        }
        String updateProjectSql = "UPDATE shca_project_project "
            + " SET cromwell_workflow_status_0=?, status=?, update_username=?, update_time=? "
            + " WHERE id=? ";
        jdbcTemplate.update(
            updateProjectSql,
            new Object[]{
                workflowStatus,
                status,
                "CromwellService.summary",
                LocalDateTime.now(),
                projectMap.get("id")});
    }
}
