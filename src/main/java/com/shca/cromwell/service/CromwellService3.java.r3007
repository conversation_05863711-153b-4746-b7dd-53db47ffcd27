package com.shca.cromwell.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.*;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.*;


public class CromwellService3 {

    private CromwellClient client;
    @Autowired
    private Db db;

    private Map<String, ProcessService> processServiceMap;

    private Map<String, QcSummaryService> qcSummaryServiceMap;

    private List<Map<String,Object>> queue = new ArrayList<>();

    private final static Logger LOGGER = Logger.getLogger( CromwellService3.class);

    public CromwellService3(DataSource dataSource) {
        //db = new Db(new MySQLPageStatement(new DefaultStatememt(dataSource)));
    }

    public CromwellService3(String s) {
    }

    public void setProcessServiceMap(Map<String, ProcessService> processServiceMap) {
        this.processServiceMap = processServiceMap;
    }

    public void setQcSummaryServiceMap(Map<String, QcSummaryService> qcSummaryServiceMap) {
        this.qcSummaryServiceMap = qcSummaryServiceMap;
    }

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    public void task() {
        try {
            LOGGER.info("CromwellService3 start...");

            fileTransfer( null);

//            if (!isContinue) {
//                LOGGER.info("CromwellService finished[2]");
//                return;
//            }

            Map<String,Object> ret = mutual();
            System.out.println( ret);
            if (ret.get( "continue") != null && (boolean)ret.get( "continue")) {
                generic();
            }

            summary();

            upload();

//            long cnt = db.selectUnique( "select count(0) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY where WORKFLOW_STATUS=? ", new LongCallback(),"Running");
//
//            if (CollectionUtils.isEmpty(this.queue) && cnt == 0L) {
//                queue = db.select("select id,project_name,sample_name_0,sample_name_1 from v_shca_project_file " +
//                                " where create_time >=? and  workflow_id is null and lims_status=? order by priority desc "
//                        , ConvertUtils.str2Date("2021-08-01"), "finished");
//
//                if (!CollectionUtils.isEmpty( queue)) {
//                    Map<String, Object> project = this.queue.remove( 0);
//                    transfer(project);
//                }
//            }

            LOGGER.info("CromwellService finished");
        } catch (Exception e) {
            LOGGER.error(LogUtils.toString(e));
            e.printStackTrace();
        }

    }

    // http://172.100.125.149:8080/shca/service-cromwellService_fileTransfer.json?project_no=
    public Map<String,Object> fileTransfer(@MethodParam(name="project_no")String projectNo) {
        Map<String,Object> ret = new HashMap<>();

        int count = db.selectUnique( "select count(*) from shca_project_project a " +
                " inner join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_2=b.WORKFLOW_EXECUTION_UUID " +
                " where b.workflow_status not in (?,?,?)", new IntegerCallback(), "Succeeded", "Failed", "Aborted");
        if (count > 0) {
            ret.put( "errcode", 1000);
            ret.put( "errmsg", "有项目正在传输: " + count);

            LOGGER.info( "fileTransfer[1]: " + ret);
            return ret;
        }
//        count = db.selectUnique( "select count(*) from shca_project_project a " +
//                " inner join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_2_2=b.WORKFLOW_EXECUTION_UUID " +
//                " where b.workflow_status not in (?,?,?)", new IntegerCallback(), "Succeeded", "Failed", "Aborted");
//        if (count > 0) {
//            ret.put( "errcode", 1000);
//            ret.put( "errmsg", "有项目正在传输: " + count);
//
//            LOGGER.info( "fileTransfer[2]: " + ret);
//            return ret;
//        }
//        count = db.selectUnique( "select count(*) from shca_project_project a " +
//                " inner join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_2_3=b.WORKFLOW_EXECUTION_UUID " +
//                " where b.workflow_status not in (?,?,?)", new IntegerCallback(), "Succeeded", "Failed", "Aborted");
//        if (count > 0) {
//            ret.put( "errcode", 1000);
//            ret.put( "errmsg", "有项目正在传输: " + count);
//
//            LOGGER.info( "fileTransfer[3]: " + ret);
//            return ret;
//        }

        Map<String,Object> project = db.selectUnique( "select a.id,a.project_name,a.sample_name_0,a.sample_name_1 " +
                " from shca_project_project a " +
                " where a.create_time >=? and a.status=? and  is_filing=? and cromwell_workflow_id_2 is null order by a.priority desc,a.id "
                , ConvertUtils.str2Date("2021-08-01"), "finished", '0');

        try {
            if (!CollectionUtils.isEmpty( project)) {
                ret = _fileTransfer(project);
                ret.put( "project", project);
            }
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString(e));

            ret.put( "errcode", 9999);
            ret.put( "errmsg", LogUtils.toString( e));
        }

        LOGGER.info( "fileTransfer[0]: " + ret);
        return ret;
    }

    public Map<String,Object> fileTransfer2(@MethodParam(name="project_no")String projectNo) {
        Map<String,Object> ret = new HashMap<>();


        Map<String,Object> project = db.selectUnique( "select a.id,a.project_name,a.sample_name_0,a.sample_name_1 " +
                        " from shca_project_project a " +
                        " where a.create_time >=? and a.status=? and project_no = ? order by a.priority desc,a.id "
                , ConvertUtils.str2Date("2021-08-01"), "finished", projectNo);


        try {
            if (!CollectionUtils.isEmpty( project)) {
                ret = _fileTransfer(project);
                ret.put("errcode",0);
                ret.put( "project", project);
            } else {
                ret.put("errcode",2000);
                ret.put("errmsg","项目未匹配到");
            }
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));

            ret.put( "errcode", 9999);
            ret.put( "errmsg", LogUtils.toString( e));
        }

        LOGGER.info( "fileTransfer[0]: " + ret);
        return ret;
    }

    private Map<String,Object> _fileTransfer(Map<String,Object> project) throws Exception {
        String projectName = (String) project.get("project_name");
        String sampleName0 = (String) project.get("sample_name_0");
        String sampleName1 = (String) project.get("sample_name_1");
        Map<String,Object> ret = new HashMap<>();


        File workflowSource = new File( "/myTools/geneAnalysisPipeline/fileTransfer-ver1125.wdl");
        if(!workflowSource.exists()) {
            ret.put( "errcode", 10);
            ret.put( "errmsg", "WDL文件不存在: " + workflowSource.getAbsolutePath());
            return ret;
        }

        JSONObject jsonObject0 = new JSONObject();
        jsonObject0.put( "fileTransfer.ACTION_FT", "create");
        File targetDir = new File ("/CloudSvr01/analysisSourceFiles/" + projectName);
        if (targetDir.exists()) {
            Cmd.exec( "rm -rf " + targetDir.getAbsolutePath());
        }
        jsonObject0.put( "fileTransfer.targetPath_FT", "/CloudSvr01/analysisSourceFiles/" + projectName);
//        jsonObject0.put( "fileTransfer.tmpPath_FT", "/CloudSvr01/analysisSourceFiles/temp/gatk");

        SysConfigManager config = SysConfigManager.getInstance();
        String sampleDir = config.getValue( "workspace.sample_path") + "/";
        String projectDir = config.getValue( "workspace.project_path") + "/" + projectName;

        JSONArray fileList = new JSONArray();
        JSONArray folderList = new JSONArray();
        if(StringUtils.hasLength( sampleName0)) {
            File baiFile0 = new File( sampleDir + sampleName0 + "/gatk/variation/" + sampleName0 + "_recal.bai");
            if(baiFile0.exists()){
                fileList.add( baiFile0.getAbsolutePath());
            }

            File bamFile0 = new  File( sampleDir + sampleName0 + "/gatk/variation/" + sampleName0 + "_recal.bam");
            if(bamFile0.exists()){
                fileList.add( bamFile0.getAbsolutePath());
            }

            File bedcovFile0 = new  File( sampleDir + sampleName0 + "/capture/beds/" + sampleName0 + ".bedcov.txt");
            if(bedcovFile0.exists()){
                fileList.add( bedcovFile0.getAbsolutePath());
            }

            File bencovAverageFile0 = new  File( sampleDir + sampleName0 + "/capture/beds/" + sampleName0 + ".bedcov.average.txt");
            if(bencovAverageFile0.exists()){
                fileList.add( bencovAverageFile0.getAbsolutePath());
            }
        }

        if(StringUtils.hasLength( sampleName1)) {
            File baiFile1 = new File( sampleDir + sampleName1 + "/gatk/variation/" + sampleName1 + "_recal.bai");
            if(baiFile1.exists()){
                fileList.add( baiFile1.getAbsolutePath());
            }

            File bamFile1 = new  File( sampleDir + sampleName1 + "/gatk/variation/" + sampleName1 + "_recal.bam");
            if(bamFile1.exists()){
                fileList.add( bamFile1.getAbsolutePath());
            }

            File bedcovFile1 = new  File( sampleDir + sampleName1 + "/capture/beds/" + sampleName1 + ".bedcov.txt");
            if(bedcovFile1.exists()){
                fileList.add( bedcovFile1.getAbsolutePath());
            }

            File bencovAverageFile1 = new  File( sampleDir + sampleName1 + "/capture/beds/" + sampleName1 + ".bedcov.average.txt");
            if(bencovAverageFile1.exists()){
                fileList.add( bencovAverageFile1.getAbsolutePath());
            }
        }

        File filesForUpload = new File( projectDir + "/filesForUpload");
        if (filesForUpload.exists()) {
            folderList.add( filesForUpload.getAbsolutePath());
        }

        File filesForSv = new File( projectDir + "/sv");
        if (filesForSv.exists()) {
            folderList.add( filesForSv.getAbsolutePath());
        }


        if (!CollectionUtils.isEmpty( fileList)) {
            jsonObject0.put( "fileTransfer.fileList_FT", fileList);
        }
        if (!CollectionUtils.isEmpty( folderList)) {
            jsonObject0.put( "fileTransfer.folderList_FT", folderList);
        }

        File prettyJson0 = new File( projectDir + "/fileTransfer_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + ".json");
        BufferedWriter prettyWriter0 = new BufferedWriter( new FileWriter( prettyJson0));
        prettyWriter0.write( JSON.toJSONString( jsonObject0, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
        prettyWriter0.close();
        WorkflowsResponse response0 = client.send( new WorkflowsRequest( workflowSource, prettyJson0, null, null));
        ret.put( "response0", response0);

        db.update( "update shca_project_project set cromwell_workflow_id_2=?,cromwell_workflow_id_2_2=?,cromwell_workflow_id_2_3=?,cromwell_workflow_type_2=? where id=? "
                ,response0.getId(), response0.getId(), response0.getId(), "create", project.get( "id"));

        ret.put( "errcode", 0);
        ret.put( "errmsg", "ok");

        return ret;
    }


    public Map<String,Object> manual() {
        Map<String, Object> ret = new HashMap<>();
        List<Map<String,Object>> projects = db.select( "select project_name from v_shca_project_upload " +
                " where project_name like ? and start>=? and start <= ? "
                , "CZ%", ConvertUtils.str2Date( "2021-10-27 21:00:00"), ConvertUtils.str2Date( "2021-11-10 23:00:00"));

        File workflowSource = new File("/myTools/geneAnalysisPipeline/fileTransfer-ver1125.wdl");
        if (!workflowSource.exists()) {
            ret.put("errcode", 10);
            ret.put("errmsg", "WDL文件不存在: " + workflowSource.getAbsolutePath());
            return ret;
        }
//        new File( "/mydata/test/filesForUpload/").mkdirs();
        for (Map<String,Object> project : projects) {
            System.out.println( "manual3 = " + project.get( "project_name"));
            try {
                String projectName = (String) project.get("project_name");


                File targetDir = new File("/CloudSvr01/analysisSourceFiles/" + projectName);
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }



                JSONObject jsonObject0 = new JSONObject();
                jsonObject0.put("fileTransfer.ACTION_FT", "create");
                jsonObject0.put("fileTransfer.targetPath_FT", "/CloudSvr01/analysisSourceFiles/" + projectName + "/filesForUpload_20211111");

                SysConfigManager config = SysConfigManager.getInstance();
                String projectDir = config.getValue("workspace.project_path") + "/" + projectName;

                JSONArray fileList = new JSONArray();
                JSONArray folderList = new JSONArray();
                File filesForUpload = new File(projectDir + "/filesForUpload");
                if (filesForUpload.exists()) {
                    folderList.add(filesForUpload.getAbsolutePath());
                    Cmd.exec( "cp -rf " + filesForUpload.getAbsolutePath() + " /CloudSvr01/analysisSourceFiles/" + projectName + "/filesForUpload_20211111" );
                }


                jsonObject0.put("fileTransfer.fileList_FT", fileList);
                jsonObject0.put("fileTransfer.folderList_FT", folderList);
                File prettyJson0 = new File( "/mydata/test/filesForUpload/" + projectName + "_" + ConvertUtils.date2Str(new Date(), "yyyyMMddHHmm") + ".json");
                BufferedWriter prettyWriter0 = new BufferedWriter(new FileWriter(prettyJson0));
                prettyWriter0.write(JSON.toJSONString(jsonObject0, SerializerFeature.PrettyFormat).replaceAll("\\t", "    "));
                prettyWriter0.close();
//                WorkflowsResponse response0 = client.send(new WorkflowsRequest(workflowSource, prettyJson0, null, null));
//                System.out.println( "manual[" + project.get( "project_name") + "]: " + JSON.toJSONString( response0));
//                db.update("update shca_project_project set cromwell_workflow_id_2=?,cromwell_workflow_id_2_2=?,cromwell_workflow_id_2_3=?,cromwell_workflow_type_2=? where id=? "
//                        , response0.getId(), response0.getId(), response0.getId(), "create", project.get("id"));


                Thread.sleep( 2000L);

//               return null;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ret.put( "errcode", 0);
        ret.put( "errmsg", "ok");

        return ret;
    }


    public Map<String,Object> generic() {
        Map<String,Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "单样本分析错误: ";


        try {
            db.update( "update shca_project_project a set priority=ifnull((select priority from shca_project_project_priority b where b.project_name=a.project_name),priority) ");
            db.update( "update shca_project_cancer a set priority=ifnull((select max(priority) from shca_project_project b where b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name), priority) ");

            errCode = 1010;
            List<Map<String, Object>> cancers = db.select("select id,sample_name,cancer_kind " +
                            " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR1 " +
                            " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR2 " +
                            " ,((select count(distinct project_name) from shca_project_project b where (b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name) and status=?) * 9000 + a.priority) _priority " +
                            " from shca_project_cancer a where status=? order by _priority desc "
                    , "R1", "successed", "R2", "successed", "wait_sample", "wait_running");


            LOGGER.info("generic-process[" + (cancers.size()) + "]...");
            ret.put( "total", cancers.size());

            errCode = 1020;
            SysConfigManager config = SysConfigManager.getInstance();
            int max = ConvertUtils.integer( config.getValue("cromwell.max_workflow_count"), 12);
            int current = db.selectUnique("select count(*) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                            " where WORKFLOW_NAME=? and  WORKFLOW_STATUS not in (?,?,?)"
                    , new IntegerCallback(), "generalProcess", "Succeeded", "Failed", "Aborted");
            ret.put( "current", current);
            ret.put( "max", max);

            errCode = 1030;
            List<Map<String,Object>> successList = new ArrayList<>();
            List<Map<String,Object>> errorList = new ArrayList<>();
            ret.put( "success", successList);
            ret.put( "errors", errorList);


            errCode = 1040;
            max = max - current;
            if (max > cancers.size()) {
                max = cancers.size();
            }

            for (int i = 0; i < max; i++) {
                Map<String, Object> cancer = cancers.get(i);
                try {
                    String sampleName = (String) cancer.get( "sample_name");
                    String cancerKind = (String) cancer.get( "cancer_kind");

                    Map<String, Object> cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                            " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 0);
                    ProcessService processService = processServiceMap.get( cancerKind);

                    if (cromwellConfig != null && processService != null) {
                        LOGGER.info("generic-process[" + sampleName + "]: start workflow...");
                        File rawDataR1 = new File((String) cancer.get("rawDataR1"));
                        File rawDataR2 = new File((String) cancer.get("rawDataR2"));


                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                        if (!options.exists()) {
                            options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                        }
                        if (!options.exists()) {
                            options = null;
                        }


                        WorkflowsResponse response = processService.generic(
                                sampleName, rawDataR1, rawDataR2, (String) cromwellConfig.get("cromwell_workflow_inputs")
                                , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);
                        cancer.put("cancer_script_0", cromwellConfig.get("cancer_script"));
                        cancer.put("cromwell_workflow_id_0", response.getId());
                        cancer.put("cromwell_workflow_status_0", response.getStatus());

                        db.update( "update shca_project_cancer set status=?,cromwell_workflow_id_0=?,cromwell_workflow_status_0=? " +
                                        " ,cancer_script_0=?,update_username=?,update_time=? where id=? "
                                , "running_step0", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), "CromwellService2.generic"
                                , now, cancer.get( "id"));
                        successList.add( cancer);

                        LOGGER.info("generic-process[" + sampleName + "] start workflow - " + response.getId());
                    }
                } catch (Exception e) {
                    errorList.add( cancer);
                    LOGGER.error( "generic-process: " + LogUtils.toString(e));
                }
            }


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");
            return ret;
        } catch (Exception e) {
            LOGGER.error( "generic-process: " + LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            return ret;
        }
    }


    public Map<String,Object> mutual() {
        Map<String,Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "对比分析错误: ";

        try {
            List<Map<String, Object>> cancers = db.select("select id,cromwell_workflow_id_0 from shca_project_cancer where status=? ", "running_step0");
            LOGGER.info("generic-process[" + cancers.size() + "]: workflow status update...");
            for (Map<String, Object> cancer : cancers) {
                try {
                    String workflowId = (String) cancer.get("cromwell_workflow_id_0");
                    String workflowStatus = db.selectUnique( "select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                            " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                    if (StringUtils.isBlank( workflowStatus)) {
                        continue;
                    }

                    String status = "running_step0";
                    if ("Succeeded".equals( workflowStatus)) {
                        status = "finished";
                    }
                    if ("Failed".equals( workflowStatus)) {
                        status = "failed";
                    }
                    if ("Aborted".equals( workflowStatus)) {
                        status = "aborted";
                    }
                    db.update("update shca_project_cancer set cromwell_workflow_status_0=?,status=?,update_username=?,update_time=? where id=? "
                            , workflowStatus, status, "CromwellService3.mutual", now, cancer.get("id"));
                } catch (Exception e) {
                    LOGGER.error( "generic-process: " + LogUtils.toString(e));
                }

            }

            errCode = 1010;
            db.update( "update shca_project_project a set priority=ifnull((select priority from shca_project_project_priority b where b.project_name=a.project_name),priority) ");

            errCode = 1020;
            List<Map<String, Object>> projects = db.select( " select a.id,a.project_sn,a.project_no,a.project_name, " +
                            " a.sample_name_0,a.sample_name_1,a.cancer_kind,a.cancer_script_0 " +
                            " from shca_project_project a " +
                            " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                            " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                            " where a.status=? and b.status=? and (a.sample_name_1 is null or c.status=?) order by a.priority desc "
                    , "wait_sample", "finished", "finished");


            LOGGER.info("mutual-process[" + (projects.size()) + "]...");
            ret.put( "total", projects.size());

            errCode = 1030;
            SysConfigManager config = SysConfigManager.getInstance();
            int max = ConvertUtils.integer( config.getValue("cromwell.max_workflow_count"), 12);
            int current = db.selectUnique("select count(*) from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                            " where WORKFLOW_NAME=? and  WORKFLOW_STATUS not in (?,?,?)"
                    , new IntegerCallback(), "generalProcess", "Succeeded", "Failed", "Aborted");
            ret.put( "current", current);
            ret.put( "max", max);

            errCode = 1040;
            List<Map<String,Object>> successList = new ArrayList<>();
            List<Map<String,Object>> errorList = new ArrayList<>();
            ret.put( "success", successList);
            ret.put( "errors", errorList);
            ret.put ( "continue", false);


            errCode = 1050;
            max = max - current;
            if (max > projects.size()) {
                max = projects.size();
                ret.put ( "continue", true);
            }
            for (int i = 0; i < max; i++) {
                Map<String, Object> project = projects.get(i);
                try {
                    String cancerKind = (String) project.get("cancer_kind");

                    Map<String, Object> cromwellConfig = null;
                    if (StringUtils.isBlank((String) project.get("cancer_script_0"))) {
                        cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 1);
                    } else {
                        cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                " from shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ", cancerKind, project.get("cancer_script_0"), 1);
                    }

                    ProcessService processService = processServiceMap.get(cancerKind);
                    if (cromwellConfig != null && processService != null) {
                        LOGGER.info( "mutect-process[" + project.get("project_sn") + "][" + project.get( "project_name") + "]: start workflow...");
                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                        if (!options.exists()) {
                            options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                        }
                        if (!options.exists()) {
                            options = null;
                        }

                        WorkflowsResponse response = processService.mutual((String) project.get("project_sn"), (String) project.get("project_name")
                                , (String) project.get("sample_name_0"), (String) project.get("sample_name_1"), (String) cromwellConfig.get("cromwell_workflow_inputs")
                                , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);
                        project.put( "cancer_script_0", cromwellConfig.get("cancer_script"));
                        project.put("cromwell_workflow_id_0", response.getId());
                        project.put("cromwell_workflow_status_0", response.getStatus());


                        db.update("update shca_project_project set status=?,cromwell_workflow_id_0=?,cromwell_workflow_status_0=?" +
                                        " ,cancer_script_0=?,update_username=?,update_time=? where id=? "
                                , "running_step1", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script")
                                , "CromwellService2.mutual", now, project.get("id"));
                        LOGGER.info("mutect-process[" + project.get("project_sn") + "][" + project.get( "project_name") + "]: start workflow - " + response.getId());


                        successList.add( project);
                    } else {
                        LOGGER.error( "mutect-process[" + project.get("project_sn") + "][" + project.get( "project_name") + "]: cromwell config is null");
                    }
                } catch (Exception e) {
                    errorList.add( project);
                    LOGGER.error( "mutect-process: " + LogUtils.toString(e));
                }
            }

            errCode = 1060;
            // 修复异常状态
            db.update("update shca_project_project set status=?" +
                    " where (sample_name_0 in (select sample_name from shca_project_cancer where cromwell_workflow_id_1 is null) " +
                    " or sample_name_1 in (select sample_name from shca_project_cancer where cromwell_workflow_id_1 is null)) " +
                    " and status=?", "running_step1", "running_step2");


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error( "mutect-process: " + LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            return ret;
        }
    }


    public Map<String,Object> summary() {
        Map<String,Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "评估报告分析错误: ";

        try {
            List<Map<String, Object>> projects = db.select("select a.id,a.cromwell_workflow_id_0,a.sample_name_0,a.sample_name_1 " +
                    " ,project_sn,a.project_no,a.project_name,a.cancer_kind " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR1_0 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR2_0 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR1_1 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR2_1 " +
                    " from shca_project_project a  " +
                    " where a.status=? ", "R1", "successed", "R2", "successed", "R1", "successed", "R2", "successed", "running_step1");
            LOGGER.info("summary-process[" + projects.size() + "]...");

            errCode = 1020;
            SysConfigManager config = SysConfigManager.getInstance();
            List<Map<String,Object>> successList = new ArrayList<>();
            List<Map<String,Object>> errorList = new ArrayList<>();
            ret.put( "success", successList);
            ret.put( "errors", errorList);


            errCode = 1030;
            for (Map<String, Object> project : projects) {
                try {
                    String workflowId = (String) project.get("cromwell_workflow_id_0");
                    String workflowStatus = db.selectUnique( "select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                            " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                    if (StringUtils.isBlank( workflowStatus)) {
                        continue;
                    }

                    String status = "running_step1";
                    if ("Succeeded".equals( workflowStatus)) {
                        status = "running_step2";
                        Map<String, Object> cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", project.get("cancer_kind"), true, 2);
                        ProcessService processService = processServiceMap.get((String) project.get("cancer_kind"));

                        if (cromwellConfig != null && processService != null) {
                            LOGGER.info("summary-process[" + project.get("project_sn") + "][" + project.get("project_name") + "][" + project.get("sample_name_0") + "]: start workflow...");

                            File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                            File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                            if (!options.exists()) {
                                options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                            }
                            if (!options.exists()) {
                                options = null;
                            }


                            WorkflowsResponse response = processService.summary((String) project.get("project_sn")
                                    , (String) project.get("project_name"), (String) project.get("sample_name_0")
                                    , new File((String) project.get("rawDataR1_0")), new File((String) project.get("rawDataR2_0"))
                                    , (String) cromwellConfig.get("cromwell_workflow_inputs")
                                    , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                            Map<String,Object> cancer = new HashMap<>();
                            cancer.put( "project_sn", project.get("project_sn"));
                            cancer.put( "project_name", project.get("project_name"));
                            cancer.put( "sample_name", project.get("sample_name_0"));
                            cancer.put("cancer_script_1", cromwellConfig.get("cancer_script"));
                            cancer.put("cromwell_workflow_id_1", response.getId());
                            cancer.put("cromwell_workflow_status_1", response.getStatus());
                            successList.add( cancer);


                            db.update( "update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_workflow_status_1=?,cancer_script_1=?" +
                                            " ,update_username=?,update_time=? where sample_name=? "
                                    , response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), "CromwellService3.summary"
                                    , now, project.get("sample_name_0"));
                            LOGGER.info("summary-process[" + project.get("project_sn") + "][" + project.get("project_name") + "][" + project.get("sample_name_0") + "]: start workflow - " + response.getId());

                            if (StringUtils.hasLength((String) project.get("sample_name_1"))) { // 单样本兼容
                                LOGGER.info("summary-process[" + project.get("project_sn") + "][" + project.get("project_name") + "][" + project.get("sample_name_1") + "]: start workflow...");

                                response = processService.summary((String) project.get("project_sn")
                                        , (String) project.get("project_name"), (String) project.get("sample_name_1")
                                        , new File((String) project.get("rawDataR1_1")), new File((String) project.get("rawDataR2_1"))
                                        , (String) cromwellConfig.get("cromwell_workflow_inputs")
                                        , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);
                                cancer = new HashMap<>();
                                cancer.put( "project_sn", project.get("project_sn"));
                                cancer.put( "project_name", project.get("project_name"));
                                cancer.put( "sample_name", project.get("sample_name_1"));
                                cancer.put( "cancer_script_1", cromwellConfig.get("cancer_script"));
                                cancer.put( "cromwell_workflow_id_1", response.getId());
                                cancer.put( "cromwell_workflow_status_1", response.getStatus());
                                successList.add( cancer);

                                db.update( "update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_workflow_status_1=?,cancer_script_1=?" +
                                                " ,update_username=?,update_time=? where sample_name=? "
                                        , response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), "CromwellService3.summary"
                                        , now, project.get("sample_name_1"));
                                LOGGER.info("summary-process[" + project.get("project_sn") + "][" + project.get("project_name") + "][" + project.get("sample_name_1") + "]: start workflow - " + response.getId());
                            }
                        }
                    }


                    if ("Failed".equals( workflowStatus)) {
                        status = "failed";
                    }
                    if ("Aborted".equals( workflowStatus)) {
                        status = "aborted";
                    }

                    db.update("update shca_project_project set cromwell_workflow_status_0=?,status=?,update_username=?,update_time=? where id=? "
                            , workflowStatus, status, "CromwellService3.summary", now, project.get("id"));
                } catch (Exception e) {
                    errorList.add( project);
                    LOGGER.error( "summary-process: " + LogUtils.toString(e));
                }
            }


            errCode = 1040;
            List<Map<String,Object>> cancers = db.select("select id,cromwell_workflow_id_1 from shca_project_cancer where cromwell_workflow_status_1 not in (?,?,?) ", "Succeeded", "Failed", "Aborted");
            LOGGER.info("summary-process[" + cancers.size() + "]: workflow status update...");
            for (Map<String, Object> cancer : cancers) {
                try {
//                    String workflowId = (String) cancer.get("cromwell_workflow_id_1");
//                    String workflowStatus = db.selectUnique( "select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
//                            " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);


                    db.update("update shca_project_cancer set cromwell_workflow_status_1=ifnull((select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY where WORKFLOW_EXECUTION_UUID=cromwell_workflow_id_1),cromwell_workflow_status_1),update_username=?,update_time=? where id=? "
                            , "CromwellService3.summary", now, cancer.get("id"));
                } catch (Exception e) {
                    LOGGER.error( "summary-process: " + LogUtils.toString(e));
                }
            }


            errCode = 1050;
            List<Map<String,Object>> successListForQcSummary = new ArrayList<>();
            List<Map<String,Object>> errorListForQcSummary = new ArrayList<>();
            ret.put( "qc_success", successListForQcSummary);
            ret.put( "qc_errors", errorListForQcSummary);

            errCode = 1060;
            projects = db.select("select a.id,b.cromwell_workflow_status_1 workflow_status_0,c.cromwell_workflow_status_1 workflow_status_1,a.cancer_kind " +
                    " ,sample_name_0,sample_name_1,a.project_no,a.project_sn,a.project_name " +
                    " from shca_project_project a " +
                    " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                    " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                    " where a.status=?", "running_step2");
            for (Map<String, Object> project : projects) {
                try {
                    if ("Succeeded".equals(project.get("workflow_status_0"))
                            && ("Succeeded".equals(project.get("workflow_status_1")) || project.get( "sample_name_1") == null)) {

                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?"
                                , "running_step3", "CromwellService2.summary", now, project.get("id"));

                        QcSummaryService qcSummaryService = qcSummaryServiceMap.get((String) project.get("cancer_kind"));
                        if (qcSummaryService != null) {
                            File qcSummaryFile = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name") + "/qc_summary/" + project.get("sample_name_0") + "_summary.txt");
                            List<QcSummary> qcSummaries = qcSummaryService.qcSummary( qcSummaryFile);
                            db.delete("delete from shca_project_project_qcsummary where project_sn=? and sample_name=?", project.get("project_sn"), project.get("sample_name_0"));
                            for (QcSummary qcSummary : qcSummaries) {
                                db.insert("insert into shca_project_project_qcsummary(project_sn,project_no,sample_name,qc_name,qc_value,qc_reference,qc_qualified,create_username,create_time,update_time,update_username)" +
                                                " values(?,?,?,?,?,?,?,?,?,?,?)", project.get("project_sn"), project.get("project_no"), project.get("sample_name_0"), qcSummary.getName(), qcSummary.getValue(), qcSummary.getReference(), qcSummary.getQualified()
                                        , "CromwellService2.summary", now, now, "CromwellService2.summary");
                            }

                            Map<String,Object> cancer = new HashMap<>();
                            cancer.put( "project_sn", project.get("project_sn"));
                            cancer.put( "project_name", project.get("project_name"));
                            cancer.put( "sample_name", project.get("sample_name_0"));
                            successListForQcSummary.add( cancer);


                            if (StringUtils.hasLength( (String)project.get("sample_name_1"))) { // 单样本兼容
                                qcSummaryFile = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name") + "/qc_summary/" + project.get("sample_name_1") + "_summary.txt");
                                qcSummaries = qcSummaryService.qcSummary( qcSummaryFile);
                                db.delete("delete from shca_project_project_qcsummary where project_sn=? and sample_name=?", project.get("project_sn"), project.get("sample_name_1"));
                                for (QcSummary qcSummary : qcSummaries) {
                                    db.insert("insert into shca_project_project_qcsummary(project_sn,project_no,sample_name,qc_name,qc_value,qc_reference,qc_qualified,create_username,create_time,update_time,update_username)" +
                                                    " values(?,?,?,?,?,?,?,?,?,?,?)", project.get("project_sn"), project.get("project_no"), project.get("sample_name_1"), qcSummary.getName(), qcSummary.getValue(), qcSummary.getReference(), qcSummary.getQualified()
                                            , "CromwellService2.summary", now, now, "CromwellService2.summary");
                                }

                                cancer = new HashMap<>();
                                cancer.put( "project_sn", project.get("project_sn"));
                                cancer.put( "project_name", project.get("project_name"));
                                cancer.put( "sample_name", project.get("sample_name_1"));
                                successListForQcSummary.add( cancer);
                            }
                        }

                    }

                    if ("Failed".equals(project.get("workflow_status_0")) || "Failed".equals(project.get("workflow_status_1"))) {
                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?"
                                , "failed", "CromwellService2.summary", now, project.get("id"));
                    }
                    if ("Aborted".equals(project.get("workflow_status_0")) || "Aborted".equals(project.get("workflow_status_1"))) {
                        db.update("update shca_project_project set status=?,update_username=?,update_time=? where id=?"
                                , "aborted", "CromwellService2.summary", now, project.get("id"));
                    }
                } catch (Exception e) {
                    errorListForQcSummary.add( project);
                    LOGGER.error( "summary-process: " + LogUtils.toString(e));
                }
            }

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error( "summary-process: " + LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            return ret;
        }
    }


    public Map<String,Object> upload() {
        Map<String,Object> ret = new HashMap<>();
        Date now = new Date();
        int errCode = 1000;
        String errMsg = "上传报告分析错误: ";

        try {
            List<Map<String, Object>> projects = db.select("select a.id,a.project_no,a.project_name,a.sample_name_0,a.sample_name_1,a.cancer_kind,a.cancer_script_0 " +
                    " ,a.project_sn,a.cancer_script_1,lims " +
                    " from shca_project_project a " +
                    " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                    " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                    " where a.status=? ", "running_step3");
            LOGGER.info("upload-process[" + projects.size() + "]... ");
            ret.put( "total", projects.size());



            errCode = 1010;
            SysConfigManager config = SysConfigManager.getInstance();
            List<Map<String,Object>> successList = new ArrayList<>();
            List<Map<String,Object>> errorList = new ArrayList<>();
            ret.put( "success", successList);
            ret.put( "errors", errorList);


            errCode = 1020;
            for (Map<String,Object> project : projects) {
                try {
                    String cancerKind = (String) project.get("cancer_kind");


                    Map<String, Object> cromwellConfig = null;
                    if (StringUtils.isBlank((String) project.get("cancer_script_1"))) {
                        cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 3);
                    } else {
                        cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                " from shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ", cancerKind, project.get("cancer_script_1"), 3);
                    }

                    ProcessService processService = processServiceMap.get(cancerKind);
                    if (cromwellConfig != null && processService != null) {
                        LOGGER.info("upload-process[" + project.get("project_no") + "][" + project.get("project_name") + "]: start workflow...");
                        File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                        File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                        if (!options.exists()) {
                            options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                        }
                        if (!options.exists()) {
                            options = null;
                        }

                        /**
                         *
                         * 2021-01-21 添加纯化质检浓度值
                         * {"code":{"BZ2003899B01":["BZ2003899B01"],"BZ2003899K01":["BZ2003899K01"]},"identification":"null,null","productId":"BZ2003899","runningNumber":"201123001","concentration":"58.0,55.0","identityCard":"","state":"0,0","productMask":"BZ510"}
                         */
                        String lims = (String) project.get("lims");
                        String workflowInputs = (String) cromwellConfig.get("cromwell_workflow_inputs");
                        if (StringUtils.hasLength(lims)) {
                            JSONObject jsonObject = JSON.parseObject(workflowInputs);
                            JSONObject limsJson = JSONObject.parseObject(lims);
                            String concentration = limsJson.getString("concentration");
                            String[] array = concentration.split(",");

                            jsonObject.put("geneFileUpload.Bconc_WF", ConvertUtils.dou(array[0]));
                            if(array.length == 2) {
                                jsonObject.put("geneFileUpload.Kconc_WF", ConvertUtils.dou(array[1]));
                            }

                            workflowInputs = jsonObject.toJSONString();
                        }

                        WorkflowsResponse response = processService.upload((String) project.get("project_sn")
                                , (String) project.get("project_name"), (String) project.get("sample_name_0")
                                , (String) project.get("sample_name_1"), workflowInputs
                                , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);
                        project.put( "cancer_script_1", cromwellConfig.get("cancer_script"));
                        project.put("cromwell_workflow_id_1", response.getId());
                        project.put("cromwell_workflow_status_1", response.getStatus());

                        db.update("update shca_project_project set status=?,cromwell_workflow_id_1=?,cromwell_workflow_status_1=?" +
                                        " ,cancer_script_1=?,update_username=?,update_time=? where id=? "
                                , "running_step4", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script")
                                , "CromwellService2.upload", now, project.get("id"));

                        LOGGER.info("upload-process[" + project.get("project_no") + "][" + project.get("project_name") + "]: start workflow - " + response.getId());
                    } else {
                        LOGGER.error("upload-process[" + project.get("project_sn") + "][" + project.get("project_name") + "]: cromwell config is null or process service is null");
                    }
                } catch (Exception e) {
                    errorList.add( project);
                    e.printStackTrace();
                    LOGGER.error( "mutect-process: " + LogUtils.toString(e));
                }
            }

            errCode = 1030;
            projects = db.select("select a.id,a.project_no,a.cancer_kind,a.cromwell_workflow_id_1 " +
                    " ,a.project_sn,a.cancer_script_1 " +
                    " from shca_project_project a " +
                    " where a.status=?", "running_step4");
            LOGGER.info("upload-process[" + projects.size() + "]: workflow status update...");
            for (Map<String, Object> project : projects) {
                String workflowId = (String) project.get("cromwell_workflow_id_1");
                String workflowStatus = db.selectUnique( "select WORKFLOW_STATUS from cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " +
                        " where WORKFLOW_EXECUTION_UUID=? ", new StringCallback(), workflowId);

                if (StringUtils.isBlank( workflowStatus)) {
                    continue;
                }

                String status = "running_step4";
                if ("Succeeded".equals( workflowStatus)) {
                    status = "finished";
                }
                if ("Failed".equals( workflowStatus)) {
                    status = "failed";
                }
                if ("Aborted".equals( workflowStatus)) {
                    status = "aborted";
                }


                QcSummaryService qcSummaryService = qcSummaryServiceMap.get((String) project.get("cancer_kind"));
                File projectDir = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name"));
                qcSummaryService.backup(projectDir);


                db.update("update shca_project_project set status=?, cromwell_workflow_status_1=?,update_username=?,update_time=? where id=? "
                        , status, workflowStatus, "CromwellService3.upload", now, project.get("id"));
            }


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error( "mutect-process: " + LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            return ret;
        }
    }
}
