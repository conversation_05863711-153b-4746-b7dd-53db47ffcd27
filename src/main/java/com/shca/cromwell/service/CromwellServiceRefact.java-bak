package com.shca.cromwell.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.io.filefilter.FileFileFilter;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.utils.ServiceUtils;
import com.shca.cromwell.api.MetadataRequest;
import com.shca.cromwell.api.MetadataResponse;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.cromwell.vo.Call;
import com.shca.cromwell.vo.Workflow;
import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.ywang.module.sys.service.SysConfigManager;

import com.ywang.sql.Page;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.DefaultStatememt;
import com.ywang.sql.support.adapter.MySQLPageStatement;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.*;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;


// create the task and the processes which are taken by cromwell
public class CromwellServiceRefact {

    private final static Logger LOGGER = Logger.getLogger(CromwellService3.class);
    // @Autowired
    // private Db db;

    // private CromwellClient client;
    private Map<String, ProcessService> processServiceMap;
    private Map<String, QcSummaryService> qcSummaryServiceMap;
    private List<Map<String, Object>> executionList = new ArrayList<>();
    private List<Map<String, Object>> uploadQueue = new ArrayList<>();
    private List<Map<String, Object>> qcSummaryQueue = new ArrayList<>();
    private List<Map<String, Object>> mutualQueue = new ArrayList<>();
    private List<Map<String, Object>> genericQueue = new ArrayList<>();
    private List<Map<String, Object>> fileTransferQueue = new ArrayList<>();
    private List<Map<String, Object>> lazyQueue = new ArrayList<>();


    // public void setClient(CromwellClient client) {
    //     this.client = client;
    // }

    public void setProcessServiceMap(Map<String, ProcessService> processServiceMap) {
        this.processServiceMap = processServiceMap;
    }

    public void setQcSummaryServiceMap(Map<String, QcSummaryService> qcSummaryServiceMap) {
        this.qcSummaryServiceMap = qcSummaryServiceMap;
    }

    public void taskDispatcher() {
    /*         to dispatch the task from the task queue
            1. get the generic tasks which are waiting
            2. get all the mutual tasks which are waiting
            3. get all the qcSummary tasks which are waiting
            4. get all the upload tasks which are waiting
            5. get all the fileTransfer tasks which are waiting
            6. get all the other tasks. */

    /*     priority algorithm:
        upload 1
        qcSummary 2
        fileTransfer standalone
        mutual 3
        generic 4
        others 5 */

    /*         generic status: 1. Waiting  2. Processing  3. Succeeded  4. Failed  5, Aborted 6. error
            mutual status: 1. Waiting_for_samples 2. Waiting 3. processing  4. Succeeded  5. Failed  6, Aborted 7, Error
            qcSummary status: 1. Waiting  2. Processing  3. Succeeded  4. Failed  5, Aborted 6, Error
            fileTransfer status: 1. Waiting  2. Processing  3. Succeeded  4. Failed  5, Aborted 6, Error
            upload status: 1. Waiting  2. Processing  3. Succeeded  4. Failed  5, Aborted 6, Error
            others status: 1. Waiting  2. Processing  3. Succeeded  4. Failed  5, Aborted 6, Error */


            // id 升序 Sid降序
            // list.sort(Comparator.comparing(Stu::getId).reversed().thenComparing(Stu::getSid));

        int maxExecutionListSize = 12; // temp value
        _updateExecutionList();

        while (executionList.size() < maxExecutionListSize) {
            Map<String, Object> task =  _pickTask();

            if ( _launchTask(task) ) {
                _addExecutionList(task);
            }
        }
    }

    private void _updateExecutionList() {
        // upate the status of the execution queue, and remove some
        for (Map<String, Object> task: executionList ) {
            String taskStatus = (String) task.get("status");
            if (taskStatus.equals("Succeeded") || taskStatus.equals("Failed")) {
                executionList.remove(task);
            }
        }
    }

    private void _updateAllQueues() {
        // put those updates in the picktask branches.
    }

    private Map<String, Object> _pickTask() {
        // pick a task from the queues and add it to the executionList
        ServiceUtils.prioritySync();

        Map<String, Object> taskElem = new HashMap<>();

        if (uploadQueue.size() > 0) {
            uploadQueue = ServiceUtils.selectUploadTasks();
            return uploadQueue.get(0);
        }

        if (qcSummaryQueue.size() > 0) {
            qcSummaryQueue = ServiceUtils.selectQcSummaryTasks();
            return qcSummaryQueue.get(0);
        }

        if (qcSummaryQueue.size() > 0) {
            return qcSummaryQueue.get(0);
        }

        if (mutualQueue.size() > 0) {
            mutualQueue = ServiceUtils.selectMutualTasks();
            genericQueue = ServiceUtils.selectMutualTasks();
            Map<String, Object> _sample = _getRelatedSample(mutualQueue.get(0));
            if ( _sample != null ) {
                return _sample;
            }
            return mutualQueue.get(0);
        }

        if (genericQueue.size() > 0) {
            genericQueue = ServiceUtils.selectMutualTasks();
            return genericQueue.get(0);
        }

        // special queue

        if ( fileTransferQueue.size() > 0 && _allowFileTransfer() ) {
            return fileTransferQueue.get(0);
        }

        if (lazyQueue.size() > 0) {
            return lazyQueue.get(0);
        }

        return taskElem;
    }

    private boolean _allowFileTransfer() {
        long defaultFileTransferQuota = 1;
        long executionFileTransferCount = executionList.stream()
        .filter( task -> task.get("task_method").equals("fileTransfer"))
        .count();

        if (executionFileTransferCount >= defaultFileTransferQuota) {
            return false;
        } else {
            return true;
        }
    }

    private boolean _launchTask(Map<String, Object> task) {
        // launch the task update the db status
        return true;
    }

    private boolean _addExecutionList ( Map<String, Object> task ) {
        executionList.add(task);
        return true;
    }

    private Map<String, Object> _getRelatedSample (Map<String, Object> project) {
        String sample_name_0 = (String) project.getOrDefault("sample_name_0", null);

        Map<String, Object> genericSample_0 = genericQueue.stream()
        .filter(sample -> sample_name_0.equals(sample.get("sample_name")))
        .findAny()
        .orElse(null);

        if(genericSample_0 != null){
            return genericSample_0;
        }

        String sample_name_1 = (String) project.getOrDefault("sample_name_1", null);
        if (sample_name_1 != null) {
            Map<String, Object> genericSample_1 = genericQueue.stream()
            .filter(sample -> sample_name_1.equals(sample.get("sample_name")))
            .findAny()
            .orElse(null);

            if (genericSample_1 != null) {
                return genericSample_1;
            }
        }

        return null;
    }

    public void fileTransfer() {
        // task type
        // make a cromwell task of the fileTransfer process


    }

    public void genericAnalysis( Map<String, Object> task ) {
        // make a cromwell task of the generic analysis process
        String cancerKind = (String) task.get("cancer_kind");
        ProcessService processService = processServiceMap.get(cancerKind);

        WorkflowsResponse response = processService.generic((String) task.get("project_sn"),
        (String) task.get("project_name"), (String) project.get("sample_name_0"),
        (String) task.get("sample_name_1"),
        (String) cromwellConfig.get("cromwell_workflow_inputs"), source,
        new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), workflowOptionsJson);




    }

    public void mutualDetect( Map<String, Object> task ) {
        // make a cromwell task of the mututal detect process
    }

    public void qcSummary( Map<String, Object> task ) {
        // make a cromwell task of the qc summary process
    }

    public void uploadProcess( Map<String, Object> task ) {
        // make a cromwell task of the upload process
        // create the zip file to be upload to report system
    }

    public void filingProcess( Map<String, Object> task ) {
        // make a cromwell task of the filing process
    }

    public void refundTarFile( Map<String, Object> task ) {
        // make a cromwell task of refund the backup tar fils process
    }



}
