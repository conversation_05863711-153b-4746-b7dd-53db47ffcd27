package com.shca.cromwell.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Service;

@Service
public class CancerFileService {
    @Resource JdbcTemplate jdbcTemplate;
    @Resource CromwellServiceSupport support;
    @Resource DataSource dataSource;
    private static final Logger log = Logger.getLogger(CancerFileService.class);
    
    public void prepare() {
        createCancerRecordByCancerFileRecord();
        updateDuplicateFastqFileStatus();
        // updateDoubleReadyFastqFileStatus();
        updateSampleTaskStatusByCromwell();
    }

    // methods for createCancerRecordByCancerFileRecord()
    private void createCancerRecordByCancerFileRecord() {
        Map<String, List<Map<String, Object>>> qualifiedSampleMapList = 
            getAvailableFastqsToCreateSample();
        qualifiedSampleMapList.entrySet().stream()
            .forEach(entry -> insertCancerRecord(entry.getValue()));
    }

    // get normal fastqs from double ready list to create sample record
    private Map<String, List<Map<String, Object>>> getAvailableFastqsToCreateSample() {
        // Map<String, List<Map<String, Object>>> qualifiedSampleMapList = getDoubleReadyStatusFastqs();
        return getDoubleReadyStatusFastqs().entrySet().stream()
            .filter(entry -> !support.sampleExist(entry.getKey()))
            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getValue()));
    }

    private Map<String, List<Map<String, Object>>> getDoubleReadyStatusFastqs() {
        // List<Map<String, Object>> samplesList = db.select("SELECT * FROM shca_project_cancer_file WHERE status=? ", "ready");
        List<Map<String, Object>> samplesList = jdbcTemplate.queryForList(
            "SELECT * FROM shca_project_cancer_file WHERE status='ready' ");
        Map<String, List<Map<String, Object>>> readySamplesListMap = samplesList.stream()
            .collect(Collectors.groupingBy( entry -> (String)entry.get("sample_name"), Collectors.toList()));

        Map<String, List<Map<String, Object>>> doubleReadyFastqMapList = readySamplesListMap
            .entrySet().stream()
            .filter(entry -> entry.getValue().size()==2)
            .filter(entry -> entry.getValue().stream().anyMatch(item->"R1".equals(item.get("rawdata"))))
            .filter(entry -> entry.getValue().stream().anyMatch(item->"R2".equals(item.get("rawdata"))))
            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getValue()));
        return doubleReadyFastqMapList;
    }

    private void insertCancerRecord( List<Map<String, Object>> sampleList) {
        Map<String, Object> r1FastqMap = sampleList.get(0);
        Map<String, Object> r2FastqMap = sampleList.get(1);
        if (!"R1".equals(sampleList.get(0).get("rawdata"))) {
            r1FastqMap = sampleList.get(1);
            r2FastqMap = sampleList.get(0);  
        } 
        Map<String, Object> sample = new HashMap<>();
        sample.put("sample_name", r1FastqMap.get("sample_name"));
        sample.put("cancer_kind", r1FastqMap.get("cancer_kind"));
        sample.put("status", "created");
        sample.put("rawdata_r1", r1FastqMap.get("id"));
        sample.put("rawdata_r2", r2FastqMap.get("id"));
        sample.put("create_username", "cromwellService");
        sample.put("create_time", LocalDateTime.now());
        sample.put("update_username", "cromwellService");
        sample.put("update_time", LocalDateTime.now());
        SimpleJdbcInsert simpleJdbcInsert = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_cancer")
            .usingGeneratedKeyColumns("id");
        simpleJdbcInsert.executeAndReturnKey(sample);

        updateFastqFileStatus((long) r1FastqMap.get("id"));
        updateFastqFileStatus((long) r2FastqMap.get("id"));
    }

    private void updateFastqFileStatus(long id) {
        jdbcTemplate.update(
            "UPDATE shca_project_cancer_file SET status=? WHERE id=? ", 
            new Object[] {"successed", id});
    }   

    
    // methods for updateDuplicateFastqFileStatus()
    // get duplicate fastq from double ready list, to be set status to duplicate
    private void updateDuplicateFastqFileStatus() {
        // Map<String, List<Map<String, Object>>> updateDuplicateFastqMapList = getDoubleReadyStatusFastqs();
        String updateSql = "UPDATE shca_project_cancer_file SET status=? WHERE id=? ";
        getDuplicateFastqFilesMap().entrySet().stream()
            .forEach(
                entry -> entry.getValue().stream()
                    .forEach( sample -> {
                        if ("ready".equals(sample.get("status"))) {
                            jdbcTemplate.update(
                                updateSql, 
                                new Object[] {"duplicate", sample.get("id")});
                        }
                    })
            );
    }   
    public Map<String, List<Map<String, Object>>> getDuplicateFastqFilesMap() {
        Map<String, List<Map<String, Object>>> qualifiedSampleMapList = getDoubleReadyStatusFastqs();
        return qualifiedSampleMapList.entrySet().stream()
            .filter(entry -> support.sampleExist(entry.getKey()))
            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getValue()));
    }   

    // methods for updateSampleTaskStatusByCromwell()
    public void updateSampleTaskStatusByCromwell() {
        log.info("updating sample tasks status by cromwell METADATA table");
        String runningStep0Sql = "SELECT id, status, cromwell_workflow_id_0 " 
            + " FROM shca_project_cancer WHERE status=? ";
        List<Map<String, Object>> sampleList =jdbcTemplate.queryForList(
            runningStep0Sql, 
            new Object[] { "running_step0" });  

        sampleList.stream().forEach(
            sample -> {
                String workflowId = (String) sample.get("cromwell_workflow_id_0");
                String workflowStatus = support.getWorkFlowStatusByWorkFlowId(workflowId);
                String status = (String) sample.get("status");
                if ("Succeeded".equals(workflowStatus)) {
                    status = "finished";
                }
                if ("Failed".equals(workflowStatus)) {
                    status = "failed";
                }
                if ("Aborted".equals(workflowStatus)) {
                    status = "aborted";
                }
                String projectCancerUpdateSql = "UPDATE shca_project_cancer " 
                    + " SET cromwell_workflow_status_0=?, status=?, update_username=?, update_time=? " 
                    +  "WHERE id=? ";
                jdbcTemplate.update(
                    projectCancerUpdateSql, 
                    new Object[] { 
                        workflowStatus, 
                        status, "CromwellService3.mutual", 
                        LocalDateTime.now(), 
                        sample.get("id")});
            }
        );
    }



}
