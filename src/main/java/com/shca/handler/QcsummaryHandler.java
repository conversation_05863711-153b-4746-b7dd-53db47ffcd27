package com.shca.handler;

import com.shca.util.DbService;
import org.apache.log4j.Logger;
import java.util.Map;

public class QcsummaryHandler {
    private final static Logger LOGGER = Logger.getLogger(QcsummaryHandler.class);

    public static boolean isQualified(DbService db, String project_sn){
        LOGGER.info("*************** isQualified:"+project_sn);
        String sql_check = "select count(0) as err_cnt from shca_project_project_qcsummary  where project_sn=? and qc_qualified=0";
        Map<String, Object> result = db.selectUnique(sql_check, project_sn);
        Long err_cnt = result != null ? ((Number) result.get("err_cnt")).longValue() : 0L;
        LOGGER.info(project_sn+"QC报告错误项个数："+err_cnt);
        if(err_cnt>0){
            return false;
        } else {
            return true;
        }
    }
}
