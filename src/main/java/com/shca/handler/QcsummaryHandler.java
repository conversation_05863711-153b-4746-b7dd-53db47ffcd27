package com.shca.handler;

import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.LongCallback;
import org.apache.log4j.Logger;

public class QcsummaryHandler {
    private final static Logger LOGGER = Logger.getLogger(QcsummaryHandler.class);

    public static boolean isQualified(Db db, String project_sn){
        LOGGER.info("*************** isQualified:"+project_sn);
        String sql_check = "select count(0) as err_cnt from shca_project_project_qcsummary  where project_sn=? and qc_qualified=0";
        Long err_cnt = db.selectUnique(sql_check,new LongCallback(),project_sn);
        LOGGER.info(project_sn+"QC报告错误项个数："+err_cnt);
        if(err_cnt>0){
            return false;
        } else {
            return true;
        }
    }
}
