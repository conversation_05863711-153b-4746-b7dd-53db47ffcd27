package com.shca.entity;

import java.time.LocalDateTime;

import com.shca.entity.enums.SampleStatusEnum;

public class Sample {
    
    private long id;
    private String sampleName;
    private String cancerName;
    private SampleStatusEnum status;
    private long fastqIdR1;
    private long fastqIdR2;
    private String samplePath;
    private int priority;
    private String createUsername;
    private LocalDateTime createTime;
    private String updateUsername;
    private LocalDateTime updateTime;

    public Sample() {
    }

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }
    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getCancerName() {
        return cancerName;
    }

    public void setCancerName(String cancerName) {
        this.cancerName = cancerName;
    }

    public SampleStatusEnum getStatus() {
        return status;
    }

    public void setStatus(SampleStatusEnum status) {
        this.status = status;
    }

    public long getFastqIdR1() {
        return fastqIdR1;
    }

    public void setFastqIdR1(long fastqIdR1) {
        this.fastqIdR1 = fastqIdR1;
    }

    public long getFastqIdR2() {
        return fastqIdR2;
    }

    public void setFastqIdR2(long fastqIdR2) {
        this.fastqIdR2 = fastqIdR2;
    }

    public String getSamplePath() {
        return samplePath;
    }

    public void setSamplePath(String samplePath) {
        this.samplePath = samplePath;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public String getCreateUsername() {
        return createUsername;
    }

    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUsername() {
        return updateUsername;
    }

    public void setUpdateUsername(String updateUsername) {
        this.updateUsername = updateUsername;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    

}
