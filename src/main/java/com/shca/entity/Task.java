package com.shca.entity;

import java.time.LocalDateTime;

import com.shca.entity.enums.TaskStatusEnum;
import com.shca.entity.enums.TaskTypeEnum;

public class Task {

    private long id;
    private String workflowId;
    private String workflowTag;
    private TaskStatusEnum status;
    private TaskTypeEnum taskType;
    private String sampleName;
    private String projectName;
    private String projectSn;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private LocalDateTime submissionTime;
    private LocalDateTime createTime;
    private String create_by;
    private LocalDateTime updateTime;
    private String update_by;

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }
    public String getWorkflowId() {
        return workflowId;
    }
    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }
    public String getWorkflowTag() {
        return workflowTag;
    }
    public void setWorkflowTag(String workflowTag) {
        this.workflowTag = workflowTag;
    }
    public TaskStatusEnum getStatus() {
        return status;
    }
    public void setStatus(TaskStatusEnum status) {
        this.status = status;
    }
    public TaskTypeEnum getTaskType() {
        return taskType;
    }
    public void setTaskType(TaskTypeEnum taskType) {
        this.taskType = taskType;
    }
    public String getSampleName() {
        return sampleName;
    }
    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }
    public String getProjectName() {
        return projectName;
    }
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getProjectSn() {
        return projectSn;
    }
    public void setProjectSn(String projectSn) {
        this.projectSn = projectSn;
    }
    public LocalDateTime getStartTime() {
        return startTime;
    }
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    public LocalDateTime getEndTime() {
        return endTime;
    }
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public LocalDateTime getSubmissionTime() {
        return submissionTime;
    }
    public void setSubmissionTime(LocalDateTime submissionTime) {
        this.submissionTime = submissionTime;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreate_by() {
        return create_by;
    }
    public void setCreate_by(String create_by) {
        this.create_by = create_by;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getUpdate_by() {
        return update_by;
    }
    public void setUpdate_by(String update_by) {
        this.update_by = update_by;
    }

}
