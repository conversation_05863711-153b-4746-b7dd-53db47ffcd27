package com.shca.entity;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

import com.shca.entity.enums.ProjectStatusEnum;

public class Project {
    
    private long id;
    private String projectSn;
    private String projectName;
    private String BSampleName;
    private String KSampleName;
    private String cancerName;
    private ProjectStatusEnum status;
    private Integer priority;
    @Enumerated(EnumType.STRING)
    private ProjectFilingStatusEnum isFiling;
    private String projectPath; //workspace
    private String limsJson;
    private String limsStatus;
    private LocalDateTime limsUploadTime;
    private String limsUploadResult;
    private String fileUploadResult;
    private LocalDateTime fileUploadTime;
    private String createUsername;
    private LocalDateTime createTime;
    private String updateUsername;
    private LocalDateTime updateTime;
    

    public enum ProjectFilingStatusEnum {
        SUCCEEDED(1),
        DUPSUCCEEDED(2),
        DUPLICATE(3),
        FAILED(4),
        UNKNOWN(5),
        DELETED(6),
        PLACEHOLDER2(7),
        READY(8);
    
        int code;
        private ProjectFilingStatusEnum(int code) {
            this.code = code;
        }
    
        private static Map<Integer,ProjectFilingStatusEnum > filingStatusMap = new HashMap<>();
        static {
            for (ProjectFilingStatusEnum value : ProjectFilingStatusEnum .values()) {
                filingStatusMap.put(value.getCode(),value);
            }
        }

        public static ProjectFilingStatusEnum gStatusEnumByCode(int code) {
            return filingStatusMap.get(code);
        }
        public int getCode() {
            return code;
        }
    }

    public Project() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getProjectSn() {
        return projectSn;
    }

    public void setProjectSn(String projectSn) {
        this.projectSn = projectSn;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getBSampleName() {
        return BSampleName;
    }

    public void setBSampleName(String bSampleName) {
        BSampleName = bSampleName;
    }

    public String getKSampleName() {
        return KSampleName;
    }

    public void setKSampleName(String kSampleName) {
        KSampleName = kSampleName;
    }

    public String getCancerName() {
        return cancerName;
    }

    public void setCancerName(String cancerName) {
        this.cancerName = cancerName;
    }

    public ProjectStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ProjectStatusEnum status) {
        this.status = status;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public  ProjectFilingStatusEnum getIsFiling() {
        return isFiling;
    }

    public void setIsFiling(ProjectFilingStatusEnum isFiling) {
        this.isFiling = isFiling;
    }

    public String getProjectPath() {
        return projectPath;
    }

    public void setProjectPath(String projectPath) {
        this.projectPath = projectPath;
    }

    public String getLimsJson() {
        return limsJson;
    }

    public void setLimsJson(String limsJson) {
        this.limsJson = limsJson;
    }

    public String getLimsStatus() {
        return limsStatus;
    }

    public void setLimsStatus(String limsStatus) {
        this.limsStatus = limsStatus;
    }

    public LocalDateTime getLimsUploadTime() {
        return limsUploadTime;
    }

    public void setLimsUploadTime(LocalDateTime limsUploadTime) {
        this.limsUploadTime = limsUploadTime;
    }

    public String getLimsUploadResult() {
        return limsUploadResult;
    }

    public void setLimsUploadResult(String limsUploadResult) {
        this.limsUploadResult = limsUploadResult;
    }

    public String getFileUploadResult() {
        return fileUploadResult;
    }

    public void setFileUploadResult(String fileUploadResult) {
        this.fileUploadResult = fileUploadResult;
    }

    public LocalDateTime getFileUploadTime() {
        return fileUploadTime;
    }

    public void setFileUploadTime(LocalDateTime fileUploadTime) {
        this.fileUploadTime = fileUploadTime;
    }

    public String getCreateUsername() {
        return createUsername;
    }

    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUsername() {
        return updateUsername;
    }

    public void setUpdateUsername(String updateUsername) {
        this.updateUsername = updateUsername;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isPeixi() {
        if (this.KSampleName != null) {
            return false;
        }
        return true;
    }

}
