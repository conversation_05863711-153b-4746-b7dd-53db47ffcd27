/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 00:08:27
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-14 00:20:17
 * @FilePath: /analysis_engine/src/main/java/com/shca/entity/UploadFile.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.entity;

import java.time.LocalDateTime;

import org.springframework.util.DigestUtils;

public final class UploadFile {

    private final String fileName;
    private final String fileType;
    private final byte[] content;
    private final long size;
    private final String md5;
    private String memo;
    private String createUserName;
    private LocalDateTime createTime;

    public UploadFile(String fileName, byte[] content) {
        this(fileName.substring(0, fileName.lastIndexOf(".")), fileName.substring(fileName.lastIndexOf(".") + 1), content);
    }

    public UploadFile(String fileName, String fileType, byte[] content) {
        this.fileName = fileName;
        this.fileType = fileType.toLowerCase();
        this.content = content;
        this.size = content.length;
        this.md5 = DigestUtils.md5DigestAsHex(content);
    }


    public String getFileName() {
        return fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public byte[] getContent() {
        return content;
    }

    public String getMd5() {
        return md5;
    }

    public long getSize() {
        return size;
    }

    public String getMemo() {
        return memo;
    }

    public String getFullName() {
        return fileName + "." + fileType;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
