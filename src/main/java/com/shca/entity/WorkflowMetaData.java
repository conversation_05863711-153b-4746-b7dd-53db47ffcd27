package com.shca.entity;

import java.time.LocalDateTime;

import com.shca.entity.enums.WorkflowMetaDataStatusEnum;

public class WorkflowMetaData {
    
    private long WORKFLOW_METADATA_SUMMARY_ENTRY_ID;
    private String WORKFLOW_EXECUTION_UUID;
    private String WORKFLOW_NAME;
    private WorkflowMetaDataStatusEnum WORKFLOW_STATUS;
    private LocalDateTime START_TIMESTAMP;
    private LocalDateTime END_TIMESTAMP;
    private LocalDateTime SUBMISSION_TIMESTAMP;
    private LocalDateTime createTime;
    private String PARENT_WORKFLOW_EXECUTION_UUID;
    private String ROOT_WORKFLOW_EXECUTION_UUID;
    private String METADATA_ARCHIVE_STATUS;
    
    public long getWORKFLOW_METADATA_SUMMARY_ENTRY_ID() {
        return WORKFLOW_METADATA_SUMMARY_ENTRY_ID;
    }
    public void setWORKFLOW_METADATA_SUMMARY_ENTRY_ID(long wORKFLOW_METADATA_SUMMARY_ENTRY_ID) {
        WORKFLOW_METADATA_SUMMARY_ENTRY_ID = wORKFLOW_METADATA_SUMMARY_ENTRY_ID;
    }
    public String getWORKFLOW_EXECUTION_UUID() {
        return WORKFLOW_EXECUTION_UUID;
    }
    public void setWORKFLOW_EXECUTION_UUID(String wORKFLOW_EXECUTION_UUID) {
        WORKFLOW_EXECUTION_UUID = wORKFLOW_EXECUTION_UUID;
    }
    public String getWORKFLOW_NAME() {
        return WORKFLOW_NAME;
    }
    public void setWORKFLOW_NAME(String wORKFLOW_NAME) {
        WORKFLOW_NAME = wORKFLOW_NAME;
    }
    public WorkflowMetaDataStatusEnum getWORKFLOW_STATUS() {
        return WORKFLOW_STATUS;
    }
    public void setWORKFLOW_STATUS(WorkflowMetaDataStatusEnum wORKFLOW_STATUS) {
        WORKFLOW_STATUS = wORKFLOW_STATUS;
    }
    public LocalDateTime getSTART_TIMESTAMP() {
        return START_TIMESTAMP;
    }
    public void setSTART_TIMESTAMP(LocalDateTime sTART_TIMESTAMP) {
        START_TIMESTAMP = sTART_TIMESTAMP;
    }
    public LocalDateTime getEND_TIMESTAMP() {
        return END_TIMESTAMP;
    }
    public void setEND_TIMESTAMP(LocalDateTime eND_TIMESTAMP) {
        END_TIMESTAMP = eND_TIMESTAMP;
    }
    public LocalDateTime getSUBMISSION_TIMESTAMP() {
        return SUBMISSION_TIMESTAMP;
    }
    public void setSUBMISSION_TIMESTAMP(LocalDateTime sUBMISSION_TIMESTAMP) {
        SUBMISSION_TIMESTAMP = sUBMISSION_TIMESTAMP;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getPARENT_WORKFLOW_EXECUTION_UUID() {
        return PARENT_WORKFLOW_EXECUTION_UUID;
    }
    public void setPARENT_WORKFLOW_EXECUTION_UUID(String pARENT_WORKFLOW_EXECUTION_UUID) {
        PARENT_WORKFLOW_EXECUTION_UUID = pARENT_WORKFLOW_EXECUTION_UUID;
    }
    public String getROOT_WORKFLOW_EXECUTION_UUID() {
        return ROOT_WORKFLOW_EXECUTION_UUID;
    }
    public void setROOT_WORKFLOW_EXECUTION_UUID(String rOOT_WORKFLOW_EXECUTION_UUID) {
        ROOT_WORKFLOW_EXECUTION_UUID = rOOT_WORKFLOW_EXECUTION_UUID;
    }
    public String getMETADATA_ARCHIVE_STATUS() {
        return METADATA_ARCHIVE_STATUS;
    }
    public void setMETADATA_ARCHIVE_STATUS(String mETADATA_ARCHIVE_STATUS) {
        METADATA_ARCHIVE_STATUS = mETADATA_ARCHIVE_STATUS;
    }

    
}
