/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-19 00:38:10
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-10-22 18:08:33
 * @FilePath: /analysis_engine/src/main/java/com/shca/entity/WeiXinSessionUser.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.entity;

import com.ywang.http.SessionUser;

public class WeiXinSessionUser implements SessionUser {

    private final User user;

    public WeiXinSessionUser(User user) {
        this.user = user;
    }

    @Override
    public String getUserName() {
        return user.getUserId();
    }

    public String getName() {
        return user.getName();
    }

    @Override
    public String getOrgCode() {
        String orgCode = "";
        for (long department : user.getDepartment()) {
            orgCode += department + ",";
        }
        if (orgCode.length() > 0) {
            orgCode = orgCode.substring( 0, orgCode.length() - 1);
        }

        return orgCode;
    }

    @Override
    public long getRoleCode() {
        return 0;
    }

    public String getUserId() {
        return user.getUserId();
    }

    public String getMobile() {
        return user.getMobile();
    }

    public String getPosition() {
        return user.getPosition();
    }

    public String getEmail() {
        return user.getEmail();
    }

    public String getAvatar() {
        return user.getAvatar();
    }

    public String getTelephone() {
        return user.getTelephone();
    }

    public String getAlias() {
        return user.getAlias();
    }

    public String getQrCode() {
        return user.getQrCode();
    }

    public String toString() {
        return getUserName();
    }

    public User getUser() {
        return user;
    }

}
