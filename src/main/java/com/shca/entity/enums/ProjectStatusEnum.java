package com.shca.entity.enums;

public enum ProjectStatusEnum {
    WA<PERSON><PERSON><PERSON><PERSON><PERSON>, 
    READ<PERSON><PERSON><PERSON><PERSON>, RUN<PERSON><PERSON><PERSON>TU<PERSON>, <PERSON>UTU<PERSON><PERSON>IL<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ORTED,
    REA<PERSON><PERSON>Q<PERSON>,  R<PERSON><PERSON><PERSON>Q<PERSON>, QCF<PERSON><PERSON>ED, Q<PERSON><PERSON><PERSON>ED,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UP<PERSON>OADING, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON><PERSON><PERSON>IL<PERSON>,  TRA<PERSON><PERSON><PERSON>ABORTED,
    SUCCEEDED, CLEARED, DELETED, FILED
}
