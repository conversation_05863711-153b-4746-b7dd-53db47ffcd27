/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-22 17:59:58
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-10-22 18:02:28
 * @FilePath: /analysis_engine/src/main/java/com/shca/entity/User.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.entity;

import java.util.List;
import java.util.Map;

public class User {
private String userId;

    private String type;

    private String name;

    private String alias;

    private String mobile;

    private List<Long> department;

    private List<Integer> order;

    private String position;

    private List<String> positions;

    private int gender;

    private String email;

    private String telephone;

    private List<Boolean> isLeaderInDept;

    private String avatar;

    private boolean enable;

    private Map<String, Object> extAttr;

    private int status;

    private String qrCode;

    private String address;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<Long> getDepartment() {
        return department;
    }

    public void setDepartment(List<Long> department) {
        this.department = department;
    }

    public List<Integer> getOrder() {
        return order;
    }

    public void setOrder(List<Integer> order) {
        this.order = order;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public List<Boolean> getIsLeaderInDept() {
        return isLeaderInDept;
    }

    public void setIsLeaderInDept(List<Boolean> isLeaderInDept) {
        this.isLeaderInDept = isLeaderInDept;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public Map<String, Object> getExtAttr() {
        return extAttr;
    }

    public void setExtAttr(Map<String, Object> extAttr) {
        this.extAttr = extAttr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getPositions() {
        return positions;
    }

    public void setPositions(List<String> positions) {
        this.positions = positions;
    }
}
