package com.shca.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.shca.entity.User;
import com.shca.entity.WeiXinSessionUser;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * Index Controller
 * 
 * Replaces ShcaIndexView with standard Spring MVC controller
 */
@Controller
public class IndexController extends BaseController {

    @GetMapping("/")
    public ModelAndView index(HttpServletRequest request) {
        return login(request, null, null);
    }

    @GetMapping("/console.htm")
    public ModelAndView console() {
        return jspView("/shca/console.jsp", null);
    }

    @GetMapping("/login.htm")
    public ModelAndView loginPage() {
        return jspView("/shca/login.jsp", null);
    }

    @PostMapping("/login.htm")
    public ModelAndView login(HttpServletRequest request, 
                             @RequestParam(required = false) String username,
                             @RequestParam(required = false) String password) {
        
        HttpSession session = request.getSession();
        
        // Check if user is already logged in
        if (getCurrentUser(session) != null) {
            return redirect("project_cancer_list.htm");
        }

        // If no username provided, show login form
        if (username == null || username.trim().isEmpty()) {
            return jspView("/shca/login.jsp", null);
        }

        // Authenticate user
        String hashedPassword = md5(password);
        Map<String, Object> account = jdbcTemplate.queryForMap(
            "SELECT username, real_name, role_code FROM comm_rbac_account WHERE username=? AND password=?",
            username, hashedPassword);

        if (account == null) {
            Map<String, Object> model = new HashMap<>();
            model.put("errmsg", "用户名或密码错误");
            return jspView("/shca/login.jsp", model);
        }

        // Create user session
        User user = new User();
        user.setUserId((String) account.get("username"));
        user.setName((String) account.get("real_name"));

        WeiXinSessionUser sessionUser = new WeiXinSessionUser(user);
        session.setAttribute("SESSION_USER_KEY", sessionUser);
        session.setAttribute("role_code", account.get("role_code"));

        return redirect("project_cancer_list.htm");
    }

    @GetMapping("/logon.htm")
    public ModelAndView logon(HttpSession session) {
        User user = new User();
        user.setUserId("WangYun");
        user.setName("王贇");

        WeiXinSessionUser sessionUser = new WeiXinSessionUser(user);
        session.setAttribute("SESSION_USER_KEY", sessionUser);

        return jspView("/shca/project/project_project_list.jsp", null);
    }

    @GetMapping("/logout.htm")
    public ModelAndView logout(HttpSession session) {
        session.removeAttribute("SESSION_USER_KEY");
        return jspView("/shca/login.jsp", null);
    }

    /**
     * Simple MD5 hash implementation
     * TODO: Replace with proper password hashing (BCrypt)
     */
    private String md5(String input) {
        if (input == null) return null;
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
