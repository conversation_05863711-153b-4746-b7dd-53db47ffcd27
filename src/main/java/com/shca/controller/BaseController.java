package com.shca.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.shca.support.SysConfigManager;
import com.shca.util.JdbcTemplateEnhance;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * Base Controller Class
 * 
 * Replaces the com.ywang framework's View pattern with standard Spring MVC controllers.
 * Provides common functionality that was previously in ViewParams and Forward classes.
 */
public abstract class BaseController {

    @Autowired
    protected JdbcTemplateEnhance jdbcTemplate;

    @Autowired
    protected SysConfigManager config;

    /**
     * Create a JSON response - replaces JsonForward
     */
    protected ResponseEntity<Object> jsonResponse(Object data) {
        return ResponseEntity.ok(data);
    }

    /**
     * Create a JSP view response - replaces JspForward
     */
    protected ModelAndView jspView(String viewName, Map<String, Object> model) {
        ModelAndView mav = new ModelAndView(viewName);
        if (model != null) {
            mav.addAllObjects(model);
        }
        return mav;
    }

    /**
     * Create a redirect response - replaces redirect in Forward
     */
    protected ModelAndView redirect(String url) {
        return new ModelAndView("redirect:" + url);
    }

    /**
     * Get parameter as String with default value
     */
    protected String getStringParam(HttpServletRequest request, String name, String defaultValue) {
        String value = request.getParameter(name);
        return value != null ? value : defaultValue;
    }

    /**
     * Get parameter as String
     */
    protected String getStringParam(HttpServletRequest request, String name) {
        return getStringParam(request, name, null);
    }

    /**
     * Get parameter as Integer
     */
    protected Integer getIntParam(HttpServletRequest request, String name, Integer defaultValue) {
        String value = request.getParameter(name);
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * Get parameter as Long
     */
    protected Long getLongParam(HttpServletRequest request, String name, Long defaultValue) {
        String value = request.getParameter(name);
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Long.valueOf(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * Check if request is JSON (AJAX)
     */
    protected boolean isJsonRequest(HttpServletRequest request) {
        String accept = request.getHeader("Accept");
        String contentType = request.getContentType();
        return (accept != null && accept.contains("application/json")) ||
               (contentType != null && contentType.contains("application/json")) ||
               request.getServletPath().endsWith(".json");
    }

    /**
     * Get current user from session
     */
    protected Object getCurrentUser(HttpSession session) {
        return session.getAttribute("SESSION_USER_KEY");
    }

    /**
     * Create a data grid response for EasyUI
     */
    protected Map<String, Object> createDataGrid(java.util.List<?> rows, long total) {
        Map<String, Object> result = new HashMap<>();
        result.put("rows", rows);
        result.put("total", total);
        return result;
    }

    /**
     * Create a data grid response for EasyUI (without pagination)
     */
    protected Map<String, Object> createDataGrid(java.util.List<?> rows) {
        return createDataGrid(rows, rows.size());
    }
}
