/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-19 00:38:10
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 16:26:26
 * @FilePath: /analysis_engine/src/main/java/com/shca/controller/SpringSessionJdbcController.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.controller;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/custom")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CustomController {
    private static final Logger log = LoggerFactory.getLogger(CustomController.class);
    @Resource private com.shca.util.DbService db;
    // @Autowired private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @PostConstruct
    public void init() {
        System.out.println("MyController initialized");
    }
    @GetMapping("/test")
    public String getMethodName() {
        return "test ok";
    }

    @GetMapping("getSampleRecordList")
    public Map<String, Object> getSampleRecordList(@RequestParam("sample_name") String sampleName)
        throws IOException {

        String validaterMsg = sampleNameValidator(sampleName);
        Map<String, Object> rtn = new HashMap<>();
        if (!"OK".equals(validaterMsg)) {
            rtn.put("error", validaterMsg);
            return rtn;
        }
        log.info(validaterMsg);
        List<Map<String, Object>> samples = db.select(
                    "SELECT id, sample_name, status, create_time, update_time " +
                    "FROM shca_project_cancer " +
                    "WHERE sample_name = ? ",
                        sampleName);
        log.info("samples: " + samples.size());
        // HashMap<String, Object> rtn = new HashMap<>();
        rtn.put("samples", samples);
        return rtn;
    }

    @RequestMapping(value="getProjectsBySampleName", method=RequestMethod.GET)
    public Map<String, Object> getProjectsBySampleName(@RequestParam("sample_name") String sampleName)
        throws IOException {

        String validaterMsg = sampleNameValidator(sampleName);
        Map<String, Object> rtn = new HashMap<>();
        if (!"OK".equals(validaterMsg)) {
            rtn.put("error", validaterMsg);
            return rtn;
        }
        String projectName = sampleName.trim().substring(0, sampleName.length()-3);
        List<Map<String, Object>> projects = db.select(
            "SELECT id, project_sn, project_name, sample_name_0, sample_name_1, " +
            "status, create_time, update_time, lims_status, lims_time, upload_time " +
            "FROM shca_project_project " +
            "WHERE project_name = ? ",
            projectName);
        rtn.put("projects", projects);
        return rtn;
    }

    @GetMapping("/deleteSample")
    // @ResponseBody
    public Map<String, Object> deleteSample(@RequestParam("sample_name") String sampleName)
        throws IOException {

        String validaterMsg = sampleNameValidator(sampleName);
        Map<String, Object> rtn = new HashMap<>();
        if (!"OK".equals(validaterMsg)) {
            rtn.put("error", validaterMsg);
            return rtn;
        }
        String projectName = sampleName.trim().substring(0, sampleName.length()-3);
        List<Map<String, Object>> projects = db.select(
            "SELECT id FROM shca_project_project " +
            "WHERE project_name = ? AND (sample_name_0 = ? or sample_name_1 = ?) " +
            "AND status NOT IN ('deleted', 'cleared') "
            , projectName, sampleName, sampleName);
        if (!projects.isEmpty()) {
            rtn.put("error", "sample is associated with some project, can not be deleted");
            return rtn;
        }
        // status cleared | finished | filed  |Failed  |running_step0|wait_running
        List<Map<String, Object>> samples = db.select(
            "SELECT id, sample_name, status from shca_project_cancer"
                + " WHERE sample_name = ? "
                + " AND status IN ('finished','filed','Failed','running_step0','wait_running','Aborted' )"
            , sampleName);

        if (samples.isEmpty()) {
            rtn.put("error", "no valid samples found" + sampleName);
            return rtn;
        }
        if (samples.size() > 1) {
            rtn.put("error", "too many valid samples found" + sampleName);
            return rtn;
        }
        String status = (String) samples.get(0).get("status");
        if ("filed".equals(status)) {
            rtn.put("error", "sample is filed, and can not be deleted");
            return rtn;
        }
        if ("running_step0".equals(status)) {
            rtn.put("error", "sample is running, and can not be deleted");
            return rtn;
        }
        if ("wait_running".equals(status)
            || "Aborted".equals(status)
            || "Failed".equals(status)
            || "finished".equals(status)) {
            db.update("UPDATE shca_project_cancer " +
            "SET status = 'cleared' " +
            "WHERE id = ? "
            , samples.get(0).get("id"));
            // return new JsonForward("ok");
        }
        try {
            deleteFastqFileDbInfo(sampleName);
            deleteFastqFiles(sampleName);
            deleteSampleDbInfo(sampleName);
            deleteSampleAnalysisFiles(sampleName);
            rtn.put("success", "delete sample: " + sampleName + " succeeded");
            return rtn;
        } catch (Exception e) {
            log.error("deleteSample error", e);
            rtn.put("error", "delete sample: " + sampleName + " error");
        }
        // return new JsonForward("delete sample: " + sampleName + "succeeded");
        rtn.put("success", "delete sample: " + sampleName + " succeeded");
        return rtn;
    }

    private String sampleNameValidator(String sampleName) {
        if (!StringUtils.hasLength(sampleName)) {
            return "error: sample name is empty";
        }
        Pattern pattern = Pattern.compile("[A-Z]{2}\\d{7}[A-Z]\\d{2}");
        if (!pattern.matcher(sampleName).matches()) {
            return "error: sample name is not valid, should be like: BZ240123B01 ";
        }
        return "OK";
    }

    private void deleteSampleDbInfo(String sampleName) {
        try {
            Map<String, Object> sample = db.selectUnique(
                "SELECT id, status from shca_project_cancer " +
                "WHERE sample_name = ? " +
                "AND status IN ('finished', 'Failed','wait_running','Aborted' )"
                , sampleName);
            db.delete("DELETE FROM shca_project_cancer WHERE id = ? "
            , (long)sample.get("id"));
        } catch (Exception e) {
            log.error("deleteSampleDbInfo error", e);
        }
    }
    private void deleteFastqFileDbInfo(String sampleName) {
        try {
            db.delete("DELETE FROM shca_project_cancer_file " +
            "WHERE sample_name = ? AND status = 'Successed'"
            , sampleName);
        } catch (Exception e) {
            log.error("deleteSampleFileDbInfo error", e);
        }
    }

    private void deleteFastqFiles(String sampleName) {
        Path fastqPath = Paths.get("/mydata/shca/rawdata/" + sampleName);
        Path trashbinPath = Paths.get("/mydata/trashbin");
        if (!Files.exists(fastqPath)) {
            return;
        }
        if (!Files.exists(trashbinPath)) {
            try {
                Files.createDirectory(trashbinPath);
            } catch (IOException e) {
                log.error("deleteFastqFiles error", e);
                return;
            }
        }
        try {
            Files.move(fastqPath, trashbinPath);
        } catch (IOException e) {
            log.error("move files to trashbin error", e);
        }
    }

    private Boolean deleteSampleAnalysisFiles(String sampleName) {
        Path samplePath = Paths.get("/mydata/shca/samples/" + sampleName);
        Path trashbinPath = Paths.get("/mydata/trashbin");
        if (!Files.exists(samplePath)) {
            return false;
        }
        if (!Files.exists(trashbinPath)) {
            try {
                Files.createDirectory(trashbinPath);
            } catch (IOException e) {
                log.error("deleteSampleAnalysisFiles error", e);
                return false;
            }
        }
        try {
            Files.move(samplePath, trashbinPath);
        } catch (IOException e) {
            log.error("move files to trashbin error", e);
            return false;
        }
        return true;
    }
}
