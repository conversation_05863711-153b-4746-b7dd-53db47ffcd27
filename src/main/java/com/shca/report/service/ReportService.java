package com.shca.report.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.shca.util.JsonUtils;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.shca.util.DbService;
import com.shca.util.MyFile;
import com.shca.config.SystemConfigService;
import com.shca.reflect.MethodParam;
import com.shca.util.CollectionUtils;
import com.shca.util.ConvertUtils;
import com.shca.util.FileUtils;
import com.shca.util.HttpUtils;
import com.shca.util.LogUtils;
import com.shca.util.StringUtils;

@Service
public class ReportService {

    private final static Logger log = LoggerFactory.getLogger(ReportService.class);
    @Resource private DbService db;
    @Resource private SystemConfigService config;
    private final static String QUERY_URL = "http://*************:9027/api/v1/queryForm";
    private final static String UPLOAD_URL = "http://*************:9027/api/v1/uploadResultZip";

    public Map<String, Object> query(@MethodParam(name = "project_name") String projectName) {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "系统错误: ";

        try {
            log.info("query report status of project [" + projectName + "]");
            String jsonResponse = HttpUtils.getRequest(QUERY_URL + "?formCode=" + projectName);
            List<Map<String, Object>> array = JsonUtils.parseArray(jsonResponse);

            if (array.isEmpty()) { return null;}
            log.info("get report query status result of project [" + projectName + "] is " + array);
            return array.get(0);
        } catch (Exception e) {
            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    public Map<String, Object> zip(@MethodParam(name = "project_name") String projectName) {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "系统错误: ";
        try {
            // Using injected config
            Map<String, Object> project = db.selectUnique("select sample_name_0,sample_name_1,status " +
                    " from shca_project_project where project_name=? order by id desc ", projectName);

            errCode = 1010;
            if (CollectionUtils.isEmpty(project)) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + projectName + "项目不存在");
                return ret;
            }

            errCode = 1020;
            if (!"finished".equals(project.get("status"))) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + projectName + "项目还未分析完成");
                return ret;
            }

            errCode = 1030;
            File dir = new File(config.getValue("workspace.project_path") + "/" + projectName + "/filesForUpload");
            if (!dir.exists()) {
                ret.put("errcode", errCode);
                ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + dir.getAbsolutePath());
                return ret;
            }
            List<File> files = new ArrayList<>();
            File file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_0") + "_mmr.tsv");
            if (file.exists()) {
                files.add(file);
            }
            String normalSampleName = project.get("sample_name_0").toString();
            if (normalSampleName.startsWith("BJ")|| normalSampleName.startsWith("BZ")
                || normalSampleName.startsWith("AJ")|| normalSampleName.startsWith("AZ")
                || normalSampleName.startsWith("CJ") || normalSampleName.startsWith("CZ")
                || normalSampleName.startsWith("GI") || normalSampleName.startsWith("IJ")
                || normalSampleName.startsWith("GJ") || normalSampleName.startsWith("GZ")
                || normalSampleName.startsWith("HJ") || normalSampleName.startsWith("HZ")) {
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_0") + "_mutation_filter.tsv");
                if (file.exists()) {
                    files.add( file);
                }
            } else {
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_0") + "_mutation.tsv");
                if (file.exists()) {
                    files.add( file);
                }
            }

            if (StringUtils.hasLength((String) project.get("sample_name_1"))) {
                // errCode = 1054;
                // file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_mutation_filter.tsv");
                // if (file.exists()) {
                //     // files.add( file);
                // }
                errCode = 1100;
                String tumorSampleName = project.get("sample_name_1").toString();
                if (tumorSampleName.startsWith("BZ")
                    || tumorSampleName.startsWith("AZ")
                    || tumorSampleName.startsWith("CZ")
                    || tumorSampleName.startsWith("GI")
                    || tumorSampleName.startsWith("GZ")
                    || tumorSampleName.startsWith("HZ")) {
                    file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_mutation_filter.tsv");
                    if (!file.exists()) {
                        ret.put("errcode", errCode);
                        ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                        return ret;
                    }
                    files.add(file);
                } else {
                    file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_mutation.tsv");
                    if (!file.exists()) {
                        ret.put("errcode", errCode);
                        ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                        return ret;
                    }
                    files.add(file);
                }

                errCode = 1060;
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_0") + "-"
                        + project.get("sample_name_1") + "_qc.csv");
                if (!file.exists()) {
                    ret.put("errcode", errCode);
                    ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                    return ret;
                }
                files.add(file);

                errCode = 1070;
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_cnv.csv");
                if (!file.exists()) {
                    ret.put("errcode", errCode);
                    ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                    return ret;
                }
                files.add(file);

                errCode = 1090;
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_cnvraw.tsv");
                if (!file.exists()) {
                    ret.put("errcode", errCode);
                    ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                    return ret;
                }
                files.add(file);

                // errCode = 1080;
                // file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_cnv_pgi.tsv");
                // if (file.exists()) {
                //     files.add(file);
                // }
                MyFile myFile = new MyFile(dir.getAbsolutePath());
                File[] myFiles = myFile.listFiles(project.get("sample_name_1") + ".*_cnv_pgi.tsv");
                if (myFiles.length > 0) {
                    file = new File(dir.getAbsolutePath() + "/" + myFiles[0].getName()) ;
                    if (file.exists()) {
                        files.add(file);
                    }
                }

                myFiles = myFile.listFiles(project.get("sample_name_1") + ".*_msi_pgi.tsv");
                if (myFiles.length > 0) {
                    file = new File(dir.getAbsolutePath() + "/" + myFiles[0].getName()) ;
                    if (file.exists()) {
                        files.add(file);
                    }
                }
                log.info("msi_pgi.tsv files: " + files.toString());
                myFiles = myFile.listFiles(project.get("sample_name_1") + ".*_snpindel_pgi.tsv");
                if (myFiles.length > 0) {
                    file = new File(dir.getAbsolutePath() + "/" + myFiles[0].getName()) ;
                    if (file.exists()) {
                        files.add(file);
                    }
                }

                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_1") + "_output.prefix");
                if (file.exists()) {
                    files.add(file);
                }

            } else {
                errCode = 1130;
                file = new File(dir.getAbsolutePath() + "/" + project.get("sample_name_0") + "_qc.csv");
                if (!file.exists()) {
                    ret.put("errcode", errCode);
                    ret.put("errmsg", errMsg + projectName + "项目上传文件不存在 - " + file.getAbsolutePath());
                    return ret;
                }
                files.add(file);
            }

            errCode = 1200;
            File zip = new File(config.getValue("workspace.project_path") + "/" + projectName
                    + "/filesForUpload_" + projectName + "_" + ConvertUtils.date2Str(new Date(), "yyyyMMddHHmm")
                    + ".zip");
            try (FileOutputStream fos = new FileOutputStream(zip)) {
                FileUtils.zip(fos, files.toArray(new File[0]));
            }

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");
            ret.put("zip", zip.getAbsolutePath());
            return ret;

        } catch (IOException e) {
            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    public Map<String, Object> upload(@MethodParam(name = "project_name") String projectName) {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 3000;
        String errMsg = "系统错误: ";
        CloseableHttpClient httpClient;
        CloseableHttpResponse httpResponse;
        HttpEntity entity;
        Date now = new Date();
        try {
            Map<String, Object> query = query(projectName);

            if (query == null) {
                ret.put("errcode", 3010);
                ret.put("errmsg", errMsg + "项目不存在");
                return ret;
            }

            if (!(Boolean) query.get("succ") || "finishReport".equals(query.get("status"))) {
                query.put("errcode", 2000);
                log.info("UPDATE project ->" + projectName + "in TABLE shca_project_project with query result ->" + query.toString());
                db.update("update shca_project_project set upload_result=?, upload_time=? where project_name=? ", query.toString(),
                    now, projectName);
                return query;
            }

            Map<String, Object> zip = zip(projectName);

            if ((Integer) zip.get("errcode") != 0) {
                return zip;
            }

            httpClient = HttpClients.createDefault(); // ?formCode=BZ2000023&operatorCode=admin
            HttpPost httpPost = new HttpPost(UPLOAD_URL);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 * 5)
                    .setConnectTimeout(60000 * 5).build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            // httpPost.addHeader("Content-Type", "multipart/form-data;charset=utf-8");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);
            builder.addPart("file", new FileBody(new File((String) zip.get("zip"))));
            builder.addTextBody("formCode", projectName);
            builder.addTextBody("operatorCode", "admin");

            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);

            httpResponse = httpClient.execute(httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            db.update("update shca_project_project set upload_result=?,upload_time=? where project_name=? ", result,
                    now, projectName);

            ret.put("errcode", 0);
            ret.put("errmsg", "ok");
            ret.put("response", result);

            return ret;
        } catch (IOException | UnsupportedOperationException e) {
            log.error(LogUtils.toString(e));
            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    public Map<String, Object> upload(@MethodParam(name = "project_name") String projectName,
            @MethodParam(name = "zip_path") String zipPath) {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 3000;
        String errMsg = "系统错误: ";

        CloseableHttpClient httpClient;
        CloseableHttpResponse httpResponse;
        HttpEntity entity;
        Date now = new Date();

        try {
            Map<String, Object> query = query(projectName);

            // 项目不存在这么判断有问题 @sneaker
            if (query == null) {
                ret.put("errcode", 3010);
                ret.put("errmsg", errMsg + "项目不存在");
                return ret;
            }

            // 已上传 @sneaker
            if (!(Boolean) query.get("succ") || "finishReport".equals(query.get("status"))) {
                query.put("errcode", 2000);
                log.info("UPDATE project ->" + projectName + "in TABLE shca_project_project with query result ->" + query.toString());
                db.update("update shca_project_project set upload_result=?, upload_time=? where project_name=? ", query.toString(),
                    now, projectName);
                return query;
            }

            httpClient = HttpClients.createDefault(); // ?formCode=BZ2000023&operatorCode=admin
            HttpPost httpPost = new HttpPost(UPLOAD_URL);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 * 5)
                    .setConnectTimeout(60000 * 5).build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            // httpPost.addHeader("Content-Type", "multipart/form-data;charset=utf-8");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);
            builder.addPart("file", new FileBody(new File(zipPath)));
            builder.addTextBody("formCode", projectName);
            builder.addTextBody("operatorCode", "admin");

            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);

            httpResponse = httpClient.execute(httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            // 此处更新上传结果，在entity == null 时，会更新空字符串到数据库 @sneaker
            db.update("update shca_project_project set upload_result=?,upload_time=? where project_name=? ", result,
                    now, projectName);

            // 上传结束，无论report接口是否返回成功，有返回值即认为此次上传成功
            ret.put("errcode", 0);
            ret.put("errmsg", "ok");
            ret.put("response", result);

            return ret;
        } catch (IOException | UnsupportedOperationException e) {
            log.error(LogUtils.toString(e));
            ret.put("errcode", errCode);
            ret.put("errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    public void sync() {
        List<Map<String, Object>> projectRows = db.select(" select project_name from shca_project_project " + // delete 'a' @sneaker
                " where status=? and upload_time is null and lims is not null " + // depends on upload_time is not safe
                                                                                  // @sneaker
                // " and (select count(*) from shca_project_project_qcsummary b where
                // a.project_sn=b.project_sn and qc_qualified=?)=? " +
                // @sneaker 2022-09-18  add is_filing status to prevent that upload fail but manually upload, and the project has been filing.
                // in these circumstances, the upload process will always trying to upload this project
                // and report error because of not finding the upload file and folder.
                " and lims_status=? and is_filing=? ", "finished", "finished", "0");

        List<String> projectNames = projectRows.stream()
                .map(row -> (String) row.get("project_name"))
                .collect(java.util.stream.Collectors.toList());

        log.info("projects to be reported :" + "[" +projectNames.toString() + "]");

        for (String projectName : projectNames) {
            Map<String, Object> ret = upload(projectName);
            log.info("report uploaded project: [" + projectName + "] and return message :[" + ret + "]");
        }

        log.debug("Starting check report status ...");
        reportStatusQuery();
        log.debug("Finished report status checking ...");
    }

    /*
    @sneaker 2022-05-23
    需要找出符合以下条件的项目
    1. 有lims的创建项目指令的项目（防止一些测试的项目，以及其他手动创建的项目，可跟条件二一并判断，但为清晰，此处特说明）
    2. 已上传lims成功，且同步盘已同步完成（排除还在分析中的项目）
    3. 还未获得report接口返回已出报告(未标记可备份字段,避免重复反复查询结果)

    需要根据查询结果做以下操作：
    1. 对已完成报告的项目进行标记
    2. 对已完成项目，自动将同步盘状态设置为已归档
    3. 调用wdl，删除同步盘数据
    */
    public void reportStatusQuery() {
        log.info("Entering reportStatusQuery method ....");
        List<Map<String, Object>> notReportedProjectRows = db.select( "SELECT project_name FROM shca_project_project " +
        " WHERE lims_status=? AND is_filing='0' AND cromwell_workflow_type_2 is not null limit 10"
        , "finished");

        List<String> notReportedProjectNames = notReportedProjectRows.stream()
                .map(row -> (String) row.get("project_name"))
                .collect(java.util.stream.Collectors.toList());

        for (String project : notReportedProjectNames) {

            try {
                log.info("the project for query: " + project);
                Map<String, Object> reportQueryResult = query(project);
                log.info("the project query result of [" + project + "] is :" + reportQueryResult);
                boolean reportQuerySucc = (Boolean) reportQueryResult.get("succ");
                log.info("reportQuerySucc is : " + reportQuerySucc);

                if ( reportQuerySucc ) {
                    String reportQueryErrorCode = (String) reportQueryResult.getOrDefault("code", "NULL");
                    String reportQueryStatus = (String) reportQueryResult.getOrDefault("status", "NULL");
                    log.info("reportQueryErrorCode: " + reportQueryErrorCode);
                    log.info("reportQueryStatus: " + reportQueryStatus);
                    log.debug("if report query error code is 0 " + "0".equals(reportQueryErrorCode));
                    log.debug("if report query status is finishMCAP or finishESB "
                        + ("finishMCAP".equals(reportQueryStatus) || "finishESB".equals(reportQueryStatus)));

                    if ( "0".equals(reportQueryErrorCode) && ("finishMCAP".equals(reportQueryStatus) || "finishESB".equals(reportQueryStatus))) {
                        log.info("updating the project filing status to 8: " + project);
                        db.update("UPDATE shca_project_project set is_filing=? where project_name=? ", "8",
                                project);
                    }
                } else {
                    // 查询出错，并非查询返回结果中的错误
                    log.info("the query of project [" + project + "] is broken, and the errmsg is: "
                            + reportQueryResult);
                }
            } catch (Exception e) {
                log.error(LogUtils.toString(e));
                log.info("continue ......");
            }
        }
    }

    public static void main(String... args) {

        // *************:9027/api/v1/queryForm?formCode=BZ2003899
        String json = HttpUtils.getRequest("http://***********:9027/api/v1/queryForm?formCode=B2001156");
        System.out.println("#1" + json);

        CloseableHttpClient httpClient;
        CloseableHttpResponse httpResponse;
        HttpEntity entity;
        try {
            httpClient = HttpClients.createDefault(); // ?formCode=BZ2000023&operatorCode=admin
            HttpPost httpPost = new HttpPost("http://***********:9027/api/v1/uploadResultZip");
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 * 5)
                    .setConnectTimeout(60000 * 5).build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            // httpPost.addHeader("Content-Type", "multipart/form-data;charset=utf-8");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);
            builder.addPart("file",
                    new FileBody(new File("/Users/<USER>/Desktop/filesForUpload_B2001156_202106111447.zip")));
            // builder.addPart( "B2000818K01", new FileBody( new File(
            // "/Users/<USER>/Desktop/workspaces/B2000818B-B2000818K_qc.csv")));
            builder.addTextBody("formCode", "B2001156");
            builder.addTextBody("operatorCode", "admin");

            HttpEntity multipart = builder.build();

            httpPost.setEntity(multipart);

            httpResponse = httpClient.execute(httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            System.out.println("upload: " + result);
        } catch (IOException | UnsupportedOperationException e) {
            throw new RuntimeException(e);
        }
    }
}
