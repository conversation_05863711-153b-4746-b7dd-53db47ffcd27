/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-25 16:58:57
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 12:19:25
 * @FilePath: /analysis_engine/src/main/java/com/shca/support/SysConfigManager.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.support;

import java.util.List;
import java.util.Map;

public interface SysConfigManager {

    // public static SysConfigManager getInstance() {
    //     return DbSysConfigManager.getInstance();
    // }

    public  void refresh();

    public  String getValue(String key);

    public  String getValue(String key, String defValue);

    public  void remove(String keyString);

    public  void setValue(String key, String value);

    public  Map<String,Object> getProperty(String key);

    public  List<Map<String, Object>> getProperties(String group);
}
