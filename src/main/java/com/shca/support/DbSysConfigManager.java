package com.shca.support;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

public class DbSysConfigManager implements SysConfigManager, ApplicationListener<ContextRefreshedEvent> {

    @Resource private JdbcTemplate jdbcTemplate;
    private Map<Object, Map<String, Object>> configs = new HashMap<>();
    // private SysConfigManager INSTANCE = new DbSysConfigManager();

    @Override
    public synchronized void refresh() {
        System.out.println("refresh");
        this.configs.clear();
        if (jdbcTemplate == null) {
            System.out.println("jdbc null");
        }
        System.out.println("jdbc template class -> " + jdbcTemplate.getClass());
        String configSql = "SELECT id, config_name AS name, config_value AS value, group_name AS group_name "
            + " FROM comm_sys_config ORDER BY SORT";
        List<Map<String, Object>> configList = jdbcTemplate.queryForList(configSql);
        System.out.println("refresh3");

        // this.configs = CollectionUtils.list2Map(configs, "name");
        Map<Object, Map<String, Object>> configsMap = new HashMap<>();
        for (Map<String, Object> elem: configList) {
            configsMap.put(elem.get("name"), elem);
        }
        this.configs = configsMap;
        setValue("param.os_name", System.getProperties().getProperty("os.name"));
    }

    // public SysConfigManager getInstance() {
    //     refresh();
    //     return this.INSTANCE;
    // }

    @Override
    public String getValue(String key) {
        return getValue(key, null);
    }

    @Override
    public String getValue(String key, String defValue) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return defValue;
        }
        Object value = config.get("value");
        if (value == null) {
            return defValue;
        }
        return value.toString();
    }


    @Override
    public void setValue(String key, String value) {
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return;
        }
        jdbcTemplate.update("update comm_sys_config set config_value=?,update_time=? where id=? ",
            new Object[] {value, new Date(), config.get("id")});
        config.put("value", value);
    }


    @Override
    public Map<String, Object> getProperty(String key) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        return Collections.unmodifiableMap(configs.get(key));
    }

    @Override
    public List<Map<String,Object>> getProperties(String group) {
        if (!StringUtils.hasLength(group)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        List<Map<String,Object>> configList = new ArrayList<>();
        for (Map<String,Object> config : this.configs.values()) {
            if (config.get( "group_name").equals( group)) {
                configList.add( config);
            }
        }
        return Collections.unmodifiableList( configList);
    }

    @Override
    public void remove(String keyString) {
        if( keyString.isEmpty()) {
            return;
        }
        if (configs.get(keyString) == null) {
            return;
        }

        configs.remove(keyString);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        refresh();
    }
}
