package com.shca.support;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

public class DbSysConfigManager extends SysConfigManager {

    @Autowired
    JdbcTemplate jdbcTemplate;

    private Map<Object, Map<String, Object>> configs = new HashMap<Object, Map<String, Object>>();
    private static SysConfigManager INSTANCE;

    public DbSysConfigManager(JdbcTemplate jdbcTemplate) {
        if (jdbcTemplate == null) {
            throw new IllegalArgumentException("db cannot be null");
        }

        this.jdbcTemplate = jdbcTemplate;

        if (INSTANCE != null) {
            throw new RuntimeException("Config Class is Singleton");
        }
        refresh();
        INSTANCE = this;
    }

    public synchronized void refresh() {
        System.out.println("refresh");
        this.configs.clear();
        if (jdbcTemplate == null) {
            System.out.println("jdbc null");
        }
        System.out.println("jdbc template class -> " + jdbcTemplate.getClass());
        String configSql = "SELECT id, config_name as name, config_value as value, group_name as group_name " 
            + " FROM comm_sys_config order by sort";
        List<Map<String, Object>> configs = jdbcTemplate.queryForList(configSql);
        System.out.println("refresh3");

        // this.configs = CollectionUtils.list2Map(configs, "name");
        Map<Object, Map<String, Object>> configsMap = new HashMap<>();
        for (Map<String, Object> elem: configs) {
            configsMap.put(elem.get("name"), elem);
        }
        this.configs = configsMap;
        setValue("param.os_name", System.getProperties().getProperty("os.name"));
    }

    public static SysConfigManager getInstance() {
        return INSTANCE;
    }

    public String getValue(String key) {
        return getValue(key, null);
    }

    public String getValue(String key, String defValue) {
        if (key.length()==0 || key == null) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return defValue;
        }
        Object value = config.get("value");
        if (value == null) {
            return defValue;
        }
        return value.toString();
    }


    public void setValue(String key, String value) {
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return;
        }
        jdbcTemplate.update("update comm_sys_config set config_value=?,update_time=? where id=? ", 
            new Object[] {value, new Date(), config.get("id")});
        config.put("value", value);
    }


    public Map<String, Object> getProperty(String key) {
        if (key.length()==0 || key == null) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        return Collections.unmodifiableMap(configs.get(key));
    }

    public List<Map<String,Object>> getProperties(String group) {
        if (group.length()==0 || group == null) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        List<Map<String,Object>> configs = new ArrayList<>();
        for (Map<String,Object> config : this.configs.values()) {
            if (config.get( "group_name").equals( group)) {
                configs.add( config);
            }
        }
        return Collections.unmodifiableList( configs);
    }

    public void remove(String keyString) {
        if( keyString.isEmpty()) {
            return;
        }
        if (configs.get(keyString) == null) {
            return;
        }

        configs.remove(keyString);
    }
}
