package com.shca.lims.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.report.service.ReportService;
import com.ywang.framework.mvc.Constant;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.reflect.MethodParam;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.*;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.log4j.Logger;

import java.io.*;
import java.util.*;

/**
 * Created by henrywang on 2021/6/18.
 */
public class LimsService2 {

    private final static Logger LOGGER = Logger.getLogger( LimsService2.class);
    private Db db;
    private CromwellClient client;

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    private String projectListUrl;
    private String uploadQcSummaryUrl;
    private int interval = 2;
    private ReportService reportService;

    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    public void setDb(Db db) {
        this.db = db;
    }

    public void setProjectListUrl(String projectListUrl) {
        this.projectListUrl = projectListUrl;
    }

    public void setUploadQcSummaryUrl(String uploadQcSummaryUrl) {
        this.uploadQcSummaryUrl = uploadQcSummaryUrl;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }


    // http://localhost:8080/shca/service-limsService_getProjectList.json?start_time=2021-11-25&end_time=2021-11-27
    public Map<String,Object> getProjectList(@MethodParam(name="start_time")Date start, @MethodParam(name="end_time")Date end) {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "Lims获取项目列表失败: ";
        List<Map<String,Object>> projects = new ArrayList<>();
        List<Map<String,Object>> deletedProjects = new ArrayList<>();

        try {
            if (end == null) {
                end = (start == null ? new Date() : new Date( start.getTime() + DateUtils.ONE_DATE * interval));
            }
            if (start == null) {
                start = new Date(end.getTime() - DateUtils.ONE_DATE * interval);
            }

            errCode = 1010;
            Map<String,String> params = new HashMap<>();
            params.put( "startTime", ConvertUtils.date2Str( start, "yyyy-MM-dd HH:mm"));
            params.put( "endTime", ConvertUtils.date2Str( end, "yyyy-MM-dd HH:mm"));
            LOGGER.info( "Lims Request: " + JSON.toJSONString( params));
            String json = HttpUtils.postRequest( projectListUrl,  JSON.toJSONString( params));

            errCode = 1020;
            JSONObject jsonObject = JSON.parseObject( json);

            boolean success = jsonObject.getBoolean("success");
            if (!success) {
                jsonObject.put( "errcode", errCode);
                return jsonObject;
            }
            LOGGER.info( "Lims Response success. " );
            errCode = 1030;
            SysConfigManager config = SysConfigManager.getInstance();
            JSONArray array = jsonObject.getJSONArray("analysisTaskResult");
            LOGGER.info( "Lims Response result array size: [" + array.size() + "]");
            for (int i = 0; i < array.size(); i++) {
                JSONObject taskJSONObject = array.getJSONObject(i);
                // System.out.println( "project_sn =" + taskJSONObject.getString( "runningNumber"));
                Map<String, Object> project = new HashMap<>();
                project.put( "project_sn", taskJSONObject.getString( "runningNumber"));
                project.put( "project_no", taskJSONObject.getString( "runningNumber"));
                project.put( "project_name", taskJSONObject.getString( "productId"));
                project.put( "lims", taskJSONObject.toJSONString());

                String reanalysis = taskJSONObject.getString("reanalysis");
                //  System.out.println( "#project_sn =" + taskJSONObject.getString( "runningNumber") + ": " + reanalysis);
                if (StringUtils.isBlank(reanalysis)) { // 新项目
                    JSONArray codeJSONArray = taskJSONObject.getJSONArray("code");
                    for (int j = 0; j < codeJSONArray.size(); j++) {
                        JSONObject codeJSONObject = codeJSONArray.getJSONObject(j);
                        for (String key : codeJSONObject.keySet()) {
                            project.put("sample_name_" + j, key);
                        }
                    }

                    String cancerKind = config.getValue("cancer_kind." + taskJSONObject.getString("productMask"));
                    if (StringUtils.isBlank(cancerKind)) {
                        cancerKind = taskJSONObject.getString("productMask");
                    }
                    project.put("cancer_kind", cancerKind);
                    LOGGER.info("[LimsService2] cancerKind2->" + cancerKind);

                    projects.add( project);
                } else { // 删除项目
                    project.put( "sample_names", reanalysis);

                    JSONArray codeJSONArray = taskJSONObject.getJSONArray("code");
                    for (int j = 0; j < codeJSONArray.size(); j++) {
                        JSONObject codeJSONObject = codeJSONArray.getJSONObject(j);
                        for (String key : codeJSONObject.keySet()) {
                            project.put("sample_name_" + j, key);
                        }
                    }

                    String cancerKind = config.getValue("cancer_kind." + taskJSONObject.getString("productMask"));
                    if (StringUtils.isBlank(cancerKind)) {
                        cancerKind = taskJSONObject.getString("productMask");
                    }
                    project.put("cancer_kind", cancerKind);

                    deletedProjects.add( project);
                }
            }


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");
            ret.put( "projects", projects);
            ret.put( "delete_projects", deletedProjects);

            return ret;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());
            return ret;
        }
    }

    // http://localhost:8080/shca/service-limsService_uploadQcSummary.json?project_no=210601032
    public Map<String,Object> uploadQcSummary(@MethodParam(name="project_no")String projectNo) {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "QcSummary上传失败: ";
        Date now = new Date();
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        String result = "";

        try {
            SysConfigManager config = SysConfigManager.getInstance();
            Map<String,Object> project = db.selectUnique( "select id,project_sn,project_name,sample_name_0,sample_name_1,status,lims " +
                    " from shca_project_project where project_sn=? ", projectNo);

            if (CollectionUtils.isEmpty( project)) {
                ret.put( "errcode", errCode);
                ret.put( "errmsg", errMsg + "项目不存在");
                return ret;
            }

            errCode = 1010;
            // hard code  @sneaker
            if (! "finished".equals( project.get( "status"))) {
                ret.put( "errcode", errCode);
                ret.put( "errmsg", errMsg + "项目没有分析完成 - " + project.get( "status"));
                return ret;
            }

            errCode = 1020;
            String homePath = config.getValue("workspace.project_path") + "/" + project.get("project_name");

            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( uploadQcSummaryUrl);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 * 5).setConnectTimeout(60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            //            httpPost.addHeader("Content-Type", "multipart/form-data;charset=utf-8");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);

            errCode = 1030;
            File summaryFile0 = new File( homePath + "/qc_summary/" + project.get("sample_name_0") + "_summary.txt");
            System.out.println( summaryFile0.getName() + ": " + summaryFile0.exists());
            builder.addPart(  summaryFile0.getName(), new FileBody( summaryFile0));

            errCode = 1040;
            if (StringUtils.hasLength( (String)project.get("sample_name_1"))) {
                File summaryFile1 = new File( homePath + "/qc_summary/" + project.get("sample_name_1") + "_summary.txt");
                System.out.println( summaryFile1.getName() + ": " + summaryFile1.exists());
                builder.addPart(  summaryFile1.getName(), new FileBody( summaryFile1));
            }

            errCode = 1050;
            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);

            errCode = 1060;
            httpResponse = httpClient.execute( httpPost);
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            errCode = 1070;
            String limsResult = db.selectUnique( "select lims_result from shca_project_project where id=?", new StringCallback(), project.get("id"));
            JSONArray jsonArray = new JSONArray();
            if (JSON.isValidArray( limsResult)) {
                jsonArray = JSON.parseArray( limsResult);
            }
            if (JSON.isValidObject( limsResult)) {
                jsonArray.add( JSON.parseObject( limsResult));
            }
            JSONObject resultJson = JSON.parseObject( result);
            resultJson.put( "oper_time", ConvertUtils.date2Str( now, "yyyy-MM-dd HH:mm"));
            resultJson.put( "oper_name", "uploadQcSummary");
            jsonArray.add( 0, resultJson);
            db.update( "update shca_project_project set lims_status=?,lims_time=?,lims_result=?,update_time=?,update_username=? where id=? "
                    , "finished", now, jsonArray.toJSONString(), now, "LimsService2.uploadQcSummary", project.get("id"));

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");
            ret.put( "lims_result", result);
            return ret;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());
            return ret;
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }

    public Map<String,Object> deleteProject(@MethodParam(name="project_no")String projectNo, @MethodParam(name="sample_names")String sampleNames, @MethodParam(name="lims")String lims) {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "删除配对任务失败: ";
        Date now = new Date();
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        String result = "";

        try {
            String[] array = sampleNames.split( ",");

            for (String sampleName : array) {
                int cnt = db.selectUnique( "select count(*) from shca_project_cancer where sample_name=? and status not in (?,?,?)"
                        , new IntegerCallback(), sampleName, "wait_step0", "finished", "aborted");

                if (cnt > 0) {
                    ret.put( "errcode", errCode);
                    ret.put( "errmsg", errMsg + "项目正在运行中");
                    return ret;
                }
            }

            errCode = 1010;
            for (String sampleName : array) {
                db.delete( "delete from shca_project_cancer where sample_name=?", sampleName);
                db.update( "update shca_project_cancer_file set status=?,update_time=?,update_username=? where sample_name=? and status=? "
                    , "deleted", now, "LimsService2#" + projectNo, sampleName, "successed");
            }

            errCode = 1020;
            int updated = db.update( "update shca_project_project set status=?,update_time=?,update_username=? where project_sn=? and status in (?,?,?,?,?) "
                    , "deleted", now, "LimsService2.deleteProject", projectNo, "finished", "wait_sample", "aborted", "failed", "deleted");
            if (updated == 0) {
                ret.put( "errcode", errCode);
                ret.put( "errmsg", errMsg + "项目不存在");
                return ret;
            }

            errCode = 1030;
            Map<String,String> params = new HashMap<>();
            params.put( "runningNumber", projectNo);
            params.put( "sampleCode", sampleNames);

            result = HttpUtils.postRequest( uploadQcSummaryUrl,  JSON.toJSONString( params));
            String limsResult = db.selectUnique( "select lims_result from shca_project_project where project_sn=?", new StringCallback(), projectNo);
            JSONArray jsonArray = new JSONArray();
            if (JSON.isValidArray( limsResult)) {
                jsonArray = JSON.parseArray( limsResult);
            }
            if (JSON.isValidObject( limsResult)) {
                jsonArray.add( JSON.parseObject( limsResult));
            }
            JSONObject resultJson = JSON.parseObject( result);
            resultJson.put( "oper_time", ConvertUtils.date2Str( now, "yyyy-MM-dd HH:mm"));
            resultJson.put( "oper_name", "deleteProject");

            if (JSON.isValidObject( lims)) {
                resultJson.put("lims", JSON.parseObject(lims));
            }
            jsonArray.add( 0, resultJson);

            db.update( "update shca_project_project set lims_status=?,lims_time=?,lims_result=?,update_time=?,update_username=? where project_sn=? "
                    , "deleted", now, jsonArray.toJSONString(), now, "LimsService2.deleteProject", projectNo);

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");
            ret.put( "project_no", projectNo);
            ret.put( "sample_names", sampleNames);
            ret.put( "lims_result", result);
            return ret;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());
            return ret;
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }

    // http://localhost:8080/shca/service-limsService_sync.json
    public void sync() {
        Date now =  DateUtils.clear( new Date());
        Date start = new Date( now.getTime() - DateUtils.ONE_DATE * interval);
        Date end = new Date( now.getTime() + DateUtils.ONE_DATE);

        now = new Date();

        Map<String,Object> resultForProjectList = getProjectList( start, end);

        if ((Integer)resultForProjectList.get( "errcode") == 0) {
            List<Map<String,Object>> projects = (List<Map<String,Object>>)resultForProjectList.get( "projects");
            List<Map<String,Object>> deletedProjects =(List<Map<String,Object>>)resultForProjectList.get( "delete_projects");

            // 删除项目
            System.out.println( "delete project = " + deletedProjects.size());
            for (Map<String,Object> project : deletedProjects) {
                Map<String,Object> ret = deleteProject( (String)project.get( "project_sn"), (String)project.get( "sample_names"), (String)project.get( "lims"));
                System.out.println( project.get( "project_sn") + ": " + ret);
            }

            // 对比分析任务同步
            System.out.println( "projects = " + projects.size());
            for (Map<String,Object> project : projects) {
                try {
//                    System.out.println( "project_sn = " + project.get( "project_sn"));
                    int cnt = db.selectUnique("select count(*) from shca_project_project where project_sn=? "
                            , new IntegerCallback(), project.get("project_sn"));
                    if (cnt > 0) { // 对比分析任务已同步
//                        System.out.println( project.get( "project_sn") + " continue");
                        continue;
                    }

                    String status = "wait_sample";
                    int duplicate = db.selectUnique("select count(*) from shca_project_project where sample_name_0=? and sample_name_1=? " +
                                    " and status not in (?,?) ", new IntegerCallback(), project.get("sample_name_0"), project.get("sample_name_1")
                            , "deleted", "duplicate");
                    if (duplicate > 0) {
                        status = "duplicate";
                    }

                    String script = null;
                    Map<String, Object> cromwellConfig = db.selectUnique(
                            " select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                    " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc"
                            , project.get("cancer_kind"), true, 1);

                    if (cromwellConfig != null) {
                        script = (String) cromwellConfig.get("cancer_script");
                    }

                    db.insert("insert into shca_project_project(project_sn,project_no,project_name,sample_name_0,sample_name_1" +
                                    ",cancer_kind,cancer_script_0,status,lims,create_username" +
                                    ",create_time,update_username,update_time,lims_status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                            , project.get("project_sn"), project.get("project_no"), project.get("project_name")
                            , project.get("sample_name_0"), project.get("sample_name_1"), project.get("cancer_kind")
                            , script, status, project.get("lims"), "LimsService2", now, "LimsService2", now, "wait_upload");
                } catch (Exception e) {
                    e.printStackTrace();
                    LOGGER.error( LogUtils.toString( e));
                }
            }
        }

        List<Map<String,Object>> uploadProjects = db.select("select a.project_sn from shca_project_project a " +
                " inner join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_2=b.WORKFLOW_EXECUTION_UUID " +
                " where a.status=? and a.lims_status=? and b.workflow_status in (?) ", "finished", "wait_upload", "Succeeded");
        for (Map<String,Object> project : uploadProjects) {
            uploadQcSummary( (String)project.get( "project_sn"));
        }
    }

    public void pocSync() {
        Date now =  DateUtils.clear( new Date());
        Date start = new Date( now.getTime() - DateUtils.ONE_DATE * interval);
        Date end = new Date( now.getTime() + DateUtils.ONE_DATE);

        now = new Date();

        Map<String,Object> resultForProjectList = getProjectList( start, end);

        if ((Integer)resultForProjectList.get( "errcode") == 0) {
            List<Map<String,Object>> projects = (List<Map<String,Object>>)resultForProjectList.get( "projects");
            List<Map<String,Object>> deletedProjects =(List<Map<String,Object>>)resultForProjectList.get( "delete_projects");
            //todo
            File dir = new File( "");

            // 对比分析任务同步
            for (Map<String,Object> project : projects) {
                try {
//                    System.out.println( "project_sn = " + project.get( "project_sn"));

                    Map<String,Object> projectMap = db.selectUnique( "select project_name,status from shca_project_project where project_sn=? ", project.get("project_sn"));

                    if (!CollectionUtils.isEmpty( projectMap)) {
                        if ("wait_sample".equals( projectMap.get( "status"))) {
                            upload( dir, (String)projectMap.get( "project_name"));
                        }
                    } else {
                        String status = "wait_sample";
                        int duplicate = db.selectUnique("select count(*) from shca_project_project where sample_name_0=? and sample_name_1=? " +
                                        " and status not in (?,?) ", new IntegerCallback(), project.get("sample_name_0"), project.get("sample_name_1")
                                , "deleted", "duplicate");
                        if (duplicate > 0) {
                            status = "duplicate";
                        }

                        String script = null;
                        Map<String, Object> cromwellConfig = db.selectUnique(
                                " select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc"
                                , project.get("cancer_kind"), true, 1);
                        if (cromwellConfig != null) {
                            script = (String) cromwellConfig.get("cancer_script");
                        }

                        db.insert("insert into shca_project_project(project_sn,project_no,project_name,sample_name_0,sample_name_1" +
                                        ",cancer_kind,cancer_script_0,status,lims,create_username" +
                                        ",create_time,update_username,update_time,lims_status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                                , project.get("project_sn"), project.get("project_no"), project.get("project_name")
                                , project.get("sample_name_0"), project.get("sample_name_1"), project.get("cancer_kind")
                                , script, status, project.get("lims"), "LimsService2", now, "LimsService2", now, "wait_upload");
                    }

                    //todo: uploadSummary copy

                    uploadQcSummary( (String)project.get("project_sn"));


                    //todo: uploadDir filesForUpload_HZ2103872_202107210710.zip
                    upload( dir, (String)project.get( "project_name"));

                } catch (Exception e) {
                    e.printStackTrace();
                    LOGGER.error( LogUtils.toString( e));
                }
            }

            // 删除项目
            System.out.println( "delete project = " + deletedProjects.size());
            for (Map<String,Object> project : deletedProjects) {
                Map<String,Object> ret = deleteProject( (String)project.get( "project_sn"), (String)project.get( "sample_names"), (String)project.get( "lims"));
                System.out.println( project.get( "project_sn") + ": " + ret);
            }
        }


        List<Map<String,Object>> uploadProjects = db.select("select project_sn from shca_project_project " +
                " where status=? and lims_status=?", "finished", "wait_upload");
        for (Map<String,Object> project : uploadProjects) {
            uploadQcSummary( (String)project.get( "project_sn"));
        }
    }

    public void upload(File dir, String projectName) {
        //filesForUpload_HZ2103872_202107210710
        for (File file : dir.listFiles()) {
            if (file.isDirectory()) {
                continue;
            }
            String fileName = file.getName();
            if (fileName.endsWith( ".zip") && fileName.startsWith( "filesForUpload_" +  projectName)) {
                reportService.upload( projectName, file.getAbsolutePath());
            }
        }
    }


    // test and old codes
    public void test() {
        Date now =  DateUtils.clear( new Date());
        Date start = new Date( now.getTime() - DateUtils.ONE_DATE * interval);
        Date end = new Date( now.getTime() + DateUtils.ONE_DATE);

        now = new Date();

        Map<String,Object> resultForProjectList = getProjectList( start, end);

        if ((Integer)resultForProjectList.get( "errcode") == 0) {
            List<Map<String,Object>> projects = (List<Map<String,Object>>)resultForProjectList.get( "projects");
            List<Map<String,Object>> deletedProjects =(List<Map<String,Object>>)resultForProjectList.get( "delete_projects");

            // 对比分析任务同步
            for (Map<String,Object> project : deletedProjects) {
                try {
                    int cnt = db.selectUnique("select count(*) from shca_project_project where project_sn=? "
                            , new IntegerCallback(), project.get("project_sn"));
                    if (cnt > 0) { // 对比分析任务已同步
                        continue;
                    }

                    String status = "wait_sample";
                    int duplicate = db.selectUnique("select count(*) from shca_project_project where sample_name_0=? and sample_name_1=? " +
                                    " and status not in (?,?) ", new IntegerCallback(), project.get("sample_name_0"), project.get("sample_name_1")
                            , "deleted", "duplicate");
                    if (duplicate > 0) {
                        status = "duplicate";
                    }

                    String script = null;
                    Map<String, Object> cromwellConfig = db.selectUnique(
                            " select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                                    " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc"
                            , project.get("cancer_kind"), true, 1);
                    if (cromwellConfig != null) {
                        script = (String) cromwellConfig.get("cancer_script");
                    }

                    db.insert("insert into shca_project_project(project_sn,project_no,project_name,sample_name_0,sample_name_1" +
                                    ",cancer_kind,cancer_script_0,status,lims,create_username" +
                                    ",create_time,update_username,update_time,lims_status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                            , project.get("project_sn"), project.get("project_no"), project.get("project_name")
                            , project.get("sample_name_0"), project.get("sample_name_1"), project.get("cancer_kind")
                            , script, status, project.get("lims"), "LimsService2.test", now, "LimsService2.test", now, "wait_upload");
                } catch (Exception e) {
                    LOGGER.error( LogUtils.toString( e));
                }
            }
        }
    }

    private boolean merge(String sampleName, String mergeSampleName0, String mergeSampleName1, String rawData, String cancerKind) {
        try {
            SysConfigManager config = SysConfigManager.getInstance();
            Date now = new Date();
            File source = new File(Constant.WEB_INF_PATH + "geneFileMerge.wdl");

            Map<String,Object> mergeFile0 = db.selectUnique( "select id,sample_name,store_path,sample_type,cancer_kind,nova " +
                            " from shca_project_cancer_file a where sample_name=? and rawdata=? and status=? "
                    , mergeSampleName0, rawData, "successed");

            if (CollectionUtils.isEmpty( mergeFile0)) {
                return false;
            }

            Map<String,Object> mergeFile1 = db.selectUnique( "select id,sample_name,store_path,sample_type,cancer_kind,nova " +
                            " from shca_project_cancer_file a where sample_name=? and rawdata=? and status=? "
                    , mergeSampleName1, rawData, "successed");

            if (CollectionUtils.isEmpty( mergeFile1)) {
                return false;
            }


            File mergeDir = new File( config.getValue("workspace.merge_path") + "/" + ConvertUtils.date2Str( "yyyyMMdd") + "/" + sampleName);
            if (!mergeDir.exists()) {
                mergeDir.mkdirs();
            }
            String fileName = "lims-" + sampleName + "_merge_" + rawData + ".fastq.gz";
            String storePath = mergeDir.getAbsolutePath() + "/" + fileName;

            File json = new File( mergeDir + "/" + sampleName + "_" + ConvertUtils.date2Str(new Date(), "yyyyMMddHHmm") + "_pretty.json");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("geneFileMerge.originalFile_WF", (String)mergeFile0.get("store_path"));
            jsonObject.put("geneFileMerge.appendedFile_WF", (String)mergeFile1.get("store_path"));
            jsonObject.put("geneFileMerge.outputFile_WF", storePath);

            BufferedWriter writer = new BufferedWriter(new FileWriter( json));
            writer.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            writer.close();


            WorkflowsResponse response = client.send(new WorkflowsRequest(source, json, null, null));
            LOGGER.info( "Lims Merge: " + JSON.toJSONString( response));

            Map<String,Object> file = new HashMap<>();
            file.put( "sample_name", sampleName);
            file.put( "cancer_kind", cancerKind);
            file.put( "sample_type", mergeFile0.get( "sample_type"));
            file.put( "rawdata", rawData);
            file.put( "nova", -1);
            file.put( "file_name", fileName);
            file.put( "file_size", -1);
            file.put( "store_path", storePath);
            file.put( "modified_time", new Date());
            file.put( "status", "merge");
            file.put( "cromwell_workflow_id_0", response.getId());
            file.put( "cromwell_workflow_status_0", response.getStatus());
            file.put( "create_username", "LimsService");
            file.put( "create_time", now);
            file.put( "update_username", "LimsService");
            file.put( "update_time", now);

            List<Object> params = new ArrayList<>();
            String sql = DbUtils.insertSql( "shca_project_cancer_file", file, params);
            db.insert( sql, params.toArray( new Object[0]));

            db.update( "update from shca_project_cancer_file set status=? where id in (?,?)", "to_merge", mergeFile0.get( "id"), mergeFile1.get( "id"));
            db.delete( "delete from shca_project_cancer where sample_name=?", sampleName);

            return true;
        } catch (Exception e) {
            LogUtils.toString( e);

            return false;
        }
    }

    public static void main(String...args) {
        Date now =  DateUtils.clear( new Date());
        Date start = new Date( now.getTime() - DateUtils.ONE_DATE * 2);
        Date end = new Date( now.getTime() + DateUtils.ONE_DATE);

        Map<String,String> params = new HashMap<>();
        params.put( "startTime", ConvertUtils.date2Str( start, "yyyy-MM-dd HH:mm"));
        params.put( "endTime", ConvertUtils.date2Str( end, "yyyy-MM-dd HH:mm"));
        System.out.println( JSON.toJSONString( params));
        String json = HttpUtils.postRequest( "http://*************:9977/rest/RestfulService/informationCapture",  JSON.toJSONString( params));
        //        JSONObject jsonObject = JSON.parseObject( json);

        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "QcSummary上传失败: ";

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        String result = "";

        try {

            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( "http://*************:9977/rest/RestfulService/getAnalysisResult");
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 * 5).setConnectTimeout(60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            //            httpPost.addHeader("Content-Type", "multipart/form-data;charset=utf-8");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);


            File summaryFile0 = new File( "/Users/<USER>/Desktop/BZ2103607B01_summary.txt");
            File summaryFile1 = new File( "/Users/<USER>/Desktop/BZ2103607F01_summary.txt");

            builder.addPart(  summaryFile0.getName(), new FileBody( summaryFile0));
            builder.addPart(  summaryFile1.getName(), new FileBody( summaryFile1));

            errCode = 1050;
            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);

            errCode = 1060;
            httpResponse = httpClient.execute( httpPost);
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            System.out.println( result);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }


    public void adjust() {
        List<Map<String,Object>> projects = db.select( "select a.id,project_no,a.project_name,sample_name_0,sample_name_1 " +
                " ,(select count(*) from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and status=?) num_0 " +
                " ,(select count(*) from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and status=?) num_1 " +
                " from shca_project_project a " +
                " where status=?", "deleted", "deleted", "deleted");

        for (Map<String,Object> project : projects) {
            int num0 = ((Number)project.get( "num_0")).intValue();
            int num1 = ((Number)project.get( "num_1")).intValue();

            if (num0 <= 2 && num1 <= 2) {
                db.update( "update shca_project_cancer_file set update_username=? where sample_name in (?,?)  "
                        , "LimsService2#" + project.get( "project_no"), project.get( "sample_name_0"), project.get( "sample_name_1"));
            }
        }

    }
}
