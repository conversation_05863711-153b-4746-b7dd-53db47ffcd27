package com.shca.lims.service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.shca.support.DbSysConfigManager;

import com.shca.util.CmdOperation;

public class LimsServiceImpl {

    private final static Logger log = LoggerFactory.getLogger( LimsServiceImpl.class);
    // @Resource private Db db;
    @Resource ObjectMapper objectMapper;
    @Resource JdbcTemplate jmt;
    @Resource RestTemplate restTemplate;
    @Resource DbSysConfigManager config;

    private String projectListUrl;
    private String uploadQcSummaryUrl;
    private int interval = 15;

    public void setProjectListUrl(String projectListUrl) {
        this.projectListUrl = projectListUrl;
    }
    public void setUploadQcSummaryUrl(String uploadQcSummaryUrl) {
        this.uploadQcSummaryUrl = uploadQcSummaryUrl;
    }
    public void setInterval(int interval) {
        this.interval = interval;
    }

    // http://localhost:8080/shca/service-limsService_sync.json
    @SuppressWarnings("unchecked")
    public void sync() {
        LocalDate today = LocalDate.now();
        LocalDate startDate = today.minusDays(interval);
        LocalDate endDate = today.plusDays(1);

        Map<String,Object> resultForProjectList = getProjectListFromLims( startDate, endDate);
        if ((Integer)resultForProjectList.get( "errCode") != 0) {
            return;
        }
        if (resultForProjectList.isEmpty()) {
            return;
        }
        List<Map<String,Object>> projects =
            (List<Map<String,Object>>)resultForProjectList.get( "projects");
        List<Map<String,Object>> deletedProjects =
            (List<Map<String,Object>>)resultForProjectList.get( "delete_projects");

        // 删除项目
        log.info( "lims delete projects count = " + deletedProjects.size());
        if (!deletedProjects.isEmpty()) {
            deletedProjects.stream()
                .forEach(
                    project -> deleteProject(
                        (String)project.get( "project_sn"),
                        (String)project.get( "delete_names")));
        }
        // 对比分析任务同步
        log.info( "lims sync projects count = " + projects.size());
        if (!projects.isEmpty()) {
            projects.stream()
                .filter(this::projectNotExists)
                .forEach(this::insertNewProject);
        }

        List<String> qcUploadProjects = getQcUploadProjects();
        if (qcUploadProjects.isEmpty()) {
            return;
        }
        qcUploadProjects.forEach(this::uploadQcSummary);
        // qcUploadProjects.forEach(this::uploadReport);
    }

    // http://localhost:8080/shca/service-limsService_getProjectList.json?start_time=2021-11-25&end_time=2021-11-27
    @SuppressWarnings("unchecked")
    private Map<String,Object> getProjectListFromLims(LocalDate startDate, LocalDate endDate) {
        // @MethodParam(name="start_time") LocalDate startDate,
        // @MethodParam(name="end_time") LocalDate endDate) {

        Map<String,Object> ret = new HashMap<>();
        ObjectNode objectNode;
        String errMsg = "Lims获取项目列表失败: ";

        String limsResult = getLimsQueryResult(startDate, endDate);
        if (limsResult.isEmpty()) {
            ret.put("errCode", 2);
            ret.put("errMsg", errMsg + "lims query failed");
            return ret;
        }

        try {
            objectNode = (ObjectNode)objectMapper.readTree(limsResult);
            if (objectNode.isNull()) {
                throw new NullPointerException();
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret.put("errCode", 3);
            ret.put("errMsg", errMsg + "parse lims query result failed");
            return ret;
        }

        if (!objectNode.get("success").asBoolean() ) {
                objectNode.put("errCode", 3);
                objectNode.put( "errMsg", "lims request result error");
            try {
                return (Map<String, Object>)objectMapper.treeToValue(objectNode, Map.class);
            } catch (JsonProcessingException e) {
                ret.put("errMsg", errMsg + "json process error");
                ret.put("errCode", 4);
                e.printStackTrace();
                return ret;
            }
        }
        return parseLimsQueryResult(objectNode);
    }
    private String getLimsQueryResult(LocalDate startDate, LocalDate endDate) {
        String jsonResult = "";
        if (endDate == null) {
            endDate = (startDate == null ? LocalDate.now() : startDate.plusDays(interval));
        }
        if (startDate == null) {
            startDate = endDate.minusDays(interval);
        }
        Map<String,String> params = new HashMap<>();
        params.put( "startTime", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        params.put( "endTime", endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        try {
            jsonResult = restTemplate.postForObject(
                projectListUrl, objectMapper.writeValueAsString(params),String.class);
        } catch (RestClientException | JsonProcessingException e) {
            e.printStackTrace();
            return jsonResult;
        }
        if (jsonResult == null) {
            jsonResult = "";
        }
        return jsonResult;
    }

    private Map<String, Object> parseLimsQueryResult(ObjectNode objectNode ) {

        Map<String,Object> result = new HashMap<>();
        if (objectNode.isNull()) {
            return result;
        }
        List<Map<String,Object>> projects = new ArrayList<>();
        List<Map<String,Object>> deletedProjects = new ArrayList<>();
        ArrayNode taskArray = objectMapper.convertValue(
            objectNode.get("analysisTaskResult"),  ArrayNode.class);
        if (taskArray.isNull() || taskArray.size() <= 0 ) {
            result.put("errCode", 5);
            result.put("errMsg", "analysisTaskResult is empty");
            return result;
        }
        for(JsonNode task : taskArray) {
            ObjectNode taskObjectNode = (ObjectNode) task;
            Map<String, Object> project = new HashMap<>();
            project.put( "project_sn", taskObjectNode.get("runningNumber"));
            if (!StringUtils.hasLength(taskObjectNode.get("reanalysis").asText())) {
                project.put( "project_sn", taskObjectNode.get( "runningNumber"));
                project.put( "project_name", taskObjectNode.get( "productId"));
                project.put( "lims", taskObjectNode.toString());
                project.put("cancer_kind", taskObjectNode.get("productMask").toString());
                ArrayNode codeArrayNode = (ArrayNode) taskObjectNode.get("code");
                codeArrayNode.get(0).fieldNames()
                    .forEachRemaining(name->project.put("sample_name_0", name));
                if (codeArrayNode.size() > 1) {
                    codeArrayNode.get(1).fieldNames()
                        .forEachRemaining(name->project.put("sample_name_1", name));
                }
                projects.add( project);
            } else {
                project.put( "delete_names", taskObjectNode.get("reanalysis"));
                deletedProjects.add( project);
            }
        }

        result.put( "errMsg", "OK");
        result.put("errCode", 0);
        result.put( "projects", projects);
        result.put( "delete_projects", deletedProjects);
        return result;
    }

    private boolean projectNotExists(Map<String, Object> project) {
        boolean result = false;
        try {
            @SuppressWarnings("deprecation")
            int cnt = jmt.queryForObject(
                "SELECT count(*) FROM shca_project_project WHERE project_sn=?",
                new Object[] {project.get("project_sn")},
                 Integer.class);
        // } catch (Exception e) {
        //     int cnt = db.selectUnique("SELECT count(*) FROM shca_project_project "
        //         + " WHERE project_sn=? "
        //         , new IntegerCallback(), project.get("project_sn"));
            if (cnt > 0) {
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return result;
        }
        result = true;
        return result;
    }

    private String getInsertProjectStatus(Map<String, Object> project) {
        String status = "wait_sample";
        try {
            String dupSql =  "SELECT count(*) FROM shca_project_project "
                + " WHERE sample_name_0=? and sample_name_1=? AND status not in (?,?) ";
            @SuppressWarnings("deprecation")
            int duplicate = jmt.queryForObject(dupSql, new Object[] {
                project.get("sample_name_0"), project.get("sample_name_1"), "deleted", "duplicate"
            }, Integer.class);
            // int duplicate = db.selectUnique( "SELECT count(*) FROM shca_project_project "
            //     + " WHERE sample_name_0=? and sample_name_1=? AND status not in (?,?) "
            //     , new IntegerCallback(), project.get("sample_name_0"), project.get("sample_name_1")
            //     , "deleted", "duplicate");

            if (duplicate > 0) {
                status = "duplicate";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return status;
        }
        return status;
    }

    private String getScriptNameFromCromwellConfig(Map<String, Object> project) {
        String result = null;
        try {
            String configSql =  " SELECT cancer_script "
            + " FROM shca_cromwell_config "
            + " WHERE cancer_kind=? AND auto_script=? AND script_step=? ORDER BY ID DESC ";

            Map<String, Object> cromwellConfig = jmt.queryForMap(configSql,
                new Object[] {project.get("cancer_kind"), true, 1});

            // Map<String, Object> cromwellConfig = db.selectUnique(
            //     " SELECT cancer_script "
            //     + " FROM shca_cromwell_config "
            //     + " WHERE cancer_kind=? AND auto_script=? AND script_step=? ORDER BY ID DESC "
            //     , project.get("cancer_kind"), true, 1);
            if (cromwellConfig != null) {
                result = (String) cromwellConfig.get("cancer_script");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return result;
        }
        return result;
    }

    private void insertNewProject(Map<String, Object> project) {
        String status = getInsertProjectStatus(project);
        String script = getScriptNameFromCromwellConfig(project);
        try {
            String insertSql = "INSERT INTO shca_project_project "
            + " (project_sn, project_no, project_name, sample_name_0, sample_name_1, cancer_kind, cancer_script_0, "
            + " status, lims, create_username, create_time, update_username, update_time, lims_status) "
            + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            jmt.update(insertSql, project.get("project_sn"), project.get("project_sn"), project.get("project_name")
                , project.get("sample_name_0"), project.get("sample_name_1"), project.get("cancer_kind")
                , script, status, project.get("lims"), "LimsServiceNew", LocalDateTime.now(), "LimsService2", LocalDateTime.now(), "wait_upload");

                // db.insert("INSERT INTO shca_project_project "
                // + " (project_sn, project_no, project_name, sample_name_0, sample_name_1, cancer_kind, cancer_script_0, "
                // + " status, lims, create_username, create_time, update_username, update_time, lims_status) "
                // + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                // , project.get("project_sn"), project.get("project_sn"), project.get("project_name")
                // , project.get("sample_name_0"), project.get("sample_name_1"), project.get("cancer_kind")
                // , script, status, project.get("lims"), "LimsService2", new Date(), "LimsService2", new Date(), "wait_upload");
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
    }

    // http://localhost:8080/shca/service-limsService_uploadQcSummary.json?project_no=210601032
    private Map<String,Object> uploadQcSummary(String projectSn) {
        // @MethodParam(name="project_sn")
        Map<String,Object> ret = new HashMap<>();
        String errMsg = "QcSummary上传失败: ";
        String result = "";

        try {
            String projectSelectSql =  " SELECT id, project_sn, project_name, sample_name_0, sample_name_1, status, lims"
                + " FROM shca_project_project WHERE project_sn=? ";
            Map<String,Object> project = jmt.queryForMap(projectSelectSql, new Object[] {projectSn});
            //  Map<String,Object> project = db.selectUnique(
            //     " SELECT id, project_sn, project_name, sample_name_0, sample_name_1, status, lims" +
            //     " FROM shca_project_project WHERE project_sn=? ", projectSn);

            if (CollectionUtils.isEmpty( project)) {
                ret.put( "errMsg", errMsg + "项目不存在");
                return ret;
            }
            if (! "finished".equals( project.get( "status"))) {
                ret.put( "errMsg", errMsg + "项目状态异常 - " + project.get( "status"));
                return ret;
            }
            if (!ifTransferFinished(project)) {
                ret.put( "errMsg", errMsg + "项目同步未完成 - " + project.get( "status"));
                log.info( "projectNo->" + projectSn
                    + "  projectName->" + project.get("project_name"));
                return ret;
            }

            String homePath = config.getValue("workspace.project_path") + "/" + project.get("project_name");
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            // headers.set("Content-type", "application/json;charset=UTF-8");
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            File summaryFile0 = new File( homePath + "/qc_summary/" + project.get("sample_name_0") + "_summary.txt");
            if (summaryFile0.exists()) {
                log.info(  "adding upload file: " + summaryFile0.getName() );
                body.add( summaryFile0.getName(), summaryFile0);
            }
            if (StringUtils.hasLength((String)project.get("sample_name_1"))) {
                File summaryFile1 = new File( homePath + "/qc_summary/" + project.get("sample_name_1") + "_summary.txt");
                if (summaryFile1.exists()) {
                    log.info( "adding upload file: " + summaryFile1.getName());
                    body.add( summaryFile1.getName(), summaryFile1);
                }
            }

            HttpEntity<MultiValueMap<String, Object>> requestEntity =
                new HttpEntity<MultiValueMap<String,Object>>(body, headers);

            ResponseEntity<String> responseEntity =
                restTemplate.postForEntity(uploadQcSummaryUrl, requestEntity, String.class);
            result = responseEntity.getBody();

            if (result==null || result.isEmpty()) {
                ret.put( "errMsg", "lims upload server response error");
                return ret;
            }
            handleLimsPostResult(projectSn, result);
            ret.put( "errMsg", "ok");
            ret.put( "lims_result", result);
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            ret.put( "errMsg", errMsg + e.getMessage());
            return ret;
        }
    }

    private void deleteProject(String projectSn, String deleteNames) {
        // @MethodParam(name="project_sn")String projectSn,
        // @MethodParam(name="delete_names")String deleteNames) {

        String errMsg = "删除配对任务失败: ";
        Map<String, Object> errResult = getDeleteErrorInfo(projectSn, deleteNames);

        if (!((int)errResult.get("errCode")==0)) {
            handleLimsPostResult(projectSn, uploadLims(errResult));
            return;
        }
        if (!StringUtils.hasLength((String)errResult.get("project_sn"))) {
            errResult.put("project_sn", projectSn);
            handleLimsPostResult(projectSn, uploadLims(errResult));
            return;
        }
        // LocalDateTime now = LocalDateTime.now();
        String[] deleteNameArray = deleteNames.split( ",");

        try {
            String updateStatusSql = "UPDATE shca_project_project "
                + " SET status='deleted', update_time=?, update_username='LimsServiceNew.deleteProject' "
                + " WHERE project_sn=? ";
            jmt.update(updateStatusSql, LocalDateTime.now(), projectSn);
            // db.update( "UPDATE shca_project_project "
            //     + " SET status='deleted', update_time=?, update_username='LimsServiceNew.deleteProject' "
            //     + " WHERE project_sn=? "
            //     , now,  projectSn);
            // TODO
            // CmdOperation.moveDir("/mydata/shca/projects/" + sampleName);
        } catch (Exception e) {
            e.printStackTrace();
            errResult.put( "errMsg", errMsg + "delete project error");
            errResult.put("errCode", LimsDeleteStatusEnum.UNKNOWN.ordinal());
            handleLimsPostResult(projectSn, uploadLims(errResult));
            return;
        }

        if (deleteNameArray.length > 1) {
            String[] sampleNameArray = Arrays.copyOfRange(deleteNameArray, 1, deleteNameArray.length);
            for (String sampleName : sampleNameArray) {
                try {
                    String updateStatusSql = "UPDATE shca_project_cancer_file "
                    + " SET status='deleted', update_time=?, update_username='LimsServiceNew' "
                    + " WHERE sample_name=? and status='successed' ";
                    int updated = jmt.update(updateStatusSql, LocalDateTime.now(), sampleName);
                    // int updated = db.update( "UPDATE shca_project_cancer_file "
                    //     + " SET status='deleted', update_time=?, update_username='LimsService' "
                    //     + " WHERE sample_name=? and status='successed' "
                    //     , now, sampleName);
                    if (updated == 0) {
                        errResult.put( "errMsg", errMsg + "fastq file not exists");
                        errResult.put("errCode", LimsDeleteStatusEnum.FASTQNOTEXIST.ordinal());
                        handleLimsPostResult(projectSn, uploadLims(errResult));
                        return;
                    }
                    // TODO
                    // CmdOperation.moveDir("/mydata/shca/rawdatas/" + sampleName);
                } catch (Exception e) {
                    e.printStackTrace();
                    errResult.put( "errMsg", errMsg + "delete fastq file error");
                    errResult.put("errCode", LimsDeleteStatusEnum.UNKNOWN.ordinal());
                    handleLimsPostResult(projectSn, uploadLims(errResult));
                    return;
                }

                try {
                    jmt.update( " UPDATE shca_project_cancer SET status = 'cleared' "
                        + "WHERE sample_name=? AND status != 'cleared' ", sampleName);
                    // db.update(
                    //     " UPDATE shca_project_cancer SET status = 'cleared' "
                    //     + "WHERE sample_name=? AND status != 'cleared' "
                    //     , sampleName);
                    CmdOperation.moveDir("/mydata/shca/samples/" + sampleName);
                    // TODO mv sample files to trashbin
                } catch (Exception e) {
                    e.printStackTrace();
                    errResult.put( "errMsg", errMsg + "delete sample error");
                    errResult.put("errCode", LimsDeleteStatusEnum.UNKNOWN.ordinal());
                    handleLimsPostResult(projectSn, uploadLims(errResult));
                    return;
                }
            }
            handleLimsPostResult(projectSn, uploadLims(errResult));
        }
        errResult.put( "errMsg", "OK");
        errResult.put("errCode", LimsDeleteStatusEnum.SUCCEEDED.ordinal());

        handleLimsPostResult(projectSn, uploadLims(errResult));
        return;
    }

    private Map<String, Object> getDeleteErrorInfo(String projectSn, String deleteNames) {
        Map<String,Object> result = new HashMap<>();
        String errMsg = "删除配对任务失败: ";
        if (!StringUtils.hasLength(projectSn)) {
            result.put("errMsg", errMsg + "project sn is valid");
            result.put("errCode", LimsDeleteStatusEnum.UNKNOWN.ordinal());
            return result;
        }
        result.put( "project_sn", projectSn);

        if (deleteNames.isEmpty()) {
            result.put( "errMsg", errMsg + "delete names are empty");
            result.put("errCode", LimsDeleteStatusEnum.PROJECTEMPTY.ordinal());
            return result;
        }
        result.put( "delete_names", deleteNames);

        String[] deleteNameArray = deleteNames.split( ",");
        String projectName = StringUtils.trimAllWhitespace(deleteNameArray[0]);

        if (projectName.isEmpty()) {
            result.put( "errMsg", errMsg + "project name is empty");
            result.put("errCode", LimsDeleteStatusEnum.PROJECTEMPTY.ordinal());
            return result;
        }
        // Map<String, Object> project = db.selectUnique(
        //     " SELECT project_name, status FROM shca_project_project WHERE project_sn=? "
        //     , projectSn);

        Map<String, Object> project = jmt.queryForMap(
            " SELECT project_name, status FROM shca_project_project WHERE project_sn=? ",
            projectSn);

        if (CollectionUtils.isEmpty(project)) {
            result.put( "errMsg", errMsg + "project not exists");
            result.put("errCode", LimsDeleteStatusEnum.PROJECTNOTEXIST.ordinal());
            return result;
        }

        String projectStatus = (String)project.get("status");
        if (!(projectStatus.equals("finished") ||projectStatus.equals("aborted")
            || projectStatus.equals("failed"))) {
            result.put( "errMsg", errMsg + "project is analyzing");
            result.put("errCode", LimsDeleteStatusEnum.PROJECTRUNNING.ordinal());
            return result;
        }
        if (!projectName.equals((String)project.get("project_name"))) {
            result.put( "errMsg", errMsg + "lims info error");
            result.put("errCode", LimsDeleteStatusEnum.NOTMATCH.ordinal());
            return result;
        }

        if (deleteNameArray.length > 1) {
            String[] sampleNameArray = Arrays.copyOfRange(deleteNameArray, 1, deleteNameArray.length);
            Map<String, Object> sample = null;
            for (String sampleName : sampleNameArray) {
                sampleName = StringUtils.trimAllWhitespace(sampleName);
                if (sampleName.isEmpty()) {
                    result.put( "errMsg", errMsg + "sample name is empty");
                    result.put("errCode", LimsDeleteStatusEnum.SAMPLEEMPTY.ordinal());
                    return result;
                }
                if (!sampleName.contains(projectName)) {
                    result.put( "errMsg", errMsg + "lims info error");
                    result.put("errCode", LimsDeleteStatusEnum.NOTMATCH.ordinal());
                    return result;
                }
                // sample = db.selectUnique(
                //     "SELECT id, sample_name, status FROM shca_project_cancer "
                //     + " WHERE sample_name=? and status !=?"
                //     , sampleName,  "cleared");
                sample = jmt.queryForMap(
                    "SELECT id, sample_name, status FROM shca_project_cancer "
                    + " WHERE sample_name=? and status !=?",
                   new Object[] {sampleName, "cleared"});
                if (CollectionUtils.isEmpty(sample)) {
                    result.put( "errMsg", errMsg + "sample not exists");
                    result.put("errCode", LimsDeleteStatusEnum.SAMPLENOTEXIST.ordinal());
                    return result;
                }
                String sampleStatus = (String)sample.get("status");
                if (!(sampleStatus.equals("finished")
                    || sampleStatus.equals("aborted")
                    || sampleStatus.equals("failed")
                    ||sampleStatus.equals("wait_running"))) {
                    result.put( "errMsg", errMsg + "sample is analyzing");
                    result.put("errCode", LimsDeleteStatusEnum.SAMPLERUNNING.ordinal());
                    return result;
                }
            }
        }

        result.put( "errMsg", "OK");
        result.put("errCode", LimsDeleteStatusEnum.SUCCEEDED.ordinal());
        return result;
    }

    private void handleLimsPostResult(String projectSn, String result) {
        LocalDateTime now = LocalDateTime.now();
        if (!StringUtils.hasLength(projectSn) || result.isEmpty()) {
            return;
        }
        String updateString = "";
        try {
            ObjectNode resultNode = (ObjectNode) objectMapper.readTree(result);
            resultNode.put("oper_time",
                now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
            resultNode.put("oper_name", "uploadQcSummary");

            // String limsResult = db.selectUnique(
            //     "SELECT lims_result FROM shca_project_project WHERE project_sn=?"
            //     , new StringCallback()
            //     , projectSn);
            String limsResult = jmt.queryForObject(
                "SELECT lims_result FROM shca_project_project WHERE project_sn=?",
                String.class, projectSn);

            //db.selectUnique maybe return null
            if (!StringUtils.hasLength(limsResult)) {
                updateString = resultNode.toString();
            } else {
                ArrayNode arrayNode = objectMapper.createArrayNode();
                if (objectMapper.readTree(limsResult).isArray()) {
                    arrayNode = (ArrayNode) objectMapper.readTree(limsResult);
                }
                if (objectMapper.readTree(limsResult).isObject()) {
                    arrayNode.add(objectMapper.readTree(limsResult));
                }
                arrayNode.insert(0, resultNode);
                updateString = arrayNode.toString();
            }
            // db.update( "UPDATE shca_project_project "
            //     + "SET lims_status=?, lims_time=?, lims_result=?, update_time=?, update_username=? "
            //     + " WHERE project_sn=? "
            //     , "finished"
            //     , now, updateString
            //     , now, "LimsService.uploadQcSummary"
            //     , projectSn);
            String updateLimsResultSql =  "UPDATE shca_project_project "
            + "SET lims_status=?, lims_time=?, lims_result=?, update_time=?, update_username=? "
            + " WHERE project_sn=? ";
            jmt.update(updateLimsResultSql,
            "finished", LocalDateTime.now(), updateString, LocalDateTime.now(),
                    "LimsServiceNew.uploadQcSummary", projectSn);

        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
    }

    private List<String> getQcUploadProjects() {
        try {
            // List<String> result = db.select(
            //     "SELECT project_sn, cromwell_workflow_id_1 FROM shca_project_project "
            //     + " WHERE status=? AND lims_status=? AND is_filing=? "
            //     , new StringCallback()
            //     , "finished", "wait_upload", '0'
            // );
            String selectUploadProjectSql =
                "SELECT project_sn FROM shca_project_project "
                + " WHERE status=? AND lims_status=? AND is_filing=? ";
            List<String> resultList = jmt.queryForList(selectUploadProjectSql,  String.class,"finished", "wait_upload", '0');
            return resultList.stream()
                    .filter(projectSn->ifTransferFinished(projectSn))
                    .collect(Collectors.toList());
            // return db.select(
            //     "SELECT a.project_sn FROM shca_project_project a "
            //         + " INNER JOIN cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_1=b.WORKFLOW_EXECUTION_UUID "
            //         + " WHERE a.status=? AND a.lims_status=? AND a.is_filing=? AND b.workflow_status in (?) "
            //         , "finished", "wait_upload", '0', "Succeeded");
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    private String uploadLims(Map<String, Object> uploadParamMap) {
        log.info("upload map ->" + uploadParamMap);
        String limsResult = "";
        if (uploadParamMap.isEmpty()|| uploadParamMap == null) {
            return limsResult;
        }
        uploadParamMap.put("runningNumber", uploadParamMap.get("project_sn"));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(uploadParamMap,headers);
            limsResult = restTemplate.postForObject(
                uploadQcSummaryUrl,
                entity, String.class);
            return limsResult;
        } catch (Exception e) {
            e.printStackTrace();
            return limsResult;
        }
    }

    // private void uploadReport(File dir, String projectName) {
    //     //filesForUpload_HZ2103872_202107210710
    //     for (File file : dir.listFiles()) {
    //         if (file.isDirectory()) {
    //             continue;
    //         }
    //         String fileName = file.getName();
    //         if (fileName.endsWith( ".zip") && fileName.startsWith( "filesForUpload_" +  projectName)) {
    //             reportService.upload( projectName, file.getAbsolutePath());
    //         }
    //     }
    // }
    // private void uploadReport(String projectName) {
    //     String zipFilePath = "/mydata/shca/projects/" + projectName + "/" + projectName + "-filesForUpload.zip";
    //     File zipFile = new File(zipFilePath);
    //     if (!zipFile.exists() || !zipFile.isFile()) {
    //         return;
    //     }
    //     reportService.upload(projectName, zipFilePath);
    // }

    private boolean ifTransferFinished(Map<String, Object> projectMap) {
        if (CollectionUtils.isEmpty(projectMap)) {
            return false;
        }
        if (projectMap.get("id") != null) {
            return ifTransferFinished(((long)projectMap.get("id")));
        }
        if (projectMap.get("project_sn") != null) {
            return ifTransferFinished((String)projectMap.get("project_sn"));
        }
        return false;
    }

    private boolean ifTransferFinished(String projectSn) {
        String selectTransferStatusByProjectSn =
            "SELECT cromwell_workflow_id FROM shca_project_project_transfer_file "
            + " WHERE cromwell_workflow_type='create' AND project_sn = ? "
            + " ORDER BY update_time DESC " ;
        return ifTransferFinishedByCromwell(selectTransferStatusByProjectSn, projectSn);
    }

    private boolean ifTransferFinished(long projectId) {
        String selectTransferStatusByProjectId =
            "SELECT cromwell_workflow_id FROM shca_project_project_transfer_file "
            + " WHERE cromwell_workflow_type='create' AND project_id = ? "
            + " ORDER BY update_time DESC " ;
        return ifTransferFinishedByCromwell(selectTransferStatusByProjectId, ((Long)projectId).toString());
    }

    private boolean ifTransferFinishedByCromwell(String sqlString, String arg) {
        String cromwellWorkflowId = null;
        if (!arg.isEmpty()) {
            try {
                // cromwellWorkflowId = db.selectUnique(sqlString,
                //     new StringCallback(), arg);
                cromwellWorkflowId = jmt.queryForObject(sqlString, String.class, arg);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        if (cromwellWorkflowId==null) {
            return false;
        }
        String selectTransferStatusByWorkflowId = "SELECT WORKFLOW_STATUS "
            + " FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
            + " WHERE WORKFLOW_EXECUTION_UUID = ? AND WORKFLOW_NAME = 'fileTransfer' ";

        // String fileTransferStatus = db.selectUnique(
        //     selectTransferStatusByWorkflowId,
        //     new StringCallback(),
        //     cromwellWorkflowId);
        String fileTransferStatus = jmt.queryForObject(selectTransferStatusByWorkflowId, String.class, cromwellWorkflowId);
        if (fileTransferStatus == null) {
            return false;
        }
        if (fileTransferStatus.equals("Succeeded")) {
            return true;
        }
        return false;
    }
}
