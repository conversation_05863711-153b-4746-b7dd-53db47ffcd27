/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-10 01:53:09
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 17:37:42
 * @FilePath: /analysis_engine/src/main/java/com/shca/TomCat.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import java.io.File;

import org.apache.catalina.Context;
import org.apache.catalina.WebResourceRoot;
import org.apache.catalina.startup.Tomcat;
import org.apache.catalina.webresources.DirResourceSet;
import org.apache.catalina.webresources.StandardRoot;

public class TomcatEmbeddedServer {
    public static void main(String[] args) throws Exception {
        // Create an instance of Tomcat
        Tomcat tomcat = new Tomcat();
        tomcat.setPort(8099);
        tomcat.getConnector();
        Context tomcatContext = tomcat.addWebapp("", new File("src/main/webapp").getAbsolutePath());
        WebResourceRoot resources = new StandardRoot(tomcatContext);
        resources.addPreResources(new DirResourceSet(
            resources,
            "/WEB-INF/classes",
            new File("target/classes").getAbsolutePath(),
            "/"));
        resources.addPreResources(new DirResourceSet(
            resources,
            "/",
            new File("src/main/resources").getAbsolutePath(),
            "/"
        ));
        // resources.addPreResources(new DirResourceSet(
        //     resources,
        //     "/WEB-INF/lib",
        //     new File("web/lib").getAbsolutePath(),
        //     "/"
        // ));
        tomcatContext.setResources(resources);
        // Start the Tomcat server
        System.out.println("Tomcat started on port 8080");
        tomcat.start();
        tomcat.getServer().await();
    }
}
