package com.shca.dao;

import java.util.List;

import com.shca.entity.Fastq;
import com.shca.entity.enums.FastqStatusEnum;

public interface FastqDao {

    Fastq selectById(long id);
    long insert(Fastq fastq);
    void deleteById(long id);
    void clearedById(long id);
    long update(Fastq fastq);
    List<Fastq>  getFastqListByStatus(FastqStatusEnum enumStatus);
    List<Fastq> getReadyFastqList();
} 
