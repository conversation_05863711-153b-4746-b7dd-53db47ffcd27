package com.shca.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.TimeZone;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.Nullable;

import com.shca.entity.Sample;
import com.shca.entity.enums.SampleStatusEnum;
import com.shca.util.ConvertUtils;

public class SampleRowMapper implements RowMapper<Sample> {

    @Override
    @Nullable
    public Sample mapRow(ResultSet rs, int rowNum) throws SQLException {
        Sample sample = new Sample();
        sample.setId(rs.getLong("id"));
        sample.setSampleName(rs.getString("sample_name"));
        sample.setCancerName(rs.getString("cancer_kind"));
        sample.setStatus(
            ConvertUtils.getEnumFromString(
                SampleStatusEnum.class,
                rs.getString("status").toUpperCase()));
        System.out.println(rs.getString("status"));
        sample.setFastqIdR1(rs.getLong("rawdata_r1"));
        sample.setFastqIdR2(rs.getLong("rawdata_r2"));
        sample.setSamplePath(rs.getString("workspace"));
        sample.setPriority(rs.getInt("priority"));
        sample.setCreateUsername(rs.getString("create_username"));
        sample.setUpdateUsername(rs.getString("update_username"));
        ZoneId zoneId = ZoneId.systemDefault();
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        sample.setCreateTime(
            rs.getTimestamp("create_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        sample.setUpdateTime(
            rs.getTimestamp("update_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        return sample;
    }
    
}
