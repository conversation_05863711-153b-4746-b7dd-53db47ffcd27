package com.shca.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.TimeZone;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.Nullable;

import com.shca.entity.Project;
import com.shca.entity.Project.ProjectFilingStatusEnum;
import com.shca.entity.enums.ProjectStatusEnum;
import com.shca.util.ConvertUtils;

public class ProjectRowMapper implements RowMapper<Project>  {
    
    @Override
    @Nullable
    public Project mapRow(ResultSet rs, int rowNum) throws SQLException {
        Project project = new Project();
        ZoneId zoneId = ZoneId.systemDefault();
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        project.setId(rs.getLong("id"));
        project.setProjectSn(rs.getString("project_sn"));
        project.setProjectName(rs.getString("project_name"));
        project.setBSampleName(rs.getString("sample_name_0"));
        project.setKSampleName(rs.getString("sample_name_1"));
        project.setCancerName(rs.getString("cancer_kind"));
        project.setStatus(
            ConvertUtils.getEnumFromString(
                ProjectStatusEnum.class, 
                rs.getString("status").toUpperCase()));
        project.setPriority(rs.getInt("priority"));
        project.setIsFiling(ProjectFilingStatusEnum.gStatusEnumByCode(Integer.parseInt(rs.getString("is_filing"))));
        project.setProjectPath(rs.getString("workspace"));
        project.setLimsJson(rs.getString("lims"));
        project.setLimsStatus(rs.getString("lims_status"));
        if (rs.getTimestamp("lims_time") != null) {
            project.setLimsUploadTime(
                rs.getTimestamp("lims_time", Calendar.getInstance(timeZone)).toLocalDateTime());  
        } 
        project.setLimsUploadResult(rs.getString("lims_result"));
        project.setFileUploadResult(rs.getString("upload_result")); 
        if (rs.getTimestamp("upload_time") != null) {
            project.setFileUploadTime(
                rs.getTimestamp("upload_time", Calendar.getInstance(timeZone)).toLocalDateTime()
            ); 
        }
        project.setCreateUsername(rs.getString("create_username"));
        project.setUpdateUsername(rs.getString("update_username"));
        project.setCreateTime(
            // ((ResultSetImpl) rs).getLocalDateTime(rs.findColumn("lims_time")));
            rs.getTimestamp("create_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        project.setUpdateTime(
            rs.getTimestamp("update_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        
        return project;
    }    

}
