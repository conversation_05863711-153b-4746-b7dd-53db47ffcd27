package com.shca.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.TimeZone;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.Nullable;
import com.shca.entity.Fastq;
import com.shca.entity.enums.FastqStatusEnum;
import com.shca.util.ConvertUtils;

public class FastqRowMapper implements RowMapper<Fastq> {

    @Override
    @Nullable
    public Fastq mapRow(ResultSet rs, int rowNum) throws SQLException {
        Fastq fastq = new Fastq();
        fastq.setId(rs.getLong("id"));
        fastq.setSampleName(rs.getString("sample_name"));
        fastq.setCancerName(rs.getString("cancer_kind"));
        fastq.setRawdata(rs.getString("rawdata"));
        fastq.setFileName(rs.getString("file_name"));
        fastq.setFileSize(rs.getLong("file_size"));
        fastq.setStorePath(rs.getString("store_path"));
        fastq.setMd5(rs.getString("md5"));
        fastq.setStatus(
            ConvertUtils.getEnumFromString(
                FastqStatusEnum.class, 
                rs.getString("status").toUpperCase()));
        fastq.setCreateUsername(rs.getString("create_username"));
        fastq.setUpdateUsername(rs.getString("update_username"));
        ZoneId zoneId = ZoneId.systemDefault();
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // LocalDateTime localDateTime = LocalDateTime.parse(rs.getString("create_time"), formatter);
        fastq.setModifiedTime(
            rs.getTimestamp("modified_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        fastq.setCreateTime(
            rs.getTimestamp("create_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        // localDateTime = LocalDateTime.parse(rs.getString("update_time"), formatter);
        fastq.setUpdateTime(
            rs.getTimestamp("update_time", Calendar.getInstance(timeZone)).toLocalDateTime());
        
        return fastq;
    }
}