package com.shca.dao.daoimpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import com.shca.dao.WorkflowMetaDataDao;
import com.shca.entity.WorkflowMetaData;
import com.shca.entity.enums.WorkflowMetaDataStatusEnum;

public class WorkflowMetaDataDaoImpl implements WorkflowMetaDataDao {

    @Autowired
    private JdbcTemplate jmt;

    private final static String findRunningFileTransferSql = "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY " 
    + "WHERE WORKFLOW_NAME=? AND WORKFLOW_STATUS=? ";

    @Override
    public WorkflowMetaData selectById(long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'selectById'");
    }

    @Override
    public WorkflowMetaData selectByUUID(String uuid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'selectByUUID'");
    }

    @Override
    public int jobCountByStatus(WorkflowMetaDataStatusEnum status) {
        return jmt.queryForObject(
            findRunningFileTransferSql, 
            new Object[] {status.toString()}, 
            Integer.class);
    }
    
}
