/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-25 16:58:57
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 19:29:54
 * @FilePath: /analysis_engine/src/main/java/com/shca/dao/daoimpl/WorkflowMetaDataDaoImpl.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.dao.daoimpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import com.shca.dao.WorkflowMetaDataDao;
import com.shca.entity.WorkflowMetaData;
import com.shca.entity.enums.WorkflowMetaDataStatusEnum;

public class WorkflowMetaDataDaoImpl implements WorkflowMetaDataDao {

    @Autowired
    private JdbcTemplate jmt;

    private final static String FIND_RUNNING_FILE_TRANSFER_SQL = "SELECT count(*) FROM cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY "
    + "WHERE WORKFLOW_NAME=? AND WORKFLOW_STATUS=? ";

    @Override
    public WorkflowMetaData selectById(long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'selectById'");
    }

    @Override
    public WorkflowMetaData selectByUUID(String uuid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'selectByUUID'");
    }

    @SuppressWarnings("deprecation")
    @Override
    public int jobCountByStatus(WorkflowMetaDataStatusEnum status) {
        return jmt.queryForObject(
            FIND_RUNNING_FILE_TRANSFER_SQL,
            new Object[] {status.toString()},
            Integer.class);
    }
}
