package com.shca.dao.daoimpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Component;

import com.shca.dao.ProjectDao;
import com.shca.dao.mapper.ProjectRowMapper;
import com.shca.entity.Project;
import com.shca.entity.enums.ProjectStatusEnum;

@Component
public class ProjectDaoImpl implements ProjectDao{

    private final static Logger log = LoggerFactory.getLogger(ProjectDaoImpl.class);

    @Resource private JdbcTemplate jmt;
    @Resource private DataSource dataSource;

    private final static String SELECT_SQL = "SELECT * FROM shca_project_project WHERE id=? ";
    private final static String UPDATE_SQL = "UPDATE shca_project_project "
        + " SET project_sn=?, project_name=?, sample_name_0=?, sample_name_1=?, cancer_kind=?, "
        + " status=?, priority=?, is_filing=?, workspace=?, lims=?, lims_status=?, "
        + " lims_time=?, lims_result=?, upload_result=?, upload_time=?, "
        + " create_username=?, create_time=?, update_username=?, update_time=? "
        + " WHERE id=? ";
    private final static String CLEAR_SQL = "UPDATE shca_project_project SET status=? WHERE id=? ";
    private final static String DELETE_SQL = "DELETE FROM shca_project_project WHERE id=? ";
    private final static String SELECT_BY_STATUS_SQL = "SELECT * FROM shca_project_project WHERE status=? ";

    @SuppressWarnings("deprecation")
    @Override
    public Project selectById(long id) {
        try {
            return jmt.queryForObject(
                SELECT_SQL,
                new Object[] {id},
                new ProjectRowMapper());
        } catch (DataAccessException e) {
            log.error("selectById error", e);
            return null;
        }
    }

    @Override
    public long insert(Project project) {
        if (project == null) {
            return 0L;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("project_sn", project.getProjectSn());
        result.put("project_name", project.getProjectName());
        result.put("sample_name_0", project.getBSampleName());
        result.put("sample_name_1", project.getKSampleName());
        result.put("cancer_kind", project.getCancerName());
        result.put("status", project.getStatus().toString());
        result.put("priority", project.getPriority());
        result.put("is_filing", project.getIsFiling());
        result.put("workspace", project.getProjectPath());
        result.put("lims", project.getLimsJson());
        result.put("lims_status", project.getLimsStatus());
        result.put("lims_time", project.getLimsUploadTime());
        result.put("lims_result", project.getLimsUploadResult());
        result.put("upload_result", project.getFileUploadResult());
        result.put("upload_time", project.getFileUploadTime());
        result.put("create_username", project.getCreateUsername());
        result.put("create_time", project.getCreateTime());
        result.put("update_username", project.getUpdateUsername());
        result.put("update_time", project.getUpdateTime());

        Number returnKey = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_project")
            .usingGeneratedKeyColumns("id")
            .executeAndReturnKey(result);
        return returnKey.longValue();
    }

    @Override
    public void deleteById(long id) {
        try {
            jmt.update(DELETE_SQL, new Object[] {id});
        } catch (DataAccessException e) {
            log.error("delete by id " , e);
        }
    }

    @Override
    public long update(Project project) {
        try {
            return jmt.update(
                UPDATE_SQL,
                new Object[] {
                    project.getProjectSn(),
                    project.getProjectName(),
                    project.getBSampleName(),
                    project.getKSampleName(),
                    project.getCancerName(),
                    project.getStatus().toString(),
                    project.getPriority(),
                    (char) project.getIsFiling().getCode(),
                    project.getProjectPath(),
                    project.getLimsJson(),
                    project.getLimsStatus(),
                    project.getLimsUploadTime(),
                    project.getLimsUploadResult(),
                    project.getFileUploadResult(),
                    project.getFileUploadTime(),
                    project.getCreateUsername(),
                    project.getCreateTime(),
                    project.getUpdateUsername(),
                    project.getUpdateTime(),
                    project.getId()
                });
        } catch (DataAccessException e) {
            log.error("update error", e);
            return -1L;
        }
    }

    @SuppressWarnings("deprecation")
    @Override
    public List<Project> getProjectListByStatus(ProjectStatusEnum enumStatus) {
        try {
            return jmt.queryForList(
                SELECT_BY_STATUS_SQL,
                new Object[] {enumStatus.toString()},
                Project.class );
        } catch (DataAccessException e) {
            log.error("getProjectListByStatus error", e);
            return new ArrayList<>();
        }
    }

	@Override
	public void clearedById(long id) {
        try {
            jmt.update(CLEAR_SQL, new Object[] {ProjectStatusEnum.CLEARED.toString(), id});
        } catch (DataAccessException e) {
            log.error("clearedById error", e);
        }
	}
}
