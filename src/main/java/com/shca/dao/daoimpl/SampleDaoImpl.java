package com.shca.dao.daoimpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Component;

import com.shca.dao.SampleDao;
import com.shca.dao.mapper.SampleRowMapper;
import com.shca.entity.Sample;
import com.shca.entity.enums.FastqStatusEnum;
import com.shca.entity.enums.SampleStatusEnum;

@Component
public class SampleDaoImpl implements SampleDao {

    @Autowired
    private JdbcTemplate jmt;

    @Autowired
    private DataSource dataSource;

    private final static String selectSql = "SELECT * FROM shca_project_cancer WHERE id=? ";
    private final static String updateSql = "UPDATE shca_project_cancer "
        + " SET sample_name=?, cancer_kind=?, status=?, rawdata_r1=?, rawdata_r2=?, workspace=?, "
        + " priority=?, create_username=?, create_time=?, update_username=?, update_time=? " 
        + " WHERE id=? ";
    private final static String deleteSql = "DELETE FROM shca_project_cancer WHERE id=? ";
    private final static String clearSql = "UPDATE shca_project_cancer SET status=? WHERE id=? ";
    private final static String selectByStatusSql = "SELECT * FROM shca_project_cancer WHERE status=? ";

    @Override
    public Sample selectById(long id) {
        try {
            return jmt.queryForObject(
                selectSql, 
                new Object[] {id},
                new SampleRowMapper());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public long insert(Sample sample) {
        if (sample == null) {
            return 0L;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("sample_name", sample.getSampleName());
        result.put("cancer_kind", sample.getCancerName());
        result.put("status", sample.getStatus().toString());
        result.put("rawdata_r1", sample.getFastqIdR1());
        result.put("rawdata_r2", sample.getFastqIdR2());
        result.put("workspace", sample.getSamplePath());
        result.put("priority", sample.getPriority());
        result.put("create_username", sample.getCreateUsername());
        result.put("create_time", sample.getCreateTime());
        result.put("update_username", sample.getUpdateUsername());
        result.put("update_time", sample.getUpdateTime());
        
        Number returnKey = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_cancer")
            .usingGeneratedKeyColumns("id")
            .executeAndReturnKey(result);
        return returnKey.longValue();
    }

    @Override
    public void deleteById(long id) {
        try {
            jmt.update(deleteSql, new Object[] {id});
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public long update(Sample sample) {
        try {
            return jmt.update(
                updateSql, 
                new Object[] {
                    sample.getSampleName(),
                    sample.getCancerName(),
                    sample.getStatus().toString(),
                    sample.getFastqIdR1(),
                    sample.getFastqIdR2(),
                    sample.getSamplePath(),
                    sample.getPriority(),
                    sample.getCreateUsername(),
                    sample.getCreateTime(),
                    sample.getUpdateUsername(),
                    sample.getUpdateTime(),
                    sample.getId()
                });
        } catch (Exception e) {
            e.printStackTrace();
            return -1L;
        }
    }

    @Override
    public List<Sample> getSampleListByStatus(SampleStatusEnum enumStatus) {
        try {
            return jmt.queryForList(
                selectByStatusSql, 
                new Object[] {enumStatus.toString()},
                Sample.class );
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } 
    }

    @Override
    public List<Sample> getReadySampleList() {
        return getSampleListByStatus(SampleStatusEnum.READY);
    }

    @Override
    public void clearedById(long id) {
        try {
            jmt.update(clearSql, new Object[] {FastqStatusEnum.CLEARED.toString(), id});
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
}
