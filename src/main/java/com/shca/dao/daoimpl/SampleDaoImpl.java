package com.shca.dao.daoimpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Component;

import com.shca.dao.SampleDao;
import com.shca.dao.mapper.SampleRowMapper;
import com.shca.entity.Sample;
import com.shca.entity.enums.FastqStatusEnum;
import com.shca.entity.enums.SampleStatusEnum;

@Component
public class SampleDaoImpl implements SampleDao {

    private final static Logger log = LoggerFactory.getLogger(SampleDaoImpl.class);
    @Resource private JdbcTemplate jmt;
    @Resource private DataSource dataSource;

    private final static String SELECT_SQL = "SELECT * FROM shca_project_cancer WHERE id=? ";
    private final static String UPDATE_SQL = "UPDATE shca_project_cancer "
        + " SET sample_name=?, cancer_kind=?, status=?, rawdata_r1=?, rawdata_r2=?, workspace=?, "
        + " priority=?, create_username=?, create_time=?, update_username=?, update_time=? "
        + " WHERE id=? ";
    private final static String DELETE_SQL = "DELETE FROM shca_project_cancer WHERE id=? ";
    private final static String CLEAR_SQL = "UPDATE shca_project_cancer SET status=? WHERE id=? ";
    private final static String SELECT_BY_STATUS_SQL = "SELECT * FROM shca_project_cancer WHERE status=? ";

    @SuppressWarnings("deprecation")
    @Override
    public Sample selectById(long id) {
        try {
            return jmt.queryForObject(
                SELECT_SQL,
                new Object[] {id},
                new SampleRowMapper());
        } catch (DataAccessException e) {
            log.error("selectById failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public long insert(Sample sample) {
        if (sample == null) {
            return 0L;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("sample_name", sample.getSampleName());
        result.put("cancer_kind", sample.getCancerName());
        result.put("status", sample.getStatus().toString());
        result.put("rawdata_r1", sample.getFastqIdR1());
        result.put("rawdata_r2", sample.getFastqIdR2());
        result.put("workspace", sample.getSamplePath());
        result.put("priority", sample.getPriority());
        result.put("create_username", sample.getCreateUsername());
        result.put("create_time", sample.getCreateTime());
        result.put("update_username", sample.getUpdateUsername());
        result.put("update_time", sample.getUpdateTime());

        Number returnKey = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_cancer")
            .usingGeneratedKeyColumns("id")
            .executeAndReturnKey(result);
        return returnKey.longValue();
    }

    @Override
    public void deleteById(long id) {
        try {
            jmt.update(
                DELETE_SQL,
                new Object[] {id});
        } catch (DataAccessException e) {
            log.error("deleteById failed: " + e.getMessage());
        }
    }

    @Override
    public long update(Sample sample) {
        try {
            return jmt.update(
                UPDATE_SQL,
                new Object[] {
                    sample.getSampleName(),
                    sample.getCancerName(),
                    sample.getStatus().toString(),
                    sample.getFastqIdR1(),
                    sample.getFastqIdR2(),
                    sample.getSamplePath(),
                    sample.getPriority(),
                    sample.getCreateUsername(),
                    sample.getCreateTime(),
                    sample.getUpdateUsername(),
                    sample.getUpdateTime(),
                    sample.getId()
                });
        } catch (DataAccessException e) {
            log.error("update failed: " + e.getMessage());
            return -1L;
        }
    }

    @SuppressWarnings("deprecation")
    @Override
    public List<Sample> getSampleListByStatus(SampleStatusEnum enumStatus) {
        try {
            return jmt.queryForList(
                SELECT_BY_STATUS_SQL,
                new Object[] {enumStatus.toString()},
                Sample.class );
        } catch (DataAccessException e) {
            log .error("getSampleListByStatus failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Sample> getReadySampleList() {
        return getSampleListByStatus(SampleStatusEnum.READY);
    }

    @Override
    public void clearedById(long id) {
        try {
            jmt.update(CLEAR_SQL,
            new Object[] {FastqStatusEnum.CLEARED.toString(), id});
        } catch (DataAccessException e) {
            log.error("clearedById failed: " + e.getMessage());
        }
    }

}
