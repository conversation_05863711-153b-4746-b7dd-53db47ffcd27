package com.shca.dao.daoimpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.stereotype.Component;

import com.shca.dao.FastqDao;
import com.shca.dao.mapper.FastqRowMapper;
import com.shca.entity.Fastq;
import com.shca.entity.enums.FastqStatusEnum;

@Component
public class FastqDaoImpl implements FastqDao{

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(FastqDaoImpl.class);
    @Resource private JdbcTemplate jmt;
    private final static String SELECT_SQL = "SELECT * FROM shca_project_cancer_file WHERE id=? ";
    private final static String UPDATE_SQL = "UPDATE shca_project_cancer_file "
        + " SET sample_name=?, cancer_kind=?, rawdata=?, file_name=?, file_size=?, store_path=?, "
        + " md5=?, status=?, create_username=?, create_time=?, update_username=?, update_time=? "
        + " WHERE id=? ";
    private final static String DELETE_SQL = "DELETE FROM shca_project_cancer_file WHERE id=? ";
    private final static String CLEAR_SQL = "UPDATE shca_project_cancer_file SET status=? WHERE id=? ";
    private final static String SELECT_BY_STATUS_SQL = "SELECT * FROM shca_project_cancer_file WHERE status=? ";

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jmt) {
        this.jmt = jmt;
    }

    @Autowired
    DataSource dataSource;

    @SuppressWarnings("deprecation")
    @Override
    public Fastq selectById(long id) {
        try {
            return jmt.queryForObject(
                SELECT_SQL,
                new Object[] {id},
                new FastqRowMapper());
        } catch (DataAccessException e) {
            log.error("selectById error: ", e);
            return null;
        }
    }

    @Override
    public long insert(Fastq fastq) {
        if (fastq == null) {
            return 0L;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("sample_name", fastq.getSampleName());
        result.put("cancer_kind", fastq.getCancerName());
        result.put("rawdata", fastq.getRawdata());
        result.put("file_name", fastq.getFileName());
        result.put("file_size", fastq.getFileSize());
        result.put("store_path", fastq.getStorePath());
        result.put("md5", fastq.getMd5());
        result.put("status", fastq.getStatus().toString());
        result.put("modified_time", fastq.getModifiedTime());
        result.put("create_username", fastq.getCreateUsername());
        result.put("create_time", fastq.getCreateTime());
        result.put("update_time", fastq.getUpdateTime());
        result.put("update_username", fastq.getUpdateUsername());

        Number returnKey = new SimpleJdbcInsert(dataSource)
            .withTableName("shca_project_cancer_file")
            .usingGeneratedKeyColumns("id")
            .executeAndReturnKey(result);
        return returnKey.longValue();
    }

    @Override
    public void deleteById(long id) {
        try {
            jmt.update(DELETE_SQL, new Object[] {id});
        } catch (DataAccessException e) {
            log.error(DELETE_SQL, e);
        }
    }

    @Override
    public long update(Fastq fastq){
        try{
            return jmt.update(
                UPDATE_SQL,
                new Object[] {
                    fastq.getSampleName(),
                    fastq.getCancerName(),
                    fastq.getRawdata(),
                    fastq.getFileName(),
                    fastq.getFileSize(),
                    fastq.getStorePath(),
                    fastq.getMd5(),
                    fastq.getStatus().toString(),
                    fastq.getCreateUsername(),
                    fastq.getCreateTime(),
                    fastq.getUpdateUsername(),
                    fastq.getUpdateTime(),
                    fastq.getId()
            });
        } catch (DataAccessException e) {
            return -1L;
        }

    }
    @SuppressWarnings("deprecation")
    @Override
    public List<Fastq>  getFastqListByStatus(FastqStatusEnum enumStatus) {
        try {
            return jmt.queryForList(
                SELECT_BY_STATUS_SQL,
                new Object[] {enumStatus.toString()},
                Fastq.class );
        } catch (DataAccessException e) {
            log.error("getFastqListByStatus error: ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Fastq>  getReadyFastqList() {
        return getFastqListByStatus(FastqStatusEnum.READY);
    }

    @Override
    public void clearedById(long id) {
        try {
            jmt.update(CLEAR_SQL, new Object[] {FastqStatusEnum.CLEARED.toString(), id});
        } catch (DataAccessException e) {
            log.error(CLEAR_SQL, e);
        }
    }

}
