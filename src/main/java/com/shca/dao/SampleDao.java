package com.shca.dao;

import java.util.List;

import com.shca.entity.Sample;
import com.shca.entity.enums.SampleStatusEnum;

public interface SampleDao {
    
    Sample selectById(long id);
    long insert(Sample sample);
    void deleteById(long id);
    void clearedById(long id);
    long update(Sample sample);
    List<Sample>  getSampleListByStatus(SampleStatusEnum enumStatus);
    List<Sample> getReadySampleList();
} 
