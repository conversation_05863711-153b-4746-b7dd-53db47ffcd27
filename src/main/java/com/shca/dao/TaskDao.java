package com.shca.dao;

import java.util.List;

import com.shca.entity.SubResult;
import com.shca.entity.Task;
import com.shca.entity.enums.TaskStatusEnum;

public interface TaskDao {

    Task selectById(long id);
    Task deleteById(long id);
    Task clearedById(long id);
    long insertTask(Task task);
    long update(Task task);
    SubResult submit();
    SubResult abort();
    TaskStatusEnum queryStatus(Task task);
    void updateStatus(TaskStatusEnum status);
    List<Task> getTaskByStatus(TaskStatusEnum statusEnum);
    void syncStatus();
    void shakeTask();
}
