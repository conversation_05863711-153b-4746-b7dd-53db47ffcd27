package com.shca.module.project.view;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.MetadataRequest;
import com.shca.cromwell.api.MetadataResponse;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.cromwell.vo.Call;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.FileDownloadForward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.exception.UnknownException;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;
import com.shca.util.ConvertUtils;
import com.shca.util.CollectionUtils;
import com.shca.util.DbUtils;
import com.shca.util.LogUtils;
import com.shca.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

import javax.annotation.Resource;


public class ProjectSuitabilityView {

    private final static Logger log = LoggerFactory.getLogger( ProjectSuitabilityView.class);

    @Resource private CromwellClient client;
    @Resource private DbService db;
    private File workflowSource;

    public ProjectSuitabilityView(String workflowSourcePath) {
        workflowSource = new File( workflowSourcePath);
    }

    private String outputFolderSuitability = "/mydata/shca/BatchBvskSuitability/2022/";

    public void setOutputFolderSuitability(String outputFolderSuitability) {
        this.outputFolderSuitability = outputFolderSuitability;
    }

    public Forward list(ViewParams<?> params) {
        return new JspForward( "/shca/project/project_suitability_list.jsp", null);
    }

    public Forward dataGrid(ViewParams<?> params) throws JsonProcessingException {
        OrderPage page = params.getPage();
        List<Object> whereParams = new ArrayList<>();
        String sql = " select a.id,project_sn,a.cancer_kind,a.project_name,a.cromwell_workflow_inputs,a.cromwell_workflow_id_0" +
                " ,b.workflow_status,b.start_timestamp start_time,b.end_timestamp end_time from shca_project_suitability a " +
                " left join cromwell.WORKFLOW_METADATA_SUMMARY_ENTRY b on a.cromwell_workflow_id_0=b.workflow_execution_uuid " +
                " where 1=1 " + params.whereSql( whereParams) + params.whereSqlForKendoUI( whereParams);

        List<Map<String, Object>> records = db.select( sql  + " order by id desc ", page, whereParams.toArray(new Object[0]));

        int index = 1;
        for (Map<String,Object> record : records) {
            record.put( "row_num", index++);
            String json = (String)record.remove( "cromwell_workflow_inputs");
            if (JSON.isValidObject( json)) {
                record.put("cromwell_workflow_inputs", JSON.parseObject( json));
            }

            Date start = (Date)record.get("start_time");
            Date end = (Date)record.get("end_time");
            long runtime = 0;
            if(start!=null && end !=null){
                runtime = end.getTime()-start.getTime();
            } else if(start!=null && end == null){
                runtime = new Date().getTime() - start.getTime();
            } else  {

            }
            record.put("runtime",runtime);
        }

        Map<String, Object> grid = new HashMap<>();
        grid.put( "total", page.asPage().getTotal());
        grid.put( "rows", records);
        return new JsonForward(grid);
    }

    // 输入sample名后自动查找sample是否存在。
    public Forward autoComplete(ViewParams<?> params) throws JsonProcessingException {
        List<Object> whereParams = new ArrayList<>();
        whereParams.add( "finished");

        String sql = "select sample_name from shca_project_cancer where status=? "
                + params.whereSqlForKendoUI( whereParams) + " order by id desc ";
        System.out.println( sql);
        List<Map<String,Object>> samples = db.select( sql, whereParams.toArray(new Object[0]));

        return new JsonForward( samples);
    }


    public Forward create(ViewParams<?> params) {
        return new JspForward( "/shca/project/project_suitability_create.jsp", null);
    }

    public Forward create1(ViewParams<?> params) {
        return new JspForward( "/shca/project/project_suitability_create1.jsp", null);
    }


    public Forward save(ViewParams<?> params) throws JsonProcessingException {
        Date now = new Date();
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "相似度分析保存失败: ";

        try {

            List<String> sampleNames = new ArrayList<>();
            List<String> notFind = new ArrayList<>();
            List<String> notFinished = new ArrayList<>();
            for (String sampleName : params.getStringArray( "sample_name")) {
                if (StringUtils.hasLength( sampleName.trim())) {
                    sampleNames.add(sampleName.trim());
                    String sql = "select status from shca_project_cancer where sample_name = ? order by id  desc ";
                    Map<String,Object> map = db.selectUnique(sql,sampleName.trim());

                    if(CollectionUtils.isEmpty(map) || map.get("status")==null){
                        notFind.add(sampleName);
                    } else if(!"finished".equals((String)map.get("status"))){
                        notFinished.add(sampleName);
                    }
                }
            }

            if(notFind.size()>0){
                ret.put( "errcode", 2000);
                ret.put( "errmsg", "样本"+String.join(",", notFind)+ "未找到");
                return new JsonForward( ret);
            }

            if(notFinished.size()>0){
                ret.put( "errcode", 3000);
                ret.put( "errmsg", "样本"+String.join(",", notFind) + "未分析完成");
                return new JsonForward( ret);
            }

//            System.out.println( "1" + sampleNames.get( 4) + "1");
            if (sampleNames.size() % 2 != 0) {
                ret.put( "errcode", errCode);
                ret.put( "errmsg", errMsg + "相似度分析样本数量必须是偶数");

                return new JsonForward( ret);
            }

            errCode = 1010;
            String strProjectSN = ConvertUtils.date2Str( now, "yyyyMMdd");
            Long projectSN = db.selectUniqueLong( "select max(project_sn) from shca_project_suitability where project_sn like ? "
                    , strProjectSN + "%");
            if (projectSN == null || projectSN == 0) {
                projectSN = ConvertUtils.lon( strProjectSN + "01");
            } else {
                projectSN += 1;
            }

            errCode = 1020;
            Map<String,Object> project = new HashMap<>();
            project.put( "project_sn", projectSN);
            project.put( "cancer_kind", params.getString( "cancer_kind"));
            project.put( "project_name", params.getString( "project_name"));
            project.put( "create_time", now);
            project.put( "create_username", params.getUserName());
            project.put( "update_time", now);
            project.put( "update_username", params.getUserName());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put( "CalculateBatchBvskSuitability.sampleNames_WF", sampleNames.toArray(new String[0]));
            jsonObject.put( "CalculateBatchBvskSuitability.cancerName_TP", project.get( "cancer_kind"));
            jsonObject.put( "CalculateBatchBvskSuitability.homePath_WF", "/mydata/shca/samples");
            jsonObject.put( "CalculateBatchBvskSuitability.outputFolder_WF", this.outputFolderSuitability + projectSN);
            project.put( "cromwell_workflow_inputs", jsonObject.toString());

            errCode = 1030;
            File dir = new File( jsonObject.getString( "CalculateBatchBvskSuitability.outputFolder_WF"));
            boolean mkdirs = dir.mkdirs();

            log.info( dir.getAbsolutePath() + ": " + dir.exists() + " - " + mkdirs);


            errCode = 1040;
            File prettyJson = new File( dir.getAbsolutePath() + "/" + project.get( "project_sn") + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + ".json");
            BufferedWriter prettyWriter = new BufferedWriter( new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            errCode = 1050;
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource,  prettyJson,null,null));
            project.put( "cromwell_workflow_id_0", response.getId());
            project.put( "cromwell_workflow_status_0", response.getStatus());

            errCode = 1060;
            List<Object> sqlParams= new ArrayList<>();
            String sql = DbUtils.insertSql( "shca_project_suitability", project, sqlParams);
            long id = db.insert( sql, sqlParams.toArray( new Object[0]));


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");
            ret.put( "id", id);
//            ret.put( "response", response);

            return new JsonForward( ret);
        } catch (Exception e) {
            log.error( LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());
            throw new UnknownException( new JsonForward( ret), e);
        }
    }

    public Forward viewResult(ViewParams<?> params) {
        Map<String,Object> ret = new HashMap<>();
        String project_sn = params.getString("project_sn");

        //相似度输出文件夹
        String directoryPath =  this.outputFolderSuitability + project_sn;

        File directory = new File(directoryPath);

        if(!directory.exists()){
            ret.put("cromwell_workflow_inputs", "未找到"+project_sn+"的对应文件夹");
        } else {
            File txtFile = new File(directoryPath+File.separator+"Cor_ALL.txt");
            if(!txtFile.exists()){
                ret.put("cromwell_workflow_inputs", "未找到"+project_sn+"的Cor_ALL.txt");
            } else {
                String cromwell_workflow_inputs = readFile(txtFile);
                ret.put("cromwell_workflow_inputs", StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
            }
        }
        return new JspForward( "/shca/project/project_suitability_viewResult.jsp", ret);
    }

    public Forward downloadResult(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String project_sn = params.getString("project_sn");

        //相似度输出文件夹
        String directoryPath =  this.outputFolderSuitability + project_sn;

        File directory = new File(directoryPath);
        if(!directory.exists()){
            ret.put("cromwell_workflow_inputs", "未找到" + project_sn+"的对应文件夹");
        } else {
            File txtFile = new File(directoryPath+File.separator+"Cor_ALL.txt");
            if(!txtFile.exists()){
                ret.put("cromwell_workflow_inputs", "未找到"+project_sn+"的Cor_ALL.txt");
            } else {
                return new FileDownloadForward(project_sn+"_Cor_ALL.txt",txtFile);
            }
        }
        return new JsonForward(ret);
    }

    public Forward script(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String workflowId = (String)params.getString("workflow_id");
        ret.put("workflowId",workflowId);

        String subWorkflowId = getSubWorkflowId(workflowId);
        ret.put("subWorkflowId",subWorkflowId);
        if(subWorkflowId!=null && !subWorkflowId.equals(workflowId)){

        } else {
            ret.remove("subWorkflowId");
        }

        if (params.isJson()){
            OrderPage page = params.getPage();
            MetadataResponse metadataResponse = client.send( new MetadataRequest( workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String,Object>> list = new ArrayList<>();
            Iterator<String> iterator  = map.keySet().iterator();
            while(iterator.hasNext()){
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    tempMap.put("workflow_id",workflowId);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());
                    tempMap.put("seq_no",(list.size()+1));

                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);
                    list.add(tempMap);
                }
            }

            Collections.sort(list,new Comparator<Map<String,Object>>(){
                @Override
                public int compare(Map<String,Object> o1, Map<String,Object> o2) {
                    Date start1 = (Date)o1.get("start");
                    Date start2 = (Date)o2.get("start");
                    Date end1 = (Date)o1.get("end");
                    Date end2 = (Date)o2.get("end");
                    if(start1==null && start2==null){
                        return 0;
                    } else if(start1 == null && start2!=null){
                        return 1;
                    } else if(start1!=null && start2==null){
                        return -1;
                    } else {
                        if(start1.getTime()<start2.getTime()){
                            return -1;
                        } else if (start1.getTime()==start2.getTime()){
                            if(end1==null && end2==null){
                                return 0;
                            } else if(end1 == null && end2!=null){
                                return -1;
                            } else if(end1!=null && end2==null){
                                return 1;
                            } else {
                                if(end1.getTime()<end2.getTime()){
                                    return -1;
                                } else if (end1.getTime()==end2.getTime()){
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        } else {
                            return 1;
                        }
                    }
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_suitability_script.jsp", ret);
    }

    private String getSubWorkflowId(String workflowId){
        String subWorkflowId = null;
        try {
        }catch(Exception e){
            e.printStackTrace();
        }
        return subWorkflowId;
    }
    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }

    public static void main(String...args) {
        JSONObject json = new JSONObject();

        json.put( "CalculateBatchBvskSuitability.sampleNames_WF", new String[] {
            "BZ2005589B01","BZ2005589K01","BZ2005606B01","BZ2005606K01"
        });
        json.put( "CalculateBatchBvskSuitability.cancerName_TP", "breast");
        json.put( "CalculateBatchBvskSuitability.homePath_WF", "/mydata/shca/samples");
        json.put( "CalculateBatchBvskSuitability.outputFolder_WF", "/mydata/shca/BatchBvskSuitability/2021/2021081401");

        System.out.println( json.toJSONString());


        System.out.println( "WORKFLOW_METADATA_SUMMARY_ENTRY".toLowerCase());
    }
}
