package com.shca.module.project.view;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.*;
import com.shca.cromwell.vo.Call;
import com.shca.module.cancer.service.ProcessService;
import com.ywang.framework.mvc.UploadFile;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.JspForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.OrderPage;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.*;

public class ProjectCompareView {

    private final static Logger LOGGER = Logger.getLogger(ProjectCompareView.class);

    @Autowired
    private Db db;

    @Autowired
    private CromwellClient client;

    private Map<String, ProcessService> processServiceMap;

    private Map<String,String> processMap = new HashMap<>();

    public void setProcessServiceMap(Map<String, ProcessService> processServiceMap) {
        this.processServiceMap = processServiceMap;
    }

    public Forward list(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);
        LOGGER.info("entering project compare list ...");
        System.out.println("entering project compare list ...");

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select *, " +
                    "(CASE project_status WHEN 'wait_sample' THEN 1  " +
                    " ELSE 2 END) as sort_status_1," +
                    "(CASE workflow_status WHEN 'Succeeded' THEN 9  ELSE 1 END) as sort_status_2," +
                    " (CASE WHEN((sample_name_0 is not null && sample_status_0 is null) || (sample_name_1 is not null && sample_status_1 is null)) THEN -1 " +
                    "WHEN ((sample_name_0 is not null && sample_status_0 = 'finished') && ((sample_name_1 is not null && sample_status_1= 'finished') || sample_name_1 is null)) THEN 9 " +
                    "else 1 end)  as sort_status_3 " +
                    " from (select id,project_sn,project_no,project_name,project_status,lims_result,sample_name_0,sample_name_1," +
                    " cancer_kind,cancer_script_0,cancer_script_1,workflow_id,workflow_status,sub_workflow_id,submission,start,end,current_step,total_step,priority, " +
                    "(select status from shca_project_cancer b0 where b0.sample_name = a.sample_name_0 and b0.status!='cleared') sample_status_0 ,"+
                    "(select status from shca_project_cancer b1 where b1.sample_name = a.sample_name_1 and b1.status!='cleared') sample_status_1 "+
                    " from v_shca_project_compare a " +
                    " WHERE 1 = 1 ";
            // sql += params.whereSqlForKendoUI( sqlParams);
            sql += params.whereSql(sqlParams);
            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }
            System.out.println("params -> " + params);
            System.out.println("sql params -> " + sqlParams);
            sql += ") t  ORDER  BY sort_status_2,sort_status_1,sort_status_3,ifnull(start,'2011-01-01 00:00:00') DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            Date date = new Date();
            SysConfigManager config = SysConfigManager.getInstance();
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }

                }

                Date start = (Date)map.get("start");
                Date end = (Date)map.get("end");
                long runtime = 0;
                if(start!=null && end !=null){
                    runtime = end.getTime()-start.getTime();
                } else if(start!=null && end == null){
                    runtime = new Date().getTime() - start.getTime();
                } else  {

                }
                map.put("runtime",runtime);

                String lims_result = (String) map.get("lims_result");
                map.put("lims_result", JSONObject.isValidObject(lims_result)?JSONObject.parseObject(lims_result).get("success"):"");
                /*Integer total_step = 53;
                map.put("total_step",total_step);*/
                String workflow_id = (String) map.get("workflow_id");
                String sub_workflow_id = (String) map.get("sub_workflow_id");
                String workflow_status = (String) map.get("workflow_status");
                Long id = (Long)map.get("id");
                if("Running".equals(workflow_status) && sub_workflow_id==null){
                    sub_workflow_id =  reflashSubWorkflowId(id);
                }
                map.put("sub_workflow_id",sub_workflow_id);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_compare_list.jsp", ret);
    }

    public Forward listDel(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = "select a.id,project_no,a.project_name,a.cancer_kind,status,update_username,sample_name_0,sample_name_1,lims_status , lims_time , lims_result,priority,create_time " +
                    "from shca_project_project a " +
                    "where status='deleted' and is_filing = 0 " +
                    "and (select count(*) from shca_project_cancer_file b where (b.sample_name=a.sample_name_0 or b.sample_name=a.sample_name_1) and status='deleted'  and b.update_username=concat('LimsService2#', project_no)) > 0";

            sql += params.whereSql( sqlParams);

            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }

            sql += " order by lims_time desc";

            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            Date date = new Date();
            SysConfigManager config = SysConfigManager.getInstance();
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }

                }

                String lims_result = (String) map.get("lims_result");
                map.put("lims_result", JSONObject.isValidObject(lims_result)?JSONObject.parseObject(lims_result).get("success"):"");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_compare_listDel.jsp", ret);
    }

    public Forward viewDeleteFile(ViewParams params){
        Map<String,Object> ret = new HashMap<>();
        if(params.isJson()) {
            OrderPage page = params.getPage();

            SysConfigManager config = SysConfigManager.getInstance();

            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,sample_name_0,sample_name_1 from shca_project_project where id=?";

            Map<String, Object> cancer = db.selectUnique(sql, params.getString("id"));

            String sample_name_0 = (String) cancer.get("sample_name_0");
            String sample_name_1 = (String) cancer.get("sample_name_1");

            String sql2 = " SELECT sample_name,cancer_kind,sample_type,file_name,file_size,store_path,md5,create_username,create_time FROM shca_project_cancer_file WHERE status = 'deleted' and update_username=concat('LimsService2#', ?)";
            sql2 += " and  (sample_name = ? ";
            sql2 += " or sample_name = ? )";
            sqlParams.add(cancer.get("project_no"));
            sqlParams.add(sample_name_0);
            sqlParams.add(sample_name_1);

            sql2 += params.whereSqlForKendoUI(sqlParams);
            sql2 += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";
            List<Map<String, Object>> mapList = db.select(sql2, page, sqlParams.toArray(new Object[0]));
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, Object> map = mapList.get(i);
                map.put("seq_no", i + 1);
                //map.put("sample_type",config.getValue("simple_kind_name."+(String)map.get("sample_type")));
                map.put("sample_type", config.getValue("sample_kind_name." + (String) map.get("sample_type")));
                //map.put("creator_name","王赟");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_compare_viewDeleteFile.jsp", ret);
    }

    public Forward viewLims(ViewParams params) {
        String id = params.getString("id");

        String lims_result = db.selectUnique("SELECT lims_result from shca_project_project where id = ?",new StringCallback(),id);

        Map<String,Object> ret = new HashMap<>();
        Object obj = null;
        if(JSON.isValidObject(lims_result)){
            obj = JSON.parseObject(lims_result);
        } else if(JSON.isValidArray(lims_result)){
            obj = JSON.parseArray(lims_result);
        }
        if(obj!=null ) {
            String cromwell_workflow_inputs = JSON.toJSONString(obj, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }


    public Forward view(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();

        String sql = " select id,project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1,workflow_id,workflow_status,submission,start,end,current_step , lims from v_shca_project_compare where id=?";

        Map<String,Object> cancer = db.selectUnique(sql, params.getString("id"));
        ret.put("cancer",cancer);
        SysConfigManager config = SysConfigManager.getInstance();
        if((String)cancer.get("cancer_kind") == null){
            cancer.put("cancer_kind","--");
        } else {
            String kind = config.getValue((String)cancer.get("cancer_kind"));
            cancer.put("cancer_kind",kind==null?"--":kind);
        }
        String lims = (String)cancer.get("lims");
        if(lims!=null && JSONObject.isValidObject(lims)) {
            String cromwell_workflow_inputs = JSON.toJSONString(JSON.parseObject(lims), SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }

        //cancer.put("cancer_kind",config.getValue((String)cancer.get("cancer_kind")));

        cancer.put("status",config.getValue("run_status2."+(String)cancer.get("workflow_status")));



        String sample_name_0 = (String)cancer.get("sample_name_0");
        String sample_name_1 = (String)cancer.get("sample_name_1");
        ret.put("sample_name_0",sample_name_0);
        ret.put("sample_name_1",sample_name_1);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();

            String sql2 = " SELECT sample_name,cancer_kind,sample_type,file_name,file_size,store_path,md5,create_username,create_time FROM shca_project_cancer_file WHERE status = 'successed' ";
            sql2 += " and  (sample_name = ? ";
            sql2 += " or sample_name = ? )";
            sqlParams.add(sample_name_0);
            sqlParams.add(sample_name_1);

            sql2 += params.whereSqlForKendoUI( sqlParams);
            sql2 += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";
            List<Map<String,Object>> mapList =  db.select(sql2,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("seq_no",i+1);
                //map.put("sample_type",config.getValue("simple_kind_name."+(String)map.get("sample_type")));
                map.put("sample_type",config.getValue("sample_kind_name."+(String)map.get("sample_type")));
                //map.put("creator_name","王赟");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);

        return new JspForward( "/shca/project/project_compare_view.jsp", ret);

    }

    public Forward script(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);
        List<Object> sqlParams = new ArrayList<>();
        String sql = "select sub_workflow_id,workflow_id from v_shca_project_compare where id = ?  ";
        sqlParams.add(id);
        Map<String,Object> map_query  =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));
        String workflowId = (String)map_query.get("workflow_id");
        String subWorkflowId_query = (String)map_query.get("sub_workflow_id");
        ret.put("workflowId",workflowId);
        if(StringUtils.hasLength(subWorkflowId_query)){
            workflowId = subWorkflowId_query;
        }

        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_project set cromwell_sub_workflow_id_0 = ? where id = ?",subWorkflowId,params.getId());
            workflowId = subWorkflowId;
        } else {
            subWorkflowId = workflowId;
        }
        ret.put("subWorkflowId",subWorkflowId);
        ret.put("id",params.getId());

        if (params.isJson()){
            OrderPage page = params.getPage();



            MetadataResponse metadataResponse = client.send( new MetadataRequest( workflowId));

            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String,Object>> list = new ArrayList<>();
            Iterator<String> iterator  = map.keySet().iterator();
            while(iterator.hasNext()){
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    tempMap.put("workflow_id",workflowId);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());

                    tempMap.put("seq_no",(list.size()+1));

                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);
                    list.add(tempMap);
                }
            }

            Collections.sort(list,new Comparator<Map<String,Object>>(){
                @Override
                public int compare(Map<String,Object> o1, Map<String,Object> o2) {
                    Date start1 = (Date)o1.get("start");
                    Date start2 = (Date)o2.get("start");
                    Date end1 = (Date)o1.get("end");
                    Date end2 = (Date)o2.get("end");
                    if(start1==null && start2==null){
                        return 0;
                    } else if(start1 == null && start2!=null){
                        return 1;
                    } else if(start1!=null && start2==null){
                        return -1;
                    } else {
                        if(start1.getTime()<start2.getTime()){
                            return -1;
                        } else if (start1.getTime()==start2.getTime()){
                            if(end1==null && end2==null){
                                return 0;
                            } else if(end1 == null && end2!=null){
                                return -1;
                            } else if(end1!=null && end2==null){
                                return 1;
                            } else {
                                if(end1.getTime()<end2.getTime()){
                                    return -1;
                                } else if (end1.getTime()==end2.getTime()){
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        } else {
                            return 1;
                        }
                    }
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_compare_script.jsp", ret);
    }

    public Forward viewParam(ViewParams params) {
        String id = params.getString("id");
        String index = params.getString("scriptIndex");

        String project_sn = db.selectUnique("SELECT project_sn from shca_project_project where id = ?",new StringCallback(),id);
        SysConfigManager config = SysConfigManager.getInstance();
        //样本运行文件夹
        String directoryPath = config.getValue("workspace.project_path")+File.separator+project_sn;
        System.out.println(directoryPath);
        File d = new File(directoryPath);
        //File d = new File("C:\\Users\\<USER>\\Documents");
        String jsonStr = null;
        if(d.exists()){
            File[] files = d.listFiles(new FileFilter(){
                public boolean accept(File file) {
                    if(file.isDirectory()) {
                        return false;
                    } else {
                        String name = file.getName();
                        if(file.getName().startsWith("mutual") && file.getName().endsWith(".json"))
                            return true;
                        else
                            return false;
                    }

                }
            });
            Arrays.sort(files, new Comparator<File>(){
                @Override
                public int compare(File f1, File f2) {
                    //long diff = f1.lastModified() - f2.lastModified();
                    long diff = f2.getName().compareTo(f1.getName());
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;
                }
            });

            for (int i=0;i<files.length;i++){
                File f =  files[i];
                jsonStr = readFile(f);
                break;
            }
        } else {

        }
        Map<String,Object> ret = new HashMap<>();
//        System.out.println(jsonStr);
        if(jsonStr!=null) {
            String cromwell_workflow_inputs = JSON.toJSONString(JSON.parseObject(jsonStr), SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
      /*  String sql = "select cromwell_inputs from shca_cromwell_config where auto_script = 1 and script_step = ? ";
        Map<String,Object> ret = new HashMap<>();
        String cromwell_inputs = db.selectUnique(sql, new StringCallback() , index);
        ret.put("cromwell_inputs",cromwell_inputs);*/

        ret.put("errmsg","未找到"+project_sn+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewCommand(ViewParams params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.project_name , m.commandline" +
                " FROM shca_project_project c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        ret.put("project_name",metadata.get("project_name"));
        ret.put("cromwell_workflow_inputs",metadata.get("commandline"));
        ret.put("errmsg","");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewLog(ViewParams params) {
        String filePath = params.getString("filePath");
        String sample_name = params.getString("sample_name",false,"");

        String cromwell_workflow_inputs  = readFile(new File(filePath));;
        Map<String,Object> ret = new HashMap<>();
        ret.put("sample_name",sample_name);
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs",StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }


    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }



    public Forward sample(ViewParams params){
        Map<String, Object> ret = new HashMap<>();
        String sample_name_0 = params.getString("sample_name_0");
        String sample_name_1 = params.getString("sample_name_1");
        String project_name = db.selectUnique("SELECT project_name FROM shca_project_project where (sample_name_0 = ? and sample_name_1 = ?) or (sample_name_0 = ? and sample_name_1 = ?)",new StringCallback(),sample_name_0,sample_name_1,sample_name_1,sample_name_0);
        if(!CollectionUtils.isEmpty(project_name)){
            ret.put("project_name",project_name);
            ret.put("errcode", 1000);
            ret.put("errmsg","项目样本重复");
            return new JsonForward(ret);
        }
        ret.put("errcode", 0);
        return new JsonForward(ret);
    }

    public Forward stop(ViewParams params){

        String workflowId = params.getString("workflow_id");

        AbortResponse response = client.send( new AbortRequest( workflowId));
        //db.update("update shca_project_project set status=? where id = ?","aborted_step1",id);
        Map<String,Object> ret = new HashMap<>();
        ret.put("status",response.getStatus());
        return new JsonForward(ret);
    }

    public Forward restart(ViewParams params){
        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",1000);
        ret.put("errmsg","重新分析出错");
        try {
            long id = params.getId();
            Map<String, Object> project = db.selectUnique("select a.id,a.project_sn,a.project_no,a.project_name," +
                    " a.sample_name_0,a.sample_name_1,a.cancer_kind,a.cancer_script_0 " +
                    " from shca_project_project a " +
                    " inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                    " left join shca_project_cancer c on a.sample_name_1=c.sample_name " +
                    " where a.id = ? and a.status in (?,?,?) and b.status=? and (a.sample_name_1 is null or c.status=?) ", id, "finished","failed","aborted","finished", "finished");

            if (project == null) {
                ret.put("errcode", 2000);
                ret.put("errmsg", "状态异常，无法重新分析");
                return new JsonForward(ret);
            }

            SysConfigManager config = SysConfigManager.getInstance();

            String cancerKind = (String) project.get("cancer_kind");
            Map<String, Object> cromwellConfig = null;
            if (StringUtils.isBlank((String) project.get("cancer_script_0"))) {
                cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 1);
            } else {
                cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                        " from shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ", cancerKind, project.get("cancer_script_0"), 1);
            }
            ProcessService processService = processServiceMap.get(cancerKind);

            if (cromwellConfig != null && processService != null) {
                File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                if (!options.exists()) {
                    options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                }
                if (!options.exists()) {
                    options = null;
                }

                WorkflowsResponse response = processService.mutual((String) project.get("project_sn"), (String) project.get("project_name")
                        , (String) project.get("sample_name_0"), (String) project.get("sample_name_1"), (String) cromwellConfig.get("cromwell_workflow_inputs")
                        , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                int cnt = db.update("update shca_project_project set status=?,cromwell_workflow_id_0=?,cromwell_sub_workflow_id_0 = null,cromwell_workflow_status_0=?,cancer_script_0=?,update_username = ? where id=? "
                        , "running_step1", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), params.getUserName(),project.get("id"));

                //更新第三步状态
                cnt = db.update("update shca_project_cancer " +
                        " set cromwell_workflow_id_1=null,cromwell_workflow_status_1=null,cromwell_sub_workflow_id_1=null ,update_username = ?  where sample_name=? ",  params.getUserName(),project.get("sample_name_0"));

                cnt = db.update("update shca_project_cancer " +
                        " set cromwell_workflow_id_1=null,cromwell_workflow_status_1=null,cromwell_sub_workflow_id_1=null ,update_username = ?   where sample_name=? ", params.getUserName() , project.get("sample_name_1"));

                //更新第四步状态
                cnt = db.update("update shca_project_project set cromwell_workflow_id_1=null,cromwell_workflow_status_1=null,cromwell_sub_workflow_id_1=null ,update_username = ?   where id=? ", params.getUserName() , project.get("id"));

                //更新lims
                cnt = db.update("update shca_project_project set lims_status=?,lims_time=null,upload_result=null,upload_time=null,cromwell_workflow_id_2=null,cromwell_workflow_id_2_2=null " +
                        " where id=? and lims_status in (?,?)", "wait_upload", project.get("id"), "wait_upload", "finished");

                ret.put("errcode",0);

            } else {
                ret.put("errcode", 3000);
                ret.put("errmsg", "项目配置异常");
                LOGGER.error("mutect-process[" + project.get("project_sn") + "][" + project.get("project_name") + "]: cromwell config is null or processService is not matched");

            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return new JsonForward(ret);
    }

    public Forward importExcel(ViewParams params) {
        Map<String, Object> ret = new HashMap<>();
        UploadFile file = params.getUploadFile("file");
        //todo 文件格式校验
        if(file==null) {
            ret.put("errcode",1000);
            ret.put("errmsg","请选择导入文件");
            return new JsonForward(ret);
        }

        if(!"xls".equals(file.getFileType())){
            ret.put("errcode",2000);
            ret.put("errmsg","请选择xls文件");
            return new JsonForward(ret);
        }

        //文件处理
        InputStream is = null;
        Date now = new Date();
        List<Map<String,Object>> quests = new ArrayList<>();
        try {
            is = new ByteArrayInputStream(file.getContent());
            SysConfigManager config = SysConfigManager.getInstance();
            ret.put("errcode",500);
            ret.put("errmsg","文件存储失败");

            String fileFolder = ConvertUtils.date2Str(new Date(),"yyyyMMddHHmmss");
            File dir = new File( config.getValue("priority.xls.store_path") + "/" + fileFolder);
            dir.mkdirs();
            String storePath = dir.getAbsolutePath() + "/" + file.getFileName()+".xls";
            OutputStream os = new FileOutputStream( storePath);
            InputStream is2 = new ByteArrayInputStream( file.getContent());
            FileUtils.transfer( is2, os);

            List<String[]> rows = FileUtils.getDataesFromExcel(is);
            //todo 内容格式校验
            if(rows.size()>=5){
                String[] titles = rows.get(4);
                boolean titleIsMatch = true;
                String[] titlesStand = {"项目编号","优先级别"};
                for(int i=0;i<titles.length&&i<titlesStand.length;i++){
                        if(!titles[i].trim().equals(titlesStand[i])){
                            titleIsMatch = false;
                        }
                }
                if(!titleIsMatch){
                    ret.put("errcode",3000);
                    ret.put("errmsg","导入格式不正确");
                    return new JsonForward(ret);
                }
                //数据清理并保存
                boolean isError = false;
                for (int i=5;i<rows.size();i++) {
                    String[] row  = rows.get(i);
                    String project_name = row[0];//项目编号
                    String priority = row[1];//优先级别
                    if(!StringUtils.hasLength(project_name.trim())){
                        isError = true;
                    } else {
                        if(isError){
                            ret.put("errcode",8000);
                            ret.put("errmsg","项目编号不能为空");
                            return new JsonForward(ret);
                        }
                    }
                    if(priority==null || !priority.trim().matches("\\d+")){
                        isError = true;
                    } else {
                        if(isError){
                            ret.put("errcode",9000);
                            ret.put("errmsg","优先级别格式不正确");
                            return new JsonForward(ret);
                        }
                    }
                }




                ret.put("errcode",4000);
                ret.put("errmsg","导入失败");
                int successNum = 0;
                int errorNum = 0;
                for (int i=5;i<rows.size();i++) {
                    String[] row  = rows.get(i);
                    String project_name = row[0];//项目编号
                    String priority = row[1];//优先级别

                    if(!StringUtils.hasLength(project_name.trim())){
                        continue;
                    }

                    if(priority==null || !priority.trim().matches("\\d+")){
                        continue;
                    }

                    //判断数据库中是否存在该项目
                    String sql = "select * from shca_project_project_priority where project_name = ?";
                    Map<String,Object> projectPriority  = db.selectUnique(sql,project_name);
                    if(projectPriority==null || projectPriority.get("id")==null){
                        Map<String,Object> map = new HashMap<>();
                        map.put("project_name",project_name);
                        map.put("priority",priority);
                        map.put("file",storePath);
                        map.put("create_time",now);
                        map.put("update_time",now);
                        map.put("create_username",params.getUserName());
                        map.put("update_username",params.getUserName());

                        List<Object> sqlParams = new ArrayList<>();
                        String sql_insert = DbUtils.insertSql( "shca_project_project_priority", map,sqlParams);

                        db.insert(sql_insert,sqlParams.toArray(new Object[0]));
                    } else {

                        String sql_update = "update shca_project_project_priority set priority = ?,file = ?,update_username=? where project_name = ?";
                        db.update(sql_update,priority,storePath,params.getUserName(),project_name);
                    }
                    successNum++;
                }
                ret.put("errcode",5000);
                ret.put("errmsg","更新项目优先级失败");
                db.update( "update shca_project_project a set priority=ifnull((select priority from shca_project_project_priority b where b.project_name=a.project_name),priority) ");

                ret.put("errcode",6000);
                ret.put("errmsg","更新样本优先级失败");
                db.update( "update shca_project_cancer a set priority=ifnull((select max(priority) from shca_project_project b where b.sample_name_0=a.sample_name or b.sample_name_1=a.sample_name), priority) ");
                //ret = service.addQuestions( params.getId(), params.getUserName(), quests, false);
                ret.put("errcode",0);
                ret.put("errmsg","成功导入"+successNum+"条");
            } else {
                ret.put("errcode",3001);
                ret.put("errmsg","导入格式不正确");
                return new JsonForward(ret);
            }
            return new JsonForward(ret);
        }catch (Exception e){
            e.printStackTrace();
            return new JsonForward(ret);
        }finally {
            FileUtils.close( is);
        }
    }

    private String reflashSubWorkflowId(long id){
        Map<String,Object> ret = new HashMap<>();
        String sql = "select ifnull(sub_workflow_id,workflow_id) as workflow_id  from v_shca_project_compare where id = ?  ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(id);
        String workflowId  =  db.selectUnique(sql,new StringCallback(),sqlParams.toArray(new Object[0]));
        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_project set cromwell_sub_workflow_id_0 = ? where id = ?",subWorkflowId,id);
            workflowId = subWorkflowId;
        }
        return workflowId;
    }
    private String getSubWorkflowId(String workflowId){
        String subWorkflowId = null;
        try {
            MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String, Object>> list = new ArrayList<>();
            Iterator<String> iterator = map.keySet().iterator();

            while (iterator.hasNext()) {
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList) {
                    if (call.getSubWorkflowId() == null) {
                        subWorkflowId = workflowId;
                    } else {
                        subWorkflowId = getSubWorkflowId(call.getSubWorkflowId());
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return subWorkflowId;
    }

    public static void main(String[] args) {
        String priority = "11a";
        System.out.println(priority==null || !priority.trim().matches("\\d+"));
    }
}
