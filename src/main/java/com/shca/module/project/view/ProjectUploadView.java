package com.shca.module.project.view;

import com.shca.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.Pipeline;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.*;
import com.shca.cromwell.vo.Call;
import com.shca.module.cancer.service.ProcessService;
import com.shca.report.service.ReportService;
import com.shca.config.SystemConfigService;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;
import com.shca.util.StringUtils;
import com.shca.util.FileUtils;
import com.shca.util.ConvertUtils;

import java.io.*;
import java.util.*;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



public class ProjectUploadView {

    private final static Logger log = LoggerFactory.getLogger(ProjectUploadView.class);

    @Resource private DbService db;
    @Resource private CromwellClient client;
    @Resource private ReportService reportService;
    @Resource private SystemConfigService config;

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,project_status,lims_status,lims_result,upload_result,upload_time,sample_name_0,sample_name_1," +
                    "(select workflow_status_0 from v_shca_project_report a where a.project_sn = b.project_sn) as prev_workflow_status_0,"+
                    "(select workflow_status_1 from v_shca_project_report a where a.project_sn = b.project_sn) as prev_workflow_status_1,"+
                    "cancer_kind,cancer_script_0,cancer_script_1,workflow_id,workflow_status,sub_workflow_id,submission,start,end,current_step,total_step from v_shca_project_upload b " +
                    " WHERE 1 = 1 ";
            sql += params.whereSql( sqlParams);

            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }
            sql += " ORDER  BY ifnull(start,'2099-01-01 00:00:00') DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                /*String code = config.getValue(groupName+"."+map.get("cancer_kind"));

                map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");*/
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }

                }


                Date start = (Date)map.get("start");
                Date end = (Date)map.get("end");
                long runtime = 0;
                if(start!=null && end !=null){
                    runtime = end.getTime()-start.getTime();
                } else if(start!=null && end == null){
                    runtime = new Date().getTime() - start.getTime();
                } else  {

                }
                map.put("runtime",runtime);

                String lims_result = (String) map.get("lims_result");
                if(JsonUtils.isValidObject(lims_result)){
                    Map<String, Object> resultObj = JsonUtils.parseObject(lims_result);
                    map.put("lims_result", resultObj.get("success"));
                } else if(JsonUtils.isValidArray(lims_result)){
                    List<Map<String, Object>> array = JsonUtils.parseArray(lims_result);
                    if (!array.isEmpty()) {
                        map.put("lims_result", array.get(0).get("success"));
                    }
                } else {
                    map.put("lims_result", "");
                }

                Date upload_time = (Date) map.get("upload_time");
                String upload_result = (String) map.get("upload_result");
                if(upload_time==null){
                    //没有上传
                    //分三类，第一类，qc报告有无效值，第二类，项目不支持上传，第三类，等待上传中
                    String sql_check = "select count(0) as err_cnt from shca_project_project_qcsummary  where project_sn=? and qc_qualified=0";
                    Long err_cnt = db.selectUniqueLong(sql_check,map.get("project_sn"));
                    if(err_cnt>0){
                        map.put("update_result","QC报告不合格");
                    } else {
                        map.put("update_result","等待上传");
                        /*//判断reportService 接口情况
                        String project_name = (String) map.get("project_name");
                        Map jsonMap = reportService.query(project_name);
                        Boolean succ = (Boolean)jsonMap.get("succ");
                        if(succ.booleanValue()){
                            map.put("update_result","等待上传");
                        } else{
                            map.put("update_result", jsonMap.get("errorMessage"));
                        }*/

                    }
                } else {
                    //分两类，上传成功，上传失败
                    if(JsonUtils.isValidObject(upload_result)){
                        Map<String, Object> resultObj = JsonUtils.parseObject(upload_result);
                        Boolean succ = (Boolean)(resultObj.get("succ"));
                        if(succ != null && succ.booleanValue()){
                            map.put("update_result","上传成功");
                        } else {
                            map.put("update_result","上传失败");
                        }
                    } else if(JsonUtils.isValidArray(upload_result)){
                        List<Map<String, Object>> array = JsonUtils.parseArray(upload_result);
                        if (!array.isEmpty()) {
                            Boolean succ = (Boolean)(array.get(0).get("succ"));
                            if(succ != null && succ.booleanValue()){
                                map.put("update_result","上传成功");
                            } else {
                                map.put("update_result","上传失败");
                            }
                        }
                    } else {
                        map.put("update_result", "--");
                    }

                }

                String sub_workflow_id = (String) map.get("sub_workflow_id");
                String workflow_status = (String) map.get("workflow_status");
                Long id = (Long)map.get("id");
                if("Running".equals(workflow_status) && sub_workflow_id==null){
                    sub_workflow_id =  reflashSubWorkflowId(id);
                }
                map.put("sub_workflow_id",sub_workflow_id);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_upload_list.jsp", ret);
    }

    public Forward view(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();

        String sql = " select id,project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1," +
                "workflow_id,workflow_status,submission,start,end,current_step , lims  from v_shca_project_upload where id=?";

        Map<String,Object> cancer = db.selectUnique(sql, params.getString("id"));
        ret.put("cancer",cancer);
        if((String)cancer.get("cancer_kind") == null){
            cancer.put("cancer_kind","--");
        } else {
            String kind = config.getValue((String)cancer.get("cancer_kind"));
            cancer.put("cancer_kind",kind==null?"--":kind);
        }

        String lims = (String)cancer.get("lims");
        Object obj = null;
        if(JsonUtils.isValidObject(lims)){
            obj = JsonUtils.parseObject(lims);
        } else if(JsonUtils.isValidArray(lims)){
            obj = JsonUtils.parseArray(lims);
        }
        if(lims!=null ) {
            String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(obj);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }

        cancer.put("status",config.getValue("run_status2."+(String)cancer.get("workflow_status")));

        String sample_name_0 = (String)cancer.get("sample_name_0");
        String sample_name_1 = (String)cancer.get("sample_name_1");
        ret.put("sample_name_0",sample_name_0);
        ret.put("sample_name_1",sample_name_1);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql2 = " SELECT sample_name,cancer_kind,sample_type,file_name,file_size,store_path,md5,create_username,create_time FROM shca_project_cancer_file WHERE status = 'successed' ";
            sql2 += " and  (sample_name = ? ";
            sql2 += " or sample_name = ? )";
            sqlParams.add(sample_name_0);
            sqlParams.add(sample_name_1);

            sql2 += params.whereSqlForKendoUI( sqlParams);
            sql2 += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";
            List<Map<String,Object>> mapList =  db.select(sql2,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("seq_no",i+1);
                //map.put("sample_type",config.getValue("simple_kind_name."+(String)map.get("sample_type")));
                map.put("sample_type",config.getValue("sample_kind_name."+(String)map.get("sample_type")));
                //map.put("creator_name","王赟");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);

        return new JspForward( "/shca/project/project_upload_view.jsp", ret);

    }

    public Forward script(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);
        List<Object> sqlParams = new ArrayList<>();
        String sql = "select sub_workflow_id,workflow_id from v_shca_project_upload where id = ?  ";
        sqlParams.add(id);
        Map<String,Object> map_query  =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));
        String workflowId = (String)map_query.get("workflow_id");
        String subWorkflowId_query = (String)map_query.get("sub_workflow_id");
        ret.put("workflowId",workflowId);
        if(StringUtils.hasLength(subWorkflowId_query)){
            workflowId = subWorkflowId_query;
        }

        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_project set cromwell_sub_workflow_id_1 = ? where id = ?",subWorkflowId,params.getId());
            workflowId = subWorkflowId;
        }
        else {
            subWorkflowId = workflowId;
        }
        ret.put("subWorkflowId",subWorkflowId);
        ret.put("id",params.getId());

        if (params.isJson()){
            OrderPage page = params.getPage();
            MetadataResponse metadataResponse = client.send( new MetadataRequest( workflowId));

            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String,Object>> list = new ArrayList<>();
            Iterator<String> iterator  = map.keySet().iterator();
            while(iterator.hasNext()){
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    tempMap.put("workflow_id",workflowId);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());
                    tempMap.put("seq_no",(list.size()+1));
                    list.add(tempMap);
                }
            }

            Collections.sort(list, new Comparator<Map<String,Object>>(){
                @Override
                public int compare(Map<String,Object> o1, Map<String,Object> o2) {
                    Date start1 = (Date)o1.get("start");
                    Date start2 = (Date)o2.get("start");
                    Date end1 = (Date)o1.get("end");
                    Date end2 = (Date)o2.get("end");
                    if(start1==null && start2==null){
                        return 0;
                    } else if(start1 == null && start2!=null){
                        return 1;
                    } else if(start1!=null && start2==null){
                        return -1;
                    } else {
                        if(start1.getTime()<start2.getTime()){
                            return -1;
                        } else if (start1.getTime()==start2.getTime()){
                            if(end1==null && end2==null){
                                return 0;
                            } else if(end1 == null && end2!=null){
                                return 1;
                            } else if(end1!=null && end2==null){
                                return -1;
                            } else {
                                if(end1.getTime()<end2.getTime()){
                                    return -1;
                                } else if (end1.getTime()==end2.getTime()){
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        } else {
                            return 1;
                        }
                    }
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_upload_script.jsp", ret);
    }

    public Forward viewParam(ViewParams<?> params) {
        String id = params.getString("id");
        String project_sn = db.selectUniqueString("SELECT project_sn from v_shca_project_upload where id = ?",id);
        //样本运行文件夹
        String directoryPath = config.getValue("workspace.project_path")+File.separator+project_sn;
        System.out.println(directoryPath);
        File d = new File(directoryPath);
        String jsonStr = null;
        if(d.exists()){
            File[] files = d.listFiles( new FileFilter(){
                public boolean accept(File file) {
                    if(file.isDirectory()) {
                        return false;
                    } else {
                        if(file.getName().startsWith("mutual") && file.getName().endsWith(".json"))
                            return true;
                        else
                            return false;
                    }
                }
            });

            Arrays.sort(files, new Comparator<File>(){
                @Override
                public int compare(File f1, File f2) {
                    //long diff = f1.lastModified() - f2.lastModified();
                    long diff = f2.getName().compareTo(f1.getName());
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;
                }
            });

            for ( int i=0; i<files.length; ){
                File f =  files[i];
                jsonStr = readFile(f);
                break;
            }
        } else {

        }
        Map<String,Object> ret = new HashMap<>();
//        System.out.println(jsonStr);
        if(jsonStr!=null) {
            Object parsedObj = JsonUtils.parseObject(jsonStr);
            String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(parsedObj);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
      /*  String sql = "select cromwell_inputs from shca_cromwell_config where auto_script = 1 and script_step = ? ";
        Map<String,Object> ret = new HashMap<>();
        String cromwell_inputs = db.selectUnique(sql, new StringCallback() , index);
        ret.put("cromwell_inputs",cromwell_inputs);*/

        ret.put("errmsg","未找到"+project_sn+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewCommand(ViewParams<?> params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.project_name , m.commandline" +
                " FROM shca_project_project c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        ret.put("project_name",metadata.get("project_name"));
        ret.put("cromwell_workflow_inputs",metadata.get("commandline"));
        ret.put("errmsg","");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewLog(ViewParams<?> params) {
        String filePath = params.getString("filePath");
        String sample_name = params.getString("sample_name",false,"");

        String cromwell_workflow_inputs  = readFile(new File(filePath));;
        Map<String,Object> ret = new HashMap<>();
        ret.put("sample_name",sample_name);
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs",StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }


    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }



    public Forward sample(ViewParams<?> params) throws JsonProcessingException{
        Map<String, Object> ret = new HashMap<>();
        String sample_name_0 = params.getString("sample_name_0");
        String sample_name_1 = params.getString("sample_name_1");
        String project_name = db.selectUniqueString("SELECT project_name FROM shca_project_project where (sample_name_0 = ? and sample_name_1 = ?) or (sample_name_0 = ? and sample_name_1 = ?)",sample_name_0,sample_name_1,sample_name_1,sample_name_0);
        if(!StringUtils.isEmpty(project_name)){
            ret.put("project_name",project_name);
            ret.put("errcode", 1000);
            ret.put("errmsg","项目样本重复");
            return new JsonForward(ret);
        }
        ret.put("errcode", 0);
        return new JsonForward(ret);
    }


    public Forward stop(ViewParams<?> params) throws JsonProcessingException{
        String workflowId = params.getString("workflow_id");
        AbortResponse<?,?> response = client.send( new AbortRequest( workflowId));
        Map<String,Object> ret = new HashMap<>();
        ret.put("status",response.getStatus());
        return new JsonForward(ret);
    }

    public Forward restart(ViewParams<?> params) throws Exception {
        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",1000);
        ret.put("errmsg","重新分析出错");
        try {
            long id = params.getId();
            Date now = new Date();
            Map<String, Object> project = db.selectUnique("select a.id,a.project_no,a.project_name,a.sample_name_0,a.sample_name_1,a.cancer_kind,a.cancer_script_0,  " +
                    "                     a.project_sn,a.cancer_script_1,lims  " +
                    "                     from shca_project_project a  " +
                    "                     inner join shca_project_cancer b on a.sample_name_0=b.sample_name  " +
                    "                     left join shca_project_cancer c on a.sample_name_1=c.sample_name  " +
                    "                     where a.id = ? and a.status in (?,?,?) and b.status=? and (a.sample_name_1 is null or c.status=?) ", id, "finished", "failed", "aborted", "finished", "finished");

            if (project == null) {
                ret.put("errcode", 2000);
                ret.put("errmsg", "状态异常，无法重新分析");
                return new JsonForward(ret);
            }

            File sampleDir0 = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name") + "/" + project.get("sample_name_0"));
            File sampleDir1 = new File(config.getValue("workspace.project_path") + "/" + project.get("project_name") + "/" + project.get("sample_name_1"));

            db.delete("delete from shca_project_cancer_outputfile where project_sn=?", project.get("project_sn"));
            File[] htmlFiles = FileUtils.findFiles(sampleDir0, ".html");
            for (File file : htmlFiles) {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                InputStream is = new FileInputStream(file);
                FileUtils.transfer(is, bos);
                String md5 = StringUtils.md5(bos.toByteArray());

                db.insert("insert into shca_project_cancer_outputfile(project_sn, project_no,sample_name,file_name,file_size,store_path" +
                                ",md5,create_username,create_time,update_username,update_time,modified_time) values(?,?,?,?,?,?,?,?,?,?,?,?) "
                        , project.get("project_sn"), project.get("project_no"), project.get("sample_name_0"), file.getName(), bos.size(), file.getAbsolutePath(), md5
                        , "CromwellService", now, "CromwellService", now, new Date(file.lastModified()));
            }


            if (StringUtils.hasLength((String) project.get("sample_name_1"))) { // 单样本兼容

                htmlFiles = FileUtils.findFiles(sampleDir1, ".html");
                for (File file : htmlFiles) {
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    InputStream is = new FileInputStream(file);
                    FileUtils.transfer(is, bos);
                    String md5 = StringUtils.md5(bos.toByteArray());

                    db.insert("insert into shca_project_cancer_outputfile(project_sn, project_no,sample_name,file_name,file_size,store_path" +
                                    ",md5,create_username,create_time,update_username,update_time,modified_time) values(?,?,?,?,?,?,?,?,?,?,?,?) "
                            , project.get("project_sn"), project.get("project_no"), project.get("sample_name_1"), file.getName(), bos.size(), file.getAbsolutePath(), md5
                            , "CromwellService", now, "CromwellService", now, new Date(file.lastModified()));
                }

            }


            String cancerKind = (String) project.get("cancer_kind");
            Map<String, Object> cromwellConfig = null;

            if (!StringUtils.hasLength((String) project.get("cancer_script_1"))) {
                cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 3);
            } else {
                cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                        " from shca_cromwell_config where cancer_kind=? and cancer_script=? and script_step=? ", cancerKind, project.get("cancer_script_1"), 3);
            }
            ProcessService processService = Pipeline.parse(cancerKind).getProcessService();
            if (cromwellConfig != null && processService != null) {

                File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                if (!options.exists()) {
                    options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                }
                if (!options.exists()) {
                    options = null;
                }
                /**
                 *
                 * 2021-01-21 添加纯化质检浓度值
                 * {"code":{"BZ2003899B01":["BZ2003899B01"],"BZ2003899K01":["BZ2003899K01"]},"identification":"null,null","productId":"BZ2003899","runningNumber":"201123001","concentration":"58.0,55.0","identityCard":"","state":"0,0","productMask":"BZ510"}
                 */
                String lims = (String) project.get("lims");
                String workflowInputs = (String) cromwellConfig.get("cromwell_workflow_inputs");
                if (StringUtils.hasLength(lims)) {
                    Map<String, Object> jsonObject = JsonUtils.parseObject(workflowInputs);
                    Map<String, Object> limsJson = JsonUtils.parseObject(lims);
                    String concentration = (String) limsJson.get("concentration");
                    String[] array = concentration.split(",");

                    jsonObject.put("geneFileUpload.Bconc_WF", ConvertUtils.dou(array[0]));
                    if (array.length == 2) {
                        jsonObject.put("geneFileUpload.Kconc_WF", ConvertUtils.dou(array[1]));
                    }

                    workflowInputs = JsonUtils.toJSONString(jsonObject);
                }

                WorkflowsResponse response = processService.upload((String) project.get("project_sn")
                        , (String) project.get("project_name"), (String) project.get("sample_name_0")
                        , (String) project.get("sample_name_1"), workflowInputs
                        , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                db.update("update shca_project_project set status=?,cromwell_workflow_id_1=?,cromwell_sub_workflow_id_1=null,cromwell_workflow_status_1=?,cancer_script_1=? where id=? "
                        , "running_step4", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), project.get("id"));

                //更新lims
                db.update("update shca_project_project set lims_status=?,lims_time=null,upload_result=null,upload_time=null,cromwell_workflow_id_2=null,cromwell_workflow_id_2_2=null" +
                        " where id=? and lims_status in (?,?)", "wait_upload", project.get("id"), "wait_upload", "finished");

                ret.put("errcode",0);
            } else {
                ret.put("errcode", 3000);
                ret.put("errmsg", "项目配置异常");
                log.error("upload-process[" + project.get("project_sn") + "][" + project.get("project_name") + "]: cromwell config is null or processService is not matched");
            }

        }catch(Exception e){
            e.printStackTrace();
        }

        return new JsonForward(ret);
    }
    private String reflashSubWorkflowId(long id){
        String sql = "select ifnull(sub_workflow_id,workflow_id) as workflow_id  from v_shca_project_upload where id = ?  ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(id);
        String workflowId  =  db.selectUniqueString(sql,sqlParams.toArray(new Object[0]));
        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_project set cromwell_sub_workflow_id_1 = ? where id = ?",subWorkflowId,id);
            workflowId = subWorkflowId;
        }
        return workflowId;
    }
    private String getSubWorkflowId(String workflowId){
        String subWorkflowId = null;
        try {
            MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            Iterator<String> iterator = map.keySet().iterator();

            while (iterator.hasNext()) {
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList) {
                    if (call.getSubWorkflowId() == null) {
                        subWorkflowId = workflowId;
                    } else {
                        subWorkflowId = getSubWorkflowId(call.getSubWorkflowId());
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return subWorkflowId;
    }

    public Forward viewResult(ViewParams<?> params) {
        String id = params.getString("id");

        Map<String,Object> map = db.selectUnique("SELECT project_sn,project_name,lims_result,upload_result from shca_project_project where id = ?",id);

        Map<String,Object> ret = new HashMap<>();
//        System.out.println(jsonStr);
        String result = (String)map.get(params.getString("result_type"));
        if(result!=null) {
            Object obj = null;
            if(JsonUtils.isValidObject(result)){
                obj = JsonUtils.parseObject(result);
            } else if(JsonUtils.isValidArray(result)){
                obj = JsonUtils.parseArray(result);
            }
            if(obj!=null) {
                String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(obj);
                ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
            } else {
                ret.put("cromwell_workflow_inputs", result);
            }
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
      /*  String sql = "select cromwell_inputs from shca_cromwell_config where auto_script = 1 and script_step = ? ";
        Map<String,Object> ret = new HashMap<>();
        String cromwell_inputs = db.selectUnique(sql, new StringCallback() , index);
        ret.put("cromwell_inputs",cromwell_inputs);*/

        //ret.put("errmsg","未找到"+map.get("project_name")+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewResult1(ViewParams<?> params) {
        String id = params.getString("id");

        Map<String,Object> map = db.selectUnique("SELECT project_sn,project_name,lims_result,upload_result from shca_project_project where id = ?",id);
        Map<String,Object> ret = new HashMap<>();
        String project_name = (String) map.get("project_name");
        Map<String,Object> jsonMap = reportService.query(project_name);

        String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(jsonMap);
        ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);

        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }
}
