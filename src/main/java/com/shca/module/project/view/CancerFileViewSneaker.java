package com.shca.module.project.view;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.config.SystemConfigService;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;


public class CancerFileViewSneaker {

    @Autowired
    private DbService db;

    @Autowired
    private SystemConfigService config;

    private Map<String, Object> _getSampleEntry(String sampleId) {
        return db.selectUnique(
                " SELECT id, sample_name, cancer_kind, cancer_script_0, workflow_id, workflow_status, submission, "
                        + "start,end, project_name, current_step FROM v_shca_project_sample where id=? ",
                sampleId);
    }

    private String _getSampleEntryStatus(SystemConfigService sysConfig, Map<String, Object> sampleEntry) {
        return sysConfig.getValue("run_status." + (String) sampleEntry.get("status"));
    }

    private String _getSampleEntryCancerType(SystemConfigService sysConfig, Map<String, Object> sampleEntry) {
        String sysconfigCancerType = sysConfig.getValue((String) sampleEntry.get("cancer_kind"));

        return sampleEntry.get("cancer_kind") == null
                && sysconfigCancerType == null ? "--" : sysconfigCancerType;
    }

    private List<Map<String, Object>> _getSamplesListByPage(ViewParams<?> params, String sample_name) {
        OrderPage page = params.getPage(); // get the page number, page 3 for example.
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(sample_name);

        String sql = " SELECT sample_name, cancer_kind, sample_type, file_name, file_size, store_path, md5, create_username, create_time "
                + "FROM shca_project_cancer_file "
                + "WHERE status = 'successed' ";
        sql += " and sample_name = ? ";
        sql += params.whereSqlForKendoUI(sqlParams); // treat filter on page, merged in sql string.
        sql += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";

        return db.select(sql, page, sqlParams.toArray(new Object[0]));
    }

    private void _addIndexOnSampleList(SystemConfigService sysConfig, List<Map<String, Object>> mapList) {
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> map = mapList.get(i);
            map.put("seq_no", i + 1);
            map.put("sample_type", sysConfig.getValue("sample_kind_name." + (String) map.get("sample_type")));
        }
    }

    public Forward view(ViewParams<?> params) throws JsonProcessingException {
        Map<String, Object> ret = new HashMap<>();
        // Using injected config
        Map<String, Object> sampleEntry = _getSampleEntry(params.getString("id"));

        String sampleName = (String) sampleEntry.get("sample_name");
        String sampleCancerType = _getSampleEntryCancerType(config, sampleEntry);
        sampleEntry.put("status", _getSampleEntryStatus(config, sampleEntry));

        ret.put("cancer", sampleEntry); // this 'cancer' key should be altered later.
        ret.put("sample_name", sampleName);
        ret.put("cancer_kind", sampleCancerType);

        if (params.isJson()) {
            OrderPage page = params.getPage(); // get the page number, page 3 for example.
            List<Map<String, Object>> sampleList = _getSamplesListByPage(params, sampleName);
            _addIndexOnSampleList(config, sampleList);

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", sampleList);
            return new JsonForward(ret);
        }

        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList", kindList);

        return new JspForward("/shca/project/project_cancerfile_view.jsp", ret); // hardcode the url
    }


    public Forward viewQC(ViewParams<?> params) throws JsonProcessingException {
        // Using injected config
        Map<String, Object> ret = new HashMap<>();

        Map<String, Object> cancer = db
                .selectUnique(
                        "SELECT sample_name, cancer_kind, cancer_script_0, cancer_script_1, cancer_script_2, status " +
                                " FROM shca_project_cancer WHERE id = ?",
                        params.getString("id"));

        ret.put("cancer", cancer);
        cancer.put("cancer_kind", config.getValue((String) cancer.get("cancer_kind")));
        cancer.put("status", config.getValue("run_status." + (String) cancer.get("status")));

        String sample_name = (String) cancer.get("sample_name");
        ret.put("sample_name", sample_name);

        OrderPage page = params.getPage();
        List<Object> sqlParams = new ArrayList<>();
        String sql = " SELECT project_no , sample_name , qc_name , qc_value , qc_reference , qc_qualified , " +
                "create_username , create_time , update_username , update_time FROM shca_project_project_qcsummary WHERE 1 = 1 ";

        if (sample_name != null && !sample_name.isEmpty()) {
            sql += " and sample_name = ? ";
            sqlParams.add((sample_name));
        }

        sql += params.whereSql(sqlParams);
        sql += " ORDER BY id";
        List<Map<String, Object>> mapList = db.select(sql, page, sqlParams.toArray(new Object[0]));
        Iterator<Map<String, Object>> it = mapList.iterator();
        int index = 0;
        int mapSize = mapList.size();

        while (it.hasNext()) {
            Map<String, Object> map = it.next();
            if (index >= (mapSize + 1) / 2) {
                Map<String, Object> temp = mapList.get(index - (mapSize + 1) / 2);
                temp.put("project_no1", map.get("project_no"));
                temp.put("sample_name1", map.get("sample_name"));
                temp.put("qc_name1", map.get("qc_name"));
                temp.put("qc_value1", map.get("qc_value"));
                temp.put("qc_reference1", map.get("qc_reference"));
                temp.put("qc_qualified1", map.get("qc_qualified"));
                temp.put("create_username1", map.get("create_username"));
                temp.put("create_time1", map.get("create_time"));
                temp.put("update_username1", map.get("update_username"));
                temp.put("update_time1", map.get("update_time"));

                it.remove();
            }
            index++;
        }

        ret.put("total", page.asPage().getTotal());
        ret.put("rows", mapList);
        return new JsonForward(ret);
    }
}
