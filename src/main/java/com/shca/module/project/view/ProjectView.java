package com.shca.module.project.view;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.config.SystemConfigService;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.ConvertUtils;
import com.shca.util.DbUtils;
import com.shca.util.CollectionUtils;
import com.shca.util.StringUtils;
import com.shca.util.DbService;

import java.io.*;
import java.util.*;

import javax.annotation.Resource;

public class ProjectView {

    @Resource private DbService db;
    @Resource private SystemConfigService config;

    public Forward create(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "创建失败";

        if(params.isJson()){
            String project_no = params.getString("project_no");
            String project_name = params.getString("project_name");
            String sample_name_0 = params.getString("sample_name_0");
            String sample_name_1 = params.getString("sample_name_1");
            String cancer_script_0 = params.getString("cancer_script_0");
            String cancer_kind = params.getString("cancer_kind");
            // String run_time = params.getString("run_time");
            // String description = params.getString("description");
            String status = params.getString("status",false,"wait_sample");

            try{
                Date now = new Date();
                Map<String,Object> template  =  new HashMap<>();
                template.put("project_sn", ConvertUtils.date2Str(now, "MMddHHmm"));
                template.put("project_no",project_no);
                template.put("project_name",project_name);
                template.put("sample_name_0",sample_name_0);

                if(StringUtils.hasLength(sample_name_1)) {
                    template.put("sample_name_1", sample_name_1);
                }
                template.put("cancer_script_0",cancer_script_0);
                template.put("status",status);
                template.put("cancer_kind",cancer_kind);
                template.put("create_username", params.getUserName());
                template.put("create_time",now);
                template.put("update_username", params.getUserName());
                template.put("update_time",now);

                System.out.println(template);

                List<Object> sqlParams = new ArrayList<>();
                String sql = DbUtils.insertSql( "shca_project_project", template, sqlParams);
                long id = db.insert( sql, sqlParams.toArray( new Object[0]));

                ret.put("id",id);
                errCode = 0;
            }catch (Exception e){
                e.printStackTrace();
                errMsg+="："+e.getMessage();
            }
            ret.put("errCode",errCode);
            ret.put("errMsg",errMsg);
            return new JsonForward(ret);

        }

        String sql2 = " SELECT id,sample_name FROM shca_project_cancer ";
        List<Map<String,Object>> sampleList =  db.select(sql2);
        ret.put("sampleList",sampleList);

        String sql = " SELECT id,cancer_kind,cancer_script FROM shca_cromwell_config WHERE script_step = 1 ";
        sql += " ORDER BY create_time DESC";
        List<Map<String,Object>> mapList =  db.select(sql);
        ret.put("cancer_script",mapList);

        List<Map<String,Object>> kindList = config.getProperties("cancer_kind_name");
        System.out.println("kindList:"+ JSONObject.toJSONString(kindList));
        ret.put("kindList",kindList);
        return new JspForward( "/shca/project/project_project_create.jsp", ret);
    }

    public Forward dashboard(ViewParams<?> params) {

        return new JspForward("/shca/project/project_project_dashboard.jsp",null);
    }

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " SELECT id,project_no,project_name,sample_name_0,sample_name_1,cancer_script_0,status,create_time,create_username,cancer_kind,cromwell_workflow_id_0 " +
                    " , (select min(start) from cromwell_workflow_metadata where workflow_id =c.cromwell_workflow_id_0) as start " +
                    " , (select max(end) from cromwell_workflow_metadata where workflow_id =c.cromwell_workflow_id_0) as end " +
                    "FROM shca_project_project c WHERE 1 = 1 ";
            sql += params.whereSql( sqlParams);
            sql += " ORDER  BY create_time DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("cancer_kind",config.getValue((String)map.get("cancer_kind")));

                if(map.get("end") == null){
                    Map<String,Object> endMap = db.selectUnique("select max(update_time) as update_time from cromwell_workflow_metadata where workflow_id = ?",map.get("cromwell_workflow_id_0"));
                    map.replace("end",endMap.get("update_time"));
                }

                Integer total_step = 53;
                Integer current_step = db.selectUniqueInt("SELECT COUNT(0) FROM cromwell_workflow_metadata WHERE workflow_id = ? and execution_status = ?",map.get("cromwell_workflow_id_0"),"Done");

                map.put("total_step",total_step);
                map.put("current_step",current_step);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_project_list.jsp", ret);
    }

    public Forward view(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();


        Map<String,Object> cancer = db.selectUnique("select id,project_no,project_name,sample_name_0,sample_name_1,cancer_script_0,status,create_time,create_username,cancer_kind,cromwell_workflow_id_0" +
                " , (select min(start) from cromwell_workflow_metadata where workflow_id=c.cromwell_workflow_id_0) as start " +
                " , (select max(end) from cromwell_workflow_metadata where workflow_id=c.cromwell_workflow_id_0) as end " +
                " from shca_project_project c where id = ?", params.getString("id"));
        ret.put("cancer",cancer);
        cancer.put("cancer_kind",config.getValue((String)cancer.get("cancer_kind")));

        cancer.put("status",config.getValue("run_status."+(String)cancer.get("status")));

        String sample_name_0 = (String)cancer.get("sample_name_0");
        String sample_name_1 = (String)cancer.get("sample_name_1");
        ret.put("sample_name_0",sample_name_0);
        ret.put("sample_name_1",sample_name_1);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " SELECT sample_name,cancer_kind,sample_type,file_name,file_size,store_path,create_username,create_time FROM shca_project_cancer_file WHERE 1 = 1 ";
            if(!StringUtils.isEmpty(sample_name_0) && !sample_name_0.equals("") && !StringUtils.isEmpty(sample_name_1) && !sample_name_1.equals("") ){
                sql += " and file_name like ? ";
                sql += " or file_name like ? ";
                sqlParams.add(("%"+sample_name_0+"%"));
                sqlParams.add(("%"+sample_name_1+"%"));
            }
            sql += params.whereSqlForKendoUI( sqlParams);
            sql += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("seq_no",i+1);
                //map.put("sample_type",config.getValue("simple_kind_name."+(String)map.get("sample_type")));
                map.put("sample_type",config.getValue("sample_kind_name."+(String)map.get("sample_type")));
                //map.put("creator_name","王赟");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);

        return new JspForward( "/shca/project/project_projectfile_view.jsp", ret);

    }

    public Forward script(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);
        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select * from cromwell_workflow_metadata where workflow_id = ( " +
                    "select cromwell_workflow_id_0 from shca_project_project where id = ?) ";
            sqlParams.add(id);
            sql += " ORDER  BY start , end ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("seq_no",i+1);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_project_script.jsp", ret);
    }

    public Forward viewParam(ViewParams<?> params) {
        String id = params.getString("id");

        String project_sn = db.selectUniqueString("SELECT project_sn from shca_project_project where id = ?",id);
        //样本运行文件夹
        String directoryPath = config.getValue("workspace.project_path")+File.separator+project_sn;
        System.out.println(directoryPath);
        File d = new File(directoryPath);
        //File d = new File("C:\\Users\\<USER>\\Documents");
        String jsonStr = null;
        if(d.exists()){
            File[] files = d.listFiles(new FileFilter(){
                public boolean accept(File file) {
                    if(file.isDirectory()) {
                        return false;
                    } else {
                        if(file.getName().startsWith("mutual") && file.getName().endsWith(".json"))
                            return true;
                        else
                            return false;
                    }

                }
            });
            Arrays.sort(files, new Comparator<File>(){
                @Override
                public int compare(File f1, File f2) {
                    //long diff = f1.lastModified() - f2.lastModified();
                    long diff = f2.getName().compareTo(f1.getName());
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;
                }
            });

            for (int i=0;i<files.length;){
                File f =  files[i];
                jsonStr = readFile(f);
                break;
            }
        } else {

        }
        Map<String,Object> ret = new HashMap<>();
//        System.out.println(jsonStr);
        if(jsonStr!=null) {
            String cromwell_workflow_inputs = JSON.toJSONString(JSON.parseObject(jsonStr), SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
//            System.out.println(cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
      /*  String sql = "select cromwell_inputs from shca_cromwell_config where auto_script = 1 and script_step = ? ";
        Map<String,Object> ret = new HashMap<>();
        String cromwell_inputs = db.selectUnique(sql, new StringCallback() , index);
        ret.put("cromwell_inputs",cromwell_inputs);*/

        ret.put("errmsg","未找到"+project_sn+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewCommand(ViewParams<?> params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.project_name , m.commandline" +
                " FROM shca_project_project c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        ret.put("project_name",metadata.get("project_name"));
        ret.put("cromwell_workflow_inputs",metadata.get("commandline"));
        ret.put("errmsg","");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewLog(ViewParams<?> params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        String type = params.getString("type");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.project_name , m.stdout,m.stderr" +
                " FROM shca_project_project c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        String stdout = (String)metadata.get("stdout");
        String stderr = (String)metadata.get("stderr");
        String cromwell_workflow_inputs = null;
        if(stdout != null){
            if("stdout".equals(type)){
                cromwell_workflow_inputs = readFile(new File(stdout));
            } else if("stderr".equals(type)){
                cromwell_workflow_inputs = readFile(new File(stderr));
            } else {
                cromwell_workflow_inputs = readFile(new File(stdout));
            }
        }
        ret.put("project_name",metadata.get("project_name"));
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs",StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }


    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }

    public Forward report(ViewParams<?> params) throws JsonProcessingException {
        Map<String, Object> ret = new HashMap<>();
//        String id = params.getString("id");
        Map<String,Object> cancer = db.selectUnique("select sample_name_0,sample_name_1 from shca_project_project where id = ?", params.getString("id"));
        ret.put("cancer",cancer);
        if (params.isJson()) {
//            List<Map<String, Object>> outputList = db.select("SELECT sample_name,id,file_name,file_size,store_path" +
//                    " FROM shca_project_cancer_outputfile" +
//                    " where project_no = (SELECT project_no from shca_project_project where id = ?) ", id);
            List<Map<String, Object>> outputList = db.select("SELECT sample_name,id,file_name,file_size,store_path" +
                    " FROM shca_project_cancer_outputfile" +
                    " where sample_name = ? ", params.getString("sample_name"));

            ret.put("rows", outputList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_project_report.jsp", ret);
    }

    public Forward sample(ViewParams<?> params) throws JsonProcessingException{
        Map<String, Object> ret = new HashMap<>();
        String sample_name_0 = params.getString("sample_name_0");
        String sample_name_1 = params.getString("sample_name_1");
        String project_name = db.selectUniqueString("SELECT project_name FROM shca_project_project where (sample_name_0 = ? and sample_name_1 = ?) or (sample_name_0 = ? and sample_name_1 = ?)",sample_name_0,sample_name_1,sample_name_1,sample_name_0);
        if(!StringUtils.isEmpty(project_name)){
            ret.put("project_name",project_name);
            ret.put("errcode", 1000);
            ret.put("errmsg","项目样本重复");
            return new JsonForward(ret);
        }
        ret.put("errcode", 0);
        return new JsonForward(ret);
    }

    public Forward qcReport(ViewParams<?> params) {
        Map<String, Object> ret = new HashMap<>();
        Map<String,Object> cancer = db.selectUnique("select project_no,sample_name_0,sample_name_1 from shca_project_project where id = ?", params.getString("id"));
        String sample_name_0_id = db.selectUniqueString("select id from shca_project_cancer where sample_name = ?",cancer.get("sample_name_0"));
        String sample_name_1_id = db.selectUniqueString("select id from shca_project_cancer where sample_name = ?",cancer.get("sample_name_1"));
        cancer.put("sample_name_0_id",sample_name_0_id);
        cancer.put("sample_name_1_id",sample_name_1_id);
        ret.put("cancer",cancer);
        return new JspForward( "/shca/project/project_project_qcReport.jsp", ret);
    }

//    public Forward cancerList(ViewParams<?> params) {
//        Map<String,Object> ret = new HashMap<>();
//        String status = params.getString("status");
//        ret.put("status",status);
//        String sample_name_0 = params.getString("sample_name_0");
//        String sample_name_1 = params.getString("sample_name_1");
//        ret.put("sample_name_0",sample_name_0);
//        ret.put("sample_name_1",sample_name_1);
//
////        if (params.isJson()){
//            OrderPage page = params.getPage();
//            List<Object> sqlParams = new ArrayList<>();
//            String sql = " SELECT id,sample_name FROM shca_project_cancer WHERE 1 = 1 ";
//            if(!CollectionUtils.isEmpty(sample_name_0) && !CollectionUtils.isEmpty(sample_name_1)){
//                sql += " and sample_name in (?,?) ";
//                sqlParams.add(sample_name_0);
//                sqlParams.add(sample_name_1);
//            }
//            sql += params.whereSql( sqlParams);
//            sql += " ORDER  BY create_time DESC ";
//            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
//
//            for(int i=0;i<mapList.size();i++){
//                Map<String,Object> map = mapList.get(i);
//                if(sample_name_0.equals(map.get("sample_name"))){
//                    ret.put("sample_name_0_id",map.get("id"));
//                }
//                if(sample_name_1.equals(map.get("sample_name"))){
//                    ret.put("sample_name_1_id",map.get("id"));
//                }
//
//            }
//
////            return new JsonForward(ret);
////        }
//        if(!CollectionUtils.isEmpty(params.getString("script"))&&params.getInt("script")==1){
//            return new JspForward( "/shca/project/project_project_script_list.jsp", ret);
//        }
//        return new JspForward( "/shca/project/project_project_cancer_list.jsp", ret);
//    }
}
