package com.shca.module.project.view;

import com.shca.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.Pipeline;
import com.shca.config.SystemConfigService;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.*;
import com.shca.cromwell.vo.Call;
import com.shca.cromwell.vo.Workflow;
import com.shca.module.cancer.service.ProcessService;

import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;
import com.shca.util.StringUtils;
import org.apache.log4j.Logger;

import java.io.*;
import java.util.*;

import javax.annotation.Resource;

public class ProjectReportView {
    private final static Logger LOGGER = Logger.getLogger(ProjectUploadView.class);

    @Resource private DbService db;
    @Resource private CromwellClient client;
    @Resource private SystemConfigService config;

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,project_status,lims_result,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1," +
                    "(select workflow_status from v_shca_project_compare a where a.project_sn = b.project_sn) as prev_workflow_status,"+
                    "workflow_id_0,workflow_status_0,start_0,end_0,current_step_0,workflow_id_1,workflow_status_1,start_1,end_1,current_step_1,total_step from v_shca_project_report b " +
                    " WHERE 1 = 1 ";
            sql += params.whereSql( sqlParams);
            String workflow_status = params.getString("workflow_status");
            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }

            if("Failed".equals(workflow_status)) {
                sql += " and (workflow_status_0 = 'Failed' or workflow_status_1 = 'Failed')";
            } else if("Succeeded".equals(workflow_status) ){
                sql += " and (workflow_status_0 = 'Succeeded' and (workflow_status_1 = 'Succeeded' or workflow_status_1 is null))";
            } else if("Wait".equals(workflow_status) ){
                sql += " and (workflow_status_0 like 'Wait%' and workflow_status_1 like 'Wait%' )";
            } else if("Running".equals(workflow_status) ){
                sql += " and (workflow_status_0 like 'Running%' or workflow_status_1 like 'Running%' ) and workflow_status_0 != 'Failed' and workflow_status_0 != 'Failed' ";
            } else if("Abort".equals(workflow_status) ){
                sql += " and workflow_status_0 like 'Abort%' or workflow_status_1 like 'Abort%' ";
            } else {
            }

            sql += " ORDER  BY LEAST(ifnull(start_0,'2099-01-01 00:00:00'),ifnull(start_1,'2099-01-01 00:00:00'))  DESC ";

            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                //Map<String,Object> map = it.next();
                //String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                //map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }
                }
                String sample_name_0 = (String)map.get("sample_name_0");
                String sample_name_1 = (String)map.get("sample_name_1");

                Date start0 = (Date)map.get("start_0");
                Date end0 = (Date)map.get("end_0");

                Date start1 = (Date)map.get("start_1");
                Date end1 = (Date)map.get("end_1");
                Date start = null;
                Date end = null;
                if(StringUtils.hasLength(sample_name_0) && StringUtils.hasLength(sample_name_1)) {
                    if (start0 != null && start1 != null) {
                        start = start0.before(start1) ? start0 : start1;
                    } else if (start0 == null && start1 != null) {
                        start = start1;
                    } else if (start0 != null && start1 == null) {
                        start = start1;
                    }

                    if (end0 != null && end1 != null) {
                        end = end0.before(end1) ? end1 : end0;
                    } else {

                    }
                } else if(StringUtils.hasLength(sample_name_0) && !StringUtils.hasLength(sample_name_1)){
                        start = start0;
                        end = end0;
                }

                map.put("start",start);
                map.put("end",end);

                long runtime = 0;
                if(start!=null && end !=null){
                    runtime = end.getTime()-start.getTime();
                } else if(start!=null && end == null){
                    runtime = new Date().getTime() - start.getTime();
                } else  {

                }
                map.put("runtime",runtime);

                String workflow_status_0 = (String)map.get("workflow_status_0");
                String workflow_status_1 = (String)map.get("workflow_status_1");
                String workflow_status_str = null;
                if(StringUtils.hasLength(sample_name_0) && StringUtils.hasLength(sample_name_1)) {
                    if ("Failed".equals(workflow_status_0) || "Failed".equals(workflow_status_1)) {
                        workflow_status_str = "Failed";
                    } else if ("Succeeded".equals(workflow_status_0) && ("Succeeded".equals(workflow_status_1) )) {
                        workflow_status_str = "Succeeded";
                    } else if ("Aborting".equals(workflow_status_0) || "Aborting".equals(workflow_status_1)) {
                        workflow_status_str = "Aborting";
                    } else if ("Aborted".equals(workflow_status_0) && ("Aborted".equals(workflow_status_1) )) {
                        workflow_status_str = "Aborted";
                    } else {
                        workflow_status_str = "Running";
                    }
                } else if(StringUtils.hasLength(sample_name_0) && !StringUtils.hasLength(sample_name_1)){
                    workflow_status_str = workflow_status_0;
                }
                map.put("workflow_status",workflow_status_str);

                String lims_result = (String) map.get("lims_result");
                if (JsonUtils.isValidObject(lims_result)) {
                    Map<String, Object> resultObj = JsonUtils.parseObject(lims_result);
                    map.put("lims_result", resultObj.get("success"));
                } else {
                    map.put("lims_result", "");
                }
                String sub_workflow_id_0 = (String) map.get("sub_workflow_id_0");
                Long id = (Long)map.get("id");
                if("Running".equals(workflow_status_0) && sub_workflow_id_0==null){
                    sub_workflow_id_0 =  reflashSubWorkflowId0(id);
                }
                map.put("sub_workflow_id_0",sub_workflow_id_0);
                String sub_workflow_id_1 = (String) map.get("sub_workflow_id_1");
                /* String workflow_status_0 = (String) map.get("workflow_status_0");*/
                if("Running".equals(workflow_status_1) && sub_workflow_id_1==null){
                    sub_workflow_id_1 =  reflashSubWorkflowId1(id);
                }
                map.put("sub_workflow_id_1",sub_workflow_id_1);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_report_list.jsp", ret);
    }

    public Forward view(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();

        String sql = " select id,project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1," +
                "workflow_id_0,workflow_status_0,start_0,end_0,current_step_0,  lims ," +
                "(select workflow_status from v_shca_project_compare a where a.project_sn = b.project_sn) as prev_workflow_status,"+
                "workflow_id_1,workflow_status_1,start_1,end_1,current_step_1 from v_shca_project_report b where id=?";

        Map<String,Object> cancer = db.selectUnique(sql, params.getString("id"));

        String sample_name_0 = (String)cancer.get("sample_name_0");
        String sample_name_1 = (String)cancer.get("sample_name_1");
        Date start0 = (Date)cancer.get("start_0");
        Date end0 = (Date)cancer.get("end_0");
        Date start1 = (Date)cancer.get("start_1");
        Date end1 = (Date)cancer.get("end_1");
        String workflow_status_0 = (String)cancer.get("workflow_status_0");
        String workflow_status_1 = (String)cancer.get("workflow_status_1");
        String prev_workflow_status = (String)cancer.get("prev_workflow_status");
        Date start = null;
        Date end = null;
        if(StringUtils.hasLength(sample_name_0) && StringUtils.hasLength(sample_name_1)) {
            if (start0 != null && start1 != null) {
                start = start0.before(start1) ? start0 : start1;
            } else if (start0 == null && start1 != null) {
                start = start1;
            } else if (start0 != null && start1 == null) {
                start = start1;
            }

            if (end0 != null && end1 != null) {
                end = end0.before(end1) ? end1 : end0;
            }
        } else if(StringUtils.hasLength(sample_name_0) && !StringUtils.hasLength(sample_name_1)) {
            start = start0;
            end = end0;
        }
        String lims = (String)cancer.get("lims");
        if(lims!=null && JsonUtils.isValidObject(lims)) {
            Object parsedLims = JsonUtils.parseObject(lims);
            String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(parsedLims);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }

        cancer.put("start",start);
        cancer.put("end",end);
        ret.put("cancer",cancer);

        if((String)cancer.get("cancer_kind") == null){
            cancer.put("cancer_kind","--");
        } else {
            String kind = config.getValue((String)cancer.get("cancer_kind"));
            cancer.put("cancer_kind",kind==null?"--":kind);
        }

        String workflow_status = null;
        if(!"Failed".equals(prev_workflow_status)) {
            if (StringUtils.hasLength(sample_name_0) && StringUtils.hasLength(sample_name_1)) {
                if ("Failed".equals(workflow_status_0) || "Failed".equals(workflow_status_1)) {
                    workflow_status = "Failed";
                } else if ("Succeeded".equals(workflow_status_0) && "Succeeded".equals(workflow_status_1)) {
                    workflow_status = "Succeeded";
                } else if ("Aborting".equals(workflow_status_0) || "Aborting".equals(workflow_status_1)) {
                    workflow_status = "Aborting";
                } else if ("Aborted".equals(workflow_status_0) && "Aborted".equals(workflow_status_1)) {
                    workflow_status = "Aborted";
                } else {
                    workflow_status = "Running";
                }
            } else if (StringUtils.hasLength(sample_name_0) && !StringUtils.hasLength(sample_name_1)) {
                workflow_status = workflow_status_0;
            }
        } else {
            workflow_status = "Failed";
        }

        cancer.put("status",config.getValue("run_status2."+workflow_status));

        ret.put("sample_name_0",sample_name_0);
        ret.put("sample_name_1",sample_name_1);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql2 = " SELECT sample_name,cancer_kind,sample_type,file_name,file_size,store_path,md5,create_username,create_time FROM shca_project_cancer_file WHERE status = 'successed' ";
            sql2 += " and  (sample_name = ? ";
            sql2 += " or sample_name = ? )";
            sqlParams.add(sample_name_0);
            sqlParams.add(sample_name_1);

            sql2 += params.whereSqlForKendoUI( sqlParams);
            sql2 += " ORDER BY create_time DESC ,sample_name asc, rawdata asc";
            List<Map<String,Object>> mapList =  db.select(sql2,page,sqlParams.toArray(new Object[0]));
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("seq_no",i+1);
                //map.put("sample_type",config.getValue("simple_kind_name."+(String)map.get("sample_type")));
                map.put("sample_type",config.getValue("sample_kind_name."+(String)map.get("sample_type")));
                //map.put("creator_name","王赟");
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);

        return new JspForward( "/shca/project/project_report_view.jsp", ret);

    }

    public Forward script(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);

        List<Object> sqlParams = new ArrayList<>();
        String sql = "select sample_name_0,sample_name_1,sub_workflow_id_0,workflow_id_0, sub_workflow_id_1,workflow_id_1 from v_shca_project_report where id = ?  ";
        sqlParams.add(id);
        Map<String,Object> map_query  =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));

        String sample_name_0 = (String)map_query.get("sample_name_0");
        String sample_name_1 = (String)map_query.get("sample_name_1");
        String workflow_id_0 = (String)map_query.get("workflow_id_0");
        String workflow_id_1 = (String)map_query.get("workflow_id_1");

        ret.put("workflow_id_0",workflow_id_0);
        ret.put("workflow_id_1",workflow_id_1);


        String subWorkflowId_query_0 = (String)map_query.get("sub_workflow_id_0");

        if(StringUtils.hasLength(subWorkflowId_query_0)){
            workflow_id_0 = subWorkflowId_query_0;
        }

        String subWorkflowId_query_1 = (String)map_query.get("sub_workflow_id_1");

        if(StringUtils.hasLength(subWorkflowId_query_1)){
            workflow_id_1 = subWorkflowId_query_1;
        }

        String subWorkflowId0 = getSubWorkflowId(workflow_id_0);
        if(subWorkflowId0!=null && subWorkflowId0!=workflow_id_0){
            db.update("update shca_project_cancer set cromwell_sub_workflow_id_1 = ? where sample_name = ?",subWorkflowId0,sample_name_0);
            workflow_id_0 = subWorkflowId0;
        }else {
            subWorkflowId0 = workflow_id_0;
        }
        ret.put("subWorkflowId0",subWorkflowId0);

        String subWorkflowId1 = getSubWorkflowId(workflow_id_1);
        if(subWorkflowId1!=null && subWorkflowId1!=workflow_id_1){
            db.update("update shca_project_cancer set cromwell_sub_workflow_id_1 = ? where sample_name = ?",subWorkflowId1,sample_name_1);
            workflow_id_1 = subWorkflowId1;
        }else {
            subWorkflowId1 = workflow_id_1;
        }
        ret.put("subWorkflowId1",subWorkflowId1);

        if (params.isJson()){
            OrderPage page = params.getPage();




            List<Map<String,Object>> list = new ArrayList<>();



            MetadataResponse metadataResponse = client.send( new MetadataRequest( workflow_id_0));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            Iterator<String> iterator  = map.keySet().iterator();
            while(iterator.hasNext()){
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    tempMap.put("样本编号",sample_name_0);
                    tempMap.put("workflow_id",workflow_id_0);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());

                    tempMap.put("seq_no",(list.size()+1));

                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);
                    list.add(tempMap);
                }
            }


            MetadataResponse metadataResponse1 = client.send( new MetadataRequest( workflow_id_1));
            Map<String, List<Call>> map1 = metadataResponse1.getCalls();
            Iterator<String> iterator1  = map1.keySet().iterator();
            while(iterator1.hasNext()){
                String key = iterator1.next();
                List<Call> tList = map1.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    tempMap.put("样本编号",sample_name_1);
                    tempMap.put("workflow_id",workflow_id_1);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());

                    tempMap.put("seq_no",(list.size()+1));

                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);
                    list.add(tempMap);
                }
            }

            Collections.sort(list,new Comparator<Map<String,Object>>(){
                @Override
                public int compare(Map<String,Object> o1, Map<String,Object> o2) {
                    Date start1 = (Date)o1.get("start");
                    Date start2 = (Date)o2.get("start");
                    Date end1 = (Date)o1.get("end");
                    Date end2 = (Date)o2.get("end");
                    if(start1==null && start2==null){
                        return 0;
                    } else if(start1 == null && start2!=null){
                        return 1;
                    } else if(start1!=null && start2==null){
                        return -1;
                    } else {
                        if(start1.getTime()<start2.getTime()){
                            return -1;
                        } else if (start1.getTime()==start2.getTime()){
                            if(end1==null && end2==null){
                                return 0;
                            } else if(end1 == null && end2!=null){
                                return 1;
                            } else if(end1!=null && end2==null){
                                return -1;
                            } else {
                                if(end1.getTime()<end2.getTime()){
                                    return -1;
                                } else if (end1.getTime()==end2.getTime()){
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        } else {
                            return 1;
                        }
                    }
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_report_script.jsp", ret);
    }

    public Forward viewParam(ViewParams<?> params) {
        String id = params.getString("id");
        String project_sn = db.selectUniqueString("SELECT project_sn from v_shca_project_report where id = ?",id);
        //样本运行文件夹
        String directoryPath = config.getValue("workspace.project_path")+File.separator+project_sn;
        System.out.println(directoryPath);
        File d = new File(directoryPath);
        //File d = new File("C:\\Users\\<USER>\\Documents");
        String jsonStr = null;
        if(d.exists()){
            File[] files = d.listFiles(new FileFilter(){
                public boolean accept(File file) {
                    if(file.isDirectory()) {
                        return false;
                    } else {
                        if(file.getName().startsWith("mutual") && file.getName().endsWith(".json"))
                            return true;
                        else
                            return false;
                    }

                }
            });
            Arrays.sort(files, new Comparator<File>(){
                @Override
                public int compare(File f1, File f2) {
                    long diff = f2.getName().compareTo(f1.getName());
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;
                }
            });

            for (int i=0;i<files.length;){
                File f =  files[i];
                jsonStr = readFile(f);
                break;
            }
        } else {

        }
        Map<String,Object> ret = new HashMap<>();
        if(jsonStr!=null) {
            Object parsedObj = JsonUtils.parseObject(jsonStr);
            String cromwell_workflow_inputs = JsonUtils.toJSONStringPretty(parsedObj);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }

        ret.put("errmsg","未找到"+project_sn+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewCommand(ViewParams<?> params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.project_name , m.commandline" +
                " FROM shca_project_project c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        ret.put("project_name",metadata.get("project_name"));
        ret.put("cromwell_workflow_inputs",metadata.get("commandline"));
        ret.put("errmsg","");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewLog(ViewParams<?> params) {
        String filePath = params.getString("filePath");
        String sample_name = params.getString("sample_name",false,"");

        String cromwell_workflow_inputs  = readFile(new File(filePath));;
        Map<String,Object> ret = new HashMap<>();
        ret.put("sample_name",sample_name);
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs",StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }


    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }



    public Forward sample(ViewParams<?> params) throws JsonProcessingException{
        Map<String, Object> ret = new HashMap<>();
        String sample_name_0 = params.getString("sample_name_0");
        String sample_name_1 = params.getString("sample_name_1");
        String project_name = db.selectUniqueString("SELECT project_name FROM shca_project_project where (sample_name_0 = ? and sample_name_1 = ?) or (sample_name_0 = ? and sample_name_1 = ?)",sample_name_0,sample_name_1,sample_name_1,sample_name_0);
        if(!StringUtils.isEmpty(project_name)){
            ret.put("project_name",project_name);
            ret.put("errcode", 1000);
            ret.put("errmsg","项目样本重复");
            return new JsonForward(ret);
        }
        ret.put("errcode", 0);
        return new JsonForward(ret);
    }


    public Forward qcReport(ViewParams<?> params) {
        Map<String, Object> ret = new HashMap<>();
        Map<String,Object> project = db.selectUnique("select project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1" +
                " from v_shca_project_report c where id = ?", params.getString("id"));
        ret.put("project",project);
        return new JspForward( "/shca/project/project_project_qcReport2.jsp", ret);
    }

    public Forward viewQC(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        //查询出项目明细
        Map<String,Object> project = db.selectUnique("select project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1" +
                " from v_shca_project_report c where id = ?", params.getString("id"));
        ret.put("project",project);

        String project_sn = (String)project.get("project_sn");
        String sample_name_0 = (String)project.get("sample_name_0");
        String sample_name_1 = (String)project.get("sample_name_1");


        OrderPage page = params.getPage();
        List<Object> sqlParams = new ArrayList<>();
        String sql = " SELECT project_no , sample_name , qc_name , qc_value , qc_reference , qc_qualified , " +
                "create_username , create_time , update_username , update_time FROM shca_project_project_qcsummary WHERE 1 = 1 ";
        sql += " and sample_name = ? and  project_sn = ? ";
        sqlParams.add(sample_name_0);
        sqlParams.add(project_sn);

        sql += params.whereSql( sqlParams);
        sql += " ORDER BY id";
        List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
        Iterator<Map<String,Object>> it = mapList.iterator();
        int index = 0;
        int  mapSize = mapList.size();
        while(it.hasNext()){
            Map<String,Object> map = it.next();
            map.put("样本编号",map.get("sample_name"));
            if(index>= (mapSize+1)/2){
                Map<String,Object> temp = mapList.get(index-(mapSize+1)/2);
                temp.put("project_no1",map.get("project_no"));
                temp.put("sample_name1",map.get("sample_name"));
                temp.put("qc_name1",map.get("qc_name"));
                temp.put("qc_value1",map.get("qc_value"));
                temp.put("qc_reference1",map.get("qc_reference"));
                temp.put("qc_qualified1",map.get("qc_qualified"));
                temp.put("create_username1",map.get("create_username"));
                temp.put("create_time1",map.get("create_time"));
                temp.put("update_username1",map.get("update_username"));
                temp.put("update_time1",map.get("update_time"));
                temp.put("样本编号",temp.get("sample_name"));
                it.remove();
            }
            index++;
        }

        List<Object> sqlParams2 = new ArrayList<>();
        String sql2 = " SELECT project_no , sample_name , qc_name , qc_value , qc_reference , qc_qualified , " +
                "create_username , create_time , update_username , update_time FROM shca_project_project_qcsummary WHERE 1 = 1 ";
        sql2 += " and sample_name = ? and  project_sn = ? ";;
        sqlParams2.add((sample_name_1));
        sqlParams2.add(project_sn);

        sql2 += params.whereSql( sqlParams2);
        sql2 += " ORDER BY id";
        List<Map<String,Object>> mapList2 =  db.select(sql2,page,sqlParams2.toArray(new Object[0]));
        Iterator<Map<String,Object>> it2 = mapList2.iterator();
        int index2 = 0;
        int  mapSize2 = mapList2.size();
        while(it2.hasNext()){
            Map<String,Object> map = it2.next();
            map.put("样本编号",map.get("sample_name"));
            if(index2>= (mapSize2+1)/2){
                Map<String,Object> temp = mapList2.get(index2-(mapSize2+1)/2);
                temp.put("project_no1",map.get("project_no"));
                temp.put("sample_name1",map.get("sample_name"));
                temp.put("qc_name1",map.get("qc_name"));
                temp.put("qc_value1",map.get("qc_value"));
                temp.put("qc_reference1",map.get("qc_reference"));
                temp.put("qc_qualified1",map.get("qc_qualified"));
                temp.put("create_username1",map.get("create_username"));
                temp.put("create_time1",map.get("create_time"));
                temp.put("update_username1",map.get("update_username"));
                temp.put("update_time1",map.get("update_time"));
                it2.remove();
            }
            index2++;
        }
        mapList.addAll(mapList2);

        ret.put("total", page.asPage().getTotal());
        ret.put("rows", mapList);
        return new JsonForward(ret);
    }

    public Forward viewQC2(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        //查询出项目明细
        Map<String,Object> project = db.selectUnique("select project_sn,project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1" +
                " from v_shca_project_report c where id = ?", params.getString("id"));
        ret.put("project",project);

        String project_sn = (String)project.get("project_sn");
        String sample_name_0 = (String)project.get("sample_name_0");
        String sample_name_1 = (String)project.get("sample_name_1");
        OrderPage page = params.getPage();
        List<Object> sqlParams = new ArrayList<>();
        String sql = " SELECT project_no , sample_name , qc_name , qc_value , qc_reference , qc_qualified , " +
                "create_username , create_time , update_username , update_time FROM shca_project_project_qcsummary WHERE 1 = 1 ";
        sql += " and sample_name = ? and  project_sn = ? ";
        sqlParams.add(sample_name_0);
        sqlParams.add(project_sn);

        sql += params.whereSql( sqlParams);
        sql += " ORDER BY id";
        List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));

        List<Object> sqlParams2 = new ArrayList<>();
        sqlParams2.add(sample_name_1);
        sqlParams2.add(project_sn);
        List<Map<String,Object>> mapList2 =  db.select(sql,page,sqlParams2.toArray(new Object[0]));
        Iterator<Map<String,Object>> it = mapList.iterator();
        while (it.hasNext()) {
            Map<String, Object> map = it.next();
            String qc_name = (String) map.get("qc_name");
            map.put("qc_value1", "");
            map.put("qc_reference1", "");
            map.put("qc_qualified1", "");
            for (int i = 0; i < mapList2.size(); i++) {
                Map<String, Object> map2 = mapList2.get(i);
                String qc_name2 = (String) map2.get("qc_name");

                if (qc_name.equals(qc_name2)) {
                    map.put("qc_value1", map2.get("qc_value"));
                    map.put("qc_reference1", map2.get("qc_reference"));
                    map.put("qc_qualified1", map2.get("qc_qualified"));
                    break;
                }
            }
        }

        String parent_path = "/mydata/shca/samples/";
        String dir0 = parent_path+sample_name_0+"/capture/beds/";
        String dir1 = parent_path+sample_name_1+"/capture/beds/";
        String fileName2 = ".bedcov.average.txt";
        File file0_2 = new File(dir0+sample_name_0+fileName2);
        File file1_2 = new File(dir1+sample_name_1+fileName2);
        Map<String,Object> map = new HashMap<>();
        map.put("qc_name","bedcov.average.txt");
        map.put("qc_qualified",true);
        map.put("qc_qualified1",true);
        if(file0_2.exists()) {
            String value0_2 = readFile(file0_2);
            map.put("qc_value",value0_2);
        } else {
            map.put("qc_value","");
        }
        if(file1_2.exists()) {
            String value1_2 = readFile(file1_2);
            map.put("qc_value1",value1_2);
        } else {
            map.put("qc_value1","");
        }
        mapList.add(map);
        ret.put("total", page.asPage().getTotal());
        ret.put("rows", mapList);

        return new JsonForward(ret);
    }

    public Forward viewQCFile(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        //查询出项目明细
        Map<String,Object> project = db.selectUnique("select project_no,project_name,sample_name_0,sample_name_1,cancer_kind,cancer_script_0,cancer_script_1" +
                " from v_shca_project_report c where id = ?", params.getString("id"));
        ret.put("project",project);

        String sample_name_0 = (String)project.get("sample_name_0");
        String sample_name_1 = (String)project.get("sample_name_1");

        List<Map<String,Object>> list = new ArrayList<>();
        String parent_path = "/mydata/shca/samples/";
        String dir0 = parent_path+sample_name_0+"/capture/beds/";
        String dir1 = parent_path+sample_name_1+"/capture/beds/";

        String fileName1 = ".bedcov.txt";
        String fileName2 = ".bedcov.average.txt";


        File file0_1 = new File(dir0+sample_name_0+fileName1);
        File file0_2 = new File(dir0+sample_name_0+fileName2);

        File file1_1 = new File(dir1+sample_name_1+fileName1);
        File file1_2 = new File(dir1+sample_name_1+fileName2);

        String str_file_status_no = "未找到";
        String str_file_status_has = "已生成";
        if(file0_1.exists()){
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_0);
            map.put("file_name", file0_1.getName());
            map.put("file_size", file0_1.length());
            map.put("file_path", file0_1.getAbsolutePath());
            map.put("file_status", str_file_status_has);
            map.put("last_modify_time", file0_1.lastModified());
            list.add(map);
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_0);
            map.put("file_name", sample_name_0+fileName1);
            map.put("file_size", "--");
            map.put("file_path", null);
            map.put("file_status", str_file_status_no);
            map.put("last_modify_time", 0);
            list.add(map);
        }

        if(file0_2.exists()){
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_0);
            map.put("file_name", file0_2.getName());
            map.put("file_size", file0_2.length());
            map.put("file_path", file0_2.getAbsolutePath());
            map.put("file_status", str_file_status_has);
            map.put("last_modify_time", file0_1.lastModified());
            list.add(map);
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_0);
            map.put("file_name", sample_name_0+fileName2);
            map.put("file_size", "--");
            map.put("file_path", null);
            map.put("file_status", str_file_status_no);
            map.put("last_modify_time", 0);
            list.add(map);
        }

        if(file1_1.exists()){
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_1);
            map.put("file_name", file1_1.getName());
            map.put("file_size", file1_1.length());
            map.put("file_path", file1_1.getAbsolutePath());
            map.put("file_status", str_file_status_has);
            map.put("last_modify_time", file1_1.lastModified());
            list.add(map);
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_1);
            map.put("file_name", sample_name_1+fileName1);
            map.put("file_size", "--");
            map.put("file_path", null);
            map.put("file_status", str_file_status_no);
            map.put("last_modify_time", 0);
            list.add(map);
        }

        if(file1_2.exists()){
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_1);
            map.put("file_name", file1_2.getName());
            map.put("file_size", file1_2.length());
            map.put("file_path", file1_2.getAbsolutePath());
            map.put("file_status", str_file_status_has);
            map.put("last_modify_time", file1_2.lastModified());
            list.add(map);
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("sample_name", sample_name_1);
            map.put("file_name", sample_name_1+fileName2);
            map.put("file_size", "--");
            map.put("file_path", null);
            map.put("file_status", str_file_status_no);
            map.put("last_modify_time", 0);
            list.add(map);
        }


        ret.put("total", 4);
        ret.put("rows", list);
        return new JsonForward(ret);
    }

    public Forward stop(ViewParams<?> params) throws JsonProcessingException{
        long id = params.getId();

        String sql = "select  workflow_id_0 ,workflow_id_1 from v_shca_project_report where id = ?";
        Map<String,Object> project = db.selectUnique(sql,id);
        String workflow_id_0 = (String)project.get("workflow_id_0");
        AbortResponse<?,?> response = client.send( new AbortRequest( workflow_id_0));
        Map<String,Object> ret = new HashMap<>();
        ret.put("status",response.getStatus());
        return new JsonForward(ret);
    }

    public Forward restart(ViewParams<?> params) throws JsonProcessingException{

        Map<String,Object> ret = new HashMap<>();
        ret.put("errcode",1000);
        ret.put("errmsg","重新分析出错");
        try {
            long id = params.getId();
            Date now = new Date();

            Map<String, Object> project = db.selectUnique("select a.id,a.cromwell_workflow_id_0,a.sample_name_0,a.sample_name_1,b.cancer_kind " +
                    " ,project_sn,a.project_no,a.project_name,a.cancer_kind " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR1_0 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_0 and rawdata=? and status=?) rawDataR2_0 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR1_1 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name_1 and rawdata=? and status=?) rawDataR2_1 " +
                    " from shca_project_project a inner join shca_project_cancer b on a.sample_name_0=b.sample_name " +
                    " where a.id = ?", "R1", "successed", "R2", "successed", "R1", "successed", "R2", "successed", id);

            String workflowId = (String) project.get("cromwell_workflow_id_0");
            MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
            Workflow workflow = metadataResponse.getWorkflow();
            int cnt = db.update("update cromwell_workflow_workflow set name=?,status=?,submission=?,start=?,end=?" +
                            ",update_time=?,update_username=? where id=? ", workflow.getName(), workflow.getStatus(), workflow.getSubmission()
                    , workflow.getStart(), workflow.getEnd(), now, "CromwellService", workflow.getId());

            if (cnt == 0 && workflow.getId() != null) {
                db.insert("insert into cromwell_workflow_workflow(id,name,status,submission,start,end,create_time,create_username,update_time,update_username) " +
                                "values(?,?,?,?,?,?,?,?,?,?) ", workflow.getId(), workflow.getName(), workflow.getStatus(), workflow.getSubmission()
                        , workflow.getStart(), workflow.getEnd(), now, "CromwellService", now, "CromwellService");
            }

            String status = "running_step1";
            if ("Succeeded".equals(workflow.getStatus())) {
                status = "running_step2";
                Map<String, Object> cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                        " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", project.get("cancer_kind"), true, 2);
                ProcessService processService = Pipeline.parse((String) project.get("cancer_kind")).getProcessService();

                if (cromwellConfig != null && processService != null) {

                    File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                    File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                    if (!options.exists()) {
                        options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                    }
                    if (!options.exists()) {
                        options = null;
                    }


                    WorkflowsResponse response = processService.summary((String) project.get("project_sn")
                            , (String) project.get("project_name"), (String) project.get("sample_name_0")
                            , new File((String) project.get("rawDataR1_0")), new File((String) project.get("rawDataR2_0"))
                            , (String) cromwellConfig.get("cromwell_workflow_inputs")
                            , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                    cnt = db.update("update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_sub_workflow_id_1=null,cromwell_workflow_status_1=?,cancer_script_1=? where sample_name=? "
                            , response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), project.get("sample_name_0"));

                    if (StringUtils.hasLength((String) project.get("sample_name_1"))) { // 单样本兼容

                        response = processService.summary((String) project.get("project_sn")
                                , (String) project.get("project_name"), (String) project.get("sample_name_1")
                                , new File((String) project.get("rawDataR1_1")), new File((String) project.get("rawDataR2_1"))
                                , (String) cromwellConfig.get("cromwell_workflow_inputs")
                                , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                        cnt = db.update("update shca_project_cancer set cromwell_workflow_id_1=?,cromwell_sub_workflow_id_1=null,cromwell_workflow_status_1=?,cancer_script_1=? where sample_name=? "
                                , response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), project.get("sample_name_1"));

                    }
                } else {
                    ret.put("errcode", 3000);
                    ret.put("errmsg", "项目配置异常");

                    LOGGER.error("summary-process[" + project.get("project_sn") + "][" + project.get("project_name") + "]: cromwell config is null or processService is not matched");
                    return new JsonForward(ret);
                }
            }


            if ("Failed".equals(workflow.getStatus())) {
                status = "failed";
            }
            if ("Aborted".equals(workflow.getStatus())) {
                status = "aborted";
            }

            cnt = db.update("update shca_project_project set cromwell_workflow_status_0=?,status=? where id=? "
                    , workflow.getStatus(), status, project.get("id"));

            db.delete("delete from cromwell_workflow_metadata where workflow_id=? ", workflowId);
            for (String callName : metadataResponse.getCalls().keySet()) {
                for (Call call : metadataResponse.getCalls().get(callName)) {
                    db.insert("insert into cromwell_workflow_metadata(workflow_id,call_name,job_id,commandline,backend " +
                                    ",return_code,execution_status,backend_status,stdout,stderr " +
                                    " ,start,end,create_time,create_username,update_time,update_username) values( ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                            , workflowId, callName, call.getJobId(), call.getCommandLine(), call.getBackend()
                            , call.getReturnCode(), call.getExecutionStatus(), call.getBackendStatus(), call.getStdout(), call.getStderr()
                            , call.getStart(), call.getEnd(), now, "CromwellService", now, "CromwellService");
                }
            }


            cnt = db.update("update shca_project_project set status=? where id=? "
                    , "running_step2", project.get("id"));

            //更新第四步状态
            cnt = db.update("update shca_project_project set cromwell_workflow_id_1=null,cromwell_sub_workflow_id_1=null,cromwell_workflow_status_1=null where id=? ", project.get("id"));

            //更新lims
            cnt = db.update("update shca_project_project set lims_status=? , lims_time=null , upload_result=null,upload_time=null,cromwell_workflow_id_2=null,cromwell_workflow_id_2_2=null " +
                    " where id=? and lims_status in (?,?)", "wait_upload", project.get("id"), "wait_upload", "finished");

            ret.put("errcode",0);
            ret.put("errmsg", "提交成功");
        } catch(Exception e){
            e.printStackTrace();
        }


        return new JsonForward(ret);
    }

    private String reflashSubWorkflowId0(long id){
        String sql = "select ifnull(sub_workflow_id_0,workflow_id_0) as workflow_id,sample_name_0  from v_shca_project_report where id = ?  ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(id);
        Map<String,Object> map =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));
        String workflowId = (String)map.get("workflow_id");
        String sample_name_0 = (String)map.get("sample_name_0");
        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_cancer set cromwell_sub_workflow_id_1 = ? where sample_name = ?",subWorkflowId,sample_name_0);
            workflowId = subWorkflowId;
        }
        return workflowId;
    }
    private String reflashSubWorkflowId1(long id){
        String sql = "select ifnull(sub_workflow_id_1,workflow_id_1) as workflow_id,sample_name_1  from v_shca_project_report where id = ?  ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(id);
        Map<String,Object> map =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));
        String workflowId = (String)map.get("workflow_id");
        String sample_name_1 = (String)map.get("sample_name_1");
        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && subWorkflowId!=workflowId){
            db.update("update shca_project_cancer set cromwell_sub_workflow_id_1 = ? where sample_name = ?",subWorkflowId,sample_name_1);
            workflowId = subWorkflowId;
        }
        return workflowId;
    }
    private String getSubWorkflowId(String workflowId){
        String subWorkflowId = null;
        try {
            MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            Iterator<String> iterator = map.keySet().iterator();

            while (iterator.hasNext()) {
                String key = iterator.next();
                List<Call> tList = map.get(key);
                for (Call call : tList) {
                    if (call.getSubWorkflowId() == null) {
                        subWorkflowId = workflowId;
                    } else {
                        subWorkflowId = getSubWorkflowId(call.getSubWorkflowId());
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return subWorkflowId;
    }
}
