package com.shca.module.project.view;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.support.ProcessServiceByBreastCancer;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;

public class TestView {

    private CromwellClient client;

    private static final Logger LOGGER = Logger.getLogger( ProcessServiceByBreastCancer.class);

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    public Forward test(ViewParams params){
        File workflowSource = new File("/mydata/geneAnalysisPipeline/fileTransfer.wdl");
        File prettyJson = new File("/mydata/geneAnalysisPipeline/test/fileTransfer.json");

        if(!prettyJson.getParentFile().exists()){
            prettyJson.getParentFile().mkdirs();
        }
        JSONObject obj = new JSONObject();
        obj.put("fileTransfer.ACTION_FT","create");
        obj.put("fileTransfer.targetPath_FT","/CloudSvr01/analysisSourceFiles/aaa");
        obj.put("fileTransfer.tmpPath_FT","/CloudSvr01/analysisSourceFiles/temp");

        JSONArray array = new JSONArray();
        array.add("/mydata/shca/samples/BZ2100243B01/capture");
        obj.put("fileTransfer.fileList_FT",array);

        WriteStringToFile(prettyJson.getAbsolutePath(),obj.toJSONString());

        WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, null, null));
        System.out.println(response.getId());

        return new JsonForward(response);
    }

    private void WriteStringToFile(String filePath,String str) {
        try {
            File file = new File(filePath);
            PrintStream ps = new PrintStream(new FileOutputStream(file));
            ps.println(str);// 往文件里写入字符串
            //ps.append(str);// 在已有的基础上添加字符串
        } catch (FileNotFoundException e) {

            e.printStackTrace();
        }
    }
}
