package com.shca.module.project.view;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.file.service.FileService;
import com.shca.config.SystemConfigService;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;
import com.shca.util.StringUtils;

@Deprecated
public class ProjectFileView {

    @Resource private DbService db;
    private FileService fileService;
    @Resource private SystemConfigService config;

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,project_status,sample_name_0,sample_name_1,cancer_kind,create_time,workflow_id,workflow_type,workflow_status,workflow_status2,workflow_status3,start,end  " +
                    " from v_shca_project_file where 1=1 ";
            sql += params.whereSql( sqlParams);
                Calendar now = Calendar.getInstance();
                now.add(Calendar.DAY_OF_MONTH,-90);
                now.set(Calendar.HOUR_OF_DAY,0);
                now.set(Calendar.MINUTE,0);
                now.set(Calendar.SECOND,0);

                sql += " and create_time  >= ?  and workflow_type = ?  "; //and  upload_result LIKE '{"succ":true%'
                sqlParams.add(now.getTime());
                sqlParams.add("create");


            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }

            sql += " ORDER  BY start DESC,create_time DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }

                }

            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_file_list.jsp", ret);
    }


    public Forward list2(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,project_status,sample_name_0,sample_name_1,cancer_kind,create_time,workflow_id,workflow_type,workflow_status,workflow_status2,workflow_status3,start,end  " +
                    " from v_shca_project_file where 1=1 ";
            sql += params.whereSql( sqlParams);

                Calendar now = Calendar.getInstance();
                now.add(Calendar.DAY_OF_MONTH,-90);
                now.set(Calendar.HOUR_OF_DAY,0);
                now.set(Calendar.MINUTE,0);
                now.set(Calendar.SECOND,0);
                sql += " and create_time  >= ?  and workflow_type = ? "; //and upload_result LIKE '{"succ":true%'
                sqlParams.add(now.getTime());
                sqlParams.add("remove");
                /*sqlParams.add("Succeeded");*/


            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }

            sql += " ORDER  BY start DESC,create_time DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                }

            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_file_list2.jsp", ret);
    }



    public Forward list3(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,project_sn,project_no,project_name,project_status,sample_name_0,sample_name_1,cancer_kind,create_time,workflow_id,workflow_type,workflow_status,workflow_status2,workflow_status3,start,end  " +
                    " from v_shca_project_file where 1=1 ";
            sql += params.whereSql( sqlParams);


                Calendar now = Calendar.getInstance();
                now.add(Calendar.DAY_OF_MONTH,-90);
                now.set(Calendar.HOUR_OF_DAY,0);
                now.set(Calendar.MINUTE,0);
                now.set(Calendar.SECOND,0);
                sql += " and create_time  < ? ";
                sqlParams.add(now.getTime());
                /*sqlParams.add("remove");*/
               /* sqlParams.add("Succeeded");*/


            String sample_name = params.getString("sample_name");
            if(StringUtils.hasLength(sample_name)){
                sql += " and ( sample_name_0 like ? or sample_name_1 like ?)";
                sqlParams.add("%"+sample_name+"%");
                sqlParams.add("%"+sample_name+"%");
            }

            sql += " ORDER  BY start DESC,create_time DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                }

            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_file_list3.jsp", ret);
    }

    public Forward del(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String project_name = params.getString("project_name");
        String id = params.getString("id");
        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where project_name = ? ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(project_name);
        if(StringUtils.hasLength(id)){
            sql += " and id = ?";
            sqlParams.add(id);
        }
        sql += " order by id desc limit 1";

        Map<String,Object> map = db.selectUnique(sql,sqlParams.toArray(new Object[0]));

        Long id1 = (Long)map.get("id");
        if(id1!=null) {
            ret = fileService.delete(id1);
        } else {
            ret.put("errcode","9000");
            ret.put("errmsg","项目未找到");
        }
        return new JsonForward(ret);
    }

    public Forward delAll(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String[] ids = params.getStringArray("ids[]");

        if(ids!=null){
            for(int i=0;i<ids.length;i++){
                String id = ids[i];
                String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where 1=1 ";
                List<Object> sqlParams = new ArrayList<>();
                if(StringUtils.hasLength(id)){
                    sql += " and id = ?";
                    sqlParams.add(id);
                }
                sql += " order by id desc limit 1";

                Map<String,Object> map = db.selectUnique(sql,sqlParams.toArray(new Object[0]));

                Long id1 = (Long)map.get("id");
                if(id1!=null) {
                    ret = fileService.delete(id1);
                } else {
                    ret.put("errcode","9000");
                    ret.put("errmsg","项目未找到");
                }
            }
        }
        ret.put("errcode","0");
        return new JsonForward(ret);
    }

    public Forward create(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String project_name = params.getString("project_name");
        String id = params.getString("id");
        String sql = "select id,project_name,sample_name_0,sample_name_1 from shca_project_project where project_name = ? ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(project_name);
        if(StringUtils.hasLength(id)){
            sql += " and id = ?";
            sqlParams.add(id);
        }
        sql += " order by id desc limit 1";

        Map<String,Object> map = db.selectUnique(sql,sqlParams.toArray(new Object[0]));

        Long id1 = (Long)map.get("id");

        if(id1!=null) {
            ret = fileService.create(id1);
            String sql2 = "update shca_project_project set cromwell_workflow_id_2_2 = null,cromwell_workflow_id_2_3 = null where id = ?  ";
            db.update(sql2,id);
        } else {
            ret.put("errcode","9000");
            ret.put("errmsg","项目未找到");
        }
        return new JsonForward(ret);
    }


    // private void WriteStringToFile(String filePath,String str) {
    //     try {
    //         File file = new File(filePath);
    //         PrintStream ps = new PrintStream(new FileOutputStream(file));
    //         ps.println(str);// 往文件里写入字符串
    //         //ps.append(str);// 在已有的基础上添加字符串
    //         ps.close();
    //     } catch (FileNotFoundException e) {
    //         e.printStackTrace();
    //     }
    // }

}
