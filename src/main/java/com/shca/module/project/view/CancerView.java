package com.shca.module.project.view;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.AbortRequest;
import com.shca.cromwell.api.AbortResponse;
import com.shca.cromwell.api.MetadataRequest;
import com.shca.cromwell.api.MetadataResponse;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.cromwell.vo.Call;
import com.shca.module.cancer.service.ProcessService;
import com.shca.config.SystemConfigService;
import com.shca.framework.mvc.controller.Forward;
import com.shca.framework.mvc.controller.support.JsonForward;
import com.shca.framework.mvc.controller.support.JspForward;
import com.shca.framework.mvc.view.ViewParams;
import com.shca.sql.OrderPage;
import com.shca.util.DbService;
import com.shca.util.DbUtils;

public class CancerView {

    private static final Logger log = LoggerFactory.getLogger( CancerView.class);
    @Resource private DbService db;
    @Resource private CromwellClient client;
    @Resource private SystemConfigService config;

    private Map<String, ProcessService> processServiceMap;

    public void setProcessServiceMap(Map<String, ProcessService> processServiceMap) {
        this.processServiceMap = processServiceMap;
    }

    public Forward create(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "创建失败";

        if(params.isJson()){
            String project_no = params.getString("project_no");
            String project_name = params.getString("project_name");
            String project_kind = params.getString("project_kind");
            String project_script = params.getString("project_script");
            String run_time = params.getString("run_time");
            String description = params.getString("description");

            try{
                Date now = new Date();
                Map<String,Object> template  =  new HashMap<>();
                template.put("project_no",project_no);
                template.put("project_name",project_name);
                template.put("project_kind",project_kind);
                template.put("project_script",project_script);
                template.put("run_time",run_time);
                template.put("description",description);
                template.put("create_username", params.getUserName());
                template.put("create_time",now);
                template.put("update_username", params.getUserName());
                template.put("update_time",now);
                System.out.println(template);
                List<Object> sqlParams = new ArrayList<>();
                String sql = DbUtils.insertSql( "shca_project_cancer", template, sqlParams);
                long id = db.insert( sql, sqlParams.toArray( new Object[0]));

                ret.put("id",id);
                errCode = 0;
            }catch (Exception e){
                log.error("创建项目失败",e);
            }
            ret.put("errCode",errCode);
            ret.put("errMsg",errMsg);
            return new JsonForward(ret);
        }

        String sql = " SELECT id,cancer_script FROM shca_cromwell_config WHERE 1 = 1 ";
        sql += " ORDER BY create_time DESC";
        List<Map<String,Object>> mapList =  db.select(sql);
        ret.put("cancer_script",mapList);
        return new JspForward( "/shca/project/project_cancer_create.jsp", ret);
    }

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);
        log.info("param status -> " + params.getString("status"));

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select *, " +
                    "(CASE cancer_status WHEN 'wait_running' THEN 1  " +
                    " ELSE 2 END) as sort_status_1," +
                    "(CASE workflow_status WHEN 'Succeeded' THEN 9  ELSE 1 END) as sort_status " +
                    " FROM (select id,sample_name,cancer_kind,cancer_script_0,cancer_status,workflow_id,sub_workflow_id,workflow_status,submission,start,end,project_name,current_step,total_step,priority from v_shca_project_sample " +
                    " WHERE 1 = 1 ";
            sql += params.whereSql( sqlParams);
            sql += " ) t ORDER  BY sort_status,sort_status_1,ifnull(start,'2099-01-01 00:00:00') DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));

            // Map<String,String> keyMap = new HashMap<>();
            // keyMap.put("breast_cancer","B/BZ");
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue((String)map.get("cancer_kind"));
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                    }

                }

                Date start = (Date)map.get("start");
                Date end = (Date)map.get("end");
                long runtime = 0;
                if(start!=null && end !=null){
                    runtime = end.getTime()-start.getTime();
                } else if(start!=null && end == null){
                    runtime = new Date().getTime() - start.getTime();
                } else  {

                }
                map.put("runtime",runtime);
                String sub_workflow_id = (String) map.get("sub_workflow_id");
                String workflow_status = (String) map.get("workflow_status");
                Long id = (Long)map.get("id");
                if("Running".equals(workflow_status) && sub_workflow_id==null){
                    sub_workflow_id =  reflashSubWorkflowId(id);
                }
                map.put("sub_workflow_id",sub_workflow_id);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_cancer_list.jsp", ret);
    }

    public Forward listData(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        Long id = params.getId();

        String sql = " SELECT id,sample_name,cancer_kind,cancer_script_0,workflow_id,sub_workflow_id,workflow_status,submission,start,end,project_name,current_step,total_step from v_shca_project_sample " +
                " WHERE id = ? ";

        Map<String,Object> map  =  db.selectUnique(sql,id);
        // Map<String,String> keyMap = new HashMap<>();
        // keyMap.put("breast_cancer","B/BZ");
        String groupName = "cancer_kind_code";

        if(map.get("cancer_kind")==null){
            map.put("cancer_kind","--");
        } else {
            String code = config.getValue(groupName+"."+map.get("cancer_kind"));
            String kind = config.getValue((String)map.get("cancer_kind"));
            if(kind==null){
                map.put("cancer_kind","--");
            } else {
                map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
            }
        }

        Date start = (Date)map.get("start");
        Date end = (Date)map.get("end");
        long runtime = 0;
        if(start!=null && end !=null){
            runtime = end.getTime()-start.getTime();
        } else if(start!=null && end == null){
            runtime = new Date().getTime() - start.getTime();
        } else  {

        }
        map.put("runtime",runtime);
        String sub_workflow_id = (String) map.get("sub_workflow_id");
        String workflow_status = (String) map.get("workflow_status");

        if("Running".equals(workflow_status) && sub_workflow_id==null){
            sub_workflow_id =  reflashSubWorkflowId(id);
        }
        map.put("sub_workflow_id",sub_workflow_id);

        ret.put("project", map);
        return new JsonForward(ret);

    }

    public Forward listOld(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String status = params.getString("status");
        ret.put("status",status);

        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = " select id,sample_name,cancer_kind,cancer_script_0,workflow_id,sub_workflow_id,workflow_status,submission,start,end,project_name,current_step,total_step from v_shca_project_sample " +
                    " WHERE 1 = 1 ";
            sql += params.whereSql( sqlParams);
            sql += " ORDER  BY create_time DESC ";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            // Map<String,String> keyMap = new HashMap<>();
            // keyMap.put("breast_cancer","B/BZ");
            String groupName = "cancer_kind_code";
            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String code = config.getValue(groupName+"."+map.get("cancer_kind"));
                    map.put("cancer_kind",config.getValue((String)map.get("cancer_kind"))+"("+code+")");
                }

                Date start = (Date)map.get("start");
                Date end = (Date)map.get("end");
                long runtime = 0;
                if(start!=null && end !=null){
                    runtime = end.getTime()-start.getTime();
                } else if(start!=null && end == null){
                    runtime = new Date().getTime() - start.getTime();
                } else  {

                }
                map.put("runtime",runtime);
                String sub_workflow_id = (String) map.get("sub_workflow_id");
                String workflow_status = (String) map.get("workflow_status");
                Long id = (Long)map.get("id");
                if("Running".equals(workflow_status) && sub_workflow_id==null){
                    sub_workflow_id =  reflashSubWorkflowId(id);
                }
                map.put("sub_workflow_id",sub_workflow_id);
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_cancer_listOld.jsp", ret);
    }

    private String reflashSubWorkflowId(long id){
        String sql = "select ifnull(sub_workflow_id,workflow_id) as workflow_id  from v_shca_project_sample where id = ?  ";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(id);
        String workflowId  =  db.selectUniqueString(sql,sqlParams.toArray(new Object[0]));
        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && !subWorkflowId.equals(workflowId)){
            db.update("UPDATE shca_project_cancer SET cromwell_sub_workflow_id_0 = ? WHERE id = ?",subWorkflowId,id);
            workflowId = subWorkflowId;
        }
        return workflowId;
   }

    public Forward script(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);
        List<Object> sqlParams = new ArrayList<>();
        String sql = "select sub_workflow_id,workflow_id  from v_shca_project_sample where id = ?  ";
        sqlParams.add(id);
        Map<String,Object> map_query  =  db.selectUnique(sql,sqlParams.toArray(new Object[0]));
        String workflowId = (String)map_query.get("workflow_id");
        String subWorkflowId_query = (String)map_query.get("sub_workflow_id");
        ret.put("workflowId",workflowId);
        if(StringUtils.hasLength(subWorkflowId_query)){
            workflowId = subWorkflowId_query;
        }

        String subWorkflowId = getSubWorkflowId(workflowId);
        if(subWorkflowId!=null && !subWorkflowId.equals(workflowId)){
            db.update("UPDATE shca_project_cancer SET cromwell_sub_workflow_id_0 = ? WHERE id = ?",subWorkflowId,params.getId());
            workflowId = subWorkflowId;
        } else {
            subWorkflowId = workflowId;
        }
        ret.put("subWorkflowId",subWorkflowId);
        ret.put("id",params.getId());
        if (params.isJson()){
            OrderPage page = params.getPage();
            MetadataResponse metadataResponse = client.send( new MetadataRequest(workflowId));

            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String,Object>> list = new ArrayList<>();
            for (String key : map.keySet()) {
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    String rid = DigestUtils.md5DigestAsHex(key.getBytes());
                    tempMap.put("id",rid);
                    tempMap.put("parentId",null);
                    tempMap.put("workflow_id",workflowId);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());

                    tempMap.put("seq_no",(list.size()+1));
                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);
                    list.add(tempMap);

                }
            }

            Collections.sort(list, (Map<String,Object> o1, Map<String,Object> o2) -> {
                Date start1 = (Date)o1.get("start");
                Date start2 = (Date)o2.get("start");
                Date end1 = (Date)o1.get("end");
                Date end2 = (Date)o2.get("end");
                if(start1==null && start2==null){
                    return 0;
                } else if(start1 == null && start2!=null){
                    return 1;
                } else if(start1!=null && start2==null){
                    return -1;
                }
                if(start1.getTime() < start2.getTime()){
                    return -1;
                } else if (start1.getTime()==start2.getTime()){
                    if(end1==null && end2==null){
                        return 0;
                    } else if(end1 == null && end2!=null){
                        return 1;
                    } else if(end1!=null && end2==null){
                        return -1;
                    } else {
                        if(end1.getTime()<end2.getTime()){
                            return -1;
                        } else if (end1.getTime()==end2.getTime()){
                            return 0;
                        } else {
                            return 1;
                        }
                    }
                } else {
                    return 1;
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            ret.put("workflowId",workflowId);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_cancer_script.jsp", ret);
    }

    public Forward script2(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        String id = params.getString("id");
        ret.put("id",id);
        List<Object> sqlParams = new ArrayList<>();
        String sql = "select workflow_id from v_shca_project_sample where id = ?  ";
        sqlParams.add(id);
        String workflowId  =  db.selectUniqueString(sql,sqlParams.toArray(new Object[0]));
        ret.put("workflowId",workflowId);
        if (params.isJson()){
            OrderPage page = params.getPage();
            MetadataResponse metadataResponse = client.send( new MetadataRequest( workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            List<Map<String,Object>> list = new ArrayList<>();
            for (String key : map.keySet()) {
                List<Call> tList = map.get(key);
                for (Call call : tList){
                    Map<String,Object> tempMap = new HashMap<>();
                    String rid = DigestUtils.md5DigestAsHex(key.getBytes());
                    tempMap.put("id",rid);
                    tempMap.put("parentId",null);
                    tempMap.put("workflow_id",workflowId);
                    tempMap.put("call_name",key);
                    tempMap.put("job_id",call.getJobId());
                    tempMap.put("commandline",call.getCommandLine());
                    tempMap.put("backend",call.getBackend());
                    tempMap.put("return_code",call.getReturnCode());
                    tempMap.put("execution_status",call.getExecutionStatus());
                    tempMap.put("backend_status",call.getBackendStatus());
                    tempMap.put("stdout",call.getStdout());
                    tempMap.put("stderr",call.getStderr());
                    tempMap.put("start",call.getStart());
                    tempMap.put("end",call.getEnd());
                    tempMap.put("seq_no",(list.size()+1));
                    Date start = (Date)tempMap.get("start");
                    Date end = (Date)tempMap.get("end");
                    long runtime = 0;
                    if(start!=null && end !=null){
                        runtime = end.getTime()-start.getTime();
                    } else if(start!=null && end == null){
                        runtime = new Date().getTime() - start.getTime();
                    } else  {

                    }
                    tempMap.put("runtime",runtime);

                    list.add(tempMap);
                    List<Map<String,Object>> childList = call.getExecutionEvents();
                    if(childList!=null && !childList.isEmpty()){
                        for (Map<String,Object> temp : childList){
                            String rid_c = DigestUtils.md5DigestAsHex(temp.get("description").toString().getBytes());
                            // String rid_c = StringUtils.md5((String)temp.get("description"));
                            Map<String,Object> tempNew = new HashMap<>();
                            tempNew.put("call_name",temp.get("description"));
                            tempNew.put("start",temp.get("startTime"));
                            tempNew.put("end",temp.get("endTime"));
                            tempNew.put("execution_status",call.getExecutionStatus());

                            tempMap.put("id",rid_c);
                            tempMap.put("parentId",rid);

                            list.add(tempNew);
                        }
                    }

                }
            }

            Collections.sort(list, new Comparator<Map<String,Object>>(){
                @Override
                public int compare(Map<String,Object> o1, Map<String,Object> o2) {
                    Date start1 = (Date)o1.get("start");
                    Date start2 = (Date)o2.get("start");
                    Date end1 = (Date)o1.get("end");
                    Date end2 = (Date)o2.get("end");
                    if(start1==null && start2==null){
                        return 0;
                    } else if(start1 == null && start2!=null){
                        return 1;
                    } else if(start1!=null && start2==null){
                        return -1;
                    } else {
                        if(start1.getTime()<start2.getTime()){
                            return -1;
                        } else if (start1.getTime()==start2.getTime()){
                            if(end1==null && end2==null){
                                return 0;
                            } else if(end1 == null && end2!=null){
                                return 1;
                            } else if(end1!=null && end2==null){
                                return -1;
                            } else {
                                if(end1.getTime()<end2.getTime()){
                                    return -1;
                                } else if (end1.getTime()==end2.getTime()){
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        } else {
                            return 1;
                        }
                    }
                }
            });

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", list);
            return new JsonForward(ret);
        }

        return new JspForward( "/shca/project/project_cancer_script2.jsp", ret);
    }

    public Forward viewParam(ViewParams<?> params) {
        String id = params.getString("id");
        Map<String,Object>  sample = db.selectUnique("SELECT sample_name, " +
                "(select project_sn from shca_project_project where (sample_name_0 = c.sample_name or sample_name_1 = c.sample_name) and status != 'wait_sample' limit 0,1) as project_sn " +
                " FROM shca_project_cancer c where id = ?",id);

        String sample_name = (String)sample.get("sample_name");
        String project_sn = (String)sample.get("project_sn");
        //样本运行文件夹
        String directoryPath = config.getValue("workspace.sample_path")+File.separator+sample_name;
        if(StringUtils.hasLength(project_sn)){
            directoryPath = config.getValue("workspace.project_path")+File.separator+project_sn+File.separator+sample_name;
        }
        File d = new File(directoryPath);
        //File d = new File("C:\\Users\\<USER>\\Documents");
        String jsonStr = null;
        if(d.exists()){
            log.info("starting list dir: [" + d + "]");
            File[] files = d.listFiles((File file) -> {
                if(file.isDirectory()) {
                    return false;
                } else {
                    String name = file.getName();
                    return name.startsWith("generic") && name.endsWith(".json");
                }
            });
            log.info("finished list dir: [" + d + "]");

            if (files.length!=0) {
                Arrays.sort(files, (File f1, File f2) -> {
                    long diff = f2.getName().compareTo(f1.getName());
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;
                    }
                );
                for (File f : files) {
                    jsonStr = readFile(f);
                    break;
                }
            }
        }

        Map<String,Object> ret = new HashMap<>();
        if(jsonStr!=null) {
            String cromwell_workflow_inputs = JSON.toJSONString(JSON.parseObject(jsonStr), SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat);
            ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
        } else {
            ret.put("cromwell_workflow_inputs", "");
        }
        ret.put("sample_name",sample_name);
        ret.put("errmsg","未找到"+sample_name+"的参数文件");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewCommand(ViewParams<?> params) {
        String id = params.getString("id");
        String metaId = params.getString("metaId");
        Map<String,Object> ret = new HashMap<>();

        Map<String,Object>  metadata = db.selectUnique("SELECT c.sample_name , m.commandline" +
                " FROM shca_project_cancer c left join cromwell_workflow_metadata m on c.cromwell_workflow_id_0 = m.workflow_id  " +
                " where c.id = ? and m.id = ?",id,metaId);

        ret.put("sample_name",metadata.get("sample_name"));
        ret.put("cromwell_workflow_inputs",metadata.get("commandline"));
        ret.put("errmsg","");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward viewLog(ViewParams<?> params) {
        String filePath = params.getString("filePath");
        String sample_name = params.getString("sample_name",false,"");
        String cromwell_workflow_inputs  = readFile(new File(filePath));
        Map<String,Object> ret = new HashMap<>();
        ret.put("sample_name",sample_name);
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs",StringUtils.hasLength(cromwell_workflow_inputs)?cromwell_workflow_inputs : " ");
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    public Forward report(ViewParams<?> params) throws JsonProcessingException {
        Map<String, Object> ret = new HashMap<>();
        String id = params.getString("id");
        if (params.isJson()) {
            List<Map<String, Object>> outputList = db.select("SELECT c.sample_name , o.id , o.file_name,o.file_size,o.store_path" +
                    " FROM shca_project_cancer c left join shca_project_cancer_outputfile o on c.sample_name = o.sample_name  " +
                    " where c.id = ? ", id);

            ret.put("rows", outputList);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/project/project_cancer_report.jsp", ret);
    }

    public Forward reportFile(ViewParams<?> params) {
        Map<String, Object> ret = new HashMap<>();
        String id = params.getString("id");
        String storePath = db.selectUniqueString("select store_path from shca_project_cancer_outputfile where id = ?", id);
        String htmlContent = null ;

        if(storePath!=null){
            File reprotFile = new File(storePath);
            htmlContent = readFile(reprotFile);

        }
        ret.put("reportFile",htmlContent);

        return new JspForward( "/shca/project/project_cancer_reportFile.jsp", ret);
    }

    public Forward dashboard(ViewParams<?> params) {
        return new JspForward( "/shca/project/project_project_dashboard.jsp", null);
    }

    public Forward testnew(ViewParams<?> params) throws JsonProcessingException {
        String sample_name = params.getString("sample_name", false, "");
        return new JsonForward(sample_name);
    }

    public Forward chooseFile(ViewParams<?> params) throws JsonProcessingException{
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "创建失败";

        try{
            Integer id = params.getInt("id");
            String[] project_file = params.getStringArray("project_file[]");
            String ids = project_file[0]+"";
            for (int i=1;i<project_file.length;i++){
                ids += ",";
                ids += project_file[i];
            }

            db.update( "UPDATE shca_project_cancer set project_file = ? WHERE id = ? ",ids,id);
            errCode = 0;
        }catch (Exception e){
            log.error("error: ",e);
            errMsg+="："+e.getMessage();
        }
        ret.put("errCode",errCode);
        ret.put("errMsg",errMsg);
        return new JsonForward(ret);
    }

    public Forward test(ViewParams<?> params) throws JsonProcessingException{
        String workflowId = params.getString("workflow_id");
        MetadataResponse metadataResponse = client.send( new MetadataRequest( workflowId));
        Map<String,Object> ret = new HashMap<>();
        Map<String, List<Call>> map = metadataResponse.getCalls();
        List<Call> list = new ArrayList<>();
        for (String key : map.keySet()) {
            List<Call> tList = map.get(key);
            list.addAll(tList);
        }
        ret.put("metadataResponse",list);

        return new JsonForward(ret);
    }

    private  String readFile(File jsonFile) {
        StringBuilder jsonStr = new StringBuilder("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            try (BufferedReader br = new BufferedReader(isr)) {
                String lineTxt;
                while ((lineTxt = br.readLine()) != null) {
                    jsonStr.append(lineTxt).append("\n");
                }
            }
        } catch (IOException e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }

    public Forward stop(ViewParams<?> params) throws JsonProcessingException{
        String workflowId = params.getString("workflow_id");
        AbortResponse<?,?> response = client.send( new AbortRequest( workflowId));
        Map<String,Object> ret = new HashMap<>();
        ret.put("status",response.getStatus());
        return new JsonForward(ret);
    }

    public Forward restart(ViewParams<?> params) throws JsonProcessingException{
        Map<String,Object> ret = new HashMap<>();
        ret.put("status", 1000);
        ret.put("errmsg", "样本异常，无法重新分析");
        try {
            long id = params.getId();
            Map<String, Object> cancer = db.selectUnique("select sample_name,cancer_kind " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR1 " +
                    " ,(select store_path from shca_project_cancer_file b where b.sample_name=a.sample_name and rawdata=? and b.status=?) rawDataR2" +
                    " from shca_project_cancer a where id =?", "R1", "successed", "R2", "successed",  id);
            if (cancer == null) {
                ret.put("errcode", 2000);
                ret.put("errmsg", "样本异常，无法重新分析");
                return new JsonForward(ret);
            }
            String cancerKind = (String) cancer.get("cancer_kind");
            String sampleName = (String) cancer.get("sample_name");
            ret.put("errcode", 3000);
            ret.put("errmsg", "样本配置异常");
            Map<String, Object> cromwellConfig = db.selectUnique("select cancer_script,cromwell_workflow_source,cromwell_workflow_dependencies,cromwell_workflow_inputs " +
                    " from shca_cromwell_config where cancer_kind=? and auto_script=? and script_step=? order by id desc", cancerKind, true, 0);
            Date now = new Date();
            ProcessService processService = processServiceMap.get(cancerKind);
            if (cromwellConfig != null && processService != null) {
                log.info("generic-process[" + sampleName + "]: start workflow...");
                File rawDataR1 = new File((String) cancer.get("rawDataR1"));
                File rawDataR2 = new File((String) cancer.get("rawDataR2"));
                File source = new File((String) cromwellConfig.get("cromwell_workflow_source"));
                File options = new File(source.getParentFile().getAbsolutePath() + "/workflow_options.json");
                if (!options.exists()) {
                    options = new File(config.getValue("cromwell.config_path") + "/workflow_options.json");
                }
                if (!options.exists()) {
                    options = null;
                }
                ret.put("errcode", 4000);
                ret.put("errmsg", "样本分析程序异常");
                WorkflowsResponse response = processService.generic(
                        sampleName, rawDataR1, rawDataR2, (String) cromwellConfig.get("cromwell_workflow_inputs")
                        , source, new File((String) cromwellConfig.get("cromwell_workflow_dependencies")), options);

                cancer.put("cancer_script_0", cromwellConfig.get("cancer_script"));
                cancer.put("cromwell_workflow_id_0", response.getId());
                cancer.put("cromwell_workflow_status_0", response.getStatus());

                ret.put("errcode", 5000);
                ret.put("errmsg", "样本更新异常");
                db.update("update shca_project_cancer set status=?,cromwell_workflow_id_0=?,cromwell_workflow_status_0=?,cromwell_sub_workflow_id_0 = null " +
                                " ,cancer_script_0=?,update_username=?,update_time=? where id=? "
                        , "running_step0", response.getId(), response.getStatus(), cromwellConfig.get("cancer_script"), params.getUserName()
                        , now, id);

                log.info("generic-process[" +id+":"+sampleName + "] restart： " + JSON.toJSONString(response));
                ret.put("errcode",0);

            } else {
                ret.put("errcode", 6000);
                ret.put("errmsg", "样本异常，无法重新分析");
                return new JsonForward(ret);
            }
        }catch(JsonProcessingException e){
            log.error("error: ",e);
        }
        return new JsonForward(ret);
    }

    private String getSubWorkflowId(String workflowId){
        String subWorkflowId = null;
        try {
            MetadataResponse metadataResponse = client.send(new MetadataRequest(workflowId));
            Map<String, List<Call>> map = metadataResponse.getCalls();
            for (String key : map.keySet()) {
                List<Call> tList = map.get(key);
                for (Call call : tList) {
                    if (call.getSubWorkflowId() == null) {
                        subWorkflowId = workflowId;
                    } else {
                        subWorkflowId = getSubWorkflowId(call.getSubWorkflowId());
                    }
                }
            }
        }catch(Exception e){
            log.error("getSubWorkflowId error", e);
        }
        return subWorkflowId;
    }

    public Forward deleteSample(@RequestParam("sample_name") String sampleName) throws IOException {
        if (!StringUtils.hasLength(sampleName)) {
            return new JsonForward("error: sample name is empty");
        }
        Pattern pattern = Pattern.compile("[A-Z]{2}\\d{7}[A-Z]\\d{2}");
        if (!pattern.matcher(sampleName).matches()) {
            return new JsonForward(pattern + " 格式不正确");
        }
        String projectName = sampleName.trim().substring(0, sampleName.length()-3);
        List<Map<String, Object>> projects = db.select(
            "SELECT id FROM shca_project_project "
                + " WHERE project_name = ? AND (sample_name_0 = ? or sample_name_1 = ?) AND status NOT IN ('deleted', 'cleared') "
            , projectName, sampleName, sampleName);
        if (!projects.isEmpty()) {
            return new JsonForward("error: 该样本已被项目使用，无法删除");
        }
        List<Map<String, Object>> samples = db.select(
            "SELECT id, sample_name, status from shca_project_cancer"
                + "WHERE sample_name = ? "
                + "AND status IN ('finished','filed','Failed','running_step0','wait_running','Aborted' )"
            , sampleName);

        if (samples.isEmpty()) {
            return new JsonForward("error: no valid samples found" + sampleName);
        }
        if (samples.size() > 1) {
            return new JsonForward("error: too many valid samples found" + sampleName);
        }
        String status = (String) samples.get(0).get("status");
        if ("filed".equals(status)) {
            return new JsonForward("error: sample is filed, and can not be deleted");
        }
        if ("running_step0".equals(status)) {
            return new JsonForward("error: sample is running, and can not be deleted");
        }
        if ("wait_running".equals(status) || "Aborted".equals(status) || "Failed".equals(status) || "finished".equals(status)) {
            db.update("update shca_project_cancer set status = 'cleared' where id = ? ", samples.get(0).get("id"));
            // return new JsonForward("ok");
        }
        try {
            deleteFastqFileDbInfo(sampleName);
            deleteFastqFiles(sampleName);
            deleteSampleDbInfo(sampleName);
            deleteSampleAnalysisFiles(sampleName);
            return new JsonForward("delete succeeded");
        } catch (JsonProcessingException e) {
            log.error("deleteSample error", e);
            return  new JsonForward("delete error");
        }
        // return new JsonForward("delete sample: " + sampleName + "succeeded");
    }

    private void deleteSampleDbInfo(String sampleName) {
        try {
            Map<String, Object> sample = db.selectUnique(
                "SELECT id, status from shca_project_cancer "
                    + " WHERE sample_name = ? AND status IN ('finished', 'Failed','wait_running','Aborted' )"
                    , sampleName);
            db.delete("delete from shca_project_cancer where id = ? ", (long)sample.get("id"));
        } catch (Exception e) {
            log.error("deleteSampleDbInfo error", e);
        }
    }
    private void deleteFastqFileDbInfo(String sampleName) {
        try {
            db.delete("DELETE from shca_project_cancer_file WHERE sample_name = ? and status = 'Successed'", sampleName);
        } catch (Exception e) {
            log.error("deleteSampleFileDbInfo error", e);
        }
    }

    private void deleteFastqFiles(String sampleName) {
        Path fastqPath = Paths.get("/mydata/shca/rawdata/" + sampleName);
        Path trashbinPath = Paths.get("/mydata/trashbin");
        if (!Files.exists(fastqPath)) {
            return;
        }
        if (!Files.exists(trashbinPath)) {
            try {
                Files.createDirectory(trashbinPath);
            } catch (IOException e) {
                log.error("deleteFastqFiles error", e);
                return;
            }
        }
        try {
            Files.move(fastqPath, trashbinPath);
        } catch (IOException e) {
            log.error("move files to trashbin error", e);
        }
    }

    private Boolean deleteSampleAnalysisFiles(String sampleName) {
        Path samplePath = Paths.get("/mydata/shca/samples/" + sampleName);
        Path trashbinPath = Paths.get("/mydata/trashbin");
        if (!Files.exists(samplePath)) {
            return false;
        }
        if (!Files.exists(trashbinPath)) {
            try {
                Files.createDirectory(trashbinPath);
            } catch (IOException e) {
                log.error("deleteSampleAnalysisFiles error", e);
                return false;
            }
        }
        try {
            Files.move(samplePath, trashbinPath);
        } catch (IOException e) {
            log.error("move files to trashbin error", e);
            return false;
        }
        return true;
    }
}
