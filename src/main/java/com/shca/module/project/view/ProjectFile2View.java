package com.shca.module.project.view;

import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class ProjectFile2View {

    private static final Logger LOGGER = Logger.getLogger( ProjectFile2View.class);

    public Forward listSpace(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        File dir = new File(params.getString("path",false,"/CloudSvr01/"));
        if(!dir.isDirectory()){
            ret.put("errcode",1000);
            ret.put("errmsg","不是目录");
            return new JsonForward(ret);
        }
        if(!dir.exists()){
            ret.put("errcode",2000);
            ret.put("errmsg","目录不存在");
            return new JsonForward(ret);
        }
        long totalSpace = dir.getTotalSpace();
        long freeSpace = dir.getFreeSpace();
        long usedSpace = totalSpace - freeSpace;

        ret.put("errcode",0);
        ret.put("totalSpace",totalSpace);
        ret.put("freeSpace",freeSpace);
        ret.put("usedSpace",usedSpace);
        return new JsonForward(ret);
    }



}
