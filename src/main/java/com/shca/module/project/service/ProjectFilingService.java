package com.shca.module.project.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ywang.reflect.MethodParam;
import com.ywang.sql.Page;
import com.ywang.sql.support.Db;

public class ProjectFilingService {

    // private final static Logger LOGGER = Logger.getLogger(ProjectFilingService.class);
    private final static Logger LOGGER = LoggerFactory.getLogger(ProjectFilingService.class);

    @Resource
    private Db db;
    private List<Map<String, Object>> allReadyProjects;

    public void setDb(Db db) {
        this.db = db;
    }

    public void setAllReadyProjects(Date date) {
        this.allReadyProjects = _getAllReadyProjectsByDate(date);
    }

    /**
     * @param projectNo
     * @return
     */

    public void task() {
        // SysConfigManager config = SysConfigManager.getInstance();
        // File filingDir = new File(config.getValue("workspace.filing_path"));
//        __deleteEmptyDir( filingDir);
        // Map<String, Object> ret = new HashMap<>();

        LOGGER.info("Entering task method." );
        System.out.print("Entering task method. [System.out.print]");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -100); // -100天策略需要调整
        Date holdDate = calendar.getTime();
        setAllReadyProjects(holdDate);

        // List<Map<String, Object>> projects = db.select("SELECT project_name FROM shca_project_project " +
        //                 " WHERE lims is not null and is_filing=? and create_time >=? and create_time <=? and status=? " +
        //                 " and project_name not in (SELECT project_name FROM shca_project_project WHERE is_filing != ?)" +
        //                 " order by id "
        //         , new Page( 10), '8', ConvertUtils.str2Date("2021-02-01"), holdDate, "finished", '0');

        // 获取所有需归档项目列表，包含重复项目
        LOGGER.debug("Getting allReadyProjects. The first 2 projects is :" );
        String allReadyProjectsSql = "SELECT project_name FROM shca_project_project WHERE status=? and is_filing=? order by id";
        List<Map<String, Object>> allReadyProjects = db.select(allReadyProjectsSql, "finished", '8');
        LOGGER.debug("[1]" + allReadyProjects.get(0).get("project_name"));
        LOGGER.debug("[2]" + allReadyProjects.get(0).get("project_name"));

        List<String> uniqReadyProjectsName = allReadyProjects.stream()
            .filter(e-> e != null)
            .collect(Collectors.toMap(k->(String) k.get("project_name"), v-> 1, (n1,n2)-> n1+n2))
            .entrySet()
            .stream()
            .filter(e->e.getValue()==1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        List<String> dupReadyProjectsName = allReadyProjects.stream()
            .filter(e-> e != null)
            .collect(Collectors.toMap(k->(String) k.get("project_name"), v-> 1, (n1,n2)-> n1+n2))
            .entrySet()
            .stream()
            .filter(e->e.getValue()>1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        if(!uniqReadyProjectsName.isEmpty()) {
            uniqReadyProjectsName.stream().forEach(proj -> {
                try {
                    filing(proj);
                } catch (IOException e1) {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                }
            });
        }

        if(!dupReadyProjectsName.isEmpty()) {
            dupReadyProjectsName.stream().forEach(proj -> dupFiling(proj));
        }
    }

    public void deleteEmptyDir() {
        _deleteEmptyDir( new File( ""));
    }

    private void _deleteEmptyDir(File dir) {
        if (dir == null || dir.isFile()) {
            return;
        }
        try {
            for (File file : dir.listFiles()) {
                _deleteEmptyDir( file);
            }
            if (dir.listFiles().length == 0) {
                dir.delete();
            } else {
                LOGGER.error("Dir is not deleted because of it is not empety.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param projectName
     * @return
     */
    // public Map<String, Object> filing(@MethodParam(name = "project_name") String projectName) {

    List<Map<String, Object>> getProjectMapListByName(@MethodParam(name = "project_name") String projectName) {
        if(projectName == null) {
            return null;
        } else {
            return allReadyProjects.stream()
            .filter(e->e.get("project_name").equals(projectName))
            .collect(Collectors.toList());
        }
    }

    private void filing(String projectName) throws IOException {
        //1. 获取projectName对应的List，并获取其中的Map<String, Object>对象
        //2. 根据projectName创建相应的目录，日志文件等
        //3. 获取fastq文件路径，移动fastq文件，修改数据库
        //4. 发送cromwell任务，完成后修改数据库；

        LOGGER.info("Entering filing method.");
        Map<String, Object> project = null;
        List<Map<String, Object>> projectMapList = getProjectMapListByName(projectName);
        if(projectMapList.isEmpty()) {
            LOGGER.error("cannot filing project [" + "projectName" + "], project was already filed or not exist.");
            return;
        } else {
            project = projectMapList.get(0);
        }

        File projectSourceDir = ProjectFilingSupport.projectSourceDir(project);
        if (!projectSourceDir.exists())  {
            LOGGER.error("filing: 项目[" + projectName + "]文件夹不存在-> " + projectSourceDir.getAbsolutePath());
            return;
        }

        if (isFilingFinished(project)) {
            updateFilingInfo(project);
            return;
        }

        LOGGER.debug("sending cromwell workflow: [" + project.get("project_name") + "]");
        // ProjectFilingSupport.sendCromwellWorkflow(project);
        // moveProjectFastqs(project);
    }

    private void dupFiling(String projectName) {
        //todos

    }

    private boolean isFilingFinished(Map<String, Object> project) {
        File logDir = ProjectFilingSupport.getProjectLogDir(project);
        File workflowFile = new File( logDir.getAbsolutePath() + "/filing_workflow.txt" );
        if (workflowFile.exists() && workflowFile.length() > 0) {
            return true;
        }
        return false;
    }

    private void updateFilingInfo(Map<String, Object> project) {
        File logDir = ProjectFilingSupport.getProjectLogDir(project);
        File workflowFile = new File( logDir.getAbsolutePath() + "/filing_workflow.txt" );

        try (BufferedReader reader = new BufferedReader(new FileReader(workflowFile))) {
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private List<Map<String, Object>> _getAllReadyProjectsByDate(Date startDate) {
        if(this.allReadyProjects == null) {
            String allReadyProjectsSql = "SELECT project_name FROM shca_project_project WHERE status=? AND is_filing=? AND create_time >=? order by id";
            List<Map<String, Object>> result = db.select(allReadyProjectsSql, new Page( 10),"finished", '8', startDate);
            this.allReadyProjects = result;
            System.out.print("result: " + result.get(0));
            return result;
        } else {
            return this.allReadyProjects;
        }
    }
    public void clearSampleName() {
        List<Map<String,Object>> samples = db.select( "select id,sample_name from shca_project_cancer");
        Date now = new Date();
        for (Map<String,Object> sample : samples) {
            File dir = new File( "/mydata/shca/samples/" + sample.get( "sample_name"));
            System.out.println( dir.getAbsolutePath() + " = " + dir.exists());
            if (!dir.exists()) {
                db.update( "update shca_project_cancer set delete_time=? where id=? ", now, sample.get( "id"));
            }
        }
    }

    // private void cmd(String cmd, BufferedWriter bufferedWriter) throws Exception {
    //     String result = Cmd.exec( cmd);
    //     bufferedWriter.write(cmd + ": " + result + "\n");
    // }
}
