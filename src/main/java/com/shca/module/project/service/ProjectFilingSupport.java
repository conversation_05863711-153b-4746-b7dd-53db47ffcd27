package com.shca.module.project.service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.utils.ConvertUtils;

public class ProjectFilingSupport {
    private static SysConfigManager config;
    private static CromwellClient client;

    public ProjectFilingSupport() {
    }

    public void setConfig(SysConfigManager config) {
        ProjectFilingSupport.config = config;
    }

    public void setClient(CromwellClient client) {
        ProjectFilingSupport.client = client;
    }

    static String getDefaultHomePath() {
        // return "/mydata/shca";
        return config.getValue("workspace.project_path");
    }

    static String getFilingPath() {
        return config.getValue("workspace.filing_path");
    }

    static String getFileTransferWDL() {
        return "/myTools/geneAnalysisPipeline/fileBackup-1008.wdl";
    }

    static File getFileTransferWDLFile() {
        return new File(getFileTransferWDL());
    }

    static File projectSourceDir(Map<String, Object> project) {
        return new File(config.getValue("workspace.project_path") + "/" + project.get("project_name"));
    }

    static String getProjectFilingPathPrefix(Map<String, Object> project) {
        String prefixOfPath = ConvertUtils.date2Str( (Date) project.get("create_time"), "yyyyMM");
        return prefixOfPath + "/" + project.get("projectName");
    }

    static String getProjectFilingPath(Map<String, Object> project) {
        return getFilingPath() + getProjectFilingPathPrefix(project);
    }

    static String getProjectLogPath(Map<String, Object> project) {
        return getFilingPath() + "/filing_log/" + getProjectFilingPathPrefix(project);
    }

    static String getProjectBackupPath(Map<String, Object> project) {
        return getFilingPath() + "/backup/" + getProjectFilingPathPrefix(project);
    }

    static File getProjectFilingDir(Map<String, Object> project) {
        return new File(getProjectFilingPath(project));
    }

    static File getProjectLogDir(Map<String, Object> project) {
        return new File(getProjectLogPath(project));
    }

    static File getProjectBackupDir(Map<String, Object> project) {
        return new File(getProjectBackupPath(project));
    }

    static File getProjectLogFile(Map<String, Object> project) {
        return new File( getProjectLogPath(project) + "/filing_log.txt" );
    }

    static File getProjectWorkFlowFile(Map<String, Object> project) {
        return new File( getProjectBackupPath(project) + "/filing_workflow.txt");
    }

    static String getProjectCategory(Map<String, Object> project) {
        String cancerKind = (String) project.get("cancer_kind");
        String result = cancerKind;
        if ("breast_cancer".equals(cancerKind)) {
            result = "breast";
        }
        if ("colorectal_cancer".equals(cancerKind)) {
            result = "colon";
        }
        if (cancerKind.startsWith("peixi") || cancerKind.endsWith("peixi")) {
            result = "peixi";
        }
        return result;
    }

    static JSONObject jsonObjectComposer(Map<String, Object> project) {
        JSONObject jsonObject = new JSONObject();
        String tumorSampleName = "peixi".equals(getProjectCategory(project))? "" : (String) project.get("sample_name_1");
        jsonObject.put("fileBackup.ACTION_WF", "backup");
        jsonObject.put("fileBackup.cancerName_WF", getProjectCategory(project));
        jsonObject.put("fileBackup.projectHomePath_WF", getDefaultHomePath());
        jsonObject.put("fileBackup.projectName_WF", project.get("project_name"));
        jsonObject.put("fileBackup.normalSampeName_WF", project.get("sample_name_0"));
        jsonObject.put("fileBackup.targetPath_WF", getProjectFilingPath(project));
        jsonObject.put("fileBackup.tmpPath_WF", getProjectBackupPath(project));
        jsonObject.put("fileBackup.tumorSampleName_WF", tumorSampleName);

        return jsonObject;
    }

    static File getCromwellJson(Map<String, Object> project) {
        return new File(getProjectLogPath(project) + "/fileBackup_" + getProjectFilingPathPrefix(project) + ".json");
    }

    static BufferedWriter getCromwellJsonWriter(Map<String, Object> project) {
        try {
            return new BufferedWriter(new FileWriter(getCromwellJson(project)));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    static void sendCromwellWorkflow(Map<String, Object> project) throws IOException {
        getProjectFilingDir(project).mkdirs();
        getProjectLogDir(project).mkdirs();
        getProjectBackupDir(project).mkdirs();

        File projectWorkflowFile = getProjectWorkFlowFile(project);
        File projectLogFile = getProjectLogFile(project);
        BufferedWriter logBufferedWriter = new BufferedWriter(new FileWriter(projectLogFile, true));
        File workflowSource = getFileTransferWDLFile();
        JSONObject jsonObject = jsonObjectComposer(project);

        BufferedWriter cromwellJsonWriter = getCromwellJsonWriter(project);
        if (cromwellJsonWriter != null) {
            cromwellJsonWriter.write(
                JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat)
                .replaceAll("\t", "    ")
            );
            cromwellJsonWriter.close();
        } else {
            logBufferedWriter.write("create cromwell json file failed... project: " + project.get("project_name"));
            logBufferedWriter.close();
            return;
        }

        WorkflowsResponse response = client.send(new WorkflowsRequest(workflowSource, ProjectFilingSupport.getCromwellJson(project), null, null));
    logBufferedWriter.write("Workflow ID: " + response.getId() + "\n");
    BufferedWriter workflowBufferWriter = new BufferedWriter(new FileWriter(projectWorkflowFile));
    workflowBufferWriter.write(response.getId());
    workflowBufferWriter.close();
    logBufferedWriter.close();
}

}
