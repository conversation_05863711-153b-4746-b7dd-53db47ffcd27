package com.shca.module.cancer.service.support;

import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.vo.QcSummary;
import com.ywang.utils.ConvertUtils;
import com.ywang.utils.FileUtils;
import com.ywang.utils.LogUtils;
import com.ywang.utils.StringUtils;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class QcSummaryByBreastCancer implements QcSummaryService {

    private Map<String,String> references = new HashMap<>();

    public void setReferences(Map<String, String> references) {
        this.references = references;
    }

    private final static Logger LOGGER = Logger.getLogger( QcSummaryService.class);

    @Override
    public List<QcSummary> qcSummary(File file) {
        LOGGER.info( "qc_summary file [" + file.getAbsolutePath() + "] parsing ... ...");
        BufferedReader reader = null;
        List<QcSummary> qcSummaries = new ArrayList<>();
        try {
            reader = new BufferedReader( new InputStreamReader( new FileInputStream( file), "UTF-8"));
            String line = null;
            while ((line = reader.readLine()) != null) {
                if (!line.contains( ":") && !line.isEmpty()) {
                    LOGGER.error( "QcSummary error line: " + line);
                    continue;
                }
                QcSummary qcSummary = new QcSummary();
                // get name and value of qc_summary file
                String[] array = line.split( ":");
                String qcName = StringUtils.trim( array[0]);
                String qcValue = StringUtils.trim( array[1]);
                qcSummary.setName( qcName);
                qcSummary.setValue( qcValue);
                Double val = ConvertUtils.dou( qcValue, null);
                String reference = references.get( qcName);
                if (reference != null) {
                    qcSummary.setReference( ">=" + reference);
                    if (val != null) {
                        qcSummary.setQualified( val > Double.parseDouble(reference));
                    }
                } else {
                    String betweenRef = references.get(qcName + "-Between");
                    String minuxRef = references.get(qcName + "-Minus");
                    if (betweenRef != null) {
                        Double valueMin = Double.parseDouble(betweenRef.split(":")[0]);
                        Double valueMax = Double.parseDouble(betweenRef.split(":")[1]);
                        qcSummary.setReference("[" + betweenRef + "]");
                        if (val != null) {
                            qcSummary.setQualified( val >= valueMin && val <= valueMax);
                        }
                    }
                    if (minuxRef != null) {
                        qcSummary.setReference( "<=" + minuxRef);
                        if (val != null) {
                            qcSummary.setQualified( val <= Double.parseDouble(minuxRef));
                        }
                    }
                }
                qcSummaries.add( qcSummary);
            }

            LOGGER.info( "qc_summary file [" + file.getAbsolutePath() + "] parsed, line count is " + qcSummaries.size());

            return qcSummaries;
        } catch(Exception e) {
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        } finally {
            FileUtils.close( reader);
        }
    }


    public void backup(File file) {


    }
}
