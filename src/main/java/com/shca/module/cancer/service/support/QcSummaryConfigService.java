/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-15 17:37:48
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 21:39:30
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/cancer/service/support/QcSummaryConfigService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.cancer.service.support;

import java.util.HashMap;
import java.util.Map;

// import com.shca.CanerTypeEnum;

public final class QcSummaryConfigService {
    private static final Map<String, String> QC_SUMMARY_CONFIG = new HashMap<>();
    private static final Map<String, String> RNA_QC_SUMMARY_CONFIG = new HashMap<>();
    private static final Map<String, String> BRCA_QC_SUMMARY_CONFIG = new HashMap<>();

    static {
        QC_SUMMARY_CONFIG.put("Q20_R1", "90");
        QC_SUMMARY_CONFIG.put("Q20_R2", "90");
        QC_SUMMARY_CONFIG.put("Q30_R1", "80");
        QC_SUMMARY_CONFIG.put("Q30_R2", "80");
        QC_SUMMARY_CONFIG.put("Mapping_rate(%)", "90");
        QC_SUMMARY_CONFIG.put("Coverage_of_target_region(%)", "90");
        QC_SUMMARY_CONFIG.put("Average_sequencing_depth_on_target", "90");
        QC_SUMMARY_CONFIG.put("Fraction_of_target_covered_with_at_least_10x(%)", "90");
        QC_SUMMARY_CONFIG.put("Median_insert_size(bp)", "80");

        RNA_QC_SUMMARY_CONFIG.put("Raw_GC(%)-Between", "35,65");
        RNA_QC_SUMMARY_CONFIG.put("Clean_Reads(M)", "20");
        RNA_QC_SUMMARY_CONFIG.put("Clean_Bases(G)", "6");
        RNA_QC_SUMMARY_CONFIG.put("Clean_Bases_Prop(%)", "70");
        RNA_QC_SUMMARY_CONFIG.put("Duplication_Rate(%)-Minus", "50");
        RNA_QC_SUMMARY_CONFIG.put("Insert_Size_Peak(%)", "100");
        RNA_QC_SUMMARY_CONFIG.put("Mapping_rate(%)", "70");
        RNA_QC_SUMMARY_CONFIG.put("rRNA(%)-Minus", "20");

        BRCA_QC_SUMMARY_CONFIG.put("平均测序覆盖深度", "100");
    }
    // public Map<String, String> getQcSummaryConfig(Pipeline pipeline) {
    //     return pipeline.getQcParams();
    // }
    public static final Map<String, String> getQcSummaryConfig() {
        return QC_SUMMARY_CONFIG;
    }
    public static final Map<String, String> getRnaQcSummaryConfig() {
        return RNA_QC_SUMMARY_CONFIG;
    }

    public static final  Map<String, String> getBrcaQcSummaryConfig() {
        return BRCA_QC_SUMMARY_CONFIG;
    }
}
