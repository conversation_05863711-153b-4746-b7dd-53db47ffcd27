package com.shca.module.cancer.service;


import com.shca.cromwell.api.WorkflowsResponse;

import java.io.File;

public interface ProcessService {

    public WorkflowsResponse generic(String sampleName, File rawDataR1, File rawDataR2,String workflowInputs
        ,File workflowSource, File workflowDependencies, File workflowOptions);

    public WorkflowsResponse mutual(String projectSN, String projectName, String sampleName0, String sampleName1, String workflowInputs
        ,File workflowSource, File workflowDependencies, File workflowOptions);

    public WorkflowsResponse summary(String projectSN, String projectName,String sampleName, File rawDataR1, File rawDataR2, String workflowInputs
        ,File workflowSource, File workflowDependencies, File workflowOptions);

    public WorkflowsResponse upload(String projectSN, String projectName,String sampleName0, String sampleName1, String workflowInputs
        ,File workflowSource, File workflowDependencies, File workflowOptions);

}
