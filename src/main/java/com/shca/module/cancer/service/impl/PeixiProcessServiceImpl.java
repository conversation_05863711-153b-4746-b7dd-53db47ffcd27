package com.shca.module.cancer.service.impl;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.shca.util.JsonUtils;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.config.SystemConfigService;
import com.shca.util.ConvertUtils;
import com.shca.util.LogUtils;

@Service
public class PeixiProcessServiceImpl implements ProcessService {

    @Resource private CromwellClient client;
    @Resource private SystemConfigService config;

    private static final Logger LOGGER = Logger.getLogger( PeixiProcessServiceImpl.class);

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    @Override
    public WorkflowsResponse generic(String sampleName, File rawDataR1, File rawDataR2,String workflowInputs
            , File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running generic-process: " + sampleName);
            if (StringUtils.hasLength( sampleName)) {
                LOGGER.error( "error generic-process: " + sampleName + " cannot be null or empty");
                throw new IllegalArgumentException( "error generic-process: " + sampleName + " cannot be null or empty");
            }
            if (rawDataR1 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
            }
            if (!rawDataR1.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
            }

            if (rawDataR2 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
            }
            if (!rawDataR2.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
            }

            // Using injected config
            String homePath = config.getValue( "workspace.sample_path");
            File sampleDir = new File( homePath + "/" + sampleName);

            deleteFile( sampleDir);
            sampleDir.mkdirs();


            // File json = new File( homePath + "/" + sampleName + "/generic_" + sampleName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + ".json");
            File prettyJson = new File( homePath + "/" + sampleName + "/generic_" + sampleName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            jsonObject.put( "generalProcess.sampleName_WF", sampleName);
            jsonObject.put( "generalProcess.rawDataR1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put( "generalProcess.rawDataR2_WF", rawDataR2.getAbsolutePath());
            jsonObject.put( "generalProcess.homePath_WF", homePath);

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.info( "running generic-process[" + sampleName + "]: preSubmit workflow");
            WorkflowsResponse response = client.send( new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running generic-process[" + sampleName + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse mutual(String projectSN, String projectName, String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running mutual-process: " + projectSN);
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error mutual-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( projectName)) {
                LOGGER.error( "error mutual-process: " + projectName + " cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName0)) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error mutect-process[" + projectSN + "]: sampleName0 cannot be null or empty");
            }

            // Using injected config
            File homePath = new File( config.getValue("workspace.project_path") + "/" + projectName);

            deleteFile(new File(homePath.getAbsoluteFile() + "/" + sampleName0));
            deleteFile(new File(homePath.getAbsoluteFile()  + "/" + sampleName0 + "/tmp_vcf"));
            deleteFile( homePath);

            homePath.mkdirs();
            String normalName = sampleName0;

            File normalDir0 = new File(config.getValue("workspace.sample_path") + "/" + normalName);
            File normalDir1 = new File( homePath.getAbsolutePath() + "/" + normalName);
            if (normalDir0.exists()) {
                copyFiles( normalDir0, normalDir1);
            }

            File prettyJson = new File( homePath.getAbsolutePath() + "/mutual_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            jsonObject.put( "generalProcess.homePath_WF", homePath.getAbsolutePath());
            jsonObject.put( "generalProcess.projectName_WF", projectName);
            jsonObject.put( "generalProcess.sampleName_WF", normalName);
            jsonObject.put( "generalProcess.normalSampleName_WF", normalName);

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();


            LOGGER.info( "running mutual-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running mutual-process[" + projectSN + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse summary(String projectSN, String projectName,String sampleName, File rawDataR1, File rawDataR2, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running summary-process[" + projectSN + "]: " + sampleName);
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error summary-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error summary-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName)) {
                LOGGER.error( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
            }
            if (rawDataR1 == null || !rawDataR1.exists()) {
                LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
            }
            if (rawDataR2 == null || !rawDataR2.exists()) {
                LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
            }

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            // Using injected config
            String homePath = config.getValue("workspace.project_path") + "/" + projectName;

            jsonObject.put( "generalProcess.sampleName_WF", sampleName);
            jsonObject.put( "generalProcess.homePath_WF", homePath);
            jsonObject.put( "generalProcess.rawDataR1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put( "generalProcess.rawDataR2_WF", rawDataR2.getAbsolutePath());

            // File json = new File( homePath + "/" + sampleName + "/summary_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + ".json");
            File prettyJson = new File( homePath + "/" + sampleName + "/summary_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.info( "running summary-process[" + projectSN + "][" + sampleName + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running summary-process[" + projectSN + "][" + sampleName + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( "JSON: " + workflowInputs);
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        }
    }


    @Override
    public WorkflowsResponse upload(String projectSN, String projectName,String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running upload-process[" + projectSN + "]:... ");
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error upload-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName0)) {
                LOGGER.error( "error upload-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process[" + projectSN + "]: sampleName cannot be null or empty");
            }

            // Using injected config
            String homePath = config.getValue("workspace.project_path") + "/" + projectName;
            File filesForUpload = new File( homePath + "/filesForUpload");
            if (filesForUpload.exists()) {
                deleteFile( filesForUpload);
            }

            String normalName = sampleName0;
            File prettyJson = new File( homePath + "/upload_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");
            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            jsonObject.put( "generalProcess.projectName_WF", projectName);
            jsonObject.put( "generalProcess.homePath_WF", homePath);
            jsonObject.put( "generalProcess.sampleName_WF", normalName);
            jsonObject.put( "generalProcess.normalSampleName_WF", normalName);

            if (jsonObject.containsKey( "geneFileUpload.Bconc_WF")) {
                jsonObject.put( "generalProcess.Bconc_WF", jsonObject.getDouble( "geneFileUpload.Bconc_WF"));
                jsonObject.remove( "geneFileUpload.Bconc_WF");
            }
            if (jsonObject.containsKey( "geneFileUpload.Kconc_WF")) {
                jsonObject.put( "generalProcess.Kconc_WF", jsonObject.getDouble( "geneFileUpload.Kconc_WF"));
                jsonObject.remove( "geneFileUpload.Kconc_WF");
            }

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.error( "running upload-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.error( "running upload-process[" + projectSN + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        }
    }

    private void deleteFile(File dir) {
        if (dir == null || !dir.exists()) {
            return;
        }
        try{
            cmd( "rm -rf " + dir.getAbsolutePath());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void copyFiles(File source, File dest) {
        String cmd = "ln -snf " + source.getAbsolutePath() + " " + dest.getAbsolutePath();
        cmd( cmd);
//        FileUtils.copyFiles( source, dest);
    }

    private void cmd(String cmd) {
        Runtime run = Runtime.getRuntime();
        try {
            LOGGER.info( "cmd: " + cmd);
            String [] cmds = { "/bin/sh","-c", cmd};
            Process process = run.exec( cmds);
            String line;
            BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuffer out = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null ) {
                out.append(line);
            }
            try {
                process.waitFor();
            } catch (InterruptedException e) {
            }
            process.destroy();
            LOGGER.info( "cmd[" + cmd + "]: " + out);
        } catch (IOException e) {
            LOGGER.error( LogUtils.toString( e));
            throw new RuntimeException( e);
        }
    }
}
