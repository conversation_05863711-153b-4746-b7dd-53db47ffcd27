package com.shca.module.cancer.service.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.utils.ConvertUtils;
import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import javax.annotation.Resource;

public class ProcessServiceByColorectalCancer implements ProcessService {

    private static final Logger LOGGER = Logger.getLogger( ProcessServiceByColorectalCancer.class);
    @Resource private CromwellClient client;

    @Override
    public WorkflowsResponse generic(String sampleName, File rawDataR1, File rawDataR2,String workflowInputs
            , File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running generic-process: " + sampleName);
            if (StringUtils.hasLength( sampleName)) {
                LOGGER.error( "error generic-process: " + sampleName + " cannot be null or empty");
                throw new IllegalArgumentException( "error generic-process: " + sampleName + " cannot be null or empty");
            }
            if (rawDataR1 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
            }
            if (!rawDataR1.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
            }


            if (rawDataR2 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
            }
            if (!rawDataR2.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
            }

            SysConfigManager config = SysConfigManager.getInstance();
            String homePath = config.getValue( "workspace.sample_path");
            File sampleDir = new File( homePath + "/" + sampleName);

            // FileUtils.deleteFile( sampleDir);
            sampleDir.delete();
            sampleDir.mkdirs();
            File prettyJson = new File( homePath + "/" + sampleName + "/generic_" + sampleName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            jsonObject.put("geneGenericProcess.sampleName_WF", sampleName);
            jsonObject.put("geneGenericProcess.rawDataR1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put("geneGenericProcess.rawDataR2_WF", rawDataR2.getAbsolutePath());
            jsonObject.put("geneGenericProcess.homePath_WF", homePath);
            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\t", "    "));
            prettyWriter.close();

            LOGGER.info( "running generic-process[" + sampleName + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running generic-process[" + sampleName + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error("error generic-process[" + sampleName + "]: " , e);
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse mutual(String projectSN, String projectName, String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running mutual-process: " + projectSN);

            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error mutual-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName0)) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error mutect-process[" + projectSN + "]: sampleName0 cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName1)) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: sampleName1 cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: sampleName1 cannot be null or empty");
            }
            SysConfigManager config = SysConfigManager.getInstance();
            File homePath = new File( config.getValue("workspace.project_path") + "/" + projectName);

            if (homePath.exists()) {
                deleteFile(new File(homePath.getAbsoluteFile() + "/" + sampleName0));
                deleteFile(new File(homePath.getAbsoluteFile() + "/" + sampleName1));

                deleteFile(new File(config.getValue("workspace.sample_path") + "/" + sampleName0 + "/tmp_vcf"));
                deleteFile(new File(config.getValue("workspace.sample_path") + "/" + sampleName1 + "/tmp_vcf"));
                deleteFile(homePath);
            }

            homePath.mkdirs();

            String normalName = sampleName0;
            String tumorName = sampleName1;
            File normalDir0 = new File(config.getValue("workspace.sample_path") + "/" + normalName);
            File normalDir1 = new File( homePath.getAbsolutePath() + "/" + normalName);
            if (normalDir0.exists()) {
                copyFiles( normalDir0, normalDir1);
            }

            File tumorDir0 = new File(config.getValue("workspace.sample_path") + "/" + tumorName);
            File tumorDir1 = new File( homePath.getAbsolutePath() + "/" + tumorName);
            if (tumorDir0.exists()) {
                copyFiles( tumorDir0, tumorDir1);
            }

            JSONObject jsonObject = JSON.parseObject( workflowInputs);

            jsonObject.put("geneMutualDetect.tumor_normal_Names_WF", new String[]{ tumorName, normalName});
            jsonObject.put("geneMutualDetect.homePath_WF", homePath.getAbsolutePath());

            Path normalVariationPath = Paths.get(normalDir0.getAbsolutePath(), "gatk/variation");
            File[]normalBams = normalVariationPath.toFile().listFiles((dir, name) -> name.endsWith("_recal.bam"));
            Path tumorVariationPath = Paths.get(tumorDir0.getAbsolutePath(), "gatk/variation");
            File[]tumorBams = tumorVariationPath.toFile().listFiles((dir, name) -> name.endsWith("_recal.bam"));
            // File[] tumorBams = FileUtils.findFiles( new File( tumorDir0.getAbsolutePath() + "/gatk/variation"), "_recal.bam");
            if ( normalBams.length == 0 ) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: " + normalName + " not found bam -" + normalDir0.getAbsolutePath() + "/gatk/variation");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: " + normalName + " not found bam");
            }
            if (tumorBams.length == 0) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: " + tumorName + " not found bam - " + tumorDir0.getAbsolutePath() + "/gatk/variation");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: " + tumorName + " not found bam");
            }
            jsonObject.put("geneMutualDetect.tumor_normal_Bams_WF",
                    new String[]{ tumorBams[0].getAbsolutePath(), normalBams[0].getAbsolutePath()});


            File[] normalBais = normalVariationPath.toFile().listFiles((dir, name) -> name.endsWith("_recal.bai"));
            File[] tumorBais = tumorVariationPath.toFile().listFiles((dir, name) -> name.endsWith("_recal.bai"));
            if (normalBais.length == 0) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: " + normalName + " not found bai");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: " + normalName + " not found bai");
            }
            if (tumorBais.length == 0) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: " + tumorName + " not found bai");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: " + tumorName + " not found bai");
            }
            jsonObject.put("geneMutualDetect.tumor_normal_BamsIndexes_WF",
                    new String[]{ tumorBais[0].getAbsolutePath(), normalBais[0].getAbsolutePath()});

            File prettyJson = new File( homePath.getAbsolutePath() + "/mutual_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");
            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();
            LOGGER.info( "running mutual-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running mutual-process[" + projectSN + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( "JSON: " + workflowInputs, e);
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse summary(String projectSN, String projectName,String sampleName, File rawDataR1, File rawDataR2, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running summary-process[" + projectSN + "]: " + sampleName);
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error summary-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error summary-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName)) {
                LOGGER.error( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
            }
            if (rawDataR1 == null || !rawDataR1.exists()) {
                LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
            }
            if (rawDataR2 == null || !rawDataR2.exists()) {
                LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
                throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
            }

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            SysConfigManager config = SysConfigManager.getInstance();
            String homePath = config.getValue("workspace.project_path") + "/" + projectName;

            jsonObject.put( "geneQcSummary.sampleName_WF", sampleName);
            jsonObject.put( "geneQcSummary.homePath_WF", homePath);
            jsonObject.put( "geneQcSummary.rawData_R1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put( "geneQcSummary.rawData_R2_WF", rawDataR2.getAbsolutePath());

            String dateString = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
            File prettyJson = Paths.get(homePath, sampleName,  "/summary_", projectName, dateString, "_pretty.json").toFile();

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.error( "running summary-process[" + projectSN + "][" + sampleName + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.error( "running summary-process[" + projectSN + "][" + sampleName + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( "JSON: " + workflowInputs, e);
            throw new RuntimeException( e);
        }

    }


    @Override
    public WorkflowsResponse upload(String projectSN, String projectName,String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running upload-process[" + projectSN + "]:... ");
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error upload-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName0)) {
                LOGGER.error( "error upload-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process[" + projectSN + "]: sampleName cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName1)) {
                LOGGER.error( "error upload-process[" + projectSN + "]: sampleName1 cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process[" + projectSN + "]: sampleName cannot be null or empty");
            }

            JSONObject jsonObject = JSON.parseObject( workflowInputs);
            SysConfigManager config = SysConfigManager.getInstance();
            String homePath = config.getValue("workspace.project_path") + "/" + projectName;

            jsonObject.put( "geneFileUpload.projectName_WF", projectName);
            jsonObject.put( "geneFileUpload.homePath_WF", homePath);

            String normalName = sampleName0;
            String tumorName = sampleName1;
            if (normalName.endsWith( "K")) {
                normalName = sampleName1;
                tumorName = sampleName0;
            }

            jsonObject.put( "geneFileUpload.normalName_WF", normalName);
            jsonObject.put( "geneFileUpload.tumorName_WF", tumorName);
            String dateString = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
            File prettyJson = Paths.get(homePath, "/upload_", projectName, "_", dateString, "_pretty.json").toFile();
            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JSON.toJSONString( jsonObject, SerializerFeature.PrettyFormat).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.error( "running summary-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.error( "running summary-process[" + projectSN + "]'s workflow: " + JSON.toJSONString( response));

            return response;
        } catch (Exception e) {
            LOGGER.error( "JSON: " + workflowInputs, e);
            throw new RuntimeException( e);
        }

    }


    private void deleteFile(File dir) {
        cmd( "rm -rf " + dir.getAbsolutePath());
    }

    private void copyFiles(File source, File dest) {
        String cmd = "ln -snf " + source.getAbsolutePath() + " " + dest.getAbsolutePath();
        cmd( cmd);
//        FileUtils.copyFiles( source, dest);
    }


    private void cmd(String cmd) {
        Runtime run = Runtime.getRuntime();
        try {
            LOGGER.info( "cmd: " + cmd);
            String [] cmds = {"/bin/sh","-c", cmd};
            Process process = run.exec( cmds);
            String line;
            BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuffer out = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null ) {
                out.append(line);
            }
            try {
                process.waitFor();
            } catch (InterruptedException e) {
            }
            process.destroy();
            LOGGER.info( "cmd[" + cmd + "]: " + out);
        } catch (IOException e) {
            LOGGER.error("cmd: " + cmd, e);
            throw new RuntimeException( e);
        }
    }
}
