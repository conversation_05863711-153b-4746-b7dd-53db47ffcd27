package com.shca.module.cancer.service.impl;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.shca.util.JsonUtils;
import java.util.Map;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.shca.module.cancer.service.ProcessService;
import com.shca.support.DbSysConfigManager;
import com.shca.util.ConvertUtils;
import com.shca.util.FileUtils;

@Service
public class NormalProcessServiceImpl implements ProcessService {

    @Resource DbSysConfigManager config;
    @Resource private CromwellClient client;

    private static final Logger LOGGER = Logger.getLogger( NormalProcessServiceImpl.class);

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    @Override
    public WorkflowsResponse generic(String sampleName, File rawDataR1, File rawDataR2, String workflowInputs
            , File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running generic-process: " + sampleName);
            if (StringUtils.hasLength(sampleName)) {
                LOGGER.error( "error generic-process: " + sampleName + " cannot be null or empty");
                throw new IllegalArgumentException( "error generic-process: " + sampleName + " cannot be null or empty");
            }
            if (rawDataR1 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 cannot be null");
            }
            if (!rawDataR1.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR1 not exists: " + rawDataR1.getAbsolutePath());
            }

            if (rawDataR2 == null) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 cannot be null");
            }
            if (!rawDataR2.exists()) {
                LOGGER.error( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
                throw new IllegalArgumentException( "error generic-process[" + sampleName + "]: rawDataR2 not exists: " + rawDataR2.getAbsolutePath());
            }

            String homePath = config.getValue( "workspace.sample_path");
            File sampleDir = new File( homePath + "/" + sampleName);

            // FileUtils.deleteFile( sampleDir);
            deleteFile(sampleDir);
            sampleDir.mkdirs();

            File prettyJson = new File( homePath + "/" + sampleName + "/generic_" + sampleName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");
            Map<String, Object> jsonObject = JsonUtils.parseObject(workflowInputs);
            jsonObject.put( "generalProcess.sampleName_WF", sampleName);
            jsonObject.put( "generalProcess.rawDataR1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put( "generalProcess.rawDataR2_WF", rawDataR2.getAbsolutePath());
            jsonObject.put( "generalProcess.homePath_WF", homePath);

            System.out.println( "&&&" + JsonUtils.toJSONStringPretty(jsonObject));
            try (BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson))) {
                prettyWriter.write( JsonUtils.toJSONStringPretty(jsonObject).replaceAll( "\\t", "    "));
            }

            LOGGER.info( "running generic-process[" + sampleName + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(
                new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running generic-process[" + sampleName + "]'s workflow: " + JsonUtils.toJSONString(response));

            return response;
        } catch (IOException | IllegalArgumentException e) {
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse mutual(String projectSN, String projectName, String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running mutual-process: " + projectSN);
            if (StringUtils.hasLength(projectSN)) {
                LOGGER.error( "error mutual-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength(sampleName0)) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error mutect-process[" + projectSN + "]: sampleName0 cannot be null or empty");
            }
            if (StringUtils.hasLength(sampleName1)) {
                LOGGER.error( "error mutual-process[" + projectSN + "]: sampleName1 cannot be null or empty");
                throw new IllegalArgumentException( "error mutual-process[" + projectSN + "]: sampleName1 cannot be null or empty");
            }

            File homePath = new File( config.getValue("workspace.project_path") + "/" + projectName);
            LOGGER.info("projectName: " + projectName);
            LOGGER.info("project_path: " + config.getValue("workspace.project_path"));

            if (homePath.exists()) {
                deleteFile(new File(homePath.getAbsoluteFile() + "/" + sampleName0));
                deleteFile(new File(homePath.getAbsoluteFile() + "/" + sampleName1));

                FileUtils.deleteFile(new File(config.getValue("workspace.sample_path") + "/" + sampleName0 + "/tmp_vcf"));
                FileUtils.deleteFile(new File(config.getValue("workspace.sample_path") + "/" + sampleName1 + "/tmp_vcf"));

                FileUtils.deleteFile(homePath);
            }

            LOGGER.info("homePath: " + homePath);
            homePath.mkdirs();

            String normalName = sampleName0;
            String tumorName = sampleName1;


            File normalDir0 = new File(config.getValue("workspace.sample_path") + "/" + normalName);
            File normalDir1 = new File( homePath.getAbsolutePath() + "/" + normalName);
            if (normalDir0.exists()) {
                copyFiles( normalDir0, normalDir1);
            }

            File tumorDir0 = new File(config.getValue("workspace.sample_path") + "/" + tumorName);
            File tumorDir1 = new File( homePath.getAbsolutePath() + "/" + tumorName);
            if (tumorDir0.exists()) {
                copyFiles( tumorDir0, tumorDir1);
            }

            File prettyJson = new File( homePath.getAbsolutePath() + "/mutual_" +
                projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") +
                "_pretty.json");
            Map<String, Object> jsonObject = JsonUtils.parseObject(workflowInputs);
            jsonObject.put( "generalProcess.homePath_WF", homePath.getAbsolutePath());
            jsonObject.put( "generalProcess.projectName_WF", projectName);
            jsonObject.put( "generalProcess.normalSampleName_WF", normalName);
            jsonObject.put( "generalProcess.tumorSampleName_WF", tumorName);

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JsonUtils.toJSONStringPretty(jsonObject).replaceAll( "\\t", "    "));
            prettyWriter.close();


            LOGGER.info( "running mutual-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.info( "running mutual-process[" + projectSN + "]'s workflow: " + JsonUtils.toJSONString(response));

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse summary(
        String projectSN,
        String projectName,
        String sampleName,
        File rawDataR1,
        File rawDataR2,
        String workflowInputs,
        File workflowSource,
        File workflowDependencies,
        File workflowOptions) {

        LOGGER.info( "running summary-process[" + projectSN + "]: " + sampleName);
        if (StringUtils.hasLength( projectSN)) {
            LOGGER.error( "error summary-process: " + projectSN + " cannot be null or empty");
            throw new IllegalArgumentException( "error summary-process: " + projectSN + " cannot be null or empty");
        }
        if (StringUtils.hasLength( sampleName)) {
            LOGGER.error( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
            throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: sampleName cannot be null or empty");
        }
        if (rawDataR1 == null || !rawDataR1.exists()) {
            LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
            throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR1 cannot be null or not exists");
        }
        if (rawDataR2 == null || !rawDataR2.exists()) {
            LOGGER.error( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
            throw new IllegalArgumentException( "error summary-process[" + projectSN + "]: rawDataR2 cannot be null or not exists");
        }
        if (workflowDependencies !=null && !workflowDependencies.exists()) {
            workflowDependencies = null;
        }
        if (workflowOptions !=null && !workflowOptions.exists()) {
            workflowOptions = null;
        }

        Map<String, Object> jsonObject = null;

        try {
            jsonObject = JsonUtils.parseObject(workflowInputs);
            String homePath = config.getValue("workspace.project_path") + "/" + projectName;
            jsonObject.put( "generalProcess.sampleName_WF", sampleName);
            jsonObject.put( "generalProcess.homePath_WF", homePath);
            jsonObject.put( "generalProcess.rawDataR1_WF", rawDataR1.getAbsolutePath());
            jsonObject.put( "generalProcess.rawDataR2_WF", rawDataR2.getAbsolutePath());

            File prettyJson = new File( homePath + "/" + sampleName + "/summary_" + projectName + "_"
                + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")) + "_pretty.json");
            LOGGER.info(prettyJson.getAbsolutePath());

            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JsonUtils.toJSONStringPretty(jsonObject).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.info( "running summary-process[" + projectSN + "][" + sampleName + "]: preSubmit workflow");
            WorkflowsRequest request = new WorkflowsRequest(
                workflowSource, prettyJson, workflowDependencies, workflowOptions);
            LOGGER.info(request.getApiUrl());
            LOGGER.info(request.getFiles());
            LOGGER.info(request.getParams());
            LOGGER.info(client.getUrl());
            LOGGER.info(client.getVersion());
            WorkflowsResponse response = client.send(request);
            LOGGER.info( "running summary-process[" + projectSN + "][" + sampleName + "]'s workflow: " + JsonUtils.toJSONString(response));
            return response;
        } catch (Exception e) {
            LOGGER.error( "JSON: " + jsonObject);
            e.printStackTrace();
            throw new RuntimeException( e);
        }
    }

    @Override
    public WorkflowsResponse upload(String projectSN, String projectName,String sampleName0, String sampleName1, String workflowInputs
            ,File workflowSource, File workflowDependencies, File workflowOptions) {
        try {
            LOGGER.info( "running upload-process[" + projectSN + "]:... ");
            if (StringUtils.hasLength( projectSN)) {
                LOGGER.error( "error upload-process: " + projectSN + " cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process: " + projectSN + " cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName0)) {
                LOGGER.error( "error upload-process[" + projectSN + "]: sampleName0 cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process[" + projectSN + "]: sampleName cannot be null or empty");
            }
            if (StringUtils.hasLength( sampleName1)) {
                LOGGER.error( "error upload-process[" + projectSN + "]: sampleName1 cannot be null or empty");
                throw new IllegalArgumentException( "error upload-process[" + projectSN + "]: sampleName cannot be null or empty");
            }


            // SysConfigManager config = .getInstance();

            String homePath = config.getValue("workspace.project_path") + "/" + projectName;

            File filesForUpload = new File( homePath + "/filesForUpload");
            if (filesForUpload.exists()) {
                deleteFile( filesForUpload);
            }
            String normalName = sampleName0;
            String tumorName = sampleName1;
            File prettyJson = new File( homePath + "/upload_" + projectName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + "_pretty.json");
            Map<String, Object> jsonObject = JsonUtils.parseObject(workflowInputs);
            jsonObject.put( "generalProcess.projectName_WF", projectName);
            jsonObject.put( "generalProcess.homePath_WF", homePath);
            jsonObject.put( "generalProcess.normalSampleName_WF", normalName);
            jsonObject.put( "generalProcess.tumorSampleName_WF", tumorName);
            if (jsonObject.containsKey( "geneFileUpload.Bconc_WF")) {
                Object bconc = jsonObject.get("geneFileUpload.Bconc_WF");
                if (bconc instanceof Number) {
                    jsonObject.put( "generalProcess.Bconc_WF", ((Number) bconc).doubleValue());
                }
                jsonObject.remove( "geneFileUpload.Bconc_WF");
            }
            if (jsonObject.containsKey( "geneFileUpload.Kconc_WF")) {
                Object kconc = jsonObject.get("geneFileUpload.Kconc_WF");
                if (kconc instanceof Number) {
                    jsonObject.put( "generalProcess.Kconc_WF", ((Number) kconc).doubleValue());
                }
                jsonObject.remove( "geneFileUpload.Kconc_WF");
            }
            BufferedWriter prettyWriter = new BufferedWriter(new FileWriter( prettyJson));
            prettyWriter.write( JsonUtils.toJSONStringPretty(jsonObject).replaceAll( "\\t", "    "));
            prettyWriter.close();

            LOGGER.error( "running summary-process[" + projectSN + "]: preSubmit workflow");
            WorkflowsResponse response = client.send(new WorkflowsRequest( workflowSource, prettyJson, workflowDependencies, workflowOptions));
            LOGGER.error( "running summary-process[" + projectSN + "]'s workflow: " + JsonUtils.toJSONString(response));

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException( e);
        }

    }


    private void deleteFile(File dir) {
        String trashbinPath = "/mydata/trashbin";
        // cmd( "rm -rf " + dir.getAbsolutePath());
        LOGGER.info("moving folder [" + dir.getName() + "] to trashbin starting" );
        cmd( "mv " + dir.getAbsolutePath() + " " + trashbinPath + "/" + dir.getName() + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm"));
        LOGGER.info("moving folder [" + dir.getName() + "] to trashbin finished");
    }

    private void copyFiles(File source, File dest) {
        String cmd = "ln -snf " + source.getAbsolutePath() + " " + dest.getAbsolutePath();
        cmd( cmd);
//        FileUtils.copyFiles( source, dest);
    }


    private void cmd(String cmd) {
        Runtime run = Runtime.getRuntime();
        try {
            LOGGER.info( "cmd: " + cmd);
            String [] cmds = {"/bin/sh","-c", cmd};
            Process process = run.exec( cmds);
            String line;
            BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuffer out = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null ) {
                out.append(line);
            }
            try {
                process.waitFor();
            } catch (InterruptedException e) {
            }
            process.destroy();
            LOGGER.info( "cmd[" + cmd + "]: " + out);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException( e);
        }
    }

}
