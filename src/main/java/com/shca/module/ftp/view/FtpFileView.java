package com.shca.module.ftp.view;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.WorkflowsRequest;
import com.shca.cromwell.api.WorkflowsResponse;
import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DownloadExcel2003Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.JspForward;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.view.ViewParams;
import com.shca.config.SystemConfigService;
import com.ywang.sql.OrderPage;
import com.ywang.sql.Page;
import com.shca.util.DbService;
import com.ywang.utils.ConvertUtils;
import com.ywang.utils.LogUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.DecimalFormat;
import java.util.*;

import javax.annotation.Resource;

public class FtpFileView {

    private final static Logger LOGGER = Logger.getLogger( FtpFileView.class);
    @Resource private CromwellClient client;
    @Resource private DbService db;
    @Resource private SystemConfigService config;

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        Map<String,Object> ret = new HashMap<>();
        // Using injected config
        if (params.isJson()){
            OrderPage page = params.getPage();
            List<Object> whereParams = new ArrayList<>();
            String sql = " select id,file_name,file_size,store_path,directory,modified_time,sample_name,rawdata,nova,memo,cancer_kind,create_time,md5" +
                    " from shca_ftp_file a where 1=1 " + params.whereSql( whereParams);

            String cancerKind = params.getString("cancer_kind");

            if(StringUtils.hasLength(cancerKind)){
                String sql_temp = "";
                List<Map<String, Object>> kindList = config.getProperties("cancer_kind");
                for (Map<String, Object> map: kindList){
                    String name = (String)map.get("name");
                    String value = (String)map.get("value");
                    if(cancerKind.equals(value)){
                        sql_temp += " cancer_kind = ? or ";
                        whereParams.add(name.substring("cancer_kind.".length()));
                    }
                }

                if(sql_temp.length()>0){
                    sql += " and ("+sql_temp+" 1=2)";
                }

            }

            List<Map<String,Object>> rows =  db.select( sql + " order by create_time desc", page, whereParams.toArray(new Object[0]));

            String groupName = "cancer_kind";
            for(int i=0;i<rows.size(); i++){
                Map<String,Object> map = rows.get(i);
                map.put("rownum",(page.asPage().getStart()-1)*page.asPage().getSize()+(i+1));
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String cancer_kind = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue(cancer_kind);
                    String code = config.getValue("cancer_kind_code."+cancer_kind);
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",kind+"("+code+")");
                    }

                }
            }


            ret.put("total", page.asPage().getTotal());
            ret.put("rows", rows);
            return new JsonForward(ret);
        }
        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        List<Map<String, Object>> kindList2 = config.getProperties("cancer_kind");
        Set<String> set = new HashSet<>();
        for (Map<String, Object> map: kindList2) {
            String value = (String)map.get("value");
            set.add(value);
        }
        List<Map<String, Object>> kindListResult = new ArrayList<>();
        for (int i=0; i<kindList.size();i++) {
            Map<String, Object> map = kindList.get(i);
            String name = (String)map.get("name");
            if(set.contains(name)){
                kindListResult.add(map);
            }
        }
        ret.put("kindList2",kindListResult);

        ret.put("today",ConvertUtils.date2Str(new Date(),"yyyy-MM-dd"));

        ret.put("kindList",kindList);
        return new JspForward( "/shca/ftp/ftp_file_list.jsp", ret);
    }

    public Forward excel(ViewParams<?> params) {
        // Using injected config

            List<Object> whereParams = new ArrayList<>();
            String sql = " select id,file_name,file_size,store_path,directory,modified_time,sample_name,rawdata,nova,memo,cancer_kind,create_time,md5" +
                  /*  " ,(select md5 from shca_project_cancer_file b where b.sample_name=a.sample_name and b.rawdata=a.rawdata) original_file" +
                    " ,(select nova+1 from shca_project_cancer_file b where b.sample_name=a.sample_name and b.rawdata=a.rawdata) max_nova " +*/
                    " from shca_ftp_file a where 1=1 " + params.whereSql( whereParams);

            String cancerKind = params.getString("cancer_kind");

            if(StringUtils.hasLength(cancerKind)){
                String sql_temp = "";
                List<Map<String, Object>> kindList = config.getProperties("cancer_kind");
                for (Map<String, Object> map: kindList){
                    String name = (String)map.get("name");
                    String value = (String)map.get("value");
                    if(cancerKind.equals(value)){
                        sql_temp += " cancer_kind = ? or ";
                        whereParams.add(name.substring("cancer_kind.".length()));
                    }
                }

                if(sql_temp.length()>0){
                    sql += " and ("+sql_temp+" 1=2)";
                }

            }

            List<Map<String,Object>> rows =  db.select( sql + " order by create_time desc",  whereParams.toArray(new Object[0]));

            String groupName = "cancer_kind";
            for(int i=0;i<rows.size(); i++){
                Map<String,Object> map = rows.get(i);
                map.put("rownum",i+1);
                if(map.get("cancer_kind")==null){
                    map.put("cancer_kind","--");
                } else {
                    String cancer_kind = config.getValue(groupName+"."+map.get("cancer_kind"));
                    String kind = config.getValue(cancer_kind);
                    String code = config.getValue("cancer_kind_code."+cancer_kind);
                    if(kind==null){
                        map.put("cancer_kind","--");
                    } else {
                        map.put("cancer_kind",kind+"("+code+")");
                    }

                }
            }

            HSSFWorkbook workBook = new HSSFWorkbook();

            HSSFSheet sheet = workBook.createSheet("sheet1");
            String[] titleList={"序号","文件名","文件md5码","文件大小","样本编号","癌症种类","Rawdata","Nova号","处理结果","上传时间"};

            HSSFRow row=sheet.createRow(0);
            HSSFCell cell;
            for(int i=0;i<titleList.length;i++){
                cell=row.createCell(i);
                cell.setCellValue(titleList[i]);
            }

            for (int i =0 ;i < rows.size();i++) {
                Map<String,Object> map = rows.get(i);

                HSSFRow nrow=sheet.createRow(i+1);
                int colIndex = 0;
                HSSFCell ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(map.get("rownum")+"");
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(map.get("file_name")+"");
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(map.get("md5")+"");
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(descFileSize((Long)map.get("file_size"),0)+"");
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(StringUtils.hasLength((String)map.get("sample_name")) ? "--" : (String) map.get("sample_name"));
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(StringUtils.hasLength((String)map.get("cancer_kind")) ? "--" : (String) map.get("cancer_kind"));
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(StringUtils.hasLength((String)map.get("rawdata")) ? "--" : (String) map.get("rawdata"));
                ncell=nrow.createCell(colIndex++);
                Integer nova = (Integer)map.get("nova");
                ncell.setCellValue(nova!=-1?nova+"":"--");
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(StringUtils.hasLength((String)map.get("memo")) ? "" : (String) map.get("memo"));
                ncell=nrow.createCell(colIndex++);
                ncell.setCellValue(ConvertUtils.date2Str((Date)map.get("create_time"),"yyyy-MM-dd HH:mm"));
            }
            sheet.setColumnWidth(1,64*256);
            sheet.setColumnWidth(2,36*256);
            sheet.setColumnWidth(4,17*256);
            sheet.setColumnWidth(5,20*256);
            sheet.setColumnWidth(8,30*256);
            sheet.setColumnWidth(9,17*256);
            return new DownloadExcel2003Forward("样本上传情况.xls",workBook);
    }
    private String descFileSize(long size,int idx){
        String[] FILE_SIZE = {"B", "KB", "MB", "GB"};
        DecimalFormat df =new DecimalFormat("0.##");
        if(size==0) return "--";
        if (size <= 1024) {
                return df.format(size)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return df.format(size)  + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024.0, idx + 1);
    }

    private String descFileSize(double size,int idx){
        String[] FILE_SIZE = {"B", "KB", "MB", "GB"};
        DecimalFormat df =new DecimalFormat("0.##");
        if(size==0) return "--";
        if (size <= 1024) {
            return df.format(size)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return df.format(size)  + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024.0, idx + 1);
    }
    public Forward cat(ViewParams<?> params) {
        Map<String,Object> ret = new HashMap<>();

        List<Map<String,Object>> files =  db.select( "select id,file_name,md5,store_path from shca_project_cancer_file order by id desc", new Page( 100));

        ret.put( "original_files", files);
        return new JspForward( "/shca/ftp/ftp_file_cat.jsp", ret);
    }


    public Forward merge(ViewParams<?> params) throws JsonProcessingException {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "样本合并失败: ";
        try {
            // Using injected config

            File source = new File( Constant.WEB_INF_PATH + "geneFileMerge.wdl");
            String sampleName = params.getString( "sample_name");

            errCode = 1100;

            String file = params.getString( "append_file");
            String dir = file.substring( 0, file.lastIndexOf( "/"));

            File json = new File( dir + "/" + sampleName + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm") + ".json");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put( "geneFileMerge.originalFile_WF", params.getString( "original_file"));
            jsonObject.put("geneFileMerge.appendedFile_WF", params.getString( "append_file"));
            jsonObject.put("geneFileMerge.outputFile_WF", config.getValue( "ftp.upload_dir") + "/shca-Nova"
                    + params.getString( "nova") + "-" + sampleName + "_merge_" + params.getString( "rawdata") + ".fastq.gz");

            BufferedWriter writer = new BufferedWriter( new FileWriter( json));
            writer.write(jsonObject.toJSONString());
            writer.close();

            errCode = 1200;
            WorkflowsResponse response = client.send( new WorkflowsRequest( source, json, null, null));

            errCode = 1300;
            db.update( "update shca_ftp_file set memo=?,cromwell_workflow_id_0=?, cromwell_workflow_status_0=? where id=? "
                    ,"样本合并中" ,response.getId(), response.getStatus(), params.getId());

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");


            return new JsonForward( ret);
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString(e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            throw new UnknownException(  new JsonForward( ret), e);
        }
    }

    // private String getName(List<Map<String,Object>> list,String value){
    //     for(int i=0;i<list.size();i++){
    //         Map<String,Object> map = list.get(i);
    //         String config_name = (String)map.get("name");
    //         String config_value = (String)map.get("value");
    //         System.out.println("comp:"+config_value +","+value);
    //         if(config_value.equals(value)){

    //             return config_name;
    //         }
    //     }
    //     return null;
    // }

    public static void main(String...args) {
        String file = "/home/<USER>/administrator/update/R20047549-mingmanova333-BZ2002680Kbu2_combined_R2.fastq.gz";
        System.out.println(  file.substring( 0, file.lastIndexOf( "/")));
    }
}
