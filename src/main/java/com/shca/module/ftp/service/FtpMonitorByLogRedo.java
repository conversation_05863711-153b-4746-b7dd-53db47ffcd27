package com.shca.module.ftp.service;

import com.shca.module.ftp.support.FtpLogSupport;
import com.shca.util.StringUtilsRefact;
import com.shca.config.SystemConfigService;
import com.shca.util.DbService;
import com.shca.util.*;
import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FtpMonitorByLogRedo {

    private DbService db;
    private SystemConfigService config;
    private long threshold;
    private final Pattern sampleName_Pattern;
    // private final Pattern R_Pattern;
    private final Pattern NOVA_Pattern;
    private final static Logger LOGGER = Logger.getLogger( FtpMonitorByLogRedo.class);

    public void setDb(DbService db) {
        this.db = db;
    }

    public FtpMonitorByLogRedo(Pattern sampleName_Pattern, Pattern R_Pattern, Pattern NOVA_Pattern) {
        this.sampleName_Pattern = sampleName_Pattern;
        // this.R_Pattern = R_Pattern;
        this.NOVA_Pattern = NOVA_Pattern;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }

    public void sync() {
        File dir = new File(config.getValue("ftp.monitor_path"));
        monitor(dir, config);
    }

    public void monitor(File dir, SystemConfigService config) {
        String title = "FTP Monitoring... [" + dir.getAbsolutePath() + "]: ";
        File[] rawLogFiles = dir.listFiles();
        LOGGER.info(  title + "ftp log count = " + rawLogFiles.length);
        // keep all fastq file info map of this time to a new map
        Map<String, HashMap<String, Object>> sampleNameObjectMap = new HashMap<>();

        // parse raw log files to a map, a log file to a FtpLog object
        Map<String, FtpLog> ftpLogMap = FtpLogSupport.parseLogFiles(rawLogFiles);
        LOGGER.info(  title + "cancer count = " + ftpLogMap.size() + ", ftp log count = " + rawLogFiles.length);

        for (FtpLog ftpLog : ftpLogMap.values()) {
            if (!ftpLogValidate(ftpLog)) {
                continue;
            }
            //parse fastq file info to map
            HashMap<String, Object> ftpFastqFileParseMap = parseFastqFileByFtpLog(ftpLog);
            if (ftpFastqFileParseMap.isEmpty()) {
                return;
            }
            // keep all fastq file info map of this time to a new map
            sampleNameObjectMap.put(
                (String)ftpFastqFileParseMap.get("sample_name"), ftpFastqFileParseMap);

            // move fastq file to target path, and add 'store_path' to the info map
            String storePath = moveFastqFile(ftpFastqFileParseMap, ftpLog, config);
            ftpFastqFileParseMap.put("store_path", (Object) storePath);

            // if fastq file already exists , if exists then update, else insert new
            createFtpFileRecord((HashMap<String, Object>) ftpFastqFileParseMap);
            if (fastqExists(ftpFastqFileParseMap)) {
                updateCancerFileRecord((HashMap<String, Object>) ftpFastqFileParseMap);
            }
            // if ("sample".equals( ftpLog.getDirectory()) && !fastqExists(ftpFastqFileParseMap)) {
            //     createCancerFileRecord((HashMap<String, Object>) ftpFastqFileParseMap);
            // }
            saveFtpLogFile(ftpLog, config);

            // update shca_project_project to start the mutual process
            updateProjectRecord(ftpFastqFileParseMap);
            updatetSingleSampleRecordStepQc(ftpFastqFileParseMap);
            updateProjectStepUpload(ftpFastqFileParseMap);
        }

        // createSingleSampleRecordBySampleName(sampleNameObjectMap, config);
        updateSingleSampleRecordBySampleName(sampleNameObjectMap);
    }

    private void updateProjectRecord(HashMap<String, Object> ftpFastqFileParseMap) {
        String sampleName = (String) ftpFastqFileParseMap.get("sample_name");
        String projectName = sampleName.substring(0,9);
        LOGGER.info("updating shca_project_project table of sample name" + projectName);
        db.update(
            "UPDATE shca_project_project SET status=?, cromwell_workflow_id_0=null, cromwell_sub_workflow_id_0=null, "
            + "cromwell_workflow_status_0=null, update_username=?, update_time=?, lims_status=?, is_filing=? WHERE project_name=? AND is_filing!=? "
            , "wait_sample", "redo", new Date(), "finished", '0', projectName, '0');

        LOGGER.info("updated successfully");
    }

    private void updatetSingleSampleRecordStepQc(HashMap<String, Object> ftpFastqFileParseMap) {
        String sampleName = (String) ftpFastqFileParseMap.get("sample_name");
        //更新第三步状态
        db.update(
            "update shca_project_cancer "
            + "set cromwell_workflow_id_1=null, cromwell_workflow_status_1=null, cromwell_sub_workflow_id_1=null , update_username = ?  "
            + " where sample_name=? "
            ,"redo", sampleName);
    }

    private void updateProjectStepUpload(HashMap<String, Object> ftpFastqFileParseMap) {
        String sampleName = (String) ftpFastqFileParseMap.get("sample_name");
        String projectName = sampleName.substring(0,9);
        //更新第四步状态
        db.update(
            "update shca_project_project "
            + " set cromwell_workflow_id_1=null,cromwell_workflow_status_1=null,cromwell_sub_workflow_id_1=null ,update_username = ? " +
            "where project_name=? "
            , "redo" , projectName);
    }

    private void updateCancerFileRecord(HashMap<String, Object> ftpFastqFileMap) {
        Date now = new Date();
        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        String reads = (String) ftpFastqFileMap.get("rawdata");
        LOGGER.info("updating Cancer record [" + sampleName + "] -> " + reads);
        ftpFastqFileMap.put("create_time", now);
        ftpFastqFileMap.put("update_time", now);

        LOGGER.info("selecting status from shca_project_cancer_file by project: " + sampleName);

        Map<String, Object> recordIdStatus = db.selectUnique(
            "SELECT id, status FROM shca_project_cancer_file WHERE sample_name=? AND rawdata=?"
            ,sampleName, reads);

        String status = "";
        long id = -1;
        if (!recordIdStatus.isEmpty()) {
            status = (String)recordIdStatus.get("status");
            id = (long)recordIdStatus.get("id");
        }

        LOGGER.info("status of " + sampleName + "is " + status + "and id is :" + id);
        if ("filed".equals((String)recordIdStatus.get("status"))) {
            LOGGER.info("SET status of " + sampleName + " from filed -> successed with successed to shca_project_cancer_file ");
            db.update("UPDATE shca_project_cancer_file SET status=? WHERE id=? ", "successed", id);
            LOGGER.info("status of " + sampleName + "changed to successed");
        }
    }

    // private void createCancerFileRecord(HashMap<String, Object> ftpFastqFileMap) {
    //     LOGGER.info("create cancer file record.");
    //     Date now = new Date();
    //     String sampleName = (String) ftpFastqFileMap.get("sample_name");
    //     String reads = (String) ftpFastqFileMap.get("rawdata");
    //     LOGGER.info("Saving Cancer record [" + sampleName + "] -> " + reads);
    //     ftpFastqFileMap.put("create_time", now);
    //     ftpFastqFileMap.put("update_time", now);

    //     try {
    //         LOGGER.info("updating shca_project_cancer_file with sample name: " + sampleName);
    //         db.update( "UPDATE shca_project_cancer_file SET status=? WHERE sample_name=? and rawdata=? and status=? "
    //             ,"expired", sampleName, reads, "successed");
    //         LOGGER.info("updated shca_project_cancer_file with sample name: " + sampleName );
    //     } catch(Exception e){
    //         LOGGER.error("update shca_project_cancer_file with sample name: " + sampleName );
    //         e.printStackTrace();
    //     }

    //     try {
    //         LOGGER.info("inserting into shca_project_cancer_file with sample name: " + sampleName);
    //         HashMap<String, Object> tmpSqlMap = (HashMap<String, Object>)ftpFastqFileMap.clone();
    //         tmpSqlMap.remove("start_time");
    //         tmpSqlMap.remove("end_time");
    //         tmpSqlMap.remove("is_deleted");
    //         tmpSqlMap.remove("directory");
    //         tmpSqlMap.remove("memo");
    //         tmpSqlMap.put("status", "successed");
    //         tmpSqlMap.put("modified_time", new Date());

    //         List<Object> sqlParams = new ArrayList<>();
    //         String sql = DbUtils.insertSql( "shca_project_cancer_file", (Map<String, Object>)tmpSqlMap, sqlParams);
    //         LOGGER.info("sql lines is: " + sql);
    //         db.insert( sql, sqlParams.toArray(new Object[0]));

    //         LOGGER.info("inserted into shca_project_cancer_file with sample name: " + sampleName);
    //     } catch (Exception e) {
    //         LOGGER.error("insert into shca_project_cancer_file with sample name: " + sampleName);
    //         e.printStackTrace();
    //     }
    // }

    // private Map<String, FtpLog> parseLogFiles(File[] logFiles) {
    //     Map<String, FtpLog> ftpLogMap = new HashMap<>();
    //     for (File logFile : logFiles) {
    //         LOGGER.info("parsing ftp log file: " + logFile.getName());
    //         if (logFile.isDirectory()) {
    //             LOGGER.info(logFile + "is directory, skip and continue...");
    //             continue;
    //         }

    //         String logFileName = logFile.getName();
    //         Date logFileTimestamp = new Date(ConvertUtils.lon(logFileName.substring(0, 10)) * 1000);
    //         String logContent = logFileName.substring(11);
    //         String sourceFilePath = FileUtils.read(logFile)[0];
    //         //check if already in ftpLogMap
    //         FtpLog ftpLog = ftpLogMap.get(sourceFilePath);
    //         String sourceFileName = logContent.split(" ")[1];
    //         if (ftpLog == null) {
    //             ftpLog = new FtpLog(sourceFileName);
    //             ftpLog.setStartTime(logFileTimestamp);
    //             ftpLog.setFilePath(sourceFilePath);
    //         }

    //         if (logContent.startsWith("CREATE")) {
    //             // update the time by new log record, 相同文件再次上传，更新文件上传时间和文件地址
    //             if (ftpLog.getStartTime() == null || logFileTimestamp.getTime() > ftpLog.getStartTime().getTime()) {
    //                 ftpLog.setStartTime(logFileTimestamp);
    //             }
    //         }

    //         if (logContent.startsWith("CLOSE_WRITE,CLOSE")) {
    //             ftpLog.setEndTime(logFileTimestamp);
    //         }
    //         if (logContent.startsWith("CLOSE_WRITE,CRACKED")) {
    //             ftpLog.setEndTime(logFileTimestamp);
    //             ftpLog.setDirectory( "cracked");
    //         }
    //         ftpLog.addLog(logFile);
    //         ftpLogMap.put(sourceFilePath, ftpLog);
    //     }
    //     return ftpLogMap;
    // }

    private HashMap<String, Object> parseFastqFileByFtpLog(FtpLog ftpLog) {
        String title = "Parsing FTP LOG: [" + ftpLog.getFileName() + "] -> ";
        String fastqFilePath = ftpLog.getFilePath();
        File fastqFile = new File( fastqFilePath);
        Date now = new Date();
        HashMap<String, Object> ftpFileMap = new HashMap<>();

        LOGGER.info( title + "parsing ...");
        if (!fastqFile.exists()) {
            LOGGER.error( title +  fastqFilePath + "source file does not exist, ignoring...");
            return ftpFileMap;
        }

        long fastqFileSize = fastqFile.length();
        if (fastqFileSize == 0) {
            LOGGER.error( title + "length of target file is 0, ignoring...");
            return ftpFileMap;
        }

        String fastqFileName = fastqFile.getName();
        Date lastModified = new Date(fastqFile.lastModified());
        String md5 = StringUtilsRefact.md5(fastqFile);

        String sampleName = null;
        String sampleType = null;
        String cancerKindPrefix = null;
        String reads = null;
        int nova = -1;

        LOGGER.info(title + "matching sample name info...");
        Matcher sampleName_Matcher = sampleName_Pattern.matcher(fastqFileName);
        if (sampleName_Matcher.find()) {
            sampleName = sampleName_Matcher.group(); //BZ23012345B01
            cancerKindPrefix = sampleName_Matcher.group(1); //BZ
            sampleType = sampleName_Matcher.group(2); //B or K
            LOGGER.info(title + "matched: sample name -> " + sampleName);
        } else {
            LOGGER.error(title + "sample name not matched ");
            return ftpFileMap;
        }

        LOGGER.info(title + "match reads number...");
        Pattern pattern = Pattern.compile("(R1|R2)\\.");
        Matcher R_Matcher = pattern.matcher(fastqFileName);
        // Matcher R_Matcher = R_Pattern.matcher(fastqFileName);
        // LOGGER.info("R_Matcher: " + R_Matcher.find());
        if (R_Matcher.find()) {
            reads = R_Matcher.group(1); //R1 or R2
            LOGGER.info(title + "matched: reads -> " + reads);
        } else {
            LOGGER.error(title + "reads not matched ");
            return ftpFileMap;
        }

        LOGGER.info(title + "matching nova...");
        String[] array = fastqFileName.split("-");
        if (array.length == 3) {
            String strNova = array[1];
            Matcher nova_Matcher = NOVA_Pattern.matcher( strNova);
            if (nova_Matcher.find()) {
                nova = ConvertUtils.integer( nova_Matcher.group());
            }
        }

        LOGGER.info( title + "sample Name=" + sampleName + ",reads=" + reads + ",sampleType=" + sampleType + ",cancerKindPrefix=" + cancerKindPrefix);
        String memo = "";
        String infoSeperator = "; ";
        if (StringUtils.hasLength(sampleName)) {
            memo += "文件名称中缺少样本号信息" + infoSeperator;
        }
        if (StringUtils.hasLength(cancerKindPrefix)) {
            memo += "系统未匹配项目类型" + infoSeperator;
        }
        if (StringUtils.hasLength(sampleType)) {
            memo += "文件名称中缺少样本种类" + infoSeperator;
        }
        if (StringUtils.hasLength(reads)) {
            memo += "文件名称中缺少Reads信息" + infoSeperator;
        }

        if(!memo.equals("")) {
            LOGGER.error(title + "error, and specific reason is" + memo);
            ftpLog.setDirectory( "parse_error");
            return ftpFileMap;
        }

        memo = "系统成功识别样本文件";
        if (fastqExistsRedo(sampleName, reads, md5)) {
            memo = "样本文件重复上传";
            ftpLog.setDirectory( "duplicate");
        }

        ftpFileMap.put("md5", md5);
        ftpFileMap.put("directory", ftpLog.getDirectory());
        ftpFileMap.put("file_name", fastqFileName);
        ftpFileMap.put("file_size", fastqFileSize);
        ftpFileMap.put("is_deleted",false);
        ftpFileMap.put("modified_time",lastModified);
        ftpFileMap.put("sample_name", sampleName);
        ftpFileMap.put("rawdata",reads);
        ftpFileMap.put("nova",nova);
        ftpFileMap.put("sample_type",sampleType);
        ftpFileMap.put("cancer_kind",cancerKindPrefix);
        ftpFileMap.put("memo",memo);
        ftpFileMap.put("create_username","FtpMonitorByLog");
        ftpFileMap.put("create_time",now);
        ftpFileMap.put("update_username","FtpMonitorByLog");
        ftpFileMap.put("update_time",now);
        ftpFileMap.put("start_time", ftpLog.getStartTime());
        ftpFileMap.put("end_time", ftpLog.getEndTime());

        return ftpFileMap;
    }

    private String moveFastqFile(Map<String, Object> fastqFileMap, FtpLog ftpLog, SystemConfigService config) {
        String logTitle = "Moving fastq file to target path: ";
        File targetDir = null;
        String fastqFileName = (String) fastqFileMap.get("file_name");
        String sampleName = (String) fastqFileMap.get("sample_name");
        String fastqFilePath = (String) ftpLog.getFilePath();
        boolean parseSucceeded = "sample".equals( ftpLog.getDirectory());
        if (parseSucceeded) {
            //file name unification
            if (fastqFileName.endsWith(".fq.gz")) {
                fastqFileName = fastqFileName.substring(0, fastqFileName.length() - 6) + ".fastq.gz";
            }

            targetDir = new File(config.getValue("workspace.rawdata_path") + "/" + sampleName);
        } else {
            targetDir = new File(config.getValue("ftp.upload_dir") + "/errorinfo/" + ftpLog.getDirectory() + "/" + ConvertUtils.date2Str("yyyyMMdd"));
        }

        File targetFastqFile = new File(targetDir.getAbsolutePath() + "/" + fastqFileName);
        LOGGER.info("moving file: " + fastqFilePath + " -> " + targetFastqFile.getAbsolutePath());
        try {
            targetDir.mkdirs();
            String cmd = Cmd.exec("mv " + fastqFilePath + " " + targetFastqFile.getAbsolutePath());
            // com.shca.util.CmdOperation.cmd("mv " + fastqFilePath + " " + targetFastqFile.getAbsolutePath());
            LOGGER.info( logTitle + " cmd mv result: " + cmd);
            LOGGER.info("Moved Sucessfully");
        } catch (Exception e) {
            LOGGER.error(logTitle + "moving fastq file to target path error");
            e.printStackTrace();
        }
        LOGGER.info("returned -> " + targetFastqFile.getAbsolutePath());
        return targetFastqFile.getAbsolutePath();
    }

    private void createFtpFileRecord(HashMap<String, Object> ftpFastqFileMap) {
        LOGGER.info("inserting into shca_ftp_file, with " + (String)ftpFastqFileMap.get("sample_name"));
        List<Object> sqlParams = new ArrayList<>();
        String sql = DbUtils.insertSql( "shca_ftp_file", ftpFastqFileMap, sqlParams);
        db.insert( sql, sqlParams.toArray(new Object[0]));
        LOGGER.info("inserted into shca_ftp_file succeeded ->  " + (String)ftpFastqFileMap.get("sample_name"));
    }

    private void saveFtpLogFile(FtpLog ftpLog, SystemConfigService config) {
        for (File logFile : ftpLog.getLogs()) {
            File targetLogDir = new File( config.getValue("workspace.ftplog_path") + "/" + ConvertUtils.date2Str( "yyyyMMdd") + "/");
            targetLogDir.mkdirs();
            File targetLogFile = new File( targetLogDir.getAbsolutePath() + "/" + logFile.getName());
            LOGGER.info( "moving log file " + logFile.getAbsolutePath() + " -> " + targetLogFile.getAbsolutePath());
            logFile.renameTo( targetLogFile);
            LOGGER.info("log file moved, " + targetLogFile.getAbsolutePath());
        }
    }

    // private void createSingleSampleRecordBySampleName( Map<String,HashMap<String,Object>>  sampleNameObjectMap, SysConfigManager config) {
    //     LOGGER.info("creating single sample records, count is : " + sampleNameObjectMap.size());
    //     for( Entry<String, HashMap<String, Object>> sampleEntry: sampleNameObjectMap.entrySet()) {
    //         Date now = new Date();
    //         String sampleName = sampleEntry.getKey();
    //         LOGGER.info("creating single sample record of :" + sampleName);

    //         long fastqRecordCount = db.selectUnique(
    //             "select count(*) from shca_project_cancer_file where sample_name=? and status=?"
    //             ,new LongCallback(), sampleName, "successed");

    //         LOGGER.info("shca_project_cancer_file record count of " + sampleName + " is : " + fastqRecordCount);

    //         if (fastqRecordCount == 2) {
    //             HashMap<String, Object> sampleMap = (HashMap<String, Object>)sampleEntry.getValue().clone();
    //             String cancerKind = config.getValue("cancer_kind." + sampleMap.get("cancer_kind"));
    //             sampleMap.put("cancer_kind", cancerKind);
    //             sampleMap.put("create_time", now);
    //             sampleMap.put("update_time", now);
    //             sampleMap.put("status", "wait_running");
    //             sampleMap.put("create_username", "administrator");
    //             sampleMap.put("update_username", "administrator");
    //             sampleMap.put("workspace", config.getValue( "workspace.sample_path") + "/" + sampleName);

    //             sampleMap.remove("file_name");
    //             sampleMap.remove("start_time");
    //             sampleMap.remove("end_time");
    //             sampleMap.remove("memo");
    //             sampleMap.remove("directory");
    //             sampleMap.remove("sample_type");
    //             sampleMap.remove("file_size");
    //             sampleMap.remove("store_path");
    //             sampleMap.remove("is_deleted");
    //             sampleMap.remove("modified_time");
    //             sampleMap.remove("rawdata");
    //             sampleMap.remove("md5");
    //             sampleMap.remove("nova");

    //             try {
    //                 LOGGER.info("inserting into shca_project_cancer, sample name is : " + sampleName);
    //                 List<Object> sqlParams = new ArrayList<>();
    //                 String sql = DbUtils.insertSql( "shca_project_cancer", sampleMap, sqlParams);
    //                 LOGGER.info("sql is : " + sql);
    //                 db.insert( sql, sqlParams.toArray(new Object[0]));
    //                 LOGGER.info("insert " + sampleName + "into shca_project_cancer successfully.");
    //             } catch (Exception e) {
    //                 LOGGER.error("insert into shca_project_cancer ERROR, sample name is : " + sampleName);
    //                 e.printStackTrace();
    //             }

    //             try {
    //                 List<Map<String, Object>> sampleMd5MapList = db.select(
    //                     "SELECT md5, store_path from shca_project_cancer_file WHERE sample_name=? and status=? "
    //                     , sampleName, "successed");
    //                 for (Map<String, Object> md5Map : sampleMd5MapList) {
    //                     LOGGER.info("updating shca_ftp_file with sample name: " + sampleName);
    //                     db.update(
    //                         "UPDATE shca_ftp_file SET memo=?, store_path=?, update_time=? WHERE sample_name=? AND directory=? AND md5=? "
    //                         ,  "样本预处理", (String)md5Map.get("store_path"), new Date(), sampleName, "sample", (String)md5Map.get("md5"));
    //                     LOGGER.info("updated shca_ftp_file successfully with sample name: "+ sampleName);
    //                 }

    //             } catch (Exception e) {
    //                 LOGGER.error("update shca_ftp_file ERROR, sample name is : " + sampleName);
    //                 e.printStackTrace();
    //             }
    //             // db.update("UPDATE shca_ftp_file set store_path=(select max(store_path) from shca_project_cancer_file where shca_project_cancer_file.md5=shca_ftp_file.md5 and status=?)" +
    //             //         " ,memo=?, update_time=? where sample_name=? and directory=?", "successed", "样本预处理", now, sampleName, "sample");
    //         }
    //     }
    //             // 创建单样本分析任务
    //     // 修改select sample_name from shca_project_cancer 为 select sample_name from shca_project_cancer where status='finished' or status='filed'
    //     // SysConfigManager config = SysConfigManager.getInstance();
    //     // List<Map<String, Object>> cancers = db.select("select sample_name, max(sample_type) sample_type, max(cancer_kind) cancer_kind " +
    //     //         " from shca_project_cancer_file a where sample_name not in (select sample_name from shca_project_cancer where status='finished' or status='filed' or status='failed') and status=? " +
    //     //         " group by sample_name having count(*)=? order by sample_name "
    //     //         , "successed", 2);
    // }

    private void updateSingleSampleRecordBySampleName(Map<String,HashMap<String,Object>>  sampleNameObjectMap) {
        LOGGER.info("update single sample record by sample name.");
        for( Entry<String, HashMap<String, Object>> sampleEntry: sampleNameObjectMap.entrySet()) {
            String sampleName = sampleEntry.getKey();
            LOGGER.info("updating status of project cancer record of :" + sampleName);

            Map<String, Object> recordIdStatus = db.selectUnique(
                "SELECT id, status FROM shca_project_cancer WHERE sample_name=?", sampleName);

            LOGGER.info("shca_project_cancer record count of " + sampleName + " is : " + recordIdStatus.size());

            if ("filed".equals((String)recordIdStatus.get("status")) && !recordIdStatus.isEmpty()) {
                LOGGER.info("SET status of " + sampleName + " with 'wait_running' to shca_project_cancer ");
                db.update(
                    "UPDATE shca_project_cancer SET status=?, update_time=?, update_username=?, " +
                    " cromwell_workflow_status_0=?, cromwell_workflow_status_1=? WHERE id=? "
                    , "wait_running", new Date(), "FtpMonitorByLogRedo", null, null, (long)recordIdStatus.get("id"));

                LOGGER.info("status of " + sampleName + "changed to successed");
            }
        }
    }

    private boolean ftpLogValidate(FtpLog ftpLog) {
        Date now = new Date();
        boolean result = false;
        if (ftpLog.getEndTime() == null) {
            LOGGER.info(  "UPLOADING file: " + ftpLog.getFilePath() + " continue...");
            return result;
        }
        if (now.getTime() - ftpLog.getEndTime().toEpochSecond(null) <= threshold) {
            LOGGER.info( ftpLog.getFilePath() + " waiting for parsing" + (now.getTime() - ftpLog.getEndTime().toEpochSecond(null)) / DateUtils.ONE_MINUTE + " mins");
            return result;
        }
        result = true;
        return result;
    }

    private boolean fastqExists(String sampleName, String reads) {
        Map<String, Object> cancer = db.selectUnique("SELECT id FROM shca_project_cancer_file " +
            " WHERE sample_name=? AND rawdata=? AND (status=? or status=?) ", sampleName, reads, "successed", "filed");

        if (!CollectionUtils.isEmpty( cancer)) {
            return true;
        }
        return false;
    }

    // private boolean fastqExists(String sampleName, String reads, String md5) {
    //     Map<String, Object> cancer = db.selectUnique("SELECT id FROM shca_project_cancer_file " +
    //         " WHERE sample_name=? AND rawdata=? AND (status=? or status=?) AND md5=?", sampleName, reads, "successed", "filed", md5);

    //     if (!CollectionUtils.isEmpty( cancer)) {
    //         return true;
    //     }
    //     return false;
    // }

    private boolean fastqExists(HashMap<String, Object> fastqInfoMap) {
        return fastqExists(
            (String)fastqInfoMap.get("sample_name")
            , (String)fastqInfoMap.get("rawdata"));
    }

    private boolean fastqExistsRedo(String sampleName, String reads, String md5) {
        Map<String, Object> cancer = db.selectUnique("SELECT id FROM shca_project_cancer_file " +
            " WHERE sample_name=? AND rawdata=? AND status=? ", sampleName, reads, "successed");

        if (!CollectionUtils.isEmpty( cancer)) {
            return true;
        }
        return false;
    }

    public static void main(String...args) {
        // String fileName = "R22029452-zhoupu02386-BZ2303940B01_combined_R1.fastq.gz";
        String sampleName = "BZ2303940B01";
        String projectName = sampleName.substring(0, 9);
        System.out.println( projectName);
    }

}




