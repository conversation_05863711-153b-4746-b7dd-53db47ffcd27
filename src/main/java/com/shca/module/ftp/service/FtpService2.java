package com.shca.module.ftp.service;


import com.shca.cromwell.CromwellClient;
import com.shca.cromwell.api.AbortRequest;
import com.shca.cromwell.api.AbortResponse;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.DefaultStatememt;
import com.ywang.sql.support.adapter.MySQLPageStatement;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;

import javax.sql.DataSource;
import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 样本文件保留在FTP目录进行基因测序
 */
public class FtpService2 {

    private Db db;

    private final Pattern sampleName_Pattern;

    private final Pattern R_Pattern;

    private final Pattern NOVA_Pattern;

    private final static Logger LOGGER = Logger.getLogger( FtpService2.class);

    private CromwellClient client;

    public void setClient(CromwellClient client) {
        this.client = client;
    }

    public FtpService2(String sampleName_Pattern, String R_Pattern, String NOVA_Pattern, DataSource dataSource) {
        this.sampleName_Pattern = Pattern.compile( sampleName_Pattern);
        this.R_Pattern = Pattern.compile( R_Pattern);
        this.NOVA_Pattern = Pattern.compile( NOVA_Pattern);
        db = new Db( new MySQLPageStatement( new DefaultStatememt( dataSource)));
    }

    public void monitor() {
        SysConfigManager config = SysConfigManager.getInstance();
        String dir = config.getValue( "ftp.upload_dir");
        monitor( new File( dir));
        monitorByUpdate( new File( dir + "/update"));
    }


    private void monitor(File dir) {
        try {
            // 1 FTP文件目录解析
            int idx = 1;
            SysConfigManager config = SysConfigManager.getInstance();
            LOGGER.info( "1 FTP文件目录解析: " + dir.getAbsolutePath() + "[" + dir.listFiles().length + "]");
            Date now = new Date();


            for (File file : dir.listFiles()) {
                String title = file.getName() + "[" + idx++ + "/" + dir.listFiles().length + "]: ";
                LOGGER.info( title + "start...");
                if (file.isFile()) {
                    if (!file.exists()) {
                        LOGGER.error( title + "no exists");
                        continue;
                    }
                    String cmdOutput = Cmd.execFromError( "gzip -t " + file.getAbsolutePath());
                    if (cmdOutput.indexOf( "unexpected end of file") > 0) {
                        LOGGER.info( title + " ftping...");
                        continue;
                    }
                    if (file.lastModified() <= 0) {
                        LOGGER.error( title + "lastModified is null");
                        continue;
                    }
                    int cnt = db.selectUnique( "select count(id) from shca_project_cancer_file where store_path=?  "
                            ,new IntegerCallback(), file.getAbsolutePath());
                    if (cnt == 1) {
                        LOGGER.info( title + " continue");
                        continue;
                    }

                    String fileName = file.getName();
                    Date lastModified = new Date( file.lastModified());
                    LOGGER.info( title + "parsing...");

                    String sampleName = null;
                    String sampleType = null;
                    String cancerKind = null;
                    String rawData = null;
                    int nova = -1;


                    Matcher sampleName_Matcher = sampleName_Pattern.matcher( fileName);
                    Matcher R_Matcher = R_Pattern.matcher( fileName);
                    Matcher NOVA_Matcher = NOVA_Pattern.matcher( fileName);
                    LOGGER.info( title + "match sample_name...");
                    if (sampleName_Matcher.find()) {
                        sampleName = sampleName_Matcher.group();
                        cancerKind = sampleName_Matcher.group( 1);
                        sampleType = sampleName_Matcher.group( 2);
                    }
                    LOGGER.info( title + "match rawdata...");
                    while(R_Matcher.find()) {
                        rawData = R_Matcher.group();
                    }
                    LOGGER.info( title + "match nova...");
                    if(NOVA_Matcher.find()) {
                        String _nova = NOVA_Matcher.group();
                        _nova = _nova.substring( 4);
                        nova = ConvertUtils.integer( _nova);
                    }


                    String md5 = StringUtils.md5( file);
                    LOGGER.info( title + "sampleName=" + sampleName + ",rawData=" + rawData + "md5=" + md5 + ",sampleType=" + sampleType + ",cancerKind=" + cancerKind);

                    cmdOutput = StringUtils.trim( cmdOutput);
                    if (StringUtils.hasLength( cmdOutput)) {
                        db.insert( "insert into shca_ftp_file(md5,directory,file_name,file_size,store_path,is_deleted,modified_time,sample_name" +
                                        " ,rawdata,nova,sample_type,cancer_kind,memo,create_username,create_time,update_username,update_time)" +
                                        " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", md5, "sample", fileName, file.length(), file.getAbsolutePath(), false
                                , lastModified, sampleName, rawData, nova, sampleType, cancerKind, "样本错误: " + cmdOutput, "administrator", now, "administrator", now);
                        continue;
                    } else {
                        String memo = "样本识别成功";
                        boolean _continue = false;
                        if (StringUtils.isBlank( sampleName) || StringUtils.isBlank( rawData) || StringUtils.isBlank( cancerKind)) {
                            memo = "样本号识别失败";
                            _continue = true;
                        }
                        if (StringUtils.isBlank( sampleType)) {
                            memo = "样本类型识别失败";
                            _continue = true;
                        }
                        if (StringUtils.isBlank( cancerKind)) {
                            memo = "未找到";
                            _continue = true;
                        }
                        db.insert( "insert into shca_ftp_file(md5,directory,file_name,file_size,store_path,is_deleted,modified_time,sample_name" +
                                        " ,rawdata,nova,sample_type,cancer_kind,memo,create_username,create_time,update_username,update_time)" +
                                        " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", md5, "sample", fileName, file.length(), file.getAbsolutePath(), false
                                , lastModified, sampleName, rawData, nova, sampleType, cancerKind, memo, "administrator", now, "administrator", now);

                        if (_continue) {
                            continue;
                        }
                    }


                    LOGGER.info( title + " save in shca_project_cancer_file...");
                    Map<String,Object> sampleMap = db.selectUnique( "select id,store_path,file_name,sample_name,nova,md5 from shca_project_cancer_file " +
                            " where sample_name=? and rawdata=? ", sampleName, rawData);
                    LOGGER.info( title + "database sample=" + sampleMap);
                    if (CollectionUtils.isEmpty( sampleMap)) {
                        long id = db.insert("insert into shca_project_cancer_file(sample_name,rawdata,file_name,file_size,store_path" +
                                        ",md5,create_username,create_time,update_username,update_time,modified_time,sample_type,cancer_kind,nova) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                                , sampleName, rawData, fileName, file.length(), file.getAbsolutePath(), md5
                                , "administrator", now, "administrator", now, lastModified, sampleType, cancerKind, nova);

                        LOGGER.info( title + " save in shca_project_cancer_file result: " + id);
                    } else {
                        int _nova = ((Number)sampleMap.get( "nova")).intValue();
                        LOGGER.info( title + "update cancer: " + nova + "|" + _nova);
                        if (nova >= _nova && !md5.equals( sampleMap.get( "md5"))) {
                            Map<String, Object> sample = new HashMap<>();

                            sample.put("file_name", fileName);
                            sample.put("nova", nova);
                            sample.put("md5", md5);
                            sample.put("file_size", file.length());
                            sample.put("store_path", file.getAbsolutePath());
                            sample.put("modified_time", lastModified);

                            Map<String, Object> ret = updateCancer(sample, sampleMap, false);
                            LOGGER.info( title + " updated cancer: " + ret);
                        }
                    }
                }
            }


            List<Map<String,Object>> files = db.select( "select a.id,b.nova from shca_ftp_file a " +
                    " inner join shca_project_cancer_file b on b.sample_name=a.sample_name and b.rawdata=a.rawdata " +
                    " where a.nova < b.nova and a.memo =? ", "系统识别成功");

            for (Map<String,Object> file : files) {
                db.update( "update shca_ftp_file set memo=?,update_username=? where id=? ", "样本号已过期", "FtpService-193", file.get( "id"));
            }

            LOGGER.info( "FTP Service completed.");

        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));
        }
    }

    private void monitorByUpdate(File dir) {
        try {
            // 1 FTP文件目录解析
            SysConfigManager config = SysConfigManager.getInstance();
            LOGGER.info( "1 FTP Update文件目录解析: " + dir.getAbsolutePath() + "[" + dir.listFiles().length + "]");
            Date now = new Date();
            for (File file : dir.listFiles()) {
                if (file.isFile()) {
                    String cmdOutput = Cmd.execFromError( "gzip -t " + file.getAbsolutePath());
                    if (cmdOutput.indexOf( "unexpected end of file") > 0) {
                        LOGGER.info( file.getAbsoluteFile() + " ftping...");
                        continue;
                    }


                    String fileName = file.getName();
                    String sampleName = null;
                    String sampleType = null;
                    String cancerKind = null;
                    String rawData = null;
                    int nova = -1;
                    Date lastModified = new Date( file.lastModified());

                    Matcher sampleName_Matcher = sampleName_Pattern.matcher(fileName);
                    Matcher R_Matcher = R_Pattern.matcher(fileName);
                    Matcher NOVA_Matcher = NOVA_Pattern.matcher(fileName);
                    if (sampleName_Matcher.find()) {
                        sampleName = sampleName_Matcher.group();
                        cancerKind = sampleName_Matcher.group(1);
                        sampleType = sampleName_Matcher.group(2);
                    }
                    while (R_Matcher.find()) {
                        rawData = R_Matcher.group();
                    }
                    if (NOVA_Matcher.find()) {
                        String _nova = NOVA_Matcher.group();
                        _nova = _nova.substring(4);
                        nova = ConvertUtils.integer(_nova);
                    }

                    File mergeDir = new File( config.getValue("workspace.merge_path") + "/" + ConvertUtils.date2Str( "yyyyMMdd") + "/" + sampleName);
                    mergeDir.mkdirs();
                    File _file = new File( mergeDir.getAbsolutePath() + "/" + file.getName());
//                    FileUtils.moveFiles( file, _file);
                    file.renameTo( _file);

                    String md5 = StringUtils.md5( _file);

                    int updated = db.update("update shca_ftp_file set is_deleted=?,update_time=?,update_username=?,file_name=?,store_path=?" +
                                    ",modified_time=?,sample_name=?,rawdata=?,nova=?,sample_type=?,cancer_kind=? where md5=? "
                            , false, now, "administrator", fileName, _file.getAbsolutePath(), lastModified, sampleName, rawData, nova, sampleType, cancerKind, md5);
                    LOGGER.info( _file.getName() + ":" + updated + "|" + _file.length() + "|" + lastModified.toLocaleString());
                    if (updated == 0) {
                        String memo = "系统识别成功";
                        db.insert("insert into shca_ftp_file(md5,directory,file_name,file_size,store_path,is_deleted,modified_time,sample_name" +
                                        " ,rawdata,nova,sample_type,cancer_kind,memo,create_username,create_time,update_username,update_time)" +
                                        " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", md5, "cat", fileName, _file.length(), _file.getAbsolutePath(), false
                                , lastModified, sampleName, rawData, nova, sampleType, cancerKind, memo, "administrator", now, "administrator", now);
                    }
                }
            }


            LOGGER.info( "FTP Service updated directory completed.");

        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));
        }
    }


    public Map<String,Object> updateCancer(Map<String,Object> sample, Map<String,Object> oldSample, boolean append) {
        int errCode = 1000;
        String errMsg = "样本替换失败: ";
        Map<String,Object> ret = new HashMap<>();
        Date now = new Date();
        try {
            String title = oldSample.get( "file_name") + "->" + sample.get( "") + "[" + append + "]: ";
            SysConfigManager config = SysConfigManager.getInstance();
            String recyclePath = config.getValue( "workspace.recycle_path") + "/samples/" + ConvertUtils.date2Str( now, "yyyyMMddHHmm");
            LOGGER.info( title + "create recycle dir=" + recyclePath);
            File recycleDir = new File( recyclePath);
            recycleDir.mkdirs();


            errCode = 1010;
            File sampleFile = new File( (String)oldSample.get( "store_path"));
            File _sampleFile = new File( recycleDir.getAbsolutePath() + "/" + oldSample.get( "file_name"));
//            FileUtils.moveFiles( sampleFile, _sampleFile);
            sampleFile.renameTo( _sampleFile);
            LOGGER.info( title + "move sample " + sampleFile.getAbsolutePath() + "->" + _sampleFile.getAbsolutePath() + " " + _sampleFile.exists());


            db.update( "update shca_ftp_file set memo=?,update_time=?,update_username=?,store_path=? where md5=? "
                    ,"样本已过期", now, "FtpService", _sampleFile.getAbsolutePath(), sample.get( "md5"));


            errCode = 1020;
            LOGGER.info( "update shca_project_cancer_file: " + oldSample.get( "id") + "|" + sample.get( "md5"));
            sample.put( "update_time", now);
            List<Object> sqlParams = new ArrayList<>();
            String sql = DbUtils.updateSql( "shca_project_cancer_file", sample, sqlParams) + " and id=? ";
            sqlParams.add( oldSample.get( "id"));
            db.update( sql, sqlParams.toArray( new Object[0]));

            errCode = 1030;
            Map<String,Object> cancer = db.selectUnique( "select id,sample_name,cromwell_workflow_id_0,cromwell_workflow_id_1" +
                    " ,cromwell_workflow_status_0,cromwell_workflow_status_1 " +
                    " from shca_project_cancer where sample_name=? ", oldSample.get( "sample_name"));
            if (!CollectionUtils.isEmpty( cancer)) {
                errCode = 1040;
                LOGGER.info( cancer.get( "sample_name") + "'Workflow[" + cancer.get("cromwell_workflow_id_0") + "]: " + cancer.get("cromwell_workflow_status_0"));
                if (!("Succeeded".equals( cancer.get("cromwell_workflow_status_0"))
                        || "Failed".equals( cancer.get("cromwell_workflow_status_0")))) {
                    AbortResponse abortResponse = client.send(new AbortRequest((String) cancer.get("cromwell_workflow_id_0")));
                    LOGGER.info( cancer.get( "sample_name") + "'Workflow abort[" + cancer.get("cromwell_workflow_id_0") + "]: " + abortResponse.getMessage());
                }

                errCode = 1050;
                LOGGER.info( cancer.get( "sample_name") + "'Workflow[" + cancer.get("cromwell_workflow_id_1") + "]: " + cancer.get("cromwell_workflow_status_1"));
                if (!("Succeeded".equals( cancer.get("cromwell_workflow_status_1"))
                        || "Failed".equals( cancer.get("cromwell_workflow_status_1")))) {
                    AbortResponse abortResponse = client.send(new AbortRequest((String) cancer.get("cromwell_workflow_status_1")));
                    LOGGER.info( cancer.get( "sample_name") + "'Workflow abort[" + cancer.get("cromwell_workflow_status_1") + "]: " + abortResponse.getMessage());
                }
                errCode = 1060;
                db.delete("delete from shca_project_cancer where id=? ", cancer.get( "id"));

                errCode = 1070;
                Map<String,Object> project = db.selectUnique("select id,project_no,cromwell_workflow_id_0,cromwell_workflow_id_1" +
                        " ,cromwell_workflow_status_0,cromwell_workflow_status_1 " +
                        " from shca_project_project where sample_name_0=? or sample_name_1=? ", cancer.get( "sample_name"), cancer.get( "sample_name"));
                if (!CollectionUtils.isEmpty( project)) {
                    boolean abort = false;
                    errCode = 1080;
                    LOGGER.info( project.get( "project_no") + "'Workflow[" + project.get("cromwell_workflow_id_0") + "]: " + project.get("cromwell_workflow_status_0"));
                    if (!("Succeeded".equals(project.get("cromwell_workflow_status_0"))
                            || "Failed".equals(project.get("cromwell_workflow_status_0")))) {
                        abort = true;
                        AbortResponse abortResponse = client.send(new AbortRequest((String) project.get("cromwell_workflow_id_0")));
                        LOGGER.info( project.get( "project_no") + "'Workflow abort[" + project.get("cromwell_workflow_id_0") + "]: " + abortResponse.getMessage());
                    }

                    errCode = 1090;
                    LOGGER.info( project.get( "project_no") + "'Workflow[" + project.get("cromwell_workflow_id_1") + "]: " + project.get("cromwell_workflow_status_1"));
                    if (!("Succeeded".equals(project.get("cromwell_workflow_status_1"))
                            || "Failed".equals(project.get("cromwell_workflow_status_1")))) {
                        abort = true;
                        AbortResponse abortResponse = client.send(new AbortRequest((String) project.get("cromwell_workflow_id_1")));
                        LOGGER.info( project.get( "project_no") + "'Workflow abort[" + project.get("cromwell_workflow_id_1") + "]: " + abortResponse.getMessage());
                    }

                    if (abort) {
                        errCode = 1100;
                        db.update( "update shca_project_project set status=?,cromwell_workflow_id_0=?,cromwell_workflow_id_1=?" +
                                        ",cromwell_workflow_status_0=?,cromwell_workflow_status_1=? where id=? "
                                , "wait_sample", null, null, null, null, project.get( "id"));
                    }
                }

            }

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return ret;
        } catch (Exception e) {
            LOGGER.error( LogUtils.toString( e));

            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            return ret;
        }
    }



    public static void main(String...args) {

//        System.out.println( new Date( 0).toLocaleString());

        String[] fileNames = new String[] {
                "R20038886-Nova277-BZ2002545B_combined_R2.fastq.gz",
//                "R20038886-Nova277-BZ2002545B_combined_R1.fastq.gz",
//                "R20038887-Nova278-BZ2002545K_combined_R2.fastq.gz",
//                "R20038887-Nova278-BZ2002545K_combined_R1.fastq.gz",
//                "R20038887-nova278-BZ2002545K_combined_R2.fastq.gz",
//                "R20034113-Nova244-B2002181B_combined_R1.fastq.gz",
//                "R20034113-Nova244-B2002181B_combined_R2.fastq.gz",
//                "R20038886-Nova277-B2002181Sbu_combined_R2.fastq.gz",
//                "R20058448-zhoupu057-CJ2003730B_combined_R1.fastq.gz",
//                "R21010770-zhoupu249-GZ2100131B01_combined_R1.fastq.gz",
//                "R21004324-zhoupu221-GI2005558B01_combined_R1.fastq.gz",
//                "R21013713-zhoupu272-AZ2100278S01_combined_R1.fastq.gz",
//                "R21016617-zhoupu281-AZ2100278B01_combined_R1.fastq.gz",
//                "R21016617-zhoupu281-AJ2100472B01_combined_R1.fastq.gz",
//                "R20027685-nova170-PZ2001356B_combined_R1.fastq.gz",
//                "R20027685-nova170-PZ2001356S_combined_R1.fastq.gz",
                "R21027867-zhoupu344-CJ6600001B_combined_R2.fastq.gz"
        };


        /**
         * BZ breast            乳腺癌535panel
         * CZ colon
         *
         * GZ gastric           胃癌559panel
         * HZ hepa              肝癌597panel
         * AZ pancreatic        胰腺panel
         * AJ peixipancreatic   胰腺（血液胚系） 单样本
         * PZ pan               泛癌种组织492panel
         * DZ fuke              妇科panel
         * GI gist              胃肠间质瘤
         * CJ peixicolon        肠癌（血液胚系） 单样本
         * HJ peixihepa         肝癌（血液胚系） 单样本
         * GJ peixigastric      胃癌（血液胚系） 单样本
         * IJ peixigist         胃肠间质瘤（血液胚系） 单样本
         * PJ peixictDNA        泛癌种（血液胚系） 单样本
         * DJ peixifuke         内膜癌/宫颈癌（血液胚系） 单样本
         *
         *
         *
         *
         *
         *
         **/


        Pattern pattern = Pattern.compile( "(AJ|BJ|B|BZ|C|CZ|G|GZ|H|HZ|P|PC|OH|PZ|GI|UT|LA|LB|LC|CJ|HJ|GJ|IJ|PJ|DJ|AZ|DZ|NZ|NJ|SZ|SJ|PM)[0-9]{7}(B|K|S|F|C)(bu){0,1}(M[a-zA-Z0-9]+){0,1}[0-9]*");
        Pattern pattern1 = Pattern.compile( "_(R(1|2)).");
        Pattern pattern2 = Pattern.compile( "[0-9]+");



        for (String fileName : fileNames) {

            if (fileName.endsWith( ".fq.gz")) {
                fileName = fileName.substring( 0, fileName.length() - 6) + ".fastq.gz";
            }


            Matcher matcher = pattern.matcher( fileName);
            Matcher matcher2 = pattern1.matcher( fileName);

            if (matcher.find()) {
//                System.out.println( matcher.groupCount() + "|" + matcher.group( 0) + "|" + matcher.group( 1) + "|" + matcher.group( 2));

                System.out.println( fileName + ": " + matcher.group());

            } else {
                System.out.println( fileName + ": Not Found");
            }
            while (matcher2.find()) {
                System.out.println( fileName + ": " + matcher2.group( 1));
            }


            String[] array = fileName.split( "-");
            if (array.length == 3) {
                String nova = array[1];
                Matcher matcher3 = pattern2.matcher( nova);
                if (matcher3.find()) {
                    System.out.println( matcher3.group());
                }

            }
        }
    }


}
