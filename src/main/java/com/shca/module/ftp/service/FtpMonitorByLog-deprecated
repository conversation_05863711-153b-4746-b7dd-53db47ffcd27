package com.shca.module.ftp.service;


import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.support.Db;
import com.ywang.utils.*;
import org.apache.log4j.Logger;

import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FtpMonitorByLog {

    private Db db;

    private long threshold;

    private final Pattern sampleName_Pattern;

    private final Pattern R_Pattern;

    private final Pattern NOVA_Pattern;

    private final static Logger LOGGER = Logger.getLogger( FtpMonitorByLog.class);

    public void setDb(Db db) {
        this.db = db;
    }

    public FtpMonitorByLog(Pattern sampleName_Pattern, Pattern R_Pattern, Pattern NOVA_Pattern) {
        this.sampleName_Pattern = sampleName_Pattern;
        this.R_Pattern = R_Pattern;
        this.NOVA_Pattern = NOVA_Pattern;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }


    public void sync() {
        SysConfigManager config = SysConfigManager.getInstance();
        File dir = new File( config.getValue("ftp.monitor_path"));

        monitor( dir);
    }

    public void monitor(File dir) {
        Map<String, FtpLog> ftpLogMap = new HashMap<>();
        String title = "FTP Monitor[" + dir.getAbsolutePath() + "]: ";
        Date now = new Date();

        LOGGER.info(  title + "ftp log count = " + dir.listFiles().length);
        for (File file : dir.listFiles()) {
            String logName = file.getName();

            if (file.isDirectory()) {
                continue;
            }

            Date timestamp = new Date(ConvertUtils.lon(logName.substring(0, 10)) * 1000);
            logName = logName.substring(11);
            String[] lines = FileUtils.read(file);
            String filePath = lines[0];


            if (logName.startsWith("CREATE")) {
                String fileName = logName.substring(7);

                FtpLog ftpLog = ftpLogMap.get(filePath);
                if (ftpLog == null) {
                    ftpLog = new FtpLog(fileName);
                    ftpLog.setStartTime(timestamp);

                    ftpLogMap.put(filePath, ftpLog);
                } else if (ftpLog.getStartTime() == null || timestamp.getTime() > ftpLog.getStartTime().getTime()) {
                    ftpLog.setStartTime(timestamp);
                }

                ftpLog.setFilePath(filePath);
                ftpLog.addLog(file);
            } else if (logName.startsWith("CLOSE_WRITE,CLOSE")) {
                FtpLog ftpLog = ftpLogMap.get(filePath);

                if (ftpLog == null) {
                    String fileName = logName.substring( 18);
                    ftpLog = new FtpLog(fileName);
                    ftpLog.setEndTime(timestamp);
                    ftpLog.setFilePath(filePath);

                    ftpLogMap.put(filePath, ftpLog);
                } else {
                    ftpLog.setEndTime(timestamp);
                }

                ftpLog.addLog( file);
            } else if (logName.startsWith("CLOSE_WRITE,CRACKED")) {
                FtpLog ftpLog = ftpLogMap.get(filePath);

                if (ftpLog == null) {
                    String fileName = logName.substring( 20);
                    ftpLog = new FtpLog(fileName);
                    ftpLog.setEndTime(timestamp);
                    ftpLog.setFilePath(filePath);

                    ftpLogMap.put(filePath, ftpLog);
                } else {
                    ftpLog.setEndTime(timestamp);
                }

                ftpLog.setDirectory( "cracked");
                ftpLog.addLog( file);
            }
        }

        LOGGER.info(  title + "cancer count = " + ftpLogMap.size() + ",ftp log count = " + dir.listFiles().length);
        for (FtpLog ftpLog : ftpLogMap.values()) {
            if (ftpLog.getEndTime() == null) {
                LOGGER.info(  title + ftpLog.getFilePath() + " continue...");
                continue;
            }
            if (now.getTime() - ftpLog.getEndTime().getTime() <= threshold) {
                System.out.println( threshold / DateUtils.ONE_MINUTE);
                System.out.println( ftpLog.getEndTime().toLocaleString() + "/" + now.toLocaleString());
                LOGGER.info(  title + ftpLog.getFilePath() + " waiting for " + (now.getTime() - ftpLog.getEndTime().getTime()) / DateUtils.ONE_MINUTE + " min");
                continue;
            }

            saveFtpLog( ftpLog);
        }

        // 创建单样本分析任务
        // 修改select sample_name from shca_project_cancer 为 select sample_name from shca_project_cancer where status='finished' or status='filed'
        SysConfigManager config = SysConfigManager.getInstance();
        List<Map<String, Object>> cancers = db.select("select id, sample_name, max(sample_type) sample_type, max(cancer_kind) cancer_kind " +
                " from shca_project_cancer_file a where status=? and sample_name not in (select sample_name from shca_project_cancer)" +
                " group by sample_name having count(*)=? order by sample_name "
                , "ready", 2);
        LOGGER.info(  title + "create cancer  = " + cancers.size());
        for (Map<String, Object> cancer : cancers) {
            try {
                String sampleName = (String) cancer.get("sample_name");
                String cancerKind = config.getValue("cancer_kind." + cancer.get("cancer_kind"));

                Map<String, Object> cancerMap = new HashMap<>();
                cancerMap.put("sample_name", sampleName);
                cancerMap.put("cancer_kind", cancerKind);
                cancerMap.put("status", "wait_running");
                cancerMap.put("create_time", now);
                cancerMap.put("create_username", "administrator");
                cancerMap.put("update_time", now);
                cancerMap.put("update_username", "administrator");
                cancerMap.put("workspace", config.getValue( "workspace.sample_path") + "/" + sampleName);

                List<Object> sqlParams = new ArrayList<>();
                String sql = DbUtils.insertSql( "shca_project_cancer", cancerMap, sqlParams);
                db.insert( sql, sqlParams.toArray(new Object[0]));
                db.update( "update shca_project_cancer_file set status=? where id=?", "successed", cancer.get("id"));
                db.update("update shca_ftp_file set store_path=(select max(store_path) from shca_project_cancer_file where shca_project_cancer_file.md5=shca_ftp_file.md5 and status=?)" +
                        " ,memo=?, update_time=? where sample_name=? and directory=?", "successed", "样本预处理", now, sampleName, "sample");
                
            } catch (Exception e) {
                LOGGER.error(LogUtils.toString(e));
            }
        }
    }

    private void saveFtpLog(FtpLog ftpLog) {
        String title = "Save FTP LOG[" + ftpLog.getFileName() + "]: ";
        SysConfigManager config = SysConfigManager.getInstance();
        LOGGER.info( title + ftpLog.getFilePath());
        File file = new File( ftpLog.getFilePath());
        Date now = new Date();
        LOGGER.info( title + "parsing...");
        try {
            if (file.exists()) {
                long fileSize = file.length();

                if (fileSize == 0) {
                    LOGGER.error( title + file.getAbsolutePath() + " file size is zero");
                    return;
                }

                String fileName = file.getName();
                Date lastModified = new Date(file.lastModified());
                String md5 = StringUtils.md5(file);

                String sampleName = null;
                String sampleType = null;
                String cancerKind = null;
                String rawData = null;
                int nova = -1;

                Matcher sampleName_Matcher = sampleName_Pattern.matcher(fileName);
                Matcher R_Matcher = R_Pattern.matcher(fileName);

                LOGGER.info(title + "match sample_name...");
                if (sampleName_Matcher.find()) {
                    sampleName = sampleName_Matcher.group();
                    cancerKind = sampleName_Matcher.group(1);
                    sampleType = sampleName_Matcher.group(2);
                }
                LOGGER.info(title + "match rawdata...");
                while (R_Matcher.find()) {
                    rawData = R_Matcher.group();
                }
                LOGGER.info(title + "match nova...");
                String[] array = fileName.split( "-");
                if (array.length == 3) {
                    String strNova = array[1];
                    Matcher nova_Matcher = NOVA_Pattern.matcher( strNova);
                    if (nova_Matcher.find()) {
                        nova = ConvertUtils.integer( nova_Matcher.group());
                    }

                }
                LOGGER.info( title + "sampleName=" + sampleName + ",rawData=" + rawData + "md5=" + md5 + ",sampleType=" + sampleType + ",cancerKind=" + cancerKind);
                String memo = "系统成功识别样本文件";
                boolean succeed = "sample".equals( ftpLog.getDirectory());

                if (StringUtils.isBlank(sampleName)) {
                    memo = "文件名称中缺少样本号信息";
                    if (succeed) {
                        ftpLog.setDirectory( "parse_error");
                    }
                } else if (StringUtils.isBlank(cancerKind)) {
                    memo = "系统未匹配项目类型";
                    if (succeed) {
                        ftpLog.setDirectory( "parse_error");
                    }
                } else if (StringUtils.isBlank(sampleType)) {
                    memo = "文件名称中缺少样本种类";
                    if (succeed) {
                        ftpLog.setDirectory( "parse_error");
                    }

                } else if (StringUtils.isBlank(rawData)) {
                    memo = "文件名称中缺少R1或R2信息";
                    if (succeed) {
                        ftpLog.setDirectory( "parse_error");
                    }
                }

                succeed = "sample".equals( ftpLog.getDirectory());
                boolean ifOverride = false;

                if (succeed) {
                    // 当系统中已存在sample+rawData时，判定为重复上传
                    Map<String, Object> cancer = db.selectUnique("select id,store_path,file_name,sample_name,nova,md5 from shca_project_cancer_file " +
                            " where sample_name=? and rawdata=? and status=? ", sampleName, rawData, "successed");
                    if (!CollectionUtils.isEmpty( cancer) && md5.equals((String) cancer.get("md5"))) {
                        memo = "样本文件重复上传";
                        ftpLog.setDirectory( "duplicate");
                    }
                    if (!CollectionUtils.isEmpty( cancer) && !md5.equals((String) cancer.get("md5"))) {
                        ifOverride = true;
                    }

                }
                /*succeed = "sample".equals( ftpLog.getDirectory());
                if (succeed) {
                    // 当系统中已存在MD5时，判定为重复上传
                    Map<String, Object> cancer = db.selectUnique("select id,store_path,file_name,sample_name,nova,md5 from shca_project_cancer_file " +
                            " where md5=? and status=? ", md5, "successed");
                    if (!CollectionUtils.isEmpty( cancer)) {
                        memo = "样本文件重复上传，与" + cancer.get( "file_name") + "文件MD5一致";
                        ftpLog.setDirectory( "duplicate");
                    }
                }*/

                succeed = "sample".equals( ftpLog.getDirectory());
                if (succeed) {
                    if (fileName.endsWith(".fq.gz")) {
                        fileName = fileName.substring(0, fileName.length() - 6) + ".fastq.gz";
                    }

                    File rawDataDir = new File(config.getValue("workspace.rawdata_path") + "/" + sampleName);
                    rawDataDir.mkdirs();
                    File renameFile = new File(rawDataDir.getAbsolutePath() + "/" + file.getName());
                    LOGGER.info("move file: " + file.getAbsolutePath() + " -> " + renameFile.getAbsolutePath());
                    String cmd = Cmd.exec("\\mv " + file.getAbsolutePath() + " " + renameFile.getAbsolutePath());

                    LOGGER.info( title + "cmd mv result: " + cmd);

                    file = renameFile;
                } else {
                    File errorDir = new File(config.getValue("ftp.upload_dir") + "/errorinfo/" + ftpLog.getDirectory() + "/" + ConvertUtils.date2Str("yyyyMMdd"));
                    errorDir.mkdirs();

                    File renameFile = new File( errorDir.getAbsolutePath() + "/" + file.getName());
                    LOGGER.info("move file: " + file.getAbsolutePath() + " -> " + renameFile.getAbsolutePath());
                    String cmd = Cmd.exec("mv " + file.getAbsolutePath() + " " + renameFile.getAbsolutePath());
                    LOGGER.info( title + "cmd mv result: " + cmd);

                    file = renameFile;
                }

                if(!file.exists()) {
                    LOGGER.error( title + file.getAbsolutePath() + " move error");
                    return;
                }

                if (ifOverride) {
                    db.update("update shca_ftp_file set md5=?, file_size=?, store_path=?, modified_time=?", md5, fileSize, file.getAbsolutePath(),lastModified);
                } else {
                    db.insert("insert into shca_ftp_file(md5,directory,file_name,file_size,store_path,is_deleted,modified_time,sample_name" +
                                    " ,rawdata,nova,sample_type,cancer_kind,memo,create_username,create_time,update_username,update_time,start_time,end_time)" +
                                    " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", md5, ftpLog.getDirectory(), fileName, fileSize, file.getAbsolutePath(), false
                            , lastModified, sampleName, rawData, nova, sampleType, cancerKind, memo, "FtpMonitorByLog"
                            , now, "FtpMonitorByLog", now, ftpLog.getStartTime(), ftpLog.getEndTime());
                }

                if (succeed) {
                    saveCancer( sampleName, rawData, sampleType, cancerKind, nova, file, md5, "FtpMonitorByLog");

                }
            } else {
                if (file.length() == 0) {
                    LOGGER.error( title + "文件I/O异常");
                } else {
                    LOGGER.error( title + "文件不存在");
                }
            }

            for (File logFile : ftpLog.getLogs()) {
                File logDir = new File( config.getValue("workspace.ftplog_path") + "/" + ConvertUtils.date2Str( "yyyyMMdd") + "/");
                logDir.mkdirs();

                File renameFile = new File( logDir.getAbsolutePath() + "/" + logFile.getName());
                LOGGER.info( title + "log file move " + logFile.getAbsolutePath() + " -> " + renameFile.getAbsolutePath());
                logFile.renameTo( renameFile);
            }
        } catch (Exception e) {
            LOGGER.error( title + LogUtils.toString( e));
        }
    }

    private void saveCancer(String sampleName, String rawData, String sampleType, String cancerKind, int nova, File file, String md5,String userName) {
        String title = "Save Cancer[" + file.getName() + "]: ";
        Date now = new Date();

        // if already exist, expired
        db.update( "update shca_project_cancer_file set status=? where sample_name=? and rawdata=? and status=? "
            ,"expired", sampleName, rawData, "successed");

        long id = db.insert("insert into shca_project_cancer_file(sample_name,rawdata,file_name,file_size,store_path" +
                        ",md5,create_username,create_time,update_username,update_time,modified_time,sample_type,cancer_kind,nova,status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
                , sampleName, rawData, file.getName(), file.length(), file.getAbsolutePath(), md5
                , userName, now, userName, now, new Date(file.lastModified()), sampleType, cancerKind, nova, "ready");

        LOGGER.info( title + "shca_project_cancer_file.id = " + id);
    }


    public static void main(String...args) {
        String fileName = "1616590396 CLOSE_WRITE,CRACKED 1_fastq.gz";
        fileName = fileName.substring(11);

        System.out.println( fileName.substring( 20));


    }

    public List<String> manual() {
        SysConfigManager config = SysConfigManager.getInstance();
        File dir = new File(config.getValue("ftp.upload_dir"));
        String title = "Manual FTP[" + dir.getAbsolutePath() + "]: ";
        Date now = new Date();

        List<String> ret = new ArrayList<>();
        for (File file : dir.listFiles()) {
            String fileName = file.getName();
            Date lastModified = new Date(file.lastModified());

            if (now.getTime() - lastModified.getTime() <= threshold) {
                LOGGER.info( title + file.getAbsolutePath() + " waiting for " + (now.getTime() - lastModified.getTime()) / DateUtils.ONE_MINUTE + " min");
                continue;
            }

            String cmdOutput = Cmd.execFromError("gzip -t " + file.getAbsolutePath());
            if (cmdOutput.indexOf("unexpected end of file") > 0) {
                LOGGER.info(title + fileName + " is uploading...");
                continue;
            }

            String sampleName = null;
            String sampleType = null;
            String cancerKind = null;
            String rawData = null;
            int nova = -1;

            Matcher sampleName_Matcher = sampleName_Pattern.matcher(fileName);
            Matcher R_Matcher = R_Pattern.matcher(fileName);
            Matcher NOVA_Matcher = NOVA_Pattern.matcher(fileName);
            LOGGER.info(title + "match sample_name...");
            if (sampleName_Matcher.find()) {
                sampleName = sampleName_Matcher.group();
                cancerKind = sampleName_Matcher.group(1);
                sampleType = sampleName_Matcher.group(2);
            }
            LOGGER.info(title + "match rawdata...");
            while (R_Matcher.find()) {
                rawData = R_Matcher.group();
            }
            LOGGER.info(title + "match nova...");
            if (NOVA_Matcher.find()) {
                String _nova = NOVA_Matcher.group();
                _nova = _nova.substring(4);
                nova = ConvertUtils.integer(_nova);
            }
            String md5 = StringUtils.md5(file);
            LOGGER.info(title + "sampleName=" + sampleName + ",rawData=" + rawData + "md5=" + md5 + ",sampleType=" + sampleType + ",cancerKind=" + cancerKind);

            String memo = "样本识别成功";
            boolean success = true;
            if (StringUtils.isBlank(sampleName)) {
                memo = "样本号识别失败: sample_name";
                success = false;
            }
            if (StringUtils.isBlank(rawData)) {
                memo = "样本号识别失败: raw_data";
                success = false;
            }
            if (StringUtils.isBlank(cancerKind)) {
                memo = "样本号识别失败: cancer_kind";
                success = false;
            }
            if (StringUtils.isBlank(sampleType)) {
                memo = "样本号识别失败: sample_type";
                success = false;
            }

            if (success) {
                if (fileName.endsWith(".fq.gz")) {
                    fileName = fileName.substring(0, fileName.length() - 6) + ".fastq.gz";
                }

                File rawDataDir = new File(config.getValue("workspace.rawdata_path") + "/" + ConvertUtils.date2Str("yyyyMMdd") + "/" + sampleName);
                rawDataDir.mkdirs();
                File renameFile = new File(rawDataDir.getAbsolutePath() + "/" + file.getName());
                LOGGER.info("move file: " + file.getAbsolutePath() + " -> " + renameFile.getAbsolutePath());
                FileUtils.moveFiles(file, renameFile);
                file = renameFile;
            }


            db.insert("insert into shca_ftp_file(md5,directory,file_name,file_size,store_path,is_deleted,modified_time,sample_name" +
                            " ,rawdata,nova,sample_type,cancer_kind,memo,create_username,create_time,update_username,update_time,start_time,end_time)" +
                            " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ", md5, "sample", fileName, file.length(), file.getAbsolutePath(), false
                    , lastModified, sampleName, rawData, nova, sampleType, cancerKind, memo, "manual"
                    , now, "manual", now, null, lastModified);

            if (success) {
                saveCancer(sampleName, rawData, sampleType, cancerKind, nova, file, md5, "manual");
                ret.add( fileName);
            }
        }

        return ret;
    }
}

class FtpLog {

    private final String fileName;

    private Date startTime;

    private Date endTime;

    private String filePath;

    private String directory = "sample";

    private List<File> logs = new ArrayList<>();

    public FtpLog(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public void addLog(File log) {
        logs.add( log);
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<File> getLogs() {
        return logs;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }
}

