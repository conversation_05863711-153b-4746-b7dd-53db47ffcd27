/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-20 16:30:20
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 16:30:23
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/ftp/service/Java8WatchService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.ftp.service;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Java8WatchService {

    static Logger log = LoggerFactory.getLogger(Java8WatchService.class);

    private final WatchService watchService;
    final Map<WatchKey, Path> keys = new HashMap<>();
    final Boolean recursive ;

    public Java8WatchService(Boolean recursive) throws IOException {
        this.watchService = FileSystems.getDefault().newWatchService();
        this.recursive = recursive;
    }
    public Java8WatchService(Path dir) throws IOException {
        this(dir, Boolean.TRUE);
    }

    public Java8WatchService(List<Path> dirs, Boolean recursive) throws IOException {
        this(recursive);
        for (Path dir : dirs) {
            registerDirectory(dir);
        }
    }
    public Java8WatchService(Path dir, Boolean recursive) throws IOException {
        this(recursive);
        registerDirectory(dir);
    }

    private void registerDirectory(Path dir) throws IOException {
        if (Boolean.TRUE.equals(this.recursive)) {
            walkAndRegisterDirectories(dir);
        } else {
            registerDirectories(dir);
        }
    }

    void registerDirectories(Path dir) throws IOException {
        log.info("registering direcotry: {}", dir);
        WatchKey key = dir.register(watchService,
            java.nio.file.StandardWatchEventKinds.ENTRY_CREATE);
            // java.nio.file.StandardWatchEventKinds.ENTRY_MODIFY
        log.info("put key and dir into map: key: {} dir: {}" ,key ,dir);
        this.keys.put(key, dir);
    }
    private void walkAndRegisterDirectories(final Path startDir) throws IOException {
        try (Stream<Path> path = Files.walk(startDir)) {
            path.filter(Files::isDirectory).forEach(
                child -> {
                    try {
                        registerDirectories(child);
                    } catch (IOException e) {
                        log.error("error registering directory: {}", child, e);
                    }
                }
            );
        }
    }

    void processEvents(BiConsumer<Path, WatchEvent.Kind<?>> processor) throws RuntimeException, IOException {
        // key circulation for watch key here
        for(;;) {
            WatchKey key = getKeyFromWatchService(this.watchService)
                .orElseThrow(() -> new RuntimeException("watch service is closed"));
            processEventKey(key, processor);
            boolean valid = key.reset();
            if (!valid) {
                keys.remove(key);
                if (keys.isEmpty()) {
                    break;
                }
            }
        }
    }

    private Optional<WatchKey> getKeyFromWatchService(WatchService watchServiceParam) {
        WatchKey key;
        try {
            key = watchServiceParam.take();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Optional.empty();
        }
        if (key == null) {
            return Optional.empty();
        }
        return Optional.of(key);
    }

    private void processEventKey(WatchKey key, BiConsumer<Path, WatchEvent.Kind<?>> processor) throws IOException {
        Path dir = keys.get(key);
        if (Objects.nonNull(dir)) {
            for(WatchEvent<?> event : key.pollEvents()) {
                WatchEvent.Kind<?> kind = event.kind();
                if (kind == java.nio.file.StandardWatchEventKinds.OVERFLOW) {
                    log.error("overflow event");
                    continue;
                }
                String kindName = kind.name();
                @SuppressWarnings("unchecked")
                Path name = ((WatchEvent<Path> )event).context();
                Path child = dir.resolve(name);
                log.info("processing: event kind: {} -> file name: {} " , kindName, child);
                processor.accept(child, kind);

                if (Boolean.TRUE.equals(this.recursive)
                    && kind == java.nio.file.StandardWatchEventKinds.ENTRY_CREATE
                    && child.toFile().isDirectory()) {
                    try {
                        walkAndRegisterDirectories(child);
                    } catch (IOException e) {
                        log.error("error registering directory: {}", child, e);
                    }
                }
            }
        }
    }
}