/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-20 15:19:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 20:45:12
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/ftp/service/FtpEventMonitor.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.ftp.service;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.WatchEvent;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

// @Component
public class FtpEventMonitor implements ApplicationListener<ContextRefreshedEvent> {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(FtpEventMonitor.class);
    @Resource FtpEventProcessor ftpProcessor;
    // @Value("#{variableBean.dirs}")
    List<String> monitoredDirs = Arrays.asList("/mydata/shca/logftp");
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        System.out.println("FtpEventMonitor.onApplicationEvent");
        List<Path> monitoredPaths = monitoredDirs.stream().map(Paths::get).collect(Collectors.toList());
        new Thread( () -> {
            try {
                new Java8WatchService(monitoredPaths, Boolean.TRUE)
                        .processEvents(processor);
            } catch (IOException | RuntimeException e) {
                log.error("inotify error", e);
            }
        }).start();
    }

    BiConsumer<Path, WatchEvent.Kind<?>> processor = (path, kind) -> {
        ftpProcessor.processEventPath(path);
    };
}
