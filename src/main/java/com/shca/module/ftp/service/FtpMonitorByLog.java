/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-14 11:52:54
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-21 00:15:23
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/ftp/service/FtpMonitorByLog.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.ftp.service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.log4j.Logger;

import com.shca.module.ftp.support.FtpLogSupport;
import com.shca.config.SystemConfigService;
import com.shca.util.DbService;

public class FtpMonitorByLog {

    @Resource private DbService db;
    @Resource private SystemConfigService config;
    private long threshold;
    private final Pattern sampleName_Pattern;
    private final Pattern R_Pattern;
    private final Pattern NOVA_Pattern;
    private final static Logger LOGGER = Logger.getLogger( FtpMonitorByLog.class);

    public HashMap<String, Pattern> getPatternMap() {
        HashMap<String, Pattern> patternMap = new HashMap<>();
        patternMap.put("sampleName", sampleName_Pattern);
        patternMap.put("reads", R_Pattern);
        patternMap.put("nova", NOVA_Pattern);
        return patternMap;
    }

    public FtpMonitorByLog(Pattern sampleName_Pattern, Pattern R_Pattern, Pattern NOVA_Pattern) {
        this.sampleName_Pattern = sampleName_Pattern;
        this.R_Pattern = R_Pattern;
        this.NOVA_Pattern = NOVA_Pattern;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }

    public void sync() {
        LOGGER.info("FTP Monitoring Start...");
        // SysConfigManager config = SysConfigManager.getInstance();
        File dir = new File( config.getValue("ftp.monitor_path"));
        LOGGER.info("Monitoring dir -> " + dir.getAbsolutePath());
        Map<String, List<Map<String, Object>>> readyCancerList = FtpLogSupport.checkCancerRecord(db);
        LOGGER.info("Ready cancer list -> " + readyCancerList.toString());
        FtpLogSupport.createSingleSampleRecordByCheckResult(readyCancerList, db, config);
        monitor( dir, config);
    }

    private void monitor(File dir, SystemConfigService config) {
            String title = "FTP Monitoring... [" + dir.getAbsolutePath() + "]: ";
            File[] targetLogFiles = dir.listFiles();
            LOGGER.info(  title + "ftp log count = " + targetLogFiles.length);
            Map<String, HashMap<String, Object>> sampleNameObjectMap = new HashMap<>();

            Map<String, FtpLog> ftpLogMap = FtpLogSupport.parseLogFiles(targetLogFiles);
            LOGGER.info(  title + "cancer count = " + ftpLogMap.size() + ", ftp log count = " + targetLogFiles.length);

            
            for (FtpLog ftpLog : ftpLogMap.values()) {
                if (!FtpLogSupport.ftpLogValidate(ftpLog, threshold)) {
                    continue;
                }
                HashMap<String, Object> ftpFastqFileParseMap = FtpLogSupport.parseFastqFileByFtpLog(ftpLog, this.getPatternMap(), db);
                if (ftpFastqFileParseMap.isEmpty()) {
                    continue;
                }
                sampleNameObjectMap.put(
                    (String)ftpFastqFileParseMap.get("sample_name"), ftpFastqFileParseMap);

                String storePath = FtpLogSupport.moveFastqFile(ftpFastqFileParseMap, ftpLog, config);
                if (storePath == null) {
                    continue;
                }
                ftpFastqFileParseMap.put("store_path", (Object) storePath);
                FtpLogSupport.createFtpFileRecord(ftpFastqFileParseMap, db);
                // FtpLogSupport.updateCancerFileRecordFileInfo(ftpFastqFileParseMap,db);
                if (!FtpLogSupport.fastqExists(ftpFastqFileParseMap, db) ) {
                    FtpLogSupport.createCancerFileRecord(ftpFastqFileParseMap, db);
                    FtpLogSupport.updateCancerFileRecordFileInfo(ftpFastqFileParseMap, db);
                } else {
                    FtpLogSupport.updateCancerFileRecordFileInfo(ftpFastqFileParseMap, db);
                    if(FtpLogSupport.shouldUpdate(ftpFastqFileParseMap, db)){
                        FtpLogSupport.updateFtpFileRecord(ftpFastqFileParseMap, db);
                    }
                }
                FtpLogSupport.saveFtpLogFile(ftpLog, config);
            }

            FtpLogSupport.createSingleSampleRecordBySampleName(sampleNameObjectMap, config, db);

    }
}


