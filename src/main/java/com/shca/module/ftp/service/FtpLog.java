/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-27 00:11:30
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-20 23:58:28
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/ftp/service/FtpLog.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.ftp.service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FtpLog {

    private final String fileName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String filePath;
    private String directory = "sample";
    private List<File> logs = new ArrayList<>();

    public FtpLog(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public void addLog(File log) {
        logs.add( log);
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<File> getLogs() {
        return logs;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }
}
