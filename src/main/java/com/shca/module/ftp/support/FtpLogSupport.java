package com.shca.module.ftp.support;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.shca.module.ftp.service.FtpLog;
import com.shca.support.DbSysConfigManager;
import com.shca.support.SysConfigManager;
import com.shca.util.ConvertUtils;
import com.shca.util.StringUtilsRefact;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.LongCallback;
import com.ywang.utils.Cmd;
import com.ywang.utils.DbUtils;

@SuppressWarnings("unchecked")
public class FtpLogSupport {
    private static final Logger LOGGER = Logger.getLogger(FtpLogSupport.class);

    public static final Map<String, FtpLog> parseLogFiles(File[] logFiles) {
        Map<String, FtpLog> ftpLogMap = new HashMap<>();
        for (File logFile : logFiles) {
            LOGGER.info("parsing ftp log file: " + logFile.getAbsolutePath());
            if (logFile.isDirectory()) {
                LOGGER.info(logFile + "is directory, skip and continue...");
                continue;
            }
            String logFileName = logFile.getName();
            Long timestamp = Long.parseLong(logFileName.substring(0, 10)) * 1000;
            LocalDateTime logFileDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp),
                    ZoneId.systemDefault());
            String logContent = logFileName.substring(11);
            String sourceFilePath = null;
            try {
                sourceFilePath = Files.readAllLines(logFile.toPath(), StandardCharsets.UTF_8).get(0);
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (!StringUtils.hasLength(sourceFilePath)) {
                continue;
            }
            // check if already in ftpLogMap
            FtpLog ftpLog = ftpLogMap.get(sourceFilePath);
            String sourceFileName = logContent.split(" ")[1];
            // new ftp log file
            if (ftpLog == null) {
                ftpLog = new FtpLog(sourceFileName);
                ftpLog.setStartTime(logFileDateTime);
                ftpLog.setFilePath(sourceFilePath);
            }

            if (logContent.startsWith("CREATE") && logFileDateTime.isAfter(ftpLog.getStartTime())) {
                // update the time by new log record, 相同文件再次上传，更新文件上传时间和文件地址
                if (ftpLog.getStartTime() == null || logFileDateTime.isAfter(ftpLog.getStartTime())) {
                    ftpLog.setStartTime(logFileDateTime);
                }
            }

            if (logContent.startsWith("CLOSE_WRITE,CLOSE")) {
                ftpLog.setEndTime(logFileDateTime);
            }
            if (logContent.startsWith("MOVED_TO")) {
                ftpLog.setStartTime(logFileDateTime);
                ftpLog.setEndTime(logFileDateTime);
            }
            if (logContent.startsWith("CLOSE_WRITE,CRACKED")) {
                ftpLog.setEndTime(logFileDateTime);
                ftpLog.setDirectory("cracked");
            }
            ftpLog.addLog(logFile);
            ftpLogMap.put(sourceFilePath, ftpLog);
        }
        return ftpLogMap;
    }

    public static HashMap<String, Object> parseFastqFileByFtpLog(
            FtpLog ftpLog, HashMap<String, Pattern> patternMap, Db db) {

        HashMap<String, Object> ftpFileMap = parseFastqFileInfo(ftpLog);
        if (ftpFileMap.isEmpty()) {
            return ftpFileMap;
        }
        ftpFileMap = parsingFileNameByFastqFileName(ftpFileMap, patternMap.get("sampleName"));
        ftpFileMap = parsingReadInfoByFastqFileName(ftpFileMap, patternMap.get("reads"));
        ftpFileMap = parsingNovaInfoByFastqFileName(ftpFileMap, patternMap.get("nova"));
        ftpFileMap = prepareMemoText(ftpFileMap, db);
        ftpLog = setFtpLogDirectoryByMemo(ftpLog, ftpFileMap);
        ftpFileMap.put("directory", ftpLog.getDirectory());
        return ftpFileMap;
    }

    private static HashMap<String, Object> parseFastqFileInfo(FtpLog ftpLog) {
        HashMap<String, Object> ftpFileMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();

        if (ftpLog == null || ftpLog.getFilePath() == null) {
            LOGGER.info("ftp log is null or file path is null, ignore...");
            return ftpFileMap;
        }
        LOGGER.info("parsing ftp log ...  " + ftpLog.getFilePath());
        String fastqFilePath = ftpLog.getFilePath();
        File fastqFile = new File(fastqFilePath);
        long fastqFileSize = fastqFile.length();
        if (!fastqFile.exists() || fastqFileSize == 0) {
            LOGGER.error(fastqFilePath + " -> source file does not exist or is 0, ignoring...");
            return ftpFileMap;
        }

        ftpFileMap.put("file_name", fastqFile.getName());
        ftpFileMap.put("file_size", fastqFileSize);
        ftpFileMap.put("sample_type", fastqFile.getName().substring(10, 11));
        ftpFileMap.put("md5", StringUtilsRefact.md5(fastqFile));
        LocalDateTime modifiedDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(fastqFile.lastModified()),
                ZoneId.systemDefault());
        ftpFileMap.put("modified_time", modifiedDateTime);
        ftpFileMap.put("start_time", ftpLog.getStartTime());
        ftpFileMap.put("end_time", ftpLog.getEndTime());
        ftpFileMap.put("create_username", "FtpMonitorByLog");
        ftpFileMap.put("create_time", now);
        ftpFileMap.put("update_username", "FtpMonitorByLog");
        ftpFileMap.put("update_time", now);
        ftpFileMap.put("is_deleted", 0);
        LOGGER.info("parsing ftp log done.");
        return ftpFileMap;
    }

    private static HashMap<String, Object> parsingFileNameByFastqFileName(HashMap<String, Object> resultMap,
            Pattern sampleNamePattern) {

        String fastqFileName = "";
        if (resultMap != null && resultMap.containsKey("file_name")) {
            fastqFileName = (String) resultMap.get("file_name");
        } else {
            return resultMap;
        }
        LOGGER.info("[" + fastqFileName + "] matching sample name info...");
        Matcher sampleNameMatcher = sampleNamePattern.matcher(fastqFileName);
        if (sampleNameMatcher.find()) {
            String sampleName = sampleNameMatcher.group(); // BZ23012345B01
            String cancerKindPrefix = sampleNameMatcher.group(1); // BZ
            String sampleType = sampleNameMatcher.group(2); // B or K
            LOGGER.info("matched: sample name -> " + sampleName);
            resultMap.put("sample_name", sampleName);
            resultMap.put("cancer_kind", cancerKindPrefix);
            resultMap.put("sample_type", sampleType);
        } else {
            LOGGER.error("sample name not matched ");
            return resultMap;
        }
        return resultMap;
    }

    private static HashMap<String, Object> parsingReadInfoByFastqFileName(HashMap<String, Object> resultMap,
            Pattern readsPattern) {

        String fastqFileName = "";
        if (resultMap != null && resultMap.containsKey("file_name")) {
            fastqFileName = (String) resultMap.get("file_name");
        } else {
            return resultMap;
        }
        LOGGER.info("[" + fastqFileName + "] match reads number...");
        Pattern pattern = Pattern.compile("(R1|R2)\\.");
        Matcher R_Matcher = pattern.matcher(fastqFileName);
        if (R_Matcher.find()) {
            String reads = R_Matcher.group(1); // R1 or R2
            LOGGER.info("matched: reads -> " + reads);
            resultMap.put("rawdata", reads);
        } else {
            LOGGER.error("reads not matched ");
            return resultMap;
        }
        return resultMap;
    }

    private static HashMap<String, Object> parsingNovaInfoByFastqFileName(HashMap<String, Object> resultMap,
            Pattern novaPattern) {

        String fastqFileName = "";
        if (resultMap != null && resultMap.containsKey("file_name")) {
            fastqFileName = (String) resultMap.get("file_name");
        } else {
            return resultMap;
        }
        LOGGER.info("[" + fastqFileName + "] matching nova...");
        String[] array = fastqFileName.split("-");
        int novaInt = -1;
        if (array.length == 3) {
            String strNova = array[1];
            Matcher novaMatcher = novaPattern.matcher(strNova);
            if (novaMatcher.find()) {
                novaInt = ConvertUtils.integer(novaMatcher.group());
            }
        }
        resultMap.put("nova", novaInt);
        return resultMap;
    }

    private static HashMap<String, Object> prepareMemoText(HashMap<String, Object> resultMap,
            Db db) {

        String memo = "";
        String infoSeperator = "; ";
        if (StringUtils.hasLength((String) resultMap.get("sample_name"))) {
            memo += "文件名称中缺少样本号信息" + infoSeperator;
        }
        if (StringUtils.hasLength((String) resultMap.get("cancer_kind"))) {
            memo += "系统未匹配项目类型" + infoSeperator;
        }
        if (StringUtils.hasLength((String) resultMap.get("sample_type"))) {
            memo += "文件名称中缺少样本种类" + infoSeperator;
        }
        if (StringUtils.hasLength((String) resultMap.get("rawdata"))) {
            memo += "文件名称中缺少Reads信息" + infoSeperator;
        }
        if (memo.isEmpty()) {
            memo = "系统成功识别样本文件";
        }
        if (FtpLogSupport.fastqExists(resultMap, db) && !FtpLogSupport.shouldUpdate(resultMap, db)) {
            memo = "样本文件重复上传";
        }
        resultMap.put("memo", memo);
        return resultMap;
    }

    private static FtpLog setFtpLogDirectoryByMemo(FtpLog ftpLog,
            HashMap<String, Object> resultMap) {

        String memo = "";
        if (resultMap != null && resultMap.containsKey("memo")) {
            memo = (String) resultMap.get("memo");
        } else {
            return ftpLog;
        }
        if ("样本文件重复上传".equals(memo)) {
            ftpLog.setDirectory("duplicate");
        }
        if (!"系统成功识别样本文件".equals(memo)) {
            LOGGER.error(ftpLog.getFileName() + "error, and specific reason is" + memo);
            ftpLog.setDirectory("parse_error");
        }

        return ftpLog;
    }

    public static String moveFastqFile(Map<String, Object> fastqFileMap,
            FtpLog ftpLog, SysConfigManager config) {

        String logTitle = "Moving fastq file to target path: ";
        Path targetDir = null;
        String fastqFileName = (String) fastqFileMap.get("file_name");
        String sampleName = (String) fastqFileMap.get("sample_name");
        String fastqFilePath = ftpLog.getFilePath();
        boolean parseSucceeded = "sample".equals(ftpLog.getDirectory());
        if (parseSucceeded) {
            // file name unification
            if (fastqFileName.endsWith(".fq.gz")) {
                fastqFileName = fastqFileName.substring(0, fastqFileName.length() - 6) + ".fastq.gz";
            }
            targetDir = Paths.get(
                    config.getValue("workspace.rawdata_path"),
                    sampleName);
        } else {
            targetDir = Paths.get(
                    config.getValue("ftp.upload_dir"),
                    "errorinfo",
                    ftpLog.getDirectory(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }
        Path targetFastqFilePath = targetDir.resolve(fastqFileName);
        LOGGER.info("moving file: " + fastqFilePath + " -> " + targetFastqFilePath);
        try {
            targetDir.toFile().mkdirs();
            File sourceFastqFile = new File(fastqFilePath);
            if (!sourceFastqFile.exists()) {
                LOGGER.error(logTitle + "source file does not exist");
                return null;
            }
            String cmd = Cmd.exec("mv " + sourceFastqFile + " " + targetFastqFilePath.toString());
            LOGGER.info(" cmd mv result: " + cmd);
            LOGGER.info("Moved Sucessfully");
        } catch (Exception e) {
            LOGGER.error(logTitle + "moving fastq file to target path error");
            e.printStackTrace();
        }
        LOGGER.info("returned -> " + targetFastqFilePath.toString());
        return targetFastqFilePath.toString();
    }

    public static void createSingleSampleRecordBySampleName(
            Map<String, HashMap<String, Object>> sampleNameObjectMap, SysConfigManager config, Db db) {

        LOGGER.info("creating single sample records, count is : " + sampleNameObjectMap.size());
        for (Entry<String, HashMap<String, Object>> sampleEntry : sampleNameObjectMap.entrySet()) {
            String sampleName = sampleEntry.getKey();
            LOGGER.info("creating single sample record of :" + sampleName);
            long fastqRecordCount = getSampleCountFromProjectCancerFile(sampleName, db);
            LOGGER.info("shca_project_cancer record count of " + sampleName + " is : " + fastqRecordCount);
            if (fastqRecordCount == 2) {
                HashMap<String, Object> sampleHashMap = sampleEntry.getValue();
                HashMap<String, Object> resultMap = removeEntriesFromSampleHashMap(sampleHashMap);
                resultMap = fillCancerKind2SampleHashMap(resultMap, config);
                resultMap = fillWorkspace2SampleHashMap(resultMap, config);
                resultMap = fillDefaultEntries2SampleHashMap(resultMap);
                createProjectCancerRecord(resultMap, db);
                // updateCancerFileRecordStatus(sampleHashMap, "ready", "successed", db);
                updateCancerFileRecordStatus(sampleName, "R1", "ready", "successed", db);
                updateCancerFileRecordStatus(sampleName, "R2", "ready", "successed", db);
                updateFtpFileRecordByMd5(sampleName, db);
            }
        }
    }

    public static void createSingleSampleRecordByCheckResult(Map<String, List<Map<String, Object>>> readyCancerList,
            Db db, SysConfigManager config) {

        for (String sampleName : readyCancerList.keySet()) {
            List<Map<String, Object>> cancerRecordList = readyCancerList.get(sampleName);
            Map<String, Object> cancerFileRecord = cancerRecordList.get(0);
            HashMap<String, Object> cancerRecordInfoMap = null;
            if ("R1".equals(cancerFileRecord.get("rawdata"))) {
                cancerRecordInfoMap = getSingleSampleRecordMapByCheckResult(
                        sampleName, cancerFileRecord, cancerRecordList.get(1), config);
            } else {
                cancerRecordInfoMap = getSingleSampleRecordMapByCheckResult(
                        sampleName, cancerRecordList.get(1), cancerFileRecord, config);
            }
            createProjectCancerRecord(cancerRecordInfoMap, db);
            updateCancerFileRecordStatus(sampleName, "R1", "ready", "successed", db);
            updateCancerFileRecordStatus(sampleName, "R2", "ready", "successed", db);
        }
    }

    public static HashMap<String, Object> getSingleSampleRecordMapByCheckResult(String sampleName,
            Map<String, Object> R1Map, Map<String, Object> R2Map, SysConfigManager config) {

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("sample_name", sampleName);
        resultMap.put("cancer_kind", config.getValue("cancer_kind." + R1Map.get("cancer_kind")));
        resultMap.put("workspace", config.getValue("workspace.sample_path") + "/" + sampleName);
        resultMap.put("create_time", LocalDateTime.now());
        resultMap.put("update_time", LocalDateTime.now());
        resultMap.put("status", "wait_running");
        resultMap.put("create_username", "administrator");
        resultMap.put("update_username", "administrator");
        return resultMap;
    }

    public static void createSingleSampleRecordBySampleNameStream(
            Map<String, HashMap<String, Object>> sampleNameObjectMap, DbSysConfigManager config, Db db) {

        LOGGER.info("creating single sample records, count is : " + sampleNameObjectMap.size());
        sampleNameObjectMap.entrySet().stream()
                .filter(entry -> getSampleCountFromProjectCancerFile(entry.getKey(), db) == 2)
                .map(entry -> entry.getValue())
                .forEach(hashMap -> {
                    LOGGER.info("creating single sample record of :" + hashMap.get("sample_name"));
                    HashMap<String, Object> resultMap = removeEntriesFromSampleHashMap(hashMap);
                    resultMap = fillCancerKind2SampleHashMap(resultMap, config);
                    resultMap = fillWorkspace2SampleHashMap(resultMap, config);
                    resultMap = fillDefaultEntries2SampleHashMap(resultMap);
                    createProjectCancerRecord(resultMap, db);
                    updateFtpFileRecordByMd5((String) resultMap.get("sample_name"), db);
                });
    }

    private static long getSampleCountFromProjectCancerFile(String sampleName, Db db) {

        return db.selectUnique(
                "select count(*) from shca_project_cancer_file where sample_name=? and status=?",
                new LongCallback(), sampleName, "ready");
    }

    private static HashMap<String, Object> removeEntriesFromSampleHashMap(HashMap<String, Object> sampleMap) {

        HashMap<String, Object> resultHashMap = (HashMap<String, Object>) sampleMap.clone();
        resultHashMap.remove("file_name");
        resultHashMap.remove("start_time");
        resultHashMap.remove("end_time");
        resultHashMap.remove("memo");
        resultHashMap.remove("directory");
        resultHashMap.remove("sample_type");
        resultHashMap.remove("file_size");
        resultHashMap.remove("store_path");
        resultHashMap.remove("is_deleted");
        resultHashMap.remove("modified_time");
        resultHashMap.remove("rawdata");
        resultHashMap.remove("md5");
        resultHashMap.remove("nova");
        return resultHashMap;
    }

    private static HashMap<String, Object> fillCancerKind2SampleHashMap(HashMap<String, Object> sampleMap,
            SysConfigManager config) {

        HashMap<String, Object> resultHashMap = (HashMap<String, Object>) sampleMap.clone();
        resultHashMap.remove("cancer_kind");
        resultHashMap.put("cancer_kind",
                config.getValue("cancer_kind." + sampleMap.get("cancer_kind")));
        LOGGER.info(resultHashMap.get("cancer_kind"));
        LOGGER.info("cancer_kind is : " + resultHashMap.get("cancer_kind"));
        return resultHashMap;
    }

    private static HashMap<String, Object> fillWorkspace2SampleHashMap(HashMap<String, Object> sampleMap,
            SysConfigManager config) {

        HashMap<String, Object> resultHashMap = (HashMap<String, Object>) sampleMap.clone();
        resultHashMap.put("workspace",
                config.getValue("workspace.sample_path") + "/" + sampleMap.get("sample_name"));
        return resultHashMap;
    }

    private static HashMap<String, Object> fillDefaultEntries2SampleHashMap(HashMap<String, Object> sampleMap) {

        HashMap<String, Object> resultHashMap = (HashMap<String, Object>) sampleMap.clone();
        resultHashMap.put("create_time", LocalDateTime.now());
        resultHashMap.put("update_time", LocalDateTime.now());
        resultHashMap.put("status", "wait_running");
        resultHashMap.put("create_username", "administrator");
        resultHashMap.put("update_username", "administrator");
        return resultHashMap;
    }

    private static void createProjectCancerRecord(HashMap<String, Object> sampleMap, Db db) {

        String sampleName = (String) sampleMap.get("sample_name");
        List<Map<String, Object>> projectCancerRecords = db
                .select("SELECT id FROM shca_project_cancer WHERE sample_name=? AND status!=?", sampleName, "deleted");

        if (projectCancerRecords.size() > 1) {
            throw new IllegalArgumentException("sample name: " + sampleName + " already exists in multiple projects");
        }

        if (projectCancerRecords.size() == 1) {
            LOGGER.info("sample name: " + sampleName + " already exists in shca_project_cancer, updating...");
            long id = (long) projectCancerRecords.get(0).get("id");
            db.update("UPDATE shca_project_cancer SET status=?, update_time=?, update_username=? WHERE id=?",
                    "wait_running", LocalDateTime.now(), "administrator", id);
            return;
        }
        if (projectCancerRecords.size() == 0) {
            try {
                LOGGER.info("inserting into shca_project_cancer, sample name is : " + sampleName);
                List<Object> sqlParams = new ArrayList<>();
                LOGGER.info("sample_name -> " + sampleMap.get("sample_name"));
                LOGGER.info("cancer_kind -> " + sampleMap.get("cancer_kind"));
                String sql = DbUtils.insertSql("shca_project_cancer", sampleMap, sqlParams);
                LOGGER.info("sql is : " + sql);
                db.insert(sql, sqlParams.toArray(new Object[0]));
                LOGGER.info("insert " + sampleName + "into shca_project_cancer successfully.");
            } catch (Exception e) {
                LOGGER.error("insert into shca_project_cancer ERROR, sample name is : " + sampleName);
                e.printStackTrace();
            }
        }
    }

    public static void updateProjectCancerRecordStatus(HashMap<String, Object> sampleMap, Db db) {
        List<Map<String, Object>> projectCancerRecords = db.select(
                "SELECT id, status FROM shca_project_cancer WHERE sample_name=? AND status in (?,?)",
                sampleMap.get("sample_name"), "finished", "failed");
        if (projectCancerRecords.isEmpty()) {
            return;
        }
        if (projectCancerRecords.size() > 1) {
            throw new IllegalArgumentException("record not unique for " + sampleMap.get("sample_name"));
        }
        long id = (long) projectCancerRecords.get(0).get("id");
        String status = (String) projectCancerRecords.get(0).get("status");
        if ("failed".equals(status) || "finished".equals(status)) {
            db.update("UPDATE shca_project_cancer SET status=?, update_time=?, update_username=? WHERE id=?",
                    "wait_running", LocalDateTime.now(), "administrator", id);
        }
    }

    // private static void updateProjectCancerRecordStatus(String sampleName, String
    // sourceStatus,String targetStatus, Db db) {

    // LOGGER.info("updating ProjectCancer record [" + sampleName + "] -> " +
    // sourceStatus);
    // List<Map<String, Object>> projectCancerRecords = db.select("SELECT id FROM
    // shca_project_cancer WHERE sample_name=? AND status=?"
    // , sampleName, sourceStatus);
    // if (projectCancerRecords.isEmpty()) {
    // throw new IllegalArgumentException("record not found for " + sampleName + "
    // and " + sourceStatus);
    // }
    // if (projectCancerRecords.size() > 1) {
    // throw new IllegalArgumentException("record not unique for " + sampleName + "
    // and " + sourceStatus);
    // }
    // long id = (long)projectCancerRecords.get(0).get("id");
    // db.update("UPDATE shca_project_cancer SET status=?, update_time=?,
    // update_username=? WHERE id=?"
    // , targetStatus, LocalDateTime.now(), "administrator", id);
    // LOGGER.info("updated ProjectCancer record [" + sampleName + "] -> " +
    // targetStatus);
    // }

    private static void updateFtpFileRecordByMd5(String sampleName,
            Db db) {

        try {
            List<Map<String, Object>> sampleMd5MapList = db.select(
                    "SELECT md5, store_path FROM shca_project_cancer_file WHERE sample_name=? AND status=? ",
                    sampleName, "successed");
            for (Map<String, Object> md5Map : sampleMd5MapList) {
                LOGGER.info("updating shca_ftp_file with sample name: " + sampleName);
                db.update(
                        "UPDATE shca_ftp_file SET memo=?, store_path=?, update_time=? WHERE sample_name=? AND directory=? AND md5=? ",
                        "样本预处理", (String) md5Map.get("store_path"), LocalDateTime.now(), sampleName, "sample",
                        (String) md5Map.get("md5"));
                LOGGER.info("updated shca_ftp_file successfully with sample name: " + sampleName);
            }

        } catch (Exception e) {
            LOGGER.error("update shca_ftp_file ERROR, sample name is : " + sampleName);
            e.printStackTrace();
        }
    }

    public static String suffixUnificatioon(String fileName) {

        if (fileName.endsWith(".fq.gz")) {
            return fileName.substring(0, fileName.length() - 6) + ".fastq.gz";
        }
        return fileName;
    }

    public static boolean ftpLogValidate(FtpLog ftpLog,
            long threshold) {

        LocalDateTime now = LocalDateTime.now();
        boolean result = false;
        if (ftpLog.getEndTime() == null) {
            LOGGER.info("UPLOADING file: " + ftpLog.getFilePath() + " continue...");
            return result;
        }
        ZoneOffset zoneOffset = ZoneOffset.of("+08:00");
        long secondSpan = now.toEpochSecond(zoneOffset) - ftpLog.getEndTime().toEpochSecond(zoneOffset);
        if (secondSpan <= threshold) {
            LOGGER.info(ftpLog.getFilePath() + " waiting for parsing "
                    + (secondSpan / 60)
                    + " minutes ");
            result = true;
            return result;
        }
        LOGGER.info("waiting for : " + secondSpan + " parsing timeout, start parsing...");
        return result;
    }

    private static boolean ifUsedByProject(String sampleName, Db db) {

        // List<Map<String, Object>> projectList = db.select("SELECT id FROM
        // shca_project_project WHERE sample_name_0=? OR sample_name_1=?"
        // , sampleName, sampleName);
        // if (projectList.isEmpty()) {
        // return false;
        // }
        // return true;
        return false;
    }

    public static boolean shouldUpdate(HashMap<String, Object> ftpFastqFileMap, Db db) {
        // TODO: check if sample name exists in shca_project_cancer_file table
        Boolean result = Boolean.FALSE;
        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        if (ifUsedByProject(sampleName, db)) {
            return result;
        }
        // if (ftpFastqFileMap.containsKey("file_size")) {
        // long fileSize = (long) ftpFastqFileMap.get("file_size");
        // if (fileSize > 150 * 1024 * 1024) {
        // LOGGER.info("file size is too large, skip updating");
        // return result;
        // }
        // }
        return true;
    }

    public static void createFtpFileRecord(HashMap<String, Object> ftpFastqFileMap, Db db) {

        LOGGER.info("inserting into shca_ftp_file, with " + (String) ftpFastqFileMap.get("sample_name"));
        List<Object> sqlParams = new ArrayList<>();
        String sql = DbUtils.insertSql("shca_ftp_file", ftpFastqFileMap, sqlParams);
        LOGGER.info("sql -> " + sql);
        LOGGER.info("create_time -> " + ftpFastqFileMap.get("create_time"));
        LOGGER.info("update_time -> " + ftpFastqFileMap.get("update_time"));
        db.insert(sql, sqlParams.toArray(new Object[0]));
        LOGGER.info("inserted into shca_ftp_file succeeded ->  " + (String) ftpFastqFileMap.get("sample_name"));
    }

    public static void updateFtpFileRecord(HashMap<String, Object> ftpFastqFileMap, Db db) {

        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        String reads = (String) ftpFastqFileMap.get("rawdata");
        LOGGER.info("updating Ftp record [" + sampleName + "] -> " + reads);
        List<Map<String, Object>> ftpFileRecordList = db.select(
                "SELECT id FROM shca_ftp_file WHERE sample_name=? AND rawdata=? AND is_deleted=? AND directory=?",
                sampleName, reads, 0, "sample");
        if (ftpFileRecordList.isEmpty()) {
            throw new IllegalArgumentException("record not found for " + sampleName + " and " + reads);
        }
        // if (ftpFileRecordList.size() > 1) {
        // throw new IllegalArgumentException("record not unique for " + sampleName + "
        // and " + reads);
        // }
        Map<String, Object> ftpFile = ftpFileRecordList.get(0);
        try {
            db.update("UPDATE shca_ftp_file SET md5=?, file_name=?, file_size=?, store_path=?, update_time=? "
                    + "WHERE id=?", (String) ftpFastqFileMap.get("md5"), (String) ftpFastqFileMap.get("file_name"),
                    (long) ftpFastqFileMap.get("file_size"), (String) ftpFastqFileMap.get("store_path"),
                    LocalDateTime.now(), (long) ftpFile.get("id"));
            LOGGER.info("updated shca_ftp_file successfully with sample name: " + sampleName);
        } catch (Exception e) {
            LOGGER.error("update shca_ftp_file ERROR, sample name is : " + sampleName);
            e.printStackTrace();
        }
    }

    public static void updateCancerFileRecordStatus(HashMap<String, Object> ftpFastqFileMap,
            String sourceStatus, String targetStatus, Db db) {

        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        String reads = (String) ftpFastqFileMap.get("rawdata");
        updateCancerFileRecordStatus(sampleName, reads, sourceStatus, targetStatus, db);
    }

    public static void updateCancerFileRecordStatus(String sampleName, String reads, String sourceStatus,
            String targetStatus, Db db) {
        LOGGER.info("updating Cancer record [" + sampleName + "] -> " + reads);
        LOGGER.info("selecting status from shca_project_cancer_file by project: " + sampleName);

        List<Map<String, Object>> recordIdStatusList = db.select(
                "SELECT id, status FROM shca_project_cancer_file WHERE sample_name=? AND status=? AND rawdata=?",
                sampleName, sourceStatus, reads);

        if (recordIdStatusList.isEmpty()) {
            throw new IllegalArgumentException(
                    "record not found for " + sampleName + " and " + reads + " and " + sourceStatus);
        }

        for (Map<String, Object> mapItem : recordIdStatusList) {
            String status = "";
            long id = -1;
            if (mapItem.isEmpty()) {
                continue;
            }
            status = (String) mapItem.get("status");
            id = (long) mapItem.get("id");

            LOGGER.info("status of " + sampleName + "is " + status + "and id is :" + id);
            if (sourceStatus.equals(status)) {
                LOGGER.info("SET status of " + sampleName
                        + " from filed -> successed with successed to shca_project_cancer_file ");
                db.update("UPDATE shca_project_cancer_file SET status=? WHERE id=? ", targetStatus, id);
                LOGGER.info("status of " + sampleName + "changed to successed");
            }
        }
    }

    public static void updateCancerFileRecordFileInfo(HashMap<String, Object> ftpFastqFileMap, Db db) {
        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        String reads = (String) ftpFastqFileMap.get("rawdata");
        List<Map<String, Object>> recordIdStatusList = db.select(
                "SELECT id FROM shca_project_cancer_file WHERE sample_name=? AND rawdata=? AND status=?", sampleName,
                reads, "successed");
        if (recordIdStatusList.size() > 1) {
            throw new IllegalArgumentException("more than one record found for " + sampleName + " and " + reads);
        }
        if (recordIdStatusList.isEmpty()) {
            recordIdStatusList = db.select(
                    "SELECT id FROM shca_project_cancer_file WHERE sample_name=? AND rawdata=? AND status=?",
                    sampleName, reads, "ready");
        }

        Map<String, Object> sample = recordIdStatusList.get(0);
        LOGGER.info("updateCancerFileRecordFileInfo, sample name is : " + sampleName);
        try {
            db.update(
                    "UPDATE shca_project_cancer_file SET md5=?, file_name=?, file_size=?, store_path=?, update_time=? "
                            + "WHERE id=? ",
                    (String) ftpFastqFileMap.get("md5"), (String) ftpFastqFileMap.get("file_name"),
                    (long) ftpFastqFileMap.get("file_size"), (String) ftpFastqFileMap.get("store_path"),
                    LocalDateTime.now(), (long) sample.get("id"));
            LOGGER.info("updated shca_project_cancer_file successfully with sample name: " + sampleName);
        } catch (Exception e) {
            LOGGER.error("update shca_project_cancer_file ERROR, sample name is : " + sampleName);
            e.printStackTrace();
        }
    }

    public static void createCancerFileRecord(HashMap<String, Object> ftpFastqFileMap, Db db) {
        LocalDateTime now = LocalDateTime.now();
        String sampleName = (String) ftpFastqFileMap.get("sample_name");
        String reads = (String) ftpFastqFileMap.get("rawdata");
        LOGGER.info("Saving Cancer record [" + sampleName + "] -> " + reads);
        ftpFastqFileMap.put("create_time", now);
        ftpFastqFileMap.put("update_time", now);

        List<Map<String, Object>> existRecords = db.select(
                "SELECT id FROM shca_project_cancer WHERE sample_name=? AND status=? ", sampleName, "finished");

        if (existRecords.size() > 1) {
            throw new IllegalArgumentException("multiple records found for " + sampleName + " and " + "successed");
        }

        // if (existRecords.size() == 1) {
        // String id = (String) existRecords.get(0).get("id");
        // try {
        // LOGGER.info("updating shca_project_cancer_file with sample name: " +
        // sampleName);
        // db.update("UPDATE shca_project_cancer_file SET status=?, update_time=?,
        // update_username=?, "
        // + "file_name=?, file_size=?, store_path=?, md5=? WHERE id=? ",
        // "successed", LocalDateTime.now(), "FtpLog"
        // , ftpFastqFileMap.get("file_name"), ftpFastqFileMap.get("file_size")
        // , ftpFastqFileMap.get("store_path"), ftpFastqFileMap.get("md5")
        // , id);
        // LOGGER.info("updated shca_project_cancer_file with sample name: " +
        // sampleName);
        // } catch (Exception e) {
        // LOGGER.error("update shca_project_cancer_file with sample name: " +
        // sampleName);
        // e.printStackTrace();
        // }
        // }

        try {
            LOGGER.info("inserting into shca_project_cancer_file with sample name: " + sampleName);
            HashMap<String, Object> tmpSqlMap = (HashMap<String, Object>) ftpFastqFileMap.clone();
            tmpSqlMap.remove("start_time");
            tmpSqlMap.remove("end_time");
            tmpSqlMap.remove("is_deleted");
            tmpSqlMap.remove("directory");
            tmpSqlMap.remove("memo");
            // tmpSqlMap.put("status", "successed");
            tmpSqlMap.put("status", "ready");
            tmpSqlMap.put("modified_time", LocalDateTime.now());

            List<Object> sqlParams = new ArrayList<>();
            String sql = DbUtils.insertSql(
                    "shca_project_cancer_file", (Map<String, Object>) tmpSqlMap, sqlParams);
            LOGGER.info("sql lines is: " + sql);
            db.insert(sql, sqlParams.toArray(new Object[0]));
            LOGGER.info(
                    "insert into shca_project_cancer_file with sample name: "
                            + sampleName);
        } catch (Exception e) {
            LOGGER.error(
                    "insert into shca_project_cancer_file with sample name: "
                            + sampleName);
            e.printStackTrace();
        }
    }

    public static boolean fastqExists(String sampleName, String reads, Db db) {

        Map<String, Object> cancer = db.selectUnique(
                "SELECT id FROM shca_project_cancer_file "
                        + " WHERE sample_name=? AND rawdata=? AND (status=? or status=? or status=?) ",
                sampleName, reads, "successed", "filed", "ready");

        if (!CollectionUtils.isEmpty(cancer)) {
            return true;
        }
        return false;
    }

    public static boolean fastqExists(String sampleName, String reads, String md5, Db db) {

        Map<String, Object> cancer = db.selectUnique(
                "SELECT id FROM shca_project_cancer_file "
                        + " WHERE sample_name=? AND rawdata=? AND (status=? or status=? or status=?) AND md5=?",
                sampleName, reads, "successed", "filed", "ready", md5);

        if (!CollectionUtils.isEmpty(cancer)) {
            return true;
        }
        return false;
    }

    public static boolean fastqExists(HashMap<String, Object> fastqInfoMap, Db db) {

        return fastqExists(
                (String) fastqInfoMap.get("sample_name"), (String) fastqInfoMap.get("rawdata"), db);
    }

    public static void saveFtpLogFile(FtpLog ftpLog, SysConfigManager config) {
        for (File logFile : ftpLog.getLogs()) {
saveFtpLogFile(logFile, config);
        }
    }

    public static Boolean saveFtpLogFile(File logFile, SysConfigManager config) {
        File targetLogDir = new File(
                config.getValue("workspace.ftplog_path") + "/"
                + ConvertUtils.date2Str("yyyyMMdd") + "/");
        File targetLogFile = new File(
            targetLogDir.getAbsolutePath() + "/" + logFile.getName());
        try {
            targetLogDir.mkdirs();
            LOGGER.info("moving log file "
                + logFile.getAbsolutePath() + " -> "
                + targetLogFile.getAbsolutePath());
            logFile.renameTo(targetLogFile);
            LOGGER.info("log file moved, " + targetLogFile.getAbsolutePath());
            return Boolean.TRUE;
        } catch (Exception e) {
            LOGGER.error("moving log file error, " + e.getMessage());
            return Boolean.FALSE;
        }
    }

    public static Map<String, List<Map<String, Object>>> checkCancerRecord(Db db) {
        List<Map<String, Object>> readyList = db
                .select("SELECT id, sample_name, cancer_kind, rawdata FROM shca_project_cancer_file WHERE status='ready'");
        Map<String, List<Map<String, Object>>> groupMap = readyList.stream()
                .collect(Collectors.groupingBy(map -> (String) map.get("sample_name")))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() == 2)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return groupMap;
    }
}
