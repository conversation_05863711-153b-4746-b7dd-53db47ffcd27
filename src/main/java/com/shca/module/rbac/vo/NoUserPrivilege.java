package com.shca.module.rbac.vo;

import com.shca.http.SessionUser;
import java.util.Map;

/**
 * Modern replacement for com.ywang.module.rbac.vo.NoUserPrivilege
 * Default privilege handler for when no specific privilege is found
 */
public class NoUserPrivilege extends AbstractPrivilege {
    
    @Override
    public boolean hasPrivilege(String url, SessionUser user, Map<String, Object> result) {
        // Default behavior - deny access
        result.put("error", "No privilege found for URL: " + url);
        return false;
    }
    
    @Override
    public String getUrl() {
        return "*";
    }
    
    @Override
    public boolean hasLogin() {
        return true;
    }
    
    @Override
    public String getRoleCode() {
        return null;
    }
    
    @Override
    public String getType() {
        return "default";
    }
}
