package com.shca.module.rbac.vo;

import com.shca.http.SessionUser;
import java.util.Map;

/**
 * Modern replacement for com.ywang.module.rbac.vo.AbstractPrivilege
 * Abstract base class for privilege checking
 */
public abstract class AbstractPrivilege {
    
    protected AbstractPrivilege privilegeChain;
    
    /**
     * Set the next privilege in the chain
     */
    public void setPrivilegeChain(AbstractPrivilege privilegeChain) {
        this.privilegeChain = privilegeChain;
    }
    
    /**
     * Check if user has privilege for the given URL
     */
    public abstract boolean hasPrivilege(String url, SessionUser user, Map<String, Object> result);
    
    /**
     * Get the URL pattern this privilege handles
     */
    public abstract String getUrl();
    
    /**
     * Check if login is required
     */
    public abstract boolean hasLogin();
    
    /**
     * Get required role code
     */
    public abstract String getRoleCode();
    
    /**
     * Get privilege type
     */
    public abstract String getType();
}
