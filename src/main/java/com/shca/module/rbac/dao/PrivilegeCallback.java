package com.shca.module.rbac.dao;

import com.shca.module.rbac.vo.AbstractPrivilege;
import com.shca.http.SessionUser;
import java.util.Map;

/**
 * Modern replacement for com.ywang.module.rbac.dao.PrivilegeCallback
 * Callback for privilege database operations
 */
public class PrivilegeCallback {
    
    /**
     * Create a privilege from database row data
     */
    public AbstractPrivilege createPrivilege(Map<String, Object> row) {
        return new DatabasePrivilege(row);
    }
    
    /**
     * Database-backed privilege implementation
     */
    private static class DatabasePrivilege extends AbstractPrivilege {
        private final String url;
        private final boolean hasLogin;
        private final String roleCode;
        private final String type;
        
        public DatabasePrivilege(Map<String, Object> row) {
            this.url = (String) row.get("url");
            this.hasLogin = Boolean.TRUE.equals(row.get("has_login"));
            this.roleCode = (String) row.get("role_code");
            this.type = (String) row.get("type");
        }
        
        @Override
        public boolean hasPrivilege(String url, SessionUser user, Map<String, Object> result) {
            // Check if URL matches
            if (!urlMatches(url)) {
                // Pass to next in chain
                if (privilegeChain != null) {
                    return privilegeChain.hasPrivilege(url, user, result);
                }
                return false;
            }
            
            // Check login requirement
            if (hasLogin && user == null) {
                result.put("error", "Login required");
                return false;
            }
            
            // Check role requirement
            if (roleCode != null && !roleCode.isEmpty() && user != null) {
                if (!user.hasRole(roleCode)) {
                    result.put("error", "Insufficient privileges");
                    return false;
                }
            }
            
            return true;
        }
        
        private boolean urlMatches(String requestUrl) {
            if (url == null || url.equals("*")) {
                return true;
            }
            return requestUrl.startsWith(url);
        }
        
        @Override
        public String getUrl() {
            return url;
        }
        
        @Override
        public boolean hasLogin() {
            return hasLogin;
        }
        
        @Override
        public String getRoleCode() {
            return roleCode;
        }
        
        @Override
        public String getType() {
            return type;
        }
    }
}
