package com.shca.module.system.view;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ywang.framework.mvc.UploadFile;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.JspForward;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.OrderPage;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.*;

public class CromwellView {

    private static final Logger LOGGER = Logger.getLogger( CromwellView.class);

    @Autowired
    private Db db;

    private String configKey = "cromwell";

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public Forward create(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "创建失败";

        if(params.isJson()){
            String cancer_kind = params.getString("cancer_kind");
            String cancer_script = params.getString("cancer_script");
            String auto_script = params.getString("auto_script");
            String script_step = params.getString("script_step");
            String cromwell_inputs = params.getString("cromwell_inputs",false,"{}");
            String cromwell_workflow_source = "";
            String cromwell_workflow_dependencies = "";

            UploadFile[] workflow_sources = params.getUploadFileArray( "upload-workflow_source-file");
            UploadFile[] workflow_dependencies  = params.getUploadFileArray( "upload-workflow_dependencies-file");

            try{
                Date now = new Date();
                Map<String,Object> template  =  new HashMap<>();
                template.put("cancer_kind",cancer_kind);
                template.put("cancer_script",cancer_script);
                template.put("auto_script",auto_script);
                template.put("script_step",script_step);
                template.put("cromwell_workflow_inputs",cromwell_inputs);
                //template.put("cromwell_workflow_source",cromwell_workflow_source);
                //template.put("cromwell_workflow_dependencies",cromwell_workflow_dependencies);
                template.put("create_username", params.getUserName());
                template.put("create_time",now);
                template.put("update_username", params.getUserName());
                template.put("update_time",now);

                System.out.println(template);

                List<Object> sqlParams = new ArrayList<>();
                String sql = DbUtils.insertSql( "shca_cromwell_config", template, sqlParams);
                long id = db.insert( sql, sqlParams.toArray( new Object[0]));

                errCode = 1010;
                SysConfigManager config = SysConfigManager.getInstance();
                File dir = new File( config.getValue( configKey + ".config_path") + "/"+cancer_kind+"/" + id);
                if(!dir.exists()){
                    dir.mkdirs();
                }

                for (UploadFile uploadFile : workflow_sources) {
                    cromwell_workflow_source = dir.getAbsolutePath() + "/workflow_source.wdl";
                    OutputStream os = new FileOutputStream(cromwell_workflow_source);
                    InputStream is = new ByteArrayInputStream( uploadFile.getContent());
                    FileUtils.transfer( is, os);
                }

                for (UploadFile uploadFile : workflow_dependencies) {
                    cromwell_workflow_dependencies = dir.getAbsolutePath() + "/workflow_dependencies.zip";
                    OutputStream os = new FileOutputStream( cromwell_workflow_dependencies);
                    InputStream is = new ByteArrayInputStream( uploadFile.getContent());
                    FileUtils.transfer( is, os);
                }


                 db.update( "update shca_cromwell_config set cromwell_workflow_source = ?,cromwell_workflow_dependencies = ? where id = ?",
                         cromwell_workflow_source,cromwell_workflow_dependencies,id);



                errCode = 0;
            }catch (Exception e){
                e.printStackTrace();
                errMsg+="："+e.getMessage();
            }

            ret.put("errcode",errCode);
            ret.put("errmsg",errMsg);
            return new JsonForward(ret);

        }

        SysConfigManager config = SysConfigManager.getInstance();
        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);
        return new JspForward( "/shca/system/system_cromwell_create.jsp", ret);
    }

    public Forward list(ViewParams params) {
        Map<String,Object> ret = new HashMap<>();
        if (params.isJson()){
//            String cancerKind = params.getString("cancer_kind");
            OrderPage page = params.getPage();
            List<Object> sqlParams = new ArrayList<>();
            String sql = "SELECT id, cancer_kind, cancer_script, auto_script, script_step, cromwell_workflow_source, " +
                    "cromwell_workflow_dependencies, create_time, create_username, update_time, update_username " +
                    " FROM shca_cromwell_config WHERE 1 = 1 ";

            sql += params.whereSql(sqlParams);
            sql += " ORDER BY create_time DESC";
            List<Map<String,Object>> mapList =  db.select(sql,page,sqlParams.toArray(new Object[0]));
            SysConfigManager config = SysConfigManager.getInstance();

            for(int i=0;i<mapList.size();i++){
                Map<String,Object> map = mapList.get(i);
                map.put("cancer_kind",config.getValue((String)map.get("cancer_kind")));
                map.put("auto_script", (boolean)map.get("auto_script")?"是":"否");
                map.put("script_step", config.getValue("script_type."+(Integer)map.get("script_step")));
            }

            ret.put("total", page.asPage().getTotal());
            ret.put("rows", mapList);
            return new JsonForward(ret);
        }

        SysConfigManager config = SysConfigManager.getInstance();
        List<Map<String, Object>> kindList = config.getProperties("cancer_kind_name");
        ret.put("kindList",kindList);
        return new JspForward("/shca/system/system_cromwell_list.jsp",ret);
    }

    public Forward deleteAll(ViewParams params){
        Map<String,Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "删除失败";

        String[] ids = params.getStringArray("id[]");

        if(CollectionUtils.isEmpty(ids)){
            ret.put("errcode",1100);
            ret.put("errmsg",errMsg+"：请选择要删除的记录");
            return new JsonForward(ret);
        }
        try{
            List<String> sqlParams = new ArrayList<>();
            String  sql ="DELETE FROM shca_cromwell_config WHERE ID in (";
            for (int i=0;i<ids.length;i++){
                sql += "?,";
                sqlParams.add(ids[i]);
            }
            sql = sql.substring(0, sql.length() - 1) + ")";
            db.delete(sql, sqlParams.toArray( new Object[0]));

            ret.put("errcode",0);
            ret.put("errmsg",errMsg);
        }catch (Exception e){
            LOGGER.error( LogUtils.toString(e));
            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());
        }
        return new JsonForward(ret);
    }

    public Forward config(ViewParams params){
        if(params.isJson()){
            Long id = params.getId();
            SysConfigManager config = SysConfigManager.getInstance();
            String options_path = config.getValue( configKey + ".config_path");
            if(id==null){
                options_path += "/workflow_options.json";
            } else {
                String  cancer_kind= db.selectUnique("select cancer_kind from shca_cromwell_config where id = ?",new StringCallback(),id);
                options_path += "/"+cancer_kind+"/"+id+"/workflow_options.json";
            }
            File file = new File(options_path);
            List<Map<String,Object>> list = new ArrayList<>();
            if(!file.exists()){

            } else {
                String json = readFile(file);
                LOGGER.info("读取配置文件："+json);
                if(JSON.isValidObject(json)){
                    JSONObject obj = JSON.parseObject(json);
                    Iterator<String> it = obj.keySet().iterator();
                    while(it.hasNext()){
                        String key = it.next();
                        Object value = obj.get(key);
                        LOGGER.info(key+":"+value);
                        Map<String,Object> map = new HashMap<>();
                        map.put("key",key);
                        map.put("value",value);
                        list.add(map);
                    }
                }
            }
            Map<String,Object> ret = new HashMap<>();
            ret.put( "rows", list);
            return new JsonForward(ret);
        }
        return new JspForward( "/shca/system/system_cromwell_config.jsp", null);
    }

    public Forward update(ViewParams params){
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        Date now = new Date();
        String errMsg = "更新失败: ";
        try {
            Long id = params.getId();
            SysConfigManager config = SysConfigManager.getInstance();
            String options_path = config.getValue( configKey + ".config_path");
            if(id==null){
                options_path += "/workflow_options.json";
            } else {
                String  cancer_kind= db.selectUnique("select cancer_kind from shca_cromwell_config where id = ?",new StringCallback(),id);
                options_path += "/"+cancer_kind+"/"+id+"/workflow_options.json";
            }
            File file = new File(options_path);
            List<Map<String,Object>> list = new ArrayList<>();
            String content = params.getString("content",false,"{}");
            LOGGER.info("配置文件路径，"+file.getAbsolutePath());
            LOGGER.info("配置文件是否存在，"+file.exists());
            if(!file.exists()){
                file.createNewFile();
                LOGGER.info("文件创建了，"+file.getAbsolutePath());
            }
            if(JSONObject.isValidObject(content)){
                JSONObject obj = JSONObject.parseObject(content);
                content = JSON.toJSONString(obj, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                        SerializerFeature.WriteDateUseDateFormat);

            }
            saveFile(file,content);

            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return new JsonForward( ret);
        } catch (Exception e) {
            LOGGER.error(LogUtils.toString( e));
            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            throw new UnknownException( new JsonForward( ret), e);
        }
    }

    public Forward viewFile(ViewParams params) {
        Long id = params.getId();
        SysConfigManager config = SysConfigManager.getInstance();
        String options_path = config.getValue( configKey + ".config_path");
        if(id==null){
            options_path += "/workflow_options.json";
        } else {
            String  cancer_kind= db.selectUnique("select cancer_kind from shca_cromwell_config where id = ?",new StringCallback(),id);
            options_path += "/"+cancer_kind+"/"+id+"/workflow_options.json";
        }
        File file = new File(options_path);
        String cromwell_workflow_inputs  = "";
        if(file.exists()){
            cromwell_workflow_inputs = readFile(file);
        }

        Map<String,Object> ret = new HashMap<>();
        ret.put("sample_name","");
        ret.put("errmsg","无");
        ret.put("cromwell_workflow_inputs", cromwell_workflow_inputs);
        return new JspForward( "/shca/project/project_cancer_viewParam.jsp", ret);
    }

    private  String readFile(File jsonFile) {
        StringBuffer jsonStr = new StringBuffer("");
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String lineTxt = null;
            while ((lineTxt = br.readLine()) != null) {
                jsonStr.append(lineTxt+"\n");
            }
            br.close();
        } catch (Exception e) {
            System.out.println("文件读取错误!");
        }
        return jsonStr.toString();
    }

    private void saveFile(File file ,String content) {

        FileWriter fw = null;
        try {
            fw = new FileWriter(file);
            fw.write(content);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 完毕，关闭所有链接
            try {
                fw.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
