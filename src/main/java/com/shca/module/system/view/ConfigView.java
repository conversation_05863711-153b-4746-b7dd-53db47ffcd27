/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 21:58:19
 * @FilePath: /analysis_engine/src/main/java/com/shca/module/system/view/ConfigView.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.module.system.view;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.JspForward;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.module.sys.service.SysConfigManager;
import com.shca.util.DbService;
import com.ywang.utils.LogUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ConfigView {

    @Autowired
    private DbService db;

    private static final Logger LOGGER = Logger.getLogger( ConfigView.class);

    public Forward config(ViewParams<?> params) {

        return new JspForward( "/shca/system/system_config_config.jsp", null);
    }

    public Forward dataGrid(ViewParams<?> params) throws JsonProcessingException {
        List<Map<String,Object>> configs = db.select( "select id,config_title,config_name,config_value,can_edit,group_value '配置组' " +
                " from comm_sys_config where scope=? order by group_value,sort", "shca");

        Map<String, Object> grid = new HashMap<>();
        grid.put( "total", configs.size());
        grid.put( "rows", configs);
        return new JsonForward( grid);
    }

    public Forward update(ViewParams<?> params) throws JsonProcessingException {
        Map<String, Object> ret = new HashMap<>();
        int errCode = 1000;
        String errMsg = "更新失败: ";
        try {
            SysConfigManager config = SysConfigManager.getInstance();
            System.out.println( config.getValue( params.getString( "config_name")));

            config.setValue( params.getString( "config_name"), params.getString( "config_value"));


            ret.put( "errcode", 0);
            ret.put( "errmsg", "ok");

            return new JsonForward( ret);
        } catch (Exception e) {
            LOGGER.error(LogUtils.toString( e));
            ret.put( "errcode", errCode);
            ret.put( "errmsg", errMsg + e.getMessage());

            throw new UnknownException( new JsonForward( ret), e);
        }
    }
}
