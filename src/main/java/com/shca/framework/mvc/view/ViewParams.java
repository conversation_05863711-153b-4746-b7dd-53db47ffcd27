package com.shca.framework.mvc.view;

import com.shca.util.PageRequest;
import com.shca.sql.OrderPage;
import java.util.List;
import java.util.Map;

/**
 * Modern replacement for com.ywang.framework.mvc.view.ViewParams
 */
public class ViewParams<T> {
    
    private Map<String, Object> params;
    private boolean json = false;
    
    public ViewParams(Map<String, Object> params) {
        this.params = params;
    }
    
    public boolean isJson() {
        return json;
    }
    
    public void setJson(boolean json) {
        this.json = json;
    }
    
    public String getString(String key) {
        Object value = params.get(key);
        return value != null ? value.toString() : null;
    }
    
    public Integer getInteger(String key) {
        Object value = params.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    public PageRequest getPage() {
        // Default page request
        return PageRequest.of(0, 20);
    }
    
    public String whereSql(List<Object> whereParams) {
        // Simplified implementation - in real usage this would build WHERE clauses
        return "";
    }
    
    public Map<String, Object> getParams() {
        return params;
    }

    public Long getId() {
        Object value = params.get("id");
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        return null;
    }
}
