package com.shca.framework.mvc;

import java.io.InputStream;

/**
 * Modern replacement for com.ywang.framework.mvc.UploadFile
 */
public class UploadFile {
    
    private String fileName;
    private String contentType;
    private byte[] content;
    private long size;
    
    public UploadFile(String fileName, String contentType, byte[] content) {
        this.fileName = fileName;
        this.contentType = contentType;
        this.content = content;
        this.size = content != null ? content.length : 0;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public byte[] getContent() {
        return content;
    }
    
    public void setContent(byte[] content) {
        this.content = content;
        this.size = content != null ? content.length : 0;
    }
    
    public long getSize() {
        return size;
    }
    
    public InputStream getInputStream() {
        return new java.io.ByteArrayInputStream(content);
    }

    public String getFileType() {
        if (fileName == null) {
            return null;
        }
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        return null;
    }
}
