package com.shca.framework.mvc.controller.support;

import com.shca.framework.mvc.controller.Forward;
import java.util.Map;

/**
 * Modern replacement for com.ywang.framework.mvc.controller.support.JspForward
 * Supports redirect functionality and session management
 */
public class J<PERSON><PERSON><PERSON><PERSON> implements Forward {

    private final String path;
    private final Object data;
    private final Map<String, Object> sessionData;
    private final boolean redirect;

    public JspForward(String path, Object data) {
        this(path, data, null, false);
    }

    public JspForward(String path, Object data, boolean redirect) {
        this(path, data, null, redirect);
    }

    public JspForward(String path, Object data, Map<String, Object> sessionData, boolean redirect) {
        this.path = path;
        this.data = data;
        this.sessionData = sessionData;
        this.redirect = redirect;
    }

    @Override
    public String getType() {
        return redirect ? "redirect" : "jsp";
    }

    @Override
    public Object getData() {
        return data;
    }

    public String getPath() {
        return path;
    }

    public Map<String, Object> getSessionData() {
        return sessionData;
    }

    public boolean isRedirect() {
        return redirect;
    }
}
