package com.shca.framework.mvc.controller.support;

import com.shca.framework.mvc.controller.Forward;

/**
 * Modern replacement for com.ywang.framework.mvc.controller.support.JspForward
 */
public class JspForward implements Forward {
    
    private final String path;
    private final Object data;
    
    public JspForward(String path, Object data) {
        this.path = path;
        this.data = data;
    }
    
    @Override
    public String getType() {
        return "jsp";
    }
    
    @Override
    public Object getData() {
        return data;
    }
    
    public String getPath() {
        return path;
    }
}
