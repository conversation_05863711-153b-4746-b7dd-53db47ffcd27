package com.shca.framework.mvc.controller.support;

import com.shca.framework.mvc.controller.Forward;
import java.io.File;

/**
 * Modern replacement for com.ywang.framework.mvc.controller.support.FileDownloadForward
 * Handles file download responses
 */
public class FileDownloadForward implements Forward {
    
    private final String fileName;
    private final File file;
    private final String contentType;
    
    public FileDownloadForward(String fileName, File file) {
        this(fileName, file, "application/octet-stream");
    }
    
    public FileDownloadForward(String fileName, File file, String contentType) {
        this.fileName = fileName;
        this.file = file;
        this.contentType = contentType;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public File getFile() {
        return file;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public String toString() {
        return "FileDownloadForward{" +
                "fileName='" + fileName + '\'' +
                ", file=" + file +
                ", contentType='" + contentType + '\'' +
                '}';
    }

    @Override
    public String getType() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getType'");
    }

    @Override
    public Object getData() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getData'");
    }
}
