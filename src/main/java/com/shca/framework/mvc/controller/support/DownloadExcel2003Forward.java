package com.shca.framework.mvc.controller.support;

import com.shca.framework.mvc.controller.Forward;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

/**
 * Modern replacement for com.ywang.framework.mvc.controller.support.DownloadExcel2003Forward
 */
public class DownloadExcel2003Forward implements Forward {
    
    private final String filename;
    private final HSSFWorkbook workbook;
    
    public DownloadExcel2003Forward(String filename, HSSFWorkbook workbook) {
        this.filename = filename;
        this.workbook = workbook;
    }
    
    @Override
    public String getType() {
        return "excel2003";
    }
    
    @Override
    public Object getData() {
        return workbook;
    }
    
    public String getFilename() {
        return filename;
    }
    
    public HSSFWorkbook getWorkbook() {
        return workbook;
    }
}
