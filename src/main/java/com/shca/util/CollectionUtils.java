package com.shca.util;

import java.util.Collection;
import java.util.Map;

/**
 * Modern replacement for com.ywang.utils.CollectionUtils
 */
public class CollectionUtils {
    
    /**
     * Check if collection is empty or null
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    
    /**
     * Check if map is empty or null
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }
    
    /**
     * Check if array is empty or null
     */
    public static boolean isEmpty(Object[] array) {
        return array == null || array.length == 0;
    }
    
    /**
     * Check if collection is not empty
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }
    
    /**
     * Check if map is not empty
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }
    
    /**
     * Check if array is not empty
     */
    public static boolean isNotEmpty(Object[] array) {
        return !isEmpty(array);
    }
}
