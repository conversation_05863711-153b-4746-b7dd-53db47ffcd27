package com.shca.util;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * Modern replacement for com.ywang.utils.LogUtils
 */
public class LogUtils {
    
    /**
     * Convert exception to string representation
     */
    public static String toString(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        return stringWriter.toString();
    }
    
    /**
     * Convert exception to string with custom message
     */
    public static String toString(String message, Throwable throwable) {
        return message + "\n" + toString(throwable);
    }
}
