package com.shca.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Modern replacement for com.ywang.utils.Cmd
 */
public class Cmd {
    
    /**
     * Execute command and return output
     */
    public static String exec(String command) throws IOException {
        Process process = Runtime.getRuntime().exec(command);
        
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        
        try {
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // Read error stream
                StringBuilder error = new StringBuilder();
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        error.append(line).append("\n");
                    }
                }
                throw new RuntimeException("Command failed with exit code " + exitCode + ": " + error.toString());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Command execution interrupted", e);
        }
        
        return output.toString().trim();
    }
}
