package com.shca.util;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * Modern replacement for com.ywang.sql.support.DbUtils
 */
public class DbUtils {
    
    /**
     * Generate INSERT SQL statement from map data
     */
    public static String insertSql(String tableName, Map<String, Object> data, List<Object> sqlParams) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("Data map cannot be null or empty");
        }
        
        StringJoiner columns = new StringJoiner(", ");
        StringJoiner placeholders = new StringJoiner(", ");
        
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            columns.add(entry.getKey());
            placeholders.add("?");
            sqlParams.add(entry.getValue());
        }
        
        return String.format("INSERT INTO %s (%s) VALUES (%s)", 
                           tableName, columns.toString(), placeholders.toString());
    }
    
    /**
     * Generate UPDATE SQL statement from map data
     */
    public static String updateSql(String tableName, Map<String, Object> data, String whereClause, List<Object> sqlParams) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("Data map cannot be null or empty");
        }
        
        StringJoiner setClause = new StringJoiner(", ");
        
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            setClause.add(entry.getKey() + " = ?");
            sqlParams.add(entry.getValue());
        }
        
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(tableName);
        sql.append(" SET ").append(setClause.toString());
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    /**
     * Generate SELECT SQL statement with WHERE clause
     */
    public static String selectSql(String tableName, String[] columns, String whereClause) {
        StringBuilder sql = new StringBuilder("SELECT ");
        
        if (columns == null || columns.length == 0) {
            sql.append("*");
        } else {
            sql.append(String.join(", ", columns));
        }
        
        sql.append(" FROM ").append(tableName);
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
}
