package com.shca.util;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * Modern replacement for com.ywang.utils.HttpUtils
 */
public class HttpUtils {
    
    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);
    
    /**
     * Perform GET request
     */
    public static String getRequest(String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                HttpEntity entity = response.getEntity();
                return entity != null ? EntityUtils.toString(entity) : null;
            }
        } catch (IOException e) {
            log.error("HTTP GET request failed: " + url, e);
            throw new RuntimeException("HTTP GET request failed", e);
        }
    }
    
    /**
     * Perform POST request
     */
    public static String postRequest(String url, Map<String, String> headers) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            
            if (headers != null) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    httpPost.setHeader(header.getKey(), header.getValue());
                }
            }
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return entity != null ? EntityUtils.toString(entity) : null;
            }
        } catch (IOException e) {
            log.error("HTTP POST request failed: " + url, e);
            throw new RuntimeException("HTTP POST request failed", e);
        }
    }
    
    /**
     * Close HTTP resources safely
     */
    public static void close(CloseableHttpClient httpClient, CloseableHttpResponse httpResponse, HttpEntity entity) {
        if (httpResponse != null) {
            try {
                httpResponse.close();
            } catch (IOException e) {
                log.warn("Failed to close HTTP response", e);
            }
        }
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.warn("Failed to close HTTP client", e);
            }
        }
    }
}
