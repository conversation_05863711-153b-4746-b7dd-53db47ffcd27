package com.shca.util;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public final class ReflectUtils {
    
    private ReflectUtils() {} 
    
    
    public static Method[] getGetMethods(Object obj) {
        if (obj == null) {
            return new Method[0];
        }

        return getGetMethods( obj.getClass());
    }

    public static Method[] getGetMethods(Class clazz) {
        if (clazz == null) {
            return new Method[0];
        }
        List<Method> methods = new ArrayList<Method>();

        for (Method method : clazz.getMethods()) {
            if (isGetMethod(method)) {
                methods.add(method);
            }
        }
        return methods.toArray(new Method[0]);
    }

    public static Method[] getSetMethods(Object obj) {
        if (obj == null) {
            return new Method[0];
        }
        Class clazz = obj.getClass();
        return getSetMethods( clazz);
    }

    public static Method[] getSetMethods(Class clazz) {
        if (clazz == null) {
            return new Method[0];
        }
        List<Method> methods = new ArrayList<Method>();
        for (Method method : clazz.getMethods()) {
            if (isSetMethod(method)) {
                methods.add(method);
            }
        }
        return methods.toArray(new Method[0]);
    }

    public static String getFieldName(Method method) {
        if (!isGetMethod(method) && !isSetMethod( method)) {
            throw new IllegalArgumentException("method must be get method or set method");
        }
        String methodName = "";
        if (method.getName().startsWith( "get") || method.getName().startsWith( "set")) {
            methodName = method.getName().substring( 3);
        } else  if (method.getName().startsWith( "is")) {
            methodName = method.getName().substring( 2);
        }


        return getFieldName( methodName);
    }

    public static String getFieldName(String name) {
        String filedName = "";
        for (char c : name.toCharArray()) {
            if (java.lang.Character.isUpperCase(c)) {
                filedName += "_" + java.lang.Character.toLowerCase( c);
            } else {
                filedName += c;
            }
        }

        if (filedName.startsWith("_")) {
            return filedName.substring(1);
        }
        return filedName;
    }
    
    public static boolean isGetMethod(Method method) {
        return method != null && !"getClass".equals(method.getName()) && (method.getName().startsWith("get") || method.getName().startsWith( "is"))
                && method.getReturnType() != void.class
                && Modifier.isPublic(method.getModifiers()) && !Modifier.isStatic(method.getModifiers())
                && method.getGenericParameterTypes().length == 0;
    }


    public static boolean isSetMethod(Method method) {
        return method != null && method.getName().startsWith("set")
                && method.getReturnType() == void.class
                && Modifier.isPublic(method.getModifiers()) && !Modifier.isStatic(method.getModifiers())
                && method.getGenericParameterTypes().length == 1;
    }

}
