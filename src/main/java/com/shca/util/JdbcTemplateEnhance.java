package com.shca.util;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.dao.DataAccessException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.RowMapperResultSetExtractor;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import com.shca.dao.mapper.FastqRowMapper;
import com.shca.dao.mapper.ProjectRowMapper;
import com.shca.dao.mapper.SampleRowMapper;
import com.shca.entity.Fastq;
import com.shca.entity.Project;
import com.shca.entity.Sample;

public class JdbcTemplateEnhance extends JdbcTemplate {
    
    public JdbcTemplateEnhance() {
    }

    public JdbcTemplateEnhance(DataSource dataSource) {
        super(dataSource);
    }

    @Override
    public <T> T queryForObject(String sql, RowMapper<T> rowMapper) 
        throws DataAccessException {
        List<T> query = super.query(sql, rowMapper);
        return canReturnEmptyResultSet(query);
    }

	@Override
	@Nullable
	public <T> T queryForObject(String sql, @Nullable Object[] args, RowMapper<T> rowMapper) throws DataAccessException {
		List<T> results = query(sql, args, new RowMapperResultSetExtractor<>(rowMapper, 1));
		// return DataAccessUtils.nullableSingleResult(results);
        return canReturnEmptyResultSet(results);
	}


	@Override
	public Map<String, Object> queryForMap(String sql, @Nullable Object... args) throws DataAccessException {
		return queryForObject(sql, args, getColumnMapRowMapper());
	}

    @Override
    public <T> List<T>  queryForList(String sql, Object[] args, Class<T> elementType) throws DataAccessException {
        if (elementType.equals(Fastq.class)) {
            System.out.println("here use FastqRowMapper");
            return (List<T>) query(sql, args, new FastqRowMapper());
        }
        if (elementType.equals(Sample.class)) {
            System.out.println("here use FastqRowMapper");
            return (List<T>) query(sql, args, new SampleRowMapper());
        } 
        if (elementType.equals(Project.class)) {
            System.out.println("here use FastqRowMapper");
            return (List<T>) query(sql, args, new ProjectRowMapper());
        }        
        return query(sql, args, getSingleColumnRowMapper(elementType));
    }

    private static <T> T canReturnEmptyResultSet(@Nullable Collection<T> results) 
        throws IncorrectResultSizeDataAccessException {
        if ( CollectionUtils.isEmpty(results)) {
            return null;
        } else if (results.size() > 1) {
            throw new IncorrectResultSizeDataAccessException(1, results.size());
        } else {
            return results.iterator().next();
        }
    }
}
