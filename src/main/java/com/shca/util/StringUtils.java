package com.shca.util;

/**
 * Modern replacement for com.ywang.utils.StringUtils
 * Note: Spring already provides StringUtils, so this is for compatibility
 */
public class StringUtils extends org.springframework.util.StringUtils {
    
    /**
     * Check if string has length (not null and not empty)
     */
    public static boolean hasLength(String str) {
        return org.springframework.util.StringUtils.hasLength(str);
    }
    
    /**
     * Check if string has text (not null, not empty, and contains non-whitespace)
     */
    public static boolean hasText(String str) {
        return org.springframework.util.StringUtils.hasText(str);
    }
    
    /**
     * Trim whitespace from string
     */
    public static String trimWhitespace(String str) {
        return org.springframework.util.StringUtils.trimWhitespace(str);
    }
    
    /**
     * Check if string is empty or null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
    
    /**
     * Check if string is not empty
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * Check if string is blank (null, empty, or only whitespace)
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Check if string is not blank
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * Generate MD5 hash from byte array
     */
    public static String md5(byte[] data) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    /**
     * Generate MD5 hash from string
     */
    public static String md5(String str) {
        if (str == null) {
            return null;
        }
        return md5(str.getBytes(java.nio.charset.StandardCharsets.UTF_8));
    }
}
