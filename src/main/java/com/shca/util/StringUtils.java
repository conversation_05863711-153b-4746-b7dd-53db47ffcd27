package com.shca.util;

/**
 * Modern replacement for com.ywang.utils.StringUtils
 * Note: Spring already provides StringUtils, so this is for compatibility
 */
public class StringUtils extends org.springframework.util.StringUtils {
    
    /**
     * Check if string has length (not null and not empty)
     */
    public static boolean hasLength(String str) {
        return org.springframework.util.StringUtils.hasLength(str);
    }
    
    /**
     * Check if string has text (not null, not empty, and contains non-whitespace)
     */
    public static boolean hasText(String str) {
        return org.springframework.util.StringUtils.hasText(str);
    }
    
    /**
     * Trim whitespace from string
     */
    public static String trimWhitespace(String str) {
        return org.springframework.util.StringUtils.trimWhitespace(str);
    }
    
    /**
     * Check if string is empty or null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
    
    /**
     * Check if string is not empty
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * Check if string is blank (null, empty, or only whitespace)
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Check if string is not blank
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
}
