package com.shca.util;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * thread safety
 */
public final class ConvertUtils {

    private ConvertUtils() {
    }

    private final static String[] PATTERNS = new String[]{
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd",
            "dd/MM/yyyy"
    };

    /**
     * thread safety
     */
    public static Date str2Date(String str) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        for (String pattern : PATTERNS) {
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
                return dateFormat.parse(str);
            } catch (ParseException e) {

            }
        }
        throw new RuntimeException("covert data error: " + str);
    }

    /**
     * thread safety
     */
    public static String date2Str(String pattern) {
        return date2Str(new Date(), pattern);
    }

    /**
     * thread safety
     */
    public static String date2Str(Date date, String pattern) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    public static String date2Json(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuffer s = new StringBuffer(dateFormat.format(date));
        s = s.replace(10, 11, "T");
        return s.toString();
    }

    /**
     * thread safety
     */
    public static Date str2Date(String str, String pattern) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        try {
            return dateFormat.parse(str);
        } catch (ParseException e) {
            throw new RuntimeException("covert data error: " + str, e);
        }
    }

    /**
     * 时间戳转换成日期格式字符串
     * @param seconds 精确到秒的字符串
     * @param format
     * @return
     */
    public static String timeStamp2DateStr(String seconds,String format) {
        if(seconds == null || seconds.isEmpty() || seconds.equals("null")){
            return "";
        }
        if(format == null || format.isEmpty()) format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(Long.parseLong(seconds+"000")));
    }
    /**
     * 日期格式字符串转换成时间戳
     * @param date_str 字符串日期
     * @param format 如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String dateStr2TimeStamp(String date_str,String format){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return String.valueOf(sdf.parse(date_str).getTime()/1000);
        } catch (ParseException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 取得当前时间戳（精确到秒）
     * @return
     */
    public static String timeStamp(){
        long time = System.currentTimeMillis();
        String t = String.valueOf(time/1000);
        return t;
    }


    public static String num2Str(Number num, String format) {
        NumberFormat numberFormat = new DecimalFormat( format);
        return numberFormat.format( num.doubleValue());
    }


    /**
     * thread safety
     */
    public static Integer integer(String str) {
        return convert(str, Integer.class, null);
    }

    /**
     * thread safety
     */
    public static Integer integer(String str, Integer defValue) {
        return convert(str, Integer.class, defValue);
    }

    /**
     * thread safety
     */
    public static Long lon(String str) {
        return convert(str, Long.class, null);
    }

    /**
     * thread safety
     */
    public static Long lon(String str, Long defValue) {
        return convert(str, Long.class, defValue);
    }

    /**
     * thread safety
     */
    public static Double dou(String str) {
        return convert(str, Double.class, null);
    }

    /**
     * thread safety
     */
    public static Double dou(String str, Double defValue) {
        return convert(str, Double.class, defValue);
    }

    /**
     * thread safety
     */
    public static <T> T convert(String str, Class<T> clazz) {
        return convert(str, clazz, null);
    }

    public static Long money(String str) {
        NumberFormat numberFormat = new DecimalFormat("#,##0.00");
        try {
            BigDecimal money = new BigDecimal(numberFormat.parse(str).doubleValue());
            return money.multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
        } catch (ParseException e) {
            throw new RuntimeException("covert data error: " + str, e);
        }
    }

    /**
     * thread safety
     */
    public static <T> T convert(String str, Class<T> clazz, T defValue) {
        if (!StringUtils.hasLength(str)) {
            return defValue;
        }
        try {
            Method valueOf = clazz.getMethod("valueOf", new Class[]{String.class});
            return clazz.cast(valueOf.invoke(null, StringUtils.trimWhitespace(str)));
        } catch (IllegalAccessException | IllegalArgumentException
        | NoSuchMethodException | SecurityException | InvocationTargetException e) {
        }
        return defValue;
    }

    public static Boolean bool(String str) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        return ("true".equalsIgnoreCase(str) || "1".equals(str));
    }

    public static boolean bool(String str, boolean defVal) {
        if (!StringUtils.hasLength(str)) {
            return defVal;
        }
        return ("true".equalsIgnoreCase(str) || "1".equals(str));
    }

    public static <T> Map<String,Object> entityToMap(T entity) {
        Map<String, Object> ret = new HashMap<>();
        try {
            for (Method method : ReflectUtils.getGetMethods(entity)) {
                ret.put(ReflectUtils.getFieldName(method), method.invoke(entity));
            }
            return ret;
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T jsonObject2Entity( ObjectNode objectNode, T entity) {
        try {
            for (Method method : ReflectUtils.getSetMethods( entity)) {
                Class<?> clazz = method.getParameterTypes()[0];

                if (clazz == Boolean.class || clazz == boolean.class) {
                    method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asBoolean(false));
                }
                if (clazz == Integer.class || clazz == int.class) {
                    method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asInt());
                }
                if (clazz == Long.class || clazz == long.class) {
                    method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asLong());
                }
                // if (clazz == Float.class || clazz == float.class) {
                //     method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asDouble());
                // }
                if (clazz == Double.class || clazz == double.class) {
                    method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asDouble());
                }
                if (clazz == String.class) {
                    method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)).asText());
                }
                // if (clazz == Date.class) {
                //     method.invoke( entity, objectNode.with( ReflectUtils.getFieldName( method)));
                // }
                if (clazz == List.class && method.getParameters()[0].getParameterizedType() instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType)method.getParameters()[0].getParameterizedType();
                    method.invoke( entity, jsonArray2List( objectNode.withArray( ReflectUtils.getFieldName( method)), (Class<?>)parameterizedType.getActualTypeArguments()[0]));
                }
            }

            return entity;
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> List<T> jsonArray2List(ArrayNode arrayNode, Class<T> clazz) {
        try {
            List<T> list = new ArrayList<>();
            arrayNode.elements().forEachRemaining(elem-> {
                T entity;
                try {
                    entity = clazz.newInstance();
                    entity = jsonObject2Entity((ObjectNode)elem, entity);
                    list.add(entity);
                } catch (InstantiationException | IllegalAccessException e) {
                    throw new RuntimeException(e.getMessage(), e);
                }
            });
            return list;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }

    }

    private static final char[] BINARY_ARRAY = "0123456789abcdefghijklmnopqrstuvwxyz".toCharArray();

    public static String toBinaryString(Long l, int binary) {
        long quotient;
        long remainder = l;

        String ret = "";
        while ((quotient = (remainder / binary)) != 0) {
            ret = BINARY_ARRAY[(int)(remainder % binary)] + ret;
            remainder = quotient;
        }
        ret = BINARY_ARRAY[(int)(remainder % binary)] + ret;
        return ret;
    }

    public static <T extends Enum<T>> T getEnumFromString(Class<T> c, String string) {
        if( c != null && string != null ) {
            try {
                return Enum.valueOf(c, string.trim().toUpperCase());
            } catch(IllegalArgumentException ex) {
            }
        }
        return null;
    }

    public static void main(String...args) {
        long l = 35;
        System.out.println( );
        System.out.println( Long.toBinaryString( l) + "/" + toBinaryString(l, 2));
        System.out.println( Long.toHexString( l) + "/" + toBinaryString(l, 16));
        System.out.println( toBinaryString(l, 36));
        System.out.println( ConvertUtils.str2Date( "2020-07-14 23:42:23").getTime());
        System.out.println( ConvertUtils.str2Date( "2020-07-16 23:50").getTime());

    }
}

