package com.shca.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Jackson-based JSON utility class to replace Alibaba FastJSON
 * Provides equivalent functionality with Jackson ObjectMapper
 */
public class JsonUtils {
    
    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    static {
        // Configure ObjectMapper for pretty printing and null handling
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }
    
    /**
     * Parse JSON string to object (replaces JSON.parseObject)
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to object: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Parse JSON string to Map (replaces JSON.parseObject for Map)
     */
    public static Map<String, Object> parseObject(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to Map: " + e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * Parse JSON string to List (replaces JSON.parseArray)
     */
    public static <T> List<T> parseArray(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to List: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * Parse JSON string to List of Maps (replaces JSON.parseArray for generic lists)
     */
    public static List<Map<String, Object>> parseArray(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to List: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * Convert object to JSON string (replaces JSON.toJSONString)
     */
    public static String toJSONString(Object obj) {
        if (obj == null) {
            return "null";
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSON: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Convert object to pretty JSON string (replaces JSON.toJSONString with SerializerFeature.PrettyFormat)
     */
    public static String toJSONStringPretty(Object obj) {
        if (obj == null) {
            return "null";
        }
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to pretty JSON: " + e.getMessage());
            return "{}";
        }
    }
    
    /**
     * Check if string is valid JSON object (replaces JSON.isValidObject)
     */
    public static boolean isValidObject(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            JsonNode node = objectMapper.readTree(json);
            return node != null && node.isObject();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Check if string is valid JSON array (replaces JSON.isValidArray)
     */
    public static boolean isValidArray(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            JsonNode node = objectMapper.readTree(json);
            return node != null && node.isArray();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Create new JSON object (replaces new JSONObject())
     */
    public static ObjectNode createObject() {
        return objectMapper.createObjectNode();
    }
    
    /**
     * Create new JSON array (replaces new JSONArray())
     */
    public static ArrayNode createArray() {
        return objectMapper.createArrayNode();
    }
    
    /**
     * Get ObjectMapper instance for advanced usage
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
}
