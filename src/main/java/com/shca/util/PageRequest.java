package com.shca.util;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;

// Avoid class name clash with Spring Data's PageRequest

import java.util.ArrayList;
import java.util.List;

/**
 * Modern Spring Boot replacement for OrderPage
 * Provides pagination and sorting functionality compatible with Spring Data
 */
public class PageRequest {
    
    private final int page;
    private final int size;
    private final String[] sortFields;
    private final String[] sortDirections;
    private int total = 0;
    
    public PageRequest(int page, int size) {
        this.page = Math.max(0, page - 1); // Convert to 0-based indexing
        this.size = Math.max(1, size);
        this.sortFields = null;
        this.sortDirections = null;
    }
    
    public PageRequest(int page, int size, String[] sortFields, String[] sortDirections) {
        this.page = Math.max(0, page - 1); // Convert to 0-based indexing
        this.size = Math.max(1, size);
        this.sortFields = sortFields;
        this.sortDirections = sortDirections;
        
        if (sortFields != null && sortDirections != null && sortFields.length != sortDirections.length) {
            throw new IllegalArgumentException("Sort fields and directions arrays must have the same length");
        }
    }
    
    /**
     * Get the page number (0-based)
     */
    public int getPage() {
        return page;
    }
    
    /**
     * Get the page size
     */
    public int getSize() {
        return size;
    }
    
    /**
     * Get the offset for SQL queries
     */
    public int getOffset() {
        return page * size;
    }
    
    /**
     * Get the total number of records
     */
    public int getTotal() {
        return total;
    }
    
    /**
     * Set the total number of records
     */
    public void setTotal(int total) {
        this.total = Math.max(0, total);
    }
    
    /**
     * Get the total number of pages
     */
    public int getTotalPages() {
        return total == 0 ? 0 : (total - 1) / size + 1;
    }
    
    /**
     * Get the current page number (1-based for display)
     */
    public int getCurrentPage() {
        return page + 1;
    }
    
    /**
     * Generate ORDER BY clause for SQL queries
     */
    public String getOrderBy() {
        if (sortFields == null || sortFields.length == 0) {
            return "";
        }
        
        StringBuilder orderBy = new StringBuilder();
        for (int i = 0; i < sortFields.length; i++) {
            if (!StringUtils.hasLength(sortFields[i])) {
                continue;
            }
            
            if (orderBy.length() > 0) {
                orderBy.append(", ");
            }
            
            orderBy.append(sortFields[i]);
            
            if (sortDirections != null && i < sortDirections.length && StringUtils.hasLength(sortDirections[i])) {
                orderBy.append(" ").append(sortDirections[i]);
            } else {
                orderBy.append(" ASC"); // Default to ASC
            }
        }
        
        return orderBy.length() > 0 ? " ORDER BY " + orderBy.toString() : "";
    }
    
    /**
     * Generate LIMIT clause for SQL queries
     */
    public String getLimit() {
        return " LIMIT " + size + " OFFSET " + getOffset();
    }
    
    /**
     * Convert to Spring Data Pageable
     */
    public Pageable toPageable() {
        if (sortFields == null || sortFields.length == 0) {
            return new org.springframework.data.domain.PageRequest(page, size);
        }
        
        List<Sort.Order> orders = new ArrayList<>();
        for (int i = 0; i < sortFields.length; i++) {
            if (!StringUtils.hasLength(sortFields[i])) {
                continue;
            }
            
            Sort.Direction direction = Sort.Direction.ASC; // Default
            if (sortDirections != null && i < sortDirections.length && StringUtils.hasLength(sortDirections[i])) {
                try {
                    direction = Sort.Direction.fromString(sortDirections[i]);
                } catch (IllegalArgumentException e) {
                    // Keep default ASC if invalid direction
                }
            }
            
            orders.add(new Sort.Order(direction, sortFields[i]));
        }
        
        Sort sort = new Sort(orders);
        return new org.springframework.data.domain.PageRequest(page, size, sort);
    }
    
    /**
     * Create a PageRequest from legacy OrderPage-style parameters
     */
    public static PageRequest of(int page, int size) {
        return new PageRequest(page, size);
    }
    
    /**
     * Create a PageRequest with sorting
     */
    public static PageRequest of(int page, int size, String[] sortFields, String[] sortDirections) {
        return new PageRequest(page, size, sortFields, sortDirections);
    }
}
