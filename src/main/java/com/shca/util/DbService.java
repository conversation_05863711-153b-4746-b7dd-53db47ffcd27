package com.shca.util;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Service;

import com.shca.sql.support.callback.IntegerCallback;
import com.shca.sql.support.callback.StringCallback;

/**
 * Modern replacement for the legacy com.ywang.sql.support.Db class
 * 
 * This service provides the same interface as the legacy Db class but uses
 * Spring's JdbcTemplate internally for better integration with Spring Boot.
 */
@Service("db")
public class DbService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Execute a SELECT query and return multiple rows
     */
    public List<Map<String, Object>> select(String sql, Object... params) {
        return jdbcTemplate.queryForList(sql, params);
    }

    /**
     * Execute a SELECT query and return a single row
     * Returns null if no results found (compatible with legacy Db behavior)
     */
    public Map<String, Object> selectUnique(String sql, Object... params) {
        try {
            return jdbcTemplate.queryForMap(sql, params);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * Execute a SELECT query and return an Integer result
     */
    public Integer selectUniqueInt(String sql, Object... params) {
        try {
            return jdbcTemplate.queryForObject(sql, Integer.class, params);
        } catch (EmptyResultDataAccessException e) {
            return 0;
        }
    }

    /**
     * Execute a SELECT query and return a String result
     */
    public String selectUniqueString(String sql, Object... params) {
        try {
            return jdbcTemplate.queryForObject(sql, String.class, params);
        } catch (EmptyResultDataAccessException e) {
            return "";
        }
    }

    /**
     * Execute a SELECT query and return a Long result
     */
    public Long selectUniqueLong(String sql, Object... params) {
        try {
            return jdbcTemplate.queryForObject(sql, Long.class, params);
        } catch (EmptyResultDataAccessException e) {
            return 0L;
        }
    }

    /**
     * Legacy compatibility method with callback support
     * Use explicit callback parameter to avoid ambiguity
     */
    @SuppressWarnings("unchecked")
    public <T> T selectUniqueWithCallback(String sql, Object callback, Object... params) {
        if (callback instanceof IntegerCallback) {
            return (T) selectUniqueInt(sql, params);
        } else if (callback instanceof StringCallback) {
            return (T) selectUniqueString(sql, params);
        } else {
            // Treat callback as a regular parameter
            Object[] allParams = new Object[params.length + 1];
            allParams[0] = callback;
            System.arraycopy(params, 0, allParams, 1, params.length);
            return (T) selectUnique(sql, allParams);
        }
    }

    /**
     * Execute an UPDATE statement
     */
    public int update(String sql, Object... params) {
        return jdbcTemplate.update(sql, params);
    }

    /**
     * Execute a DELETE statement
     */
    public int delete(String sql, Object... params) {
        return jdbcTemplate.update(sql, params);
    }

    /**
     * Execute an INSERT statement and return the generated key
     */
    public long insert(String sql, Object... params) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        
        jdbcTemplate.update(connection -> {
            var ps = connection.prepareStatement(sql, new String[]{"id"});
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        }, keyHolder);
        
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : 0L;
    }

    /**
     * Execute a paginated query and return results with pagination info
     */
    public List<Map<String, Object>> selectWithPagination(String sql, PageRequest pageRequest, Object... params) {
        // First get the total count
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_query";
        int total = jdbcTemplate.queryForObject(countSql, Integer.class, params);
        pageRequest.setTotal(total);

        // Then get the paginated results
        String paginatedSql = sql + pageRequest.getOrderBy() + pageRequest.getLimit();
        return jdbcTemplate.queryForList(paginatedSql, params);
    }

    /**
     * Execute a paginated query with custom count query
     */
    public List<Map<String, Object>> selectWithPagination(String sql, String countSql, PageRequest pageRequest, Object... params) {
        // Get the total count using custom count query
        int total = jdbcTemplate.queryForObject(countSql, Integer.class, params);
        pageRequest.setTotal(total);

        // Get the paginated results
        String paginatedSql = sql + pageRequest.getOrderBy() + pageRequest.getLimit();
        return jdbcTemplate.queryForList(paginatedSql, params);
    }

    /**
     * Get the underlying JdbcTemplate for advanced operations
     */
    public JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }
}
