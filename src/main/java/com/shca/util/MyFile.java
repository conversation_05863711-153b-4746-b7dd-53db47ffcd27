package com.shca.util;

import java.io.File;
import java.util.ArrayList;
import java.util.regex.Pattern;

public class MyFile extends File {
    public MyFile(String pathname) {
        super(pathname);
    }

    public MyFile(String parent, String child) {
        super(parent, child);
    }

    public MyFile(File parent, String child) {
        super(parent, child);
    }

    public File[] listFiles(String filter) {
        Pattern p = null;
        if(filter!=null && filter.length() > 0){
            p = Pattern.compile(filter);
        }
        return listFiles(p);
    }
    public File[] listFiles(Pattern filter) {
        String ss[] = list();
        if (ss == null) return null;
        ArrayList<File> files = new ArrayList<>();
        for (String s : ss)
            if ((filter == null) || filter.matcher(s).matches())
                files.add(new File(s));
        return files.toArray(new File[files.size()]);
    }
    public static void main(String[] args) {
        MyFile file = new MyFile("C:\\Users\\<USER>\\Downloads");
        File[] files = file.listFiles("LeaveWord-master.*.zip");
        System.out.println(files.length);
        System.out.println(files[0].getName());
        for (File file1:files) {
            System.out.println(file1.getName());
        }
    }
}
