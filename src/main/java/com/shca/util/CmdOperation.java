/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-10 14:11:45
 * @FilePath: /analysis_engine/src/main/java/com/shca/util/CmdOperation.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Date;

import com.ywang.utils.ConvertUtils;

public interface CmdOperation {
    static String cmd(String cmd) {
        Runtime run = Runtime.getRuntime();
        try {
            String [] cmds = {"/bin/sh", "-c", cmd};
            Process process = run.exec( cmds);
            String line;
            BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuffer out = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null ) {
                out.append(line);
                System.out.println(line);
                System.out.print(out);
            }
            try {
                process.waitFor();
                return "OK" + "->" + out;
            } catch (InterruptedException e) {
                e.printStackTrace();
                return "inner exception" + "->" + out;
            } finally {
                process.destroy();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "outer exception" ;
        }
    }

    static void moveDir(String dir) throws FileNotFoundException {
        String trashbinPath = "/mydata/trashbin";
        File dirFile = new File(dir);
        if (! dirFile.exists()) {
            throw new FileNotFoundException();
        }
        cmd( "mv " + dir + " " + trashbinPath + "/" + dir + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm"));
    }

    static void moveDir(File dir) throws FileNotFoundException {
        moveDir(dir.getAbsolutePath());
    }

    static void moveDir(String dir, String dest) throws FileNotFoundException {
        if(dest == null) {
            moveDir(dir);
            return;
        }
        String moveDirCmd = "mv " + dir + " " + dest + "/" + dir + "_" + ConvertUtils.date2Str( new Date(), "yyyyMMddHHmm");
        cmd(moveDirCmd);
    }

    static void softLinkDirs(File source, File dest) {
        String softLinkDirsCmd = "ln -snf " + source.getAbsolutePath() + " " + dest.getAbsolutePath();
        cmd( softLinkDirsCmd);
    }

static void printResults(Process process) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = "";
        while ((line = reader.readLine()) != null) {
            System.out.println(line);
        }
    }

    static void ShellCmd(String cmdStr) {
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command("sh", "-c", cmdStr);
        try {
            com.shca.util.CmdOperation.printResults(processBuilder.start());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
