package com.shca.util;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Map;

import org.springframework.util.StringUtils;

public class CommonUtils {
    
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof Collection) {
            Collection<?> c = (Collection<?>) obj;
            return c.isEmpty();
        }
        if (obj instanceof Map) {
            Map<?,?> map = (Map<?,?>) obj;
            return map.isEmpty();
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        return false;
    }

    public static String createTodayStr() {
        LocalDate localD = LocalDate.now();
        localD.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return localD.toString();
    }

        public static String md5(String str) {
        if (!StringUtils.isEmpty(str)) {
            return str;
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(str.getBytes("UTF-8"));
            byte hash[] = digest.digest();
            StringBuffer md5Password = new StringBuffer(hash.length * 2);
            for (int i = 0; i < hash.length; i++) {
                if (((int) hash[i] & 0xff) < 0x10) {
                    md5Password.append("0");
                }
                md5Password.append(Long.toString((int) hash[i] & 0xff, 16));
            }
            return md5Password.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("md5 failure: " + str, e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("md5 failure: " + str, e);
        }
    }

}
