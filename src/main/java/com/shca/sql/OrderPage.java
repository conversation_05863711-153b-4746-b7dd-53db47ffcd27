package com.shca.sql;

/**
 * Modern replacement for com.ywang.sql.OrderPage
 */
public class OrderPage {
    
    private int pageNumber = 0;
    private int pageSize = 20;
    private long total = 0;
    
    public OrderPage() {
    }
    
    public OrderPage(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public OrderPage(int pageNumber, int pageSize) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }
    
    public int getPageNumber() {
        return pageNumber;
    }
    
    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public long getTotal() {
        return total;
    }
    
    public void setTotal(long total) {
        this.total = total;
    }
    
    public Page asPage() {
        Page page = new Page(pageSize);
        page.setTotal(total);
        return page;
    }
}
