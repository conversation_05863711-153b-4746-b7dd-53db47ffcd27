/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 17:51:27
 * @FilePath: /analysis_engine/src/main/java/com/shca/demo/TestUnit.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.demo;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import com.shca.util.HttpUtils;

public class TestUnit {
    public static void main(String[] args) {
        //String result = post();
        //System.out.println(result);
        String result2 = getStatus("5a66a94c-5719-430e-8cd7-6f7253eb100d");
        System.out.println(result2);
    }

    private static String getStatus(String id){
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String url = "http://**************:90/api/workflows/v1/"+id+"/status";
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();


            URL _url = new URL( url);
            HttpGet httpGet = new HttpGet( new URI( _url.getProtocol(), null, _url.getHost(), _url.getPort(), _url.getPath(), _url.getQuery(), null));

            // 设置超时
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();//设置请求和传输超时时间
            httpGet.setConfig(requestConfig);


            httpResponse = httpClient.execute( httpGet);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "utf-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }


            return result;
        } catch (IOException | UnsupportedOperationException | URISyntaxException e) {
            throw new RuntimeException(  "Client request(Get)(" + url + "):", e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }
}
