package com.shca.demo;

import com.ywang.utils.HttpUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URL;

public class TestUnit {
    public static void main(String[] args) {
        //String result = post();
        //System.out.println(result);
        String result2 = getStatus("5a66a94c-5719-430e-8cd7-6f7253eb100d");
        System.out.println(result2);
    }

    private static String post(){
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        long id = System.currentTimeMillis();
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();

            String url = "http://**************:90/api/workflows/v1";
            HttpPost httpPost = new HttpPost( url);

            // 设置超时
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);


            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);
            builder.addBinaryBody("workflowUrl","http://test.douwifi.cn/myWorkflow.wdl".getBytes("utf-8"));

            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);
          /*  httpPost.setHeader("Content-Type","multipart/form-data; boundary=something");*/
            httpResponse = httpClient.execute( httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "utf-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
                return result;
            }

            return null;
        } catch (Exception e) {
            throw new RuntimeException( "Client request(Post)(" + id + ")", e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }

    private static String getStatus(String id){
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String url = "http://**************:90/api/workflows/v1/"+id+"/status";
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();


            URL _url = new URL( url);
            HttpGet httpGet = new HttpGet( new URI( _url.getProtocol(), null, _url.getHost(), _url.getPort(), _url.getPath(), _url.getQuery(), null));

            // 设置超时
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();//设置请求和传输超时时间
            httpGet.setConfig(requestConfig);


            httpResponse = httpClient.execute( httpGet);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "utf-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }


            return result;
        } catch (Exception e) {
            throw new RuntimeException(  "Client request(Get)(" + url + "):", e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }
}
