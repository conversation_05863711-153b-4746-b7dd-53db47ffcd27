/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-15 17:40:45
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2025-03-04 15:39:17
 * @FilePath: /analysis_engine/src/main/java/com/shca/CanerTypeEnum.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Qualifier;

import com.shca.module.cancer.service.ProcessService;
import com.shca.module.cancer.service.QcSummaryService;
import com.shca.module.cancer.service.support.QcSummaryConfigService;

public interface Pipeline {

    static final Class<?>[] innerClasses = Pipeline.class.getDeclaredClasses();
    public String getFullName();
    public Map<String, String> getQcParams();
    public ProcessService getProcessService();
    public QcSummaryService getQcSummaryService();

    public static Pipeline parse(String cancerType) {
        Pipeline result = null;
        for (Class<?> innerClass : innerClasses) {
            if (innerClass.isEnum()) {
                for (Object obj : innerClass.getEnumConstants()) {
                    if (obj instanceof Pipeline && ((Pipeline) obj).getFullName().equals(cancerType)) {
                        result = (Pipeline) obj;
                        break;
                    }
                }
            }
        }
        return result;
    }
    public enum NormalCancerType implements Pipeline
    {
        // TODO: add more caner types
        AZ("pancreatic"),BZ("breast"), CZ("colon"), DZ("fuke"),
        GZ("gastric"),GI("gist"), HZ("hepa"), NZ("neuro"),
        OH("ovarian"), PZ("pan"), SZ("bone"),CH("colonHDT"),
        GH("gastricHDT"),IH("gistHDT"),HH("hepaHDT"),AH("pancreaticHDT");

        @Resource
        @Qualifier("normalProcessService")
        private ProcessService processService;
        @Resource
        @Qualifier("normalQcSummaryService")
        private QcSummaryService qcSummaryService;
        private final String fullName;
        NormalCancerType(String string) {
            this.fullName = string;
        }

        @Override
        public String getFullName() {
            return this.fullName;
        }

        @Override
        public Map<String, String> getQcParams() {
            return QcSummaryConfigService.getQcSummaryConfig();
        }

        @Override
        public ProcessService getProcessService() {
            return this.processService;
        }

        @Override
        public QcSummaryService getQcSummaryService() {
            return this.qcSummaryService;
        }
    }

    public enum PeixiCancerType implements Pipeline
    {
        // TODO: add more caner types
        AJ("peixipancreatic"),BJ("peixibreast"), CJ("peixicolon"), DJ("peixifuke"),
        GJ("peixigastric"), HJ("peixihepa"), IJ("peixigist"), NJ("peixineuro"),
        PJ("peixipan"), PM("peixipanMLPA"),SJ("peixibone");

        @Resource
        @Qualifier("peixiProcessService")
        private ProcessService processService;
        @Resource
        @Qualifier("peixiQcSummaryService")
        private QcSummaryService qcSummaryService;
        private final String fullName;
        PeixiCancerType(String string) {
            this.fullName = string;
        }

        @Override
        public String getFullName() {
            return this.fullName;
        }

        @Override
        public Map<String, String> getQcParams() {
            return QcSummaryConfigService.getQcSummaryConfig();
        }

        @Override
        public ProcessService getProcessService() {
            return this.processService;
        }

        @Override
        public QcSummaryService getQcSummaryService() {
            return this.qcSummaryService;
        }
    }

    public enum RnaCancerType implements Pipeline {
        BR("peixirna"), CR("peixicolonrna");

        @Resource
        @Qualifier("peixiProcessService")
        private ProcessService processService;
        @Resource
        @Qualifier("peixiQcSummaryService")
        private QcSummaryService qcSummaryService;
        private final String fullName;
        RnaCancerType(String fullName) {
            this.fullName = fullName;
        }
        @Override
        public String getFullName() {
            return this.fullName;
        }

        @Override
        public Map<String, String> getQcParams() {
            return QcSummaryConfigService.getRnaQcSummaryConfig();
        }

        @Override
        public ProcessService getProcessService() {
            return this.processService;
        }
        @Override
        public QcSummaryService getQcSummaryService() {
            return this.qcSummaryService;
        }
    }

    public enum BrcaCancerType implements Pipeline {
        BA("brca"), BH("brcahdt");

        @Resource
        @Qualifier("peixiProcessService")
        private ProcessService processService;
        @Resource
        @Qualifier("peixiQcSummaryService")
        private QcSummaryService qcSummaryService;
        private final String fullName;
        BrcaCancerType(String fullName) {
            this.fullName = fullName;
        }
        @Override
        public String getFullName() {
            return this.fullName;
        }

        @Override
        public Map<String, String> getQcParams() {
            return QcSummaryConfigService.getBrcaQcSummaryConfig();
        }

        @Override
        public ProcessService getProcessService() {
            return this.processService;
        }
        // TODO: add more caner types
        @Override
        public QcSummaryService getQcSummaryService() {
            return this.qcSummaryService;
        }
    }

    public enum CtDNACancerType implements Pipeline {
        PC("ctDna");

        @Resource
        @Qualifier("normalProcessService")
        private ProcessService processService;
        @Resource
        @Qualifier("normalQcSummaryService")
        private QcSummaryService qcSummaryService;
        private final String fullName;
        CtDNACancerType(String fullName) {
            this.fullName = fullName;
        }
        @Override
        public String getFullName() {
            return this.fullName;
        }

        @Override
        public Map<String, String> getQcParams() {
            return QcSummaryConfigService.getQcSummaryConfig();
        }

        @Override
        public ProcessService getProcessService() {
            return this.processService;
        }
        @Override
        public QcSummaryService getQcSummaryService() {
            return this.qcSummaryService;
        }
    }
}
