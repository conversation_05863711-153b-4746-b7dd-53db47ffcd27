/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-08 22:26:06
 * @FilePath: /analysis_engine/src/main/java/com/shca/cancer/service/CancerService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.cancer.service;


import com.shca.file.service.FileService;

import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.support.Db;
import com.ywang.utils.DbUtils;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.*;

import javax.annotation.Resource;


public class CancerService {
    private final static Logger LOGGER = Logger.getLogger( FileService.class);

    @Resource
    private Db db;

    // 此方法好像只是负责根据filing结果，重新同步数据库记录。
    public Map<String, Object> filing() {
        Map<String,Object> ret = new HashMap<>();

        String sql = "SELECT * FROM shca_project_cancer";
        List<Map<String,Object>> cancerList = db.select(sql);

        SysConfigManager config = SysConfigManager.getInstance();
        String samplesHomePath = config.getValue("workspace.home_path");

        // 这样设置date会造成时间不准确。
        // Date now = new Date();

        for ( Map<String,Object> cancer: cancerList ) {
            String sampleName = (String)cancer.get("sample_name");
            File directory = new File( samplesHomePath + "/" + sampleName );

            if( !directory.exists() ){
                Date now = new Date();
                cancer.put("delete_time", now );
                List<Object> paramList = new ArrayList<>();
                String sql_filing = DbUtils.insertSql("shca_project_cancer_filing", cancer, paramList);
                db.insert(sql_filing, paramList.toArray(new Object[0]));
                db.delete("DELETE FROM shca_project_cancer WHERE id = ?" , cancer.get("id"));
            }
        }
        ret.put("errcode",0);
        return ret;
    }
}
