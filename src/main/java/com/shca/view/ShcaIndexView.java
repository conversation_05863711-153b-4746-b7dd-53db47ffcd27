/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-10-19 00:38:10
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-10-22 18:09:04
 * @FilePath: /analysis_engine/src/main/java/com/shca/view/ShcaIndexView.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.shca.view;


import com.shca.entity.User;
import com.shca.entity.WeiXinSessionUser;
import com.shca.util.StringUtilsRefact;
// import com.weixin.qyapi.http.WeiXinSessionUser;
// import com.weixin.qyapi.vo.User;
import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JspForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.http.SessionUser;
import com.ywang.sql.support.Db;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ShcaIndexView {

    @Autowired
    private Db db;

    public Forward console(ViewParams params) {
        return new JspForward( "/shca/console.jsp", (Map<String, Object>)null);
    }

    public Forward login(ViewParams params) {
        if (params.getUser() != null) {
            return new JspForward( "project_cancer_list.htm",null,true);
        }

        if (StringUtils.isEmpty( params.getString( "username"))) {
            return new JspForward( "/shca/login.jsp", null);
        }

        Map<String,Object> account = db.selectUnique( "select username,real_name,role_code from comm_rbac_account where username=? and password=?"
                ,params.getString( "username"), StringUtilsRefact.md5( params.getString( "password")));

        if (account == null) {
            Map<String,Object> ret = new HashMap<>();

            ret.put( "errmsg", "用户名或密码错误");
            return new JspForward( "/shca/login.jsp", ret);
        }

        User user = new User();
        user.setUserId( (String)account.get( "username"));
        user.setName( (String)account.get( "real_name"));

        Map<String,Object> session = new HashMap<>();
        SessionUser sessionUser = new WeiXinSessionUser(user);
        session.put( Constant.SESSION_USER_KEY, sessionUser);
        session.put("role_code",account.get("role_code"));

        //System.out.println("session role_code："+session.get("role_code"));
        return new JspForward( "project_cancer_list.htm", (Map<String, Object>) null, session, true);
    }

    public Forward logon(ViewParams params) {
        Map<String,Object> session = new HashMap<>();

        User user = new User();
        user.setUserId( "WangYun");
        user.setName( "王贇");

        SessionUser sessionUser = new WeiXinSessionUser(user);
        session.put( Constant.SESSION_USER_KEY, sessionUser);

        return new JspForward( "/shca/project/project_project_list.jsp", (Map<String, Object>) null, session, false);
    }


    public Forward logout(ViewParams params) {
        Map<String,Object> session = new HashMap<>();
        session.put( Constant.SESSION_USER_KEY, null);

        return new JspForward( "/shca/login.jsp", (Map<String, Object>) null, session, false);
    }

    public static void main(String[] args) {
        System.out.println(StringUtilsRefact.md5("admin8080"));
    }
}
