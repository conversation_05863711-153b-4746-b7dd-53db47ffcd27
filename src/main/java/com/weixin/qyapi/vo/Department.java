package com.weixin.qyapi.vo;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Department implements Serializable,Comparable<Department>{

    private Long id;

    private String name;

    private Long parentId;

    private int order;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    @Override
    public int compareTo(Department department) {
        return parentId.compareTo( department.parentId);
    }


    public static void main(String...args) {
        Department dept = new Department();
        dept.setId( 8L);
        dept.setParentId( 180L);
        Department dept2 = new Department();
        dept2.setId( 180L);
        dept2.setParentId( 222L);
        Department dept3 = new Department();
        dept3.setId( 222L);
        dept3.setParentId( 233L);


        List<Department> departments = new ArrayList<>();
        departments.add( dept);
        departments.add( dept2);
        departments.add( dept3);

        Collections.sort( departments);

        System.out.println( departments);

        System.out.println(  new Long( 1).compareTo( 2L));
    }
}
