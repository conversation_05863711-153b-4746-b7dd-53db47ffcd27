package com.weixin.qyapi.http;

import com.weixin.qyapi.vo.User;
import com.ywang.http.SessionUser;

import java.util.List;
import java.util.Map;


public class WeiXinSessionUser implements SessionUser {

    private final User user;

    public WeiXinSessionUser(User user) {
        this.user = user;
    }

    @Override
    public String getUserName() {
        return user.getUserId();
    }

    public String getName() {
        return user.getName();
    }

    @Override
    public String getOrgCode() {
        String orgCode = "";
        for (long department : user.getDepartment()) {
            orgCode += department + ",";
        }
        if (orgCode.length() > 0) {
            orgCode = orgCode.substring( 0, orgCode.length() - 1);
        }

        return orgCode;
    }

    @Override
    public long getRoleCode() {
        return 0;
    }

    public String getUserId() {
        return user.getUserId();
    }

    public String getMobile() {
        return user.getMobile();
    }

    public String getPosition() {
        return user.getPosition();
    }

    public String getEmail() {
        return user.getEmail();
    }

    public String getAvatar() {
        return user.getAvatar();
    }

    public String getTelephone() {
        return user.getTelephone();
    }

    public String getAlias() {
        return user.getAlias();
    }

    public String getQrCode() {
        return user.getQrCode();
    }

    public String toString() {
        return getUserName();
    }

    public User getUser() {
        return user;
    }

}
