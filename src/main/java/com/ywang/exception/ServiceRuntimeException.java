package com.ywang.exception;


import java.util.Map;

public class ServiceRuntimeException extends RuntimeException {

    private final Map<String,Object> errorInfo;

    public ServiceRuntimeException(String message, Throwable cause, Map<String, Object> errorInfo) {
        super(message, cause);
        this.errorInfo = errorInfo;
    }

    public Map<String, Object> getErrorInfo() {
        return errorInfo;
    }
}
