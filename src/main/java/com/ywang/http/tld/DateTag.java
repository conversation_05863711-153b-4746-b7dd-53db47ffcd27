package com.ywang.http.tld;


import com.ywang.utils.DateUtils;

import java.util.Date;

public class DateTag {
    
    public static String beforeDesc(Date date) {
        if (date == null) {
            return "";
        }
        long now = System.currentTimeMillis();
        long l = date.getTime();
        long between = now - l;
        if (between < DateUtils.ONE_MINUTE) {
            return "一分钟内";
        } 
        if (between < DateUtils.ONE_HOUR) {
            return (between / DateUtils.ONE_MINUTE) + "分钟";
        }
        if (between < DateUtils.ONE_DATE) {
            return (between / DateUtils.ONE_HOUR) + "小时";
        }
        if (between < DateUtils.ONE_MONTH) {
            return (between / DateUtils.ONE_DATE) + "天";
        }
        return "大约" + (between / DateUtils.ONE_MONTH) + "个月";
    }
}
