package com.ywang.http.tld;



import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SecurityTag {
    
    public static List owener(List roles, long roleCode) {
        List ret = new ArrayList();
        for(Map<String,Object> role : (List<Map<String,Object>>) roles) {
            Long _roleCode = (Long)role.get("role_code");
            if (_roleCode != null && ((_roleCode & roleCode) > 0)) {
                ret.add(role);
            }
        }
        return ret;
    }
    
    public static boolean hasPrivilege(Map user, String service, String strDo) {
//        return Security.hasPrivilege(user, service, strDo);
        return false;
    }

    public static boolean hasRole(Map user, String roleName) {
//        return Security.hasRole(user, roleName);
        return false;
    }
}
