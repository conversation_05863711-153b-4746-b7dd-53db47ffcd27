/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-13 21:09:10
 * @FilePath: /analysis_engine/src/main/java/com/ywang/http/listener/support/AccountHttpSessionListener.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.http.listener.support;

import com.ywang.framework.mvc.Constant;
import com.ywang.http.SessionUser;
import com.ywang.utils.LogUtils;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;
import java.util.HashMap;
import java.util.Map;


public class AccountHttpSessionListener implements HttpSessionListener {

    @Override
    public void sessionCreated(HttpSessionEvent httpSessionEvent) {
        HttpSession session = httpSessionEvent.getSession();
        ServletContext context = session.getServletContext();
        Map<String,HttpSession> sessions = (Map<String,HttpSession>)context.getAttribute(Constant.CONTEXT_SESSIONS_KEY);
        if (sessions == null) {
            sessions = new HashMap<>();
            context.setAttribute(Constant.CONTEXT_SESSIONS_KEY, sessions);
        }
        SessionUser user = (SessionUser) session.getAttribute(Constant.SESSION_USER_KEY);
        if (user == null) {
            return;
        }
        sessions.put( user.getUserName() , session);
//        String s = session.getServletContext().getInitParameter("max_account");
//        System.out.println(s);

    }

    @Override
    public void sessionDestroyed(HttpSessionEvent httpSessionEvent) {
        try {
            HttpSession session = httpSessionEvent.getSession();
            ServletContext context = session.getServletContext();
            Map<String,HttpSession> sessions = (Map<String,HttpSession>)context.getAttribute(Constant.CONTEXT_SESSIONS_KEY);

            SessionUser user = (SessionUser) session.getAttribute(Constant.SESSION_USER_KEY);
            if (user == null) {
                return;
            }
            sessions.remove( user.getUserName());
            LogUtils.FRAMEWORK_LOG.info("用户退出: " + user.getUserName());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
