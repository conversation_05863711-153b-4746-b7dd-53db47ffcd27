package com.ywang.http.filter.support;

import com.ywang.utils.LogUtils;
import com.ywang.utils.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


public class ProxyFilter implements Filter {

    private String targetUrl = "http://222.66.73.172:63396/";

    private static final String TARGET_URL_PARAM_NAME = "";

    public void init(FilterConfig config) throws ServletException {
        String encoding = config.getInitParameter(TARGET_URL_PARAM_NAME);
        if (encoding != null && encoding.length() > 0) {
            this.targetUrl = encoding;
        }
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;

        try {
//            chain.doFilter(request, response);
            String url = targetUrl;
            String servletPath = req.getServletPath();
            System.out.println("servlet path:" + servletPath);
            if (servletPath.indexOf("gisserver") < 0 && (servletPath.endsWith(".FLY")
                    || servletPath.endsWith("example.jsp") || servletPath.endsWith("test.jsp")
                    || servletPath.endsWith(".js") || servletPath.endsWith(".css") || servletPath.endsWith(".css"))) {
                chain.doFilter(request, response);
                return;
            }



            if (StringUtils.hasLength(servletPath)) {
                if (servletPath.startsWith("/") && url.endsWith("/")) {
                    servletPath = servletPath.substring(1);
                }

                url += servletPath;
            }

            if (StringUtils.hasLength(req.getQueryString())) {
                url += "?" + req.getQueryString();
            }
            System.out.println("跳转: " + url);
            LogUtils.FRAMEWORK_LOG.info("跳转: " + url);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            // 设置超时
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);



            CloseableHttpResponse response2 = httpClient.execute(httpPost);

            // 获取请求response的Body
            HttpEntity entity = response2.getEntity();
            entity.writeTo(res.getOutputStream());




        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void destroy() {

    }

}
