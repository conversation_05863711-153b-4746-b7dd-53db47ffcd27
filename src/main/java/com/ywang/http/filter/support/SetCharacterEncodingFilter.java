/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-13 01:17:59
 * @FilePath: /analysis_engine/src/main/java/com/ywang/http/filter/support/SetCharacterEncodingFilter.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.http.filter.support;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


public final class SetCharacterEncodingFilter implements Filter {

    private String encoding = "UTF-8";

    private static final String ENCODING_PARAM_NAME = "encoding";


    public void init(FilterConfig config) throws ServletException {
        String encoding = config.getInitParameter( ENCODING_PARAM_NAME);
        if (encoding != null && encoding.length() > 0) {
            this.encoding = encoding;
        }
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        try {
            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse res = (HttpServletResponse)response;
            req.setCharacterEncoding( encoding);


            res.setHeader("Cache-Control", "no-cache");
            res.setHeader("Cache-Control", "no-store");
            res.setHeader("Pragma", "no-cache");
            res.setDateHeader("Expires", 0);


            chain.doFilter(request, response);
        } catch (Exception e) {
        }
    }

    public void destroy() {
    }
}
