package com.ywang.http.filter.support;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


public final class SetCharacterEncodingFilter implements Filter {

    private String encoding = "UTF-8";

    private static final String ENCODING_PARAM_NAME = "encoding";


    public void init(FilterConfig config) throws ServletException {
        String encoding = config.getInitParameter( ENCODING_PARAM_NAME);
        if (encoding != null && encoding.length() > 0) {
            this.encoding = encoding;
        }
    }

    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {

        try {
            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse res = (HttpServletResponse)response;
            req.setCharacterEncoding( encoding);


            res.setHeader("Cache-Control", "no-cache");
            res.setHeader("Cache-Control", "no-store");
            res.setHeader("Pragma", "no-cache");
            res.setDateHeader("Expires", 0);


            chain.doFilter(request, response);
        } catch (Exception e) {
        }
    }

    public void destroy() {
    }
}
