/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-14 01:29:07
 * @FilePath: /analysis_engine/src/main/java/com/ywang/http/SessionUser.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.http;


import com.ywang.module.rbac.vo.User;
import java.io.Serializable;

public interface SessionUser extends User, Serializable {

    public String getUserName();
    public String getName();
    public String getOrgCode();
    public long getRoleCode();


}
