package com.ywang.http.support;

import com.ywang.utils.CollectionUtils;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.Principal;
import java.util.*;


public final class MultipartHttpServletRequest implements HttpServletRequest {
    
    private final Map<String,String[]> params;
    
    private final HttpServletRequest request;

    public MultipartHttpServletRequest(Map<String, String[]> params, HttpServletRequest request) {
        this.params = CollectionUtils.copy(params);
        this.request = request;
    }

    public String getAuthType() {
        return request.getAuthType();
    }

    public Cookie[] getCookies() {
        return request.getCookies(); 
    }

    public long getDateHeader(String s) {
        return request.getDateHeader(s);  
    }

    public String getHeader(String s) {
        return request.getHeader(s); 
    }

    public Enumeration getHeaders(String s) {
        return request.getHeaders(s); 
    }

    public Enumeration getHeaderNames() {
        return request.getHeaderNames(); 
    }

    public int getIntHeader(String s) {
        return request.getIntHeader(s); 
    }

    public String getMethod() {
        return request.getMethod(); 
    }

    public String getPathInfo() {
        return request.getPathInfo(); 
    }

    public String getPathTranslated() {
        return request.getPathTranslated();  
    }

    public String getContextPath() {
        return request.getContextPath(); 
    }

    public String getQueryString() {
        return request.getQueryString(); 
    }

    public String getRemoteUser() {
        return request.getRemoteUser();  
    }

    public boolean isUserInRole(String s) {
        return request.isUserInRole(s);
    }

    public Principal getUserPrincipal() {
        return request.getUserPrincipal(); 
    }

    public String getRequestedSessionId() {
        return request.getRequestedSessionId();
    }

    public String getRequestURI() {
        return request.getRequestURI();
    }

    public StringBuffer getRequestURL() {
        return request.getRequestURL();
    }

    public String getServletPath() {
        return request.getServletPath();
    }

    public HttpSession getSession(boolean b) {
        return request.getSession(b); 
    }

    public HttpSession getSession() {
        return request.getSession(); 
    }

  /*  @Override
    public String changeSessionId() {
        return null;
    }*/

    public boolean isRequestedSessionIdValid() {
        return request.isRequestedSessionIdValid(); 
    }

    public boolean isRequestedSessionIdFromCookie() {
        return request.isRequestedSessionIdFromCookie();
    }

    public boolean isRequestedSessionIdFromURL() {
        return request.isRequestedSessionIdFromURL(); 
    }

    public boolean isRequestedSessionIdFromUrl() {
        return request.isRequestedSessionIdFromUrl(); 
    }

    @Override
    public boolean authenticate(HttpServletResponse httpServletResponse) throws IOException, ServletException {
        return false;
    }

    @Override
    public void login(String s, String s1) throws ServletException {

    }

    @Override
    public void logout() throws ServletException {

    }

    @Override
    public Collection<Part> getParts() throws IOException, IllegalStateException, ServletException {
        return null;
    }

    @Override
    public Part getPart(String s) throws IOException, IllegalStateException, ServletException {
        return null;
    }

   /* @Override
    public <T extends HttpUpgradeHandler> T upgrade(Class<T> aClass) throws IOException, ServletException {
        return null;
    }*/

    public Object getAttribute(String s) {
        return request.getAttribute(s);
    }

    public Enumeration getAttributeNames() {
        return request.getAttributeNames();
    }

    public String getCharacterEncoding() {
        return request.getCharacterEncoding(); 
    }

    public void setCharacterEncoding(String s) throws UnsupportedEncodingException {
        request.setCharacterEncoding(s);
    }

    public int getContentLength() {
        return request.getContentLength(); 
    }

  /*  @Override
    public long getContentLengthLong() {
        return request.getContentLength();
    }*/

    public String getContentType() {
        return request.getContentType(); 
    }

    public ServletInputStream getInputStream() throws IOException {
        return request.getInputStream(); 
    }

    public String getParameter(String s) {
        String[] paramValues = params.get(s);
        if (CollectionUtils.isEmpty(paramValues)) {
            return null;
        }
        return paramValues[0]; 
    }

    public Enumeration getParameterNames() {
        return Collections.enumeration(params.keySet());
    }

    public String[] getParameterValues(String s) {
        return params.get(s);
    }

    public Map getParameterMap() {
        return params; 
    }

    public String getProtocol() {
        return request.getProtocol();
    }

    public String getScheme() {
        return request.getScheme();
    }

    public String getServerName() {
        return request.getServerName();
    }

    public int getServerPort() {
        return request.getServerPort();
    }

    public BufferedReader getReader() throws IOException {
        return request.getReader();
    }

    public String getRemoteAddr() {
        return request.getRemoteAddr();
    }

    public String getRemoteHost() {
        return request.getRemoteHost();
    }

    public void setAttribute(String s, Object o) {
        request.setAttribute(s,o);
    }

    public void removeAttribute(String s) {
        request.removeAttribute(s);
    }


    public Locale getLocale() {
        return request.getLocale();
    }

    public Enumeration getLocales() {
        return request.getLocales();
    }

    public boolean isSecure() {
        return request.isSecure();
    }

    public RequestDispatcher getRequestDispatcher(String s) {
        return request.getRequestDispatcher(s);
    }

    public String getRealPath(String s) {
        return request.getRealPath(s);
    }

    public int getRemotePort() {
        return request.getRemotePort();
    }

    public String getLocalName() {
        return request.getLocalName();
    }

    public String getLocalAddr() {
        return request.getLocalAddr();
    }

    public int getLocalPort() {
        return request.getLocalPort();
    }

    @Override
    public ServletContext getServletContext() {
        return null;
    }

    @Override
    public AsyncContext startAsync() {
        return null;
    }

    @Override
    public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) {
        return null;
    }

    @Override
    public boolean isAsyncStarted() {
        return false;
    }

    @Override
    public boolean isAsyncSupported() {
        return false;
    }

    @Override
    public AsyncContext getAsyncContext() {
        return null;
    }

    @Override
    public DispatcherType getDispatcherType() {
        return null;
    }
}
