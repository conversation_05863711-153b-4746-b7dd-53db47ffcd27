package com.ywang.http.support;

import com.ywang.http.SessionUser;

public class DefaultSessionUser implements SessionUser {

    private String userName;

    private String name;

    private long roleCode;

    @Override
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public long getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(long roleCode) {
        this.roleCode = roleCode;
    }

    @Override
    public String getOrgCode() {
        return "" + roleCode;
    }

    public String toString() {
        return getUserName();
    }
}
