package com.ywang.utils.fastjson;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONScanner;
import com.alibaba.fastjson.parser.deserializer.AbstractDateDeserializer;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.parser.deserializer.SqlDateDeserializer;
import com.ywang.utils.ConvertUtils;

import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.Date;


public class UnixTimeToDateDeserializer extends AbstractDateDeserializer {


    protected Date cast(DefaultJSONParser parser, Type clazz, Object fieldName, Object val) {
        if (val instanceof  Number) {
            return new Date(((Number) val).longValue() * 1000);
        }
        if (val instanceof String) {
            return new Date(ConvertUtils.lon( (String)val) * 1000);
        }
        return null;
    }

    public int getFastMatchToken() {
        return 2;
    }


    public static void main(String...args) {
        System.out.println( new Date(1538496001L * 1000).toLocaleString());
    }
}
