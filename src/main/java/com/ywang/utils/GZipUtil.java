package com.ywang.utils;

import com.alibaba.fastjson.JSONArray;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.binary.Base64;
import org.apache.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class GZipUtil {
    private final static Logger log = Logger.getLogger(GZipUtil.class);
    /**
     * 压缩GZip
     *
     * @return String
     */
    public static String gZip(String input) {
        byte[] bytes = null;
        GZIPOutputStream gzip = null;
        ByteArrayOutputStream bos = null;
        try {
            bos = new ByteArrayOutputStream();
            gzip = new GZIPOutputStream(bos);
            gzip.write(input.getBytes(CharEncoding.UTF_8));
            gzip.finish();
            gzip.close();
            bytes = bos.toByteArray();
            bos.close();
        } catch (Exception e) {
            log.error("压缩出错：", e);
        } finally {
            try {
                if (gzip != null)
                    gzip.close();
                if (bos != null)
                    bos.close();
            } catch (final IOException ioe) {
                log.error("压缩出错：", ioe);
            }
        }
        return Base64.encodeBase64String(bytes);
    }

    /**
     * 解压GZip
     *
     * @return String
     */
    public static String unGZip(String input) {
        if(input == null)  return null;
        byte[] bytes;
        String out = input;
        GZIPInputStream gzip = null;
        ByteArrayInputStream bis;
        ByteArrayOutputStream bos = null;
        try {
            bis = new ByteArrayInputStream(Base64.decodeBase64(input));
            gzip = new GZIPInputStream(bis);
            byte[] buf = new byte[1024];
            int num;
            bos = new ByteArrayOutputStream();
            while ((num = gzip.read(buf, 0, buf.length)) != -1) {
                bos.write(buf, 0, num);
            }
            bytes = bos.toByteArray();
            out = new String(bytes, CharEncoding.UTF_8);
            gzip.close();
            bis.close();
            bos.flush();
            bos.close();
        } catch (Exception e) {
            log.error("解压出错：", e);
        } finally {
            try {
                if (gzip != null)
                    gzip.close();
                if (bos != null)
                    bos.close();
            } catch (final IOException ioe) {
                log.error("解压出错：", ioe);
            }
        }
        return out;
    }

    public static void main(String[] args) {

        System.out.println(getDistance(121.435667,31.297329,121.435667,31.297329));

        String aa = "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" ;
        String lon_lat_str = unGZip(aa);
        int validCnt = 0;
        String prev_longitude = null;
        String prev_latitude = null;
        double distance = 0;
        double maxSpeed = -1;
        double minSpeed = -1;
        if (JSONArray.isValidArray(lon_lat_str)) {
            JSONArray lonLatArray = JSONArray.parseArray(lon_lat_str);
            String prevTime = null;
            for (int i = 0; i < lonLatArray.size(); i++) {
                try {
                    String lonLatStr = lonLatArray.getString(i);
                    String[] strs = lonLatStr.split(",");
                    String longitude = strs[0];
                    String latitude = strs[1];
                    String speed = strs[2];
                    String accuracy = strs[3];
                    String time = strs[4];
                    String step = strs[5];

                    //判断时间是否重复，重复的数据无法插入数据库
                    if (prevTime != null && prevTime.equals(time)) {
                        continue;
                    }
                    validCnt++;
                    prevTime = time;

                    if(!"-1".equals(longitude) &&  !"-1".equals(latitude)){
                        if(prev_longitude!=null && prev_latitude!=null){
                            distance+=getDistance(Double.parseDouble(prev_longitude),Double.parseDouble(prev_latitude),Double.parseDouble(longitude),Double.parseDouble(latitude));
                            if(maxSpeed<Double.parseDouble(speed)){
                                maxSpeed = Double.parseDouble(speed);
                            }
                            if(minSpeed>Double.parseDouble(speed)){
                                minSpeed = Double.parseDouble(speed);
                            }
                        }

                        prev_longitude = longitude;
                        prev_latitude = latitude;
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        System.out.println(distance);
    }

    private static final double EARTH_RADIUS = 6371393; // 平均半径,单位：m

    /**
     * 计算两个经纬度之间的距离
     * @param lon1
     * @param lat1
     * @param lon2
     * @param lat2
     * @return 单位米
     */
    private  static double getDistance(double lon1,double lat1,double lon2,double lat2) {
        // 经纬度（角度）转弧度。弧度用作参数，以调用Math.cos和Math.sin
        double radiansAX = java.lang.Math.toRadians(lon1); // A经弧度
        double radiansAY = java.lang.Math.toRadians(lat1); // A纬弧度
        double radiansBX = java.lang.Math.toRadians(lon2); // B经弧度
        double radiansBY = java.lang.Math.toRadians(lat2); // B纬弧度

        // 公式中“cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2”的部分，得到∠AOB的cos值
        double cos = java.lang.Math.cos(radiansAY) * java.lang.Math.cos(radiansBY) * java.lang.Math.cos(radiansAX - radiansBX)
                + java.lang.Math.sin(radiansAY) * java.lang.Math.sin(radiansBY);
        double acos = java.lang.Math.acos(cos>1?1:cos); // 反余弦值
        return EARTH_RADIUS * acos; // 最终结果

    }
}
