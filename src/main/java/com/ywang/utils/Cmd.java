package com.ywang.utils;

import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;

/**
 * Created by hen<PERSON><PERSON> on 2020/10/22.
 */
public class Cmd {

    private final static Logger LOGGER = Logger.getLogger( Cmd.class);

    public static String exec(String cmd) {
        Runtime run = Runtime.getRuntime();
        Process process = null;
        try {
            String [] cmds = {"/bin/sh","-c", cmd};
            process = run.exec( cmds);
            String line = null;
            BufferedReader stdoutReader = new LineNumberReader( new InputStreamReader(process.getInputStream()));
            StringBuffer stdout = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null) {
                stdout.append( line + "\n");
            }
            int returnCode = -1;
            try {
                returnCode = process.waitFor();
            } catch (InterruptedException e) {
            }

            LOGGER.info( cmd + "[" + returnCode + "]: " + stdout);
            return stdout.toString();
        } catch (IOException e) {
            throw new RuntimeException( e);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
    }

    public static String execFromError(String cmd) {
        Runtime run = Runtime.getRuntime();
        Process process = null;
        try {
            String [] cmds = {"/bin/sh","-c", cmd};
            process = run.exec( cmds);
            String line = null;
            BufferedReader stdoutReader = new LineNumberReader( new InputStreamReader(process.getErrorStream()));
            StringBuffer stdout = new StringBuffer();
            while ((line = stdoutReader.readLine()) != null) {
                stdout.append( line + "\n");
            }
            int returnCode = -1;
            try {
                returnCode = process.waitFor();
            } catch (InterruptedException e) {
            }

            LOGGER.info( cmd + "[" + returnCode + "]: " + stdout);
            return stdout.toString();
        } catch (IOException e) {
            throw new RuntimeException( e);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
    }
}
