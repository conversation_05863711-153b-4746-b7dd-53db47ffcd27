package com.ywang.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * thread safety
 */
public final class DateUtils {

    private DateUtils() {
    }

    public final static long ONE_MINUTE = 60 * 1000;

    public final static long ONE_HOUR = 60 * ONE_MINUTE;

    public final static long ONE_DATE = 24 * ONE_HOUR;

    public final static long ONE_MONTH = 30 * ONE_DATE;

    public final static long ONE_WEEK = 7 * ONE_DATE;

    
    public static Date max(Date...dates) {
        if (CollectionUtils.isEmpty(dates)) {
            return null;
        }
        Date ret = null;
        for (Date date : dates) {
            if (date == null) {
                continue;
            }
            if (ret == null || ret.getTime() < date.getTime()) {
                ret = date;
            }
        }
        
        return ret;
    }

    public static Date min(Date...dates) {
        if (CollectionUtils.isEmpty(dates)) {
            return null;
        }
        Date ret = null;
        for (Date date : dates) {
            if (date == null) {
                continue;
            }
            if (ret == null || ret.getTime() > date.getTime()) {
                ret = date;
            }
        }

        return ret;
    }

    public static Date getPrevMonth(Date date){
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.add( Calendar.MONTH, -1);
        return calendar.getTime();

    }

    public static Date getNextMonth(Date date){
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.add( Calendar.MONTH, 1);
        return calendar.getTime();

    }

    /**
     * 获取当前周的开始时间
     * @return
     */
    public static Date getFirstDayOfWeek(Date date){
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.add(Calendar.DAY_OF_YEAR, -7);
        }
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONTH);
        return calendar.getTime();

    }

    /**
     * 获取当前周的结束时间
     * @return
     */
    public static Date getLastDayOfWeek(Date date){
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return calendar.getTime();

    }
    
    public static Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    public static Date getFirstDayOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime( date);
        }
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        return calendar.getTime();
    }

    /**
     * 日期转换成字符串
     * @param date
     * @return str
     */
    public static String dateToStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = sdf.format(date);
        return str;
    }

    /**
     * 字符串转换成日期
     * @param str
     * @return date
     */
    public static Date strToDate(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return date;
    }

    /**
     * 字符串转换成日期
     * @param str
     * @return date
     */
    public static Date strToDate(String str, String formatStr) {
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        Date date = null;
        try {
            date = format.parse(str);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return date;
    }

    /**
     *UTA 时间格式转换
     * add by zhuojie
     */
    public static Date toUAT(long epochs){
        return new Date(epochs * 1000);
    }


    public static Date clear(Date date) {
        if (date == null) {
            return date;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    


    public static void main(String[] args)  {

    }
}
