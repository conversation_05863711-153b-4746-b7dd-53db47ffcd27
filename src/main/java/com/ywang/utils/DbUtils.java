package com.ywang.utils;


import javax.sql.DataSource;
import java.lang.reflect.Method;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.io.ByteArrayInputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * thread safety
 */
public final class DbUtils {

    public static final Pattern TABLE_NAME_FROM_INSERT_SQL = Pattern.compile( "insert\\sinto\\s(\\S+)\\s\\(.*\\)\\s.*");

    public static final Pattern TABLE_NAME_FROM_UPDATE_SQL = Pattern.compile( "update\\s(\\S+)\\sset\\s.*");

    public static final Pattern TABLE_NAME_FROM_DELETE_SQL = Pattern.compile( "delete\\sfrom\\s(\\S+)\\swhere\\s(.*)");

    private DbUtils() {
    }

    public static String getTableName(String sql) {
        if (StringUtils.isBlank( sql)) {
            return null;
        }
        Matcher matcher = TABLE_NAME_FROM_INSERT_SQL.matcher( sql);
        if (matcher.find()) {
            return StringUtils.trimAllWhitespace( matcher.group( 1));
        }
        matcher = TABLE_NAME_FROM_UPDATE_SQL.matcher( sql);
        if (matcher.find()) {
            return StringUtils.trimAllWhitespace( matcher.group( 1));
        }
        matcher = TABLE_NAME_FROM_DELETE_SQL.matcher( sql);
        if (matcher.find()) {
            return StringUtils.trimAllWhitespace( matcher.group( 1));
        }
        return null;
    }

    /**
     * thread unsafety
     */
    public static void release(ResultSet rs, java.sql.Statement ps, Connection conn) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
            }
        }
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
            }
        }
    }

    /**
     * thread unsafety
     */
    public static void rollback(Connection conn) {
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException e) {
            }
        }
    }

    public static List<String> parseTableNames(String sql) {
        List<String> tableNames = new ArrayList<>();
        Matcher matcher = Pattern.compile("(?i)from((?:\\s\\w+\\.\\w+|\\s\\w+)(?:(\\s?\\,+\\s?\\w+\\.\\w+)+|\\s?\\,+\\s?\\w+)*)|join((?:\\s\\w+\\.\\w+|\\s\\w+)(?:(\\s?\\,+\\s?\\w+\\.\\w+)+|\\s?\\,+\\s?\\w+)*)").matcher(sql);
        System.out.println( sql);
        while (matcher.find()) {
            System.out.println( matcher.groupCount());
            for (int i = 0; i < matcher.groupCount(); i++) {
                System.out.println( "match " + i + " = " + matcher.group( i));
            }
            tableNames.add( matcher.group( 1));
        }
        return tableNames;
    }


    /**
     * thread unsafety
     */
    public static Object getGeneratedKey(PreparedStatement ps) throws SQLException {
        ResultSet generatedKey = ps.getGeneratedKeys();
        if (generatedKey.next()) {
            return generatedKey.getObject(1);
        }
        return null;
    }

    /**
     * thread safety
     */
    public static Object convert(String value, Integer type) {
        if (!StringUtils.hasLength(value)) {
            return value;
        }
        switch (type) {
            case Types.BOOLEAN:
                return Boolean.valueOf(value);
            case Types.INTEGER:
                return Integer.parseInt(value);
            case Types.BIGINT:
                return Long.parseLong(value);
            case Types.FLOAT:
                return Float.parseFloat(value);
            case Types.NUMERIC:
            case Types.DECIMAL:
            case Types.DOUBLE:
                return Double.parseDouble(value);
            case Types.CHAR:
            case Types.CLOB:
            case Types.VARCHAR:
                return value;
            case Types.TIME:
            case Types.TIMESTAMP:
            case Types.DATE:
                return ConvertUtils.str2Date(value);
            default :
                return value;
        }
    }

    /**
     * thread unsafety
     */
    public static int getType(ResultSetMetaData rsmd, int idx) throws SQLException {
        int type = rsmd.getColumnType(idx);
        if (Types.CHAR == type && rsmd.getColumnDisplaySize(idx) == 1) {
            return Types.BOOLEAN;
        }
        return type;
    }

    /**
     * thread unsafety
     */
    public static Map<String, Integer> getTableColumnMap(DataSource dataSource, String tableName) {
        if (!StringUtils.hasLength(tableName)) {
            throw new IllegalArgumentException("table name cannot be null!");
        }
        Connection conn = null;
        java.sql.Statement s = null;
        ResultSet rs = null;
        Map<String, Integer> ret = new HashMap<String, Integer>();
        try {
            conn = dataSource.getConnection();
            s = conn.createStatement();
            rs = s.executeQuery(String.format("select * from %s where 1=0 ", tableName));
            ResultSetMetaData rsmd = rs.getMetaData();
            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                ret.put(rsmd.getColumnName(i), DbUtils.getType(rsmd, i));
            }

            return ret;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            release(rs, s, conn);
        }
    }

    /**
     * thread safety
     */
    public static void checkSql(String sql, String flag) {
        if (sql == null) {
            throw new IllegalArgumentException("sql cannot be null");
        }
        if (sql.toLowerCase().trim().indexOf(flag) != 0) {
            throw new IllegalArgumentException("no '" + flag + "' start with sql[" + sql + "]");
        }
    }

    /**
     * thread unsafety
     */
    public static void setObject(PreparedStatement ps, int index, Object param) throws SQLException {
        if (param instanceof Boolean) {
            ps.setBoolean(index, (Boolean) param);
        } else if (param instanceof Character) {
            ps.setString(index, param.toString());
        } else if (param instanceof String) {
            ps.setString(index, (String) param);
        } else if (param instanceof Byte) {
            ps.setInt(index, (Byte) param);
        } else if (param instanceof Short) {
            ps.setInt(index, (Short) param);
        } else if (param instanceof Integer) {
            ps.setInt(index, (Integer) param);
        } else if (param instanceof Long) {
            ps.setLong(index, (Long) param);
        } else if (param instanceof Float) {
            ps.setFloat(index, (Float) param);
        } else if (param instanceof Double) {
            ps.setDouble(index, (Double) param);
        } else if (param instanceof byte[]) {
            ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) param);
            ps.setBinaryStream(index, bis, bis.available());
        } else if (param instanceof Date)  {
            ps.setTimestamp(index, new Timestamp(((Date) param).getTime()));
        } else {

            ps.setObject(index, param);
        }
    }

    /**
     * thread unsafety
     */
    public static String insertSql(String tableName, Map<String, Object> data, List<Object> params) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (CollectionUtils.isEmpty( data)) {
            throw new IllegalArgumentException( "data cannot be null or empty!");
        }
        if (params == null) {
            throw new IllegalArgumentException( "params cannot be null!");
        }

        StringBuffer insertSql = new StringBuffer(500);
        StringBuffer valuesSql = new StringBuffer(50);
        insertSql.append("insert into " + tableName + "(");
        valuesSql.append("values(");

        for (String columnName : data.keySet()) {
            insertSql.append(columnName + ",");
            valuesSql.append("?,");
            params.add(data.get(columnName));
        }
        insertSql.deleteCharAt(insertSql.length() - 1).append(")").append(valuesSql.toString()).deleteCharAt(insertSql.length() - 1).append(")");
        return insertSql.toString();
    }

    public static String insertOracleSql(String tableName, Map<String, Object> data, List<Object> params) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (CollectionUtils.isEmpty( data)) {
            throw new IllegalArgumentException( "data cannot be null or empty!");
        }
        if (params == null) {
            throw new IllegalArgumentException( "params cannot be null!");
        }

        StringBuffer insertSql = new StringBuffer(500);
        StringBuffer valuesSql = new StringBuffer(50);
        insertSql.append("insert into " + tableName + "(id,");
        valuesSql.append("values(SEQ_" + tableName + ".NEXTVAL,");

        for (String columnName : data.keySet()) {
            insertSql.append(columnName + ",");
            valuesSql.append("?,");
            params.add(data.get(columnName));
        }
        insertSql.deleteCharAt(insertSql.length() - 1).append(")").append(valuesSql.toString()).deleteCharAt(insertSql.length() - 1).append(")");
        return insertSql.toString();
    }


    public static String insertSql(String tableName, Map<String, Object>data) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (CollectionUtils.isEmpty( data)) {
            throw new IllegalArgumentException( "data cannot be null or empty!");
        }

        StringBuffer insertSql = new StringBuffer(500);
        StringBuffer valuesSql = new StringBuffer(50);
        insertSql.append("insert into " + tableName + "(");
        valuesSql.append("values(");

        for (String columnName : data.keySet()) {
            insertSql.append(columnName + ",");
            valuesSql.append("?,");
        }
        insertSql.deleteCharAt(insertSql.length() - 1).append(")").append(valuesSql.toString()).deleteCharAt(insertSql.length() - 1).append(")");
        return insertSql.toString();
    }

    public static String insertSql(String tableName, Object entity, List<Object> params) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (entity == null) {
            throw new IllegalArgumentException( "entity cannot be null!");
        }
        Method[] methods = ReflectUtils.getGetMethods(entity);
        if (CollectionUtils.isEmpty(methods)) {
            throw new IllegalArgumentException( "entity has not any field!");
        }

        StringBuffer insertSql = new StringBuffer(500);
        StringBuffer valuesSql = new StringBuffer(50);
        insertSql.append("insert into " + tableName + "(");
        valuesSql.append("values(");

        
        for (Method method : methods) {
            insertSql.append(getColumnName(method) + ",");
            try {
                params.add(method.invoke(entity));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            valuesSql.append("?,");
        }
        insertSql.deleteCharAt(insertSql.length() - 1).append(")").append(valuesSql.toString()).deleteCharAt(insertSql.length() - 1).append(")");
        return insertSql.toString();
    }
    
    private static String getColumnName(Method method) {
        if (!ReflectUtils.isGetMethod(method)) {
            throw new IllegalArgumentException("method is not Get method");
        }
        String columnName = method.getName();
        columnName = java.lang.Character.toLowerCase(columnName.charAt(3)) + columnName.substring(4);
        StringBuffer ret = new StringBuffer(columnName.length() + 10);
        
        for (char c :columnName.toCharArray()) {
            if (java.lang.Character.isUpperCase(c)) {
                ret.append("_" + java.lang.Character.toLowerCase(c));
            } else {
                ret.append(c);
            }
        }
        return ret.toString();
        
    }

    public static String updateSql(String tableName, Map<String, Object> data, List<Object> params) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (CollectionUtils.isEmpty( data)) {
            throw new IllegalArgumentException( "data cannot be null or empty!");
        }
        if (params == null) {
            throw new IllegalArgumentException( "params cannot be null!");
        }

        StringBuffer updateSql = new StringBuffer(500);
        updateSql.append("update " + tableName + " set ");
        for (String columnName : data.keySet()) {
            if (data.get(columnName) == null) {
                updateSql.append(columnName + "=null,");
            } else {
                updateSql.append(columnName + "=?,");
                params.add(data.get(columnName));
            }
        }
        updateSql.deleteCharAt(updateSql.length() - 1).append(" where 1=1 ");
        return updateSql.toString();
    }

    public static String updateSql(String tableName, Map<String, Object> data) {
        if (!StringUtils.hasLength( tableName)) {
            throw new IllegalArgumentException( "tableName cannot be null or empty!");
        }
        if (CollectionUtils.isEmpty( data)) {
            throw new IllegalArgumentException( "data cannot be null or empty!");
        }


        StringBuffer updateSql = new StringBuffer(500);
        updateSql.append("update " + tableName + " set ");
        for (String columnName : data.keySet()) {
            updateSql.append(columnName + "=?,");
        }
        updateSql.deleteCharAt(updateSql.length() - 1).append(" where 1=1 ");
        return updateSql.toString();
    }

    public static String whereSql(Map<String, String> condition, List<Object> params) {
        if (params == null) {
            throw new IllegalArgumentException( "params cannot be null!");
        }

        StringBuffer whereSql = new StringBuffer(500);
        for (String name : condition.keySet()) {
            String[] arr = name.split("-");
            String strCondition;
            String strValue = condition.get(name);
            if (!StringUtils.hasLength(strValue)) {
                continue;
            }
            Object value = null;
            if (name.startsWith("str-")) {
                strCondition = arr[2];
                value = strValue;
            } else if (name.startsWith("date-")) {
                strCondition = arr[2];
                value = ConvertUtils.str2Date(strValue);
            } else if (name.startsWith("int-")) {
                strCondition = arr[2];
                value = ConvertUtils.integer(strValue);
            } else if (name.startsWith("dou-")) {
                strCondition = arr[2];
                value = ConvertUtils.dou(strValue);
            } else if (name.startsWith("lon-")) {
                strCondition = arr[2];
                value = ConvertUtils.lon(strValue);
            } else {
                continue;
            }
            if (value == null) {
                continue;
            }
            if ("eq".equals(condition)) {
                strCondition = "=";
            } else if ("lk".equals(condition)) {
                strCondition = "like";
                value = "%" + value + "%";
            } else if ("rlk".equals(condition)) {
                strCondition = "like";
                value = value + "%";
            } else if ("llk".equals(condition)) {
                strCondition = "like";
                value = "%" + value;
            } else if ("lt".equals(condition)) {
                strCondition = ">";
            } else if ("lteq".equals(condition)) {
                strCondition = ">=";
            } else if ("gt".equals(condition)) {
                strCondition = "<";
            } else if ("gteq".equals(condition)) {
                strCondition = "<=";
                if (value instanceof Date) {
                    Calendar c = Calendar.getInstance();
                    c.setTime((Date) value);
                    if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) == 0) {
                        c.set(Calendar.HOUR, 0);
                        c.set(Calendar.MINUTE, 0);
                        c.set(Calendar.SECOND, 0);
                        c.set(Calendar.MILLISECOND, 0);
                        c.add(Calendar.DATE, + 1);
                        value = c.getTime();
                        strCondition = "<";
                    } else {
                        value = c.getTime();
                    }

                }
            } else {
                throw new RuntimeException( "condition is error: " + strCondition);
            }
            whereSql.append(String.format( " and %s %s ?", arr[1], strCondition));
            params.add(value);
        }

        return whereSql.toString();
    }
}
