package com.ywang.utils;


public class Math {

    //单位: 千米
    public final static double EARTH_RADIUS = 6378.137;


    private static double getRadian(double degree) {
        return degree * java.lang.Math.PI / 180.0;
    }

    /**
     * 依据经纬度计算两点之间的距离
     * 距离单位: 米
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = getRadian( lat1);
        double radLat2 = getRadian( lat2);
        double a = radLat1 - radLat2; // 两点纬度差
        double b = getRadian(lng1) - getRadian(lng2); // 两点的经度差
        double s = 2 * java.lang.Math.asin( java.lang.Math.sqrt( java.lang.Math.pow( java.lang.Math.sin(a / 2), 2)
                + java.lang.Math.cos(radLat1) * java.lang.Math.cos(radLat2) * java.lang.Math.pow( java.lang.Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;

        return s * 1000;
    }
}
