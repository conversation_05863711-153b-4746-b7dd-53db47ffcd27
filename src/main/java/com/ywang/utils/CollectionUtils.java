package com.ywang.utils;

import java.lang.reflect.Method;
import java.util.*;
import java.lang.reflect.Array;

/**
 * thread safety
 */
public final class CollectionUtils {

    private CollectionUtils() {
    }

    /**
     * thread safety
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof Collection) {
            Collection c = (Collection) obj;
            return c.isEmpty();
        }
        if (obj instanceof Map) {
            Map map = (Map) obj;
            return map.isEmpty();
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        return false;
    }

    public static <T> boolean contain(T[] arr, T elem) {
        if (isEmpty(arr) || elem == null) {
            return false;
        }
        for (T arrElem : arr) {
            if (elem.equals(arrElem)) {
                return true;
            }
        }
        return false;
    }

    public static <T> Set<T> arr2Set(T[] arr) {
        if (isEmpty(arr)) {
            return null;
        }
        Set<T> set = new TreeSet<T>();
        for (T elem : arr) {
            set.add(elem);
        }
        return set;
    }


    
    public static <K,V> Map<K,V> copy(Map<K,V> target) {
        Map<K,V> copy = new HashMap<K,V>(target);
        return copy;
    }

    public static <T> List<T> copy(List<T> target) {
        List<T> copy = new ArrayList<T>(target);
        return copy;
    }

    public static <K,V> Map<V, Map<K,V>> list2Map(List<Map<K,V>> list, K key) {
        if (key == null) {
            throw new IllegalArgumentException("key cannot be null");
        }
        if (isEmpty(list)) {
            return null;
        }

        Map<V, Map<K,V>> map = new HashMap<V, Map<K,V>>();
        for (Map<K,V> elem : list) {
            map.put(elem.get(key), elem);
        }
        return map;
    }

    public static <K,V> Map<V, List<Map<K,V>>> list2Mapping(List<Map<K,V>> list, K key) {
        if (key == null) {
            throw new IllegalArgumentException("key cannot be null");
        }
        if (isEmpty(list)) {
            return null;
        }

        Map<V, List<Map<K,V>>> mapping = new HashMap<V, List<Map<K,V>>>();
        for (Map<K,V> elem : list) {
            List<Map<K,V>> _list = mapping.get(elem.get(key));
            if (_list == null) {
                _list = new ArrayList<Map<K,V>>();
                mapping.put(elem.get(key), _list);
            }
            _list.add( elem);
        }
        return mapping;
    }

    public static Collection<Map<String, Object>> list2Tree(List<Map<String, Object>> list) {
        return list2Tree(list, "id", "parent_id", null, "children");
    }


    public static <K,Object> Collection<Map<K,Object>> list2Tree(List<Map<K,Object>> list, K id, K parentId, Object rootValue,K children) {
        if (id == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        if (parentId == null) {
            throw new IllegalArgumentException("parentId cannot be null");
        }
        if (children == null) {
            throw new IllegalArgumentException("children key cannot be null");
        }
        if (isEmpty(list)) {
            return null;
        }

        Map<Object,Map<K,Object>> tree = new LinkedHashMap<Object, Map<K, Object>>();
        Map<Object,List<Map<K,Object>>> _children = new HashMap<Object, List<Map<K,Object>>>();
        for (Map<K,Object> elem : list) {
            List<Map<K,Object>> _child = _children.get(elem.get(id));
            if (_child == null) {
                _child = new ArrayList<Map<K, Object>>();
                _children.put(elem.get(id), _child);
            }
            elem.put(children, (Object)_child);
            Object _parentId = elem.get(parentId);
            if (_parentId != null && !_parentId.equals(rootValue)) {
                _child = _children.get(_parentId);
                if (_child == null) {
                    _child = new ArrayList<Map<K, Object>>();
                    _children.put(_parentId, _child); 
                }
                _child.add(elem);
            } else {
                Object _id = elem.get(id);
                tree.put(_id, elem);  
                _child = _children.get(_id);
                if (_child == null) {
                    _child = new ArrayList<Map<K,Object>>();
                    _children.put(_id, _child);
                }
            }
        }
        return tree.values();
    }


    public static Map<String,Object> obj2Map(Object obj, Class clazz, boolean ignoreNull) {
        Map<String, Object> ret = new HashMap<>();

        for (Method getter : ReflectUtils.getGetMethods( clazz)) {
            try {
                Object val = getter.invoke(obj);
                if (ignoreNull && val == null) {
                    continue;
                }
                ret.put(ReflectUtils.getFieldName(getter), val);
            } catch (Exception e) {

            }
        }

        return ret;
    }
}
