package com.ywang.utils;

import org.apache.commons.lang.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


public class EncryptionUtils {

    private EncryptionUtils() {}


    public static String md5(byte[] bytes) {
        if (CollectionUtils.isEmpty(bytes)) {
            return "";
        }
        try {
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest md5Digest = MessageDigest.getInstance( "MD5");
            // 使用指定的字节更新摘要
            md5Digest.update( bytes);
            // 获得密文
            byte[] md = md5Digest.digest();
            // 把密文转换成十六进制的字符串形式
            StringBuffer hexString = new StringBuffer();

            // 字节数组转换为 十六进制 数
            for (int i = 0; i < md.length; i++) {
                String shaHex = Integer.toHexString(md[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static byte[] hmacSHA1(String data, String key) {
        try {
            System.out.println("data: " + data);
            Mac mac = Mac.getInstance( "HmacSHA1");
            SecretKeySpec signingKey = new SecretKeySpec( key.getBytes(), mac.getAlgorithm());
            mac.init(signingKey);

            return mac.doFinal(data.getBytes());
        } catch (Exception e) {
            throw new RuntimeException( e);
        }
    }


    private static final char LAST2BYTE = (char)Integer.parseInt("00000011", 2);

    private static final char LAST4BYTE = (char) Integer.parseInt("00001111", 2);

    private static final char LAST6BYTE = (char) Integer.parseInt("00111111", 2);

    private static final char LEAD6BYTE = (char) Integer.parseInt("11111100", 2);

    private static final char LEAD4BYTE = (char) Integer.parseInt("11110000", 2);

    private static final char LEAD2BYTE = (char) Integer.parseInt("11000000", 2);

    private static final char[] ENCODE_TABLE = new char[] {
            'A', 'B', 'C', 'D',
            'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
            'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd',
            'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q',
            'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3',
            '4', '5', '6', '7', '8', '9', '+', '/' };


    public static String base64(byte[] from) {
        StringBuilder to = new StringBuilder( (int)(from.length * 1.34) + 3);
        int num = 0;
        char currentByte = 0;
        for (int i = 0; i < from.length; i++) {
            num = num % 8;
            while (num < 8) {
                switch (num) {
                    case 0:
                        currentByte = (char) (from[i] & LEAD6BYTE);
                        currentByte = (char) (currentByte >>> 2);
                        break;
                    case 2:
                        currentByte = (char) (from[i] & LAST6BYTE);
                        break;
                    case 4:
                        currentByte = (char) (from[i] & LAST4BYTE);
                        currentByte = (char) (currentByte << 2);
                        if ((i + 1) < from.length) {
                            currentByte |= (from[i + 1] & LEAD4BYTE) >>> 6;
                        }
                        break;
                    case 6:
                        currentByte = (char) (from[i] & LAST2BYTE);
                        currentByte = (char) (currentByte << 4);
                        if ((i + 1) < from.length) {
                            currentByte |= (from[i + 1] & LEAD2BYTE) >>> 4;
                        }
                        break;
                }
                to.append( ENCODE_TABLE[currentByte]);
                num += 6;
            }
        }
        if (to.length() % 4 != 0) {
            for (int i = 4 - to.length() % 4; i > 0; i--) {
                to.append("=");
            }
        }
        return to.toString();
    }


    public static String sha1(byte[] bytes) {
        if (CollectionUtils.isEmpty(bytes)) {
            return "";
        }
        try {

            // SHA1签名生成
            MessageDigest shaDigest = MessageDigest.getInstance("SHA-1");
            shaDigest.update( bytes);
            byte[] digest = shaDigest.digest();

            StringBuffer hexStr = new StringBuffer();
            String shaHex = "";
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append( 0);
                }
                hexStr.append( shaHex);
            }
            return hexStr.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
