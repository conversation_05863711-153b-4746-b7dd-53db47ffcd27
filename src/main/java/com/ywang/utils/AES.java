package com.ywang.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AES
{
    public static void main(String args[]) throws Exception {
        /**
         * 加密用的Key 可以用26个字母和数字组成，最好不要用保留字符，
         * 虽然不会错，至于怎么裁决，个人看情况而定
         *
         * key == AppSecret
         */
//        String cKey = "1234567890123456";
//        // 需要加密的字串
//        String cSrc = "{\"code\":\"0\",\"error_msg\":\"密码错误\",\"weixiao_openid\":\"12345678\",\"student_num\":\"888888888888\",\"name\":\"洪丹丹测试\",\"sign\":\"5C6E844C23C8F0C15AF382081D0663DC\"}";
//        // app_secret 取前16位;
//        String cIv = "0123456789123456";
//        System.out.println(cSrc);
//        // 加密
//        long lStart = System.currentTimeMillis();
//        String enString = AES.Encrypt(cSrc, cKey, cIv);
//        System.out.println("加密后的字串是：" + enString);
//
//        long lUseTime = System.currentTimeMillis() - lStart;
//        System.out.println("加密耗时：" + lUseTime + "毫秒");
//        // 解密
//        lStart = System.currentTimeMillis();
//        String DeString = AES.Decrypt(enString, cKey, cIv);
//        System.out.println("解密后的字串是：" + DeString);
//        lUseTime = System.currentTimeMillis() - lStart;
//        System.out.println("解密耗时：" + lUseTime + "毫秒");


        String  key = "5E4C2C842BBDFCED";
        String sSrc = "95906522ABE8A226876966EF6E087B4B";
        String s = "e9f90cd0acad2514bf05710883c69b47099818ce85de4bb59c74054bd4e0870e0a9e8161be1ce728bce7b3e732edf774f5e7fdd3c05ecbd793d4325c74a00023765161804b1640f8124c05df7c55780da1ac1e8fba2ffc5bd7169f29d3ea6130b92c5eeafbe7dab1b99bf9ce4c3ea995af820446dae963da769e8defa30bc4cd6ef4b7069bfb97f8c600b8b1b50510db31d1651c33491fc9ea06cef1fb765f5d2f9b017bf6e940adf6b5638d1975039007ee2fce7071c8be64b0d227552f9e87492c9069575fc529ce0bea12e235bc27b9adcc6ed5eaa405fa3b32549c99526acf0ef6d969d8351d4339532aa06af9ca82eb2b5ae201188009f47699da95f21823ac080ce856374fa1e1d50193a81c081169856e2badb95d606edf88a701ee0fa71f13c6053bda864ea6adaa947b53aa8c8c3560dbdf5caf1be149b9463745268a675de171154620db364cf7edb456ed053fe7ccde0aa0a60cdf030a0dea538afc6521922223fed92b9a39ea1d9c81d723bda442d32554b614d17175c99ffccb501c5d779e74f0decc0d24b82c460bc9b6c8e310e03df8cba11c16350522536b1e17a08ebfb097259924aab4d2d96213a49c4de68d5c53276f443e34a0c40ed137b3ab9aa725d345218be451de2a50df83cace5394e0109e758e85c0b25ef1ba2973b5eefbb87f6935299a3d79e642494e398789e4106be7cea8ee07903688df50f1cd941e9c75aa24eec6b2e9cded2a8e9b6d2f3be0d572952c92208be9ce638e5363418a0df457f4061bd03338a78651c1d8b8acdf5e7958fc1ac7c157cca1";


        System.out.println(key);
        System.out.println();
        System.out.println(sSrc.substring(0, 16));
        System.out.println();
        System.out.println(AES.encrypt( s, key, sSrc.substring(0, 16)));


    }

    public static String encrypt(String sSrc, String sKey, String sIv) throws Exception {

        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        int blockSize = cipher.getBlockSize();

        byte[] dataBytes = sSrc.getBytes("UTF-8");
        int plaintextLength = dataBytes.length;
        if (plaintextLength % blockSize != 0) {
            plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
        }

        byte[] plaintext = new byte[plaintextLength];
        System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);

        SecretKeySpec keyspec = new SecretKeySpec(sKey.getBytes("UTF-8"), "AES");
        IvParameterSpec ivspec = new IvParameterSpec(sIv.getBytes("UTF-8"));

        cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
        byte[] encrypted = cipher.doFinal(plaintext);

        return byte2hex(encrypted).toLowerCase();
    }

    public static String decrypt(String sSrc, String sKey, String sIv) throws Exception {

        byte[] encrypted1      = hex2byte(sSrc);

        Cipher cipher          = Cipher.getInstance("AES/CBC/NoPadding");
        SecretKeySpec keyspec  = new SecretKeySpec(sKey.getBytes("UTF-8"), "AES");
        IvParameterSpec ivspec = new IvParameterSpec(sIv.getBytes("UTF-8"));

        cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

        byte[] original = cipher.doFinal(encrypted1);
        String originalString = new String(original,"UTF-8");

        return originalString;
    }

    public static byte[] hex2byte(String strhex) {
        if (strhex == null) {
            return null;
        }
        int l = strhex.length();
        if (l % 2 == 1) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i != l / 2; i++) {
            b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2),
                    16);
        }

        return b;
    }

    public static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }

        return hs.toUpperCase();
    }
}