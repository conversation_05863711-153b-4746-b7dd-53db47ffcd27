package com.ywang.utils;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

public final class LogUtils {

    private LogUtils() {
    }

    public static final Log DB_LOG = LogFactory.getLog("db");

    public static final Log FRAMEWORK_LOG = LogFactory.getLog("framework");

    public static final Log SYSTEM_LOG = LogFactory.getLog("system");

    static {
//        PropertyConfigurator.configure(" D:/Code/conf/log4j.properties ");
    }

    public static String toString(Throwable e) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PrintStream ps = new PrintStream(os);
        e.printStackTrace(ps);
        return os.toString();
    }
}

