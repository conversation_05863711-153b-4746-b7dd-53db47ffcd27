package com.ywang.utils;

import org.w3c.dom.Document;
import org.w3c.dom.Node;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.DocumentBuilder;
import java.io.InputStream;
import java.io.ByteArrayInputStream;

/**
 * thread safety
 */
public final class XmlUtils {

    private final static DocumentBuilderFactory BUILDER_FACTORY = DocumentBuilderFactory.newInstance();

    private XmlUtils() {
    }

    /**
     * thread unsafety
     */
    public static Document createDocument(String content) {
        if (!StringUtils.hasLength(content)) {
            throw new IllegalArgumentException("xml content cannot be null or empty!");
        }
        try {
            DocumentBuilder db = BUILDER_FACTORY.newDocumentBuilder();
            InputStream is = new ByteArrayInputStream(content.getBytes("UTF-8"));
            return db.parse(is);
        } catch (Exception e) {
            throw new RuntimeException("create xml document error", e);
        }
    }

    /**
     * thread unsafety
     */
    public static Document createDocument(InputStream is) {
        if (is == null) {
            throw new IllegalArgumentException("xml stream cannot be null!");
        }
        try {
            DocumentBuilder db = BUILDER_FACTORY.newDocumentBuilder();
            return db.parse(is);
        } catch (Exception e) {
            throw new RuntimeException("create xml document error", e);
        }
    }

    /**
     * thread safety
     */
    public static String value(Node node) {
        if (node == null || node.getFirstChild() == null) {
            return null;
        }
        return node.getFirstChild().getNodeValue();
    }

    /**
     * thread safety
     */
    public static Node getParent(Node node, String parent) {
        if (node == null) {
            return node;
        }
        if (!StringUtils.hasLength(parent)) {
            return node;
        }
        Node parentNode = node.getParentNode();
        if (parentNode == null) {
            return parentNode;
        }
        if (parent.equals(parentNode.getNodeName())) {
            return parentNode;
        }
        return getParent(parentNode, parent);
    }
}
