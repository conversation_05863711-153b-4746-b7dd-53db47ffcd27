package com.ywang.utils;

// import net.sf.json.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.util.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

/**
 * thread safety
 */
public final class StringUtils {

    private StringUtils() {
    }

    private final static char[] H_UNIT = {'拾', '佰', '仟'};

    private final static char[] V_UNIT = {'万', '亿'};

    private final static char[] DIGIT = {'零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'};

    private final static ObjectMapper mapper = new ObjectMapper();

    public static String toString(Object obj, String nullVal) {
        if (obj == null) {
            return nullVal;
        }
        return obj.toString();
    }

    /**
     * thread safety
     */
    public static String numToCny(Number num) {
        if (num == null || (num.toString().indexOf('.') > 16) || (Double.isNaN(num.doubleValue()))) {
            return null;
        }
        long lonNum = (long) (num.doubleValue() * 100);
        String strNum = lonNum + "";
        String headNum = strNum.substring(0, strNum.length() - 2);
        String railNum = strNum.substring(strNum.length() - 2);
        String prefix = "";
        String suffix = "";
        if ("00".equals(railNum)) {
            suffix = "��";
        } else {
            suffix = DIGIT[railNum.charAt(0) - '0'] + "��" + DIGIT[railNum.charAt(1) - '0'] + "��";
        }
        char[] chDig = headNum.toCharArray();
        char zero = '0';
        byte zeroSerNum = 0;
        for (int i = 0; i < chDig.length; i++) {
            int idx = (chDig.length - i - 1) % 4;
            int vidx = (chDig.length - i - 1) / 4;
            if (chDig[i] == zero) {
                zeroSerNum++;
                if (zero == '0') {
                    zero = DIGIT[0];
                } else if (idx == 0 && vidx > 0 && zeroSerNum < 4) {
                    prefix += V_UNIT[vidx - 1];
                    zero = '0';
                }
                continue;
            }
            zeroSerNum = 0;
            if (zero != '0') {
                prefix += zero;
                zero = '0';
            }
            prefix += DIGIT[chDig[i] - '0'];
            if (idx > 0) prefix += H_UNIT[idx - 1];
            if (idx == 0 && vidx > 0) {
                prefix += V_UNIT[vidx - 1];
            }
        }
        if (prefix.length() > 0) {
            prefix += 'Բ';
        }
        return prefix + suffix;
    }

    /**
     * thread safety
     */
    public static boolean hasLength(String str) {
        return str != null && str.length() > 0;
    }

    public static boolean isBlank(String str) {
        return !hasLength( str);
    }

    /**
     * thread safety
     */
    public static boolean startsWith(String str, String prefix) {
        return hasLength(str) && str.startsWith(prefix);
    }

    /**
     * thread safety
     */
    public static String trim(String str) {
        if (!hasLength(str)) {
            return "";
        }
        char[] arrs = str.toCharArray();
        int start = 0;
        int end = arrs.length - 1;
        while (start < arrs.length && Character.isWhitespace(arrs[start])) {
            start++;
        }
        while (end > 0 && Character.isWhitespace(arrs[end])) {
            end--;
        }
        if (start > end) {
            return "";
        }

        return new String(arrs, start, end - start + 1);
    }

    /**
     * thread safety
     */
    public static String lpad(String str, char padding, int length) {
        int loop = length;
        if (str != null) {
            loop = length - str.length();
        }
        StringBuffer ret = new StringBuffer(length);
        for (int i = 0; i < loop; i++) {
            ret.append(padding);
        }
        ret.append(str);

        return ret.toString();
    }

    /**
     * thread safety
     */
    public static String capitalize(String str) {
        if (!hasLength(str)) {
            return str;
        }
        return (Character.toUpperCase(str.charAt(0)) + str.substring(1).toLowerCase());
    }

    public static String trimAllWhitespace(String str) {
        if (!hasLength(str)) {
            return str;
        }
        int len = str.length();
        StringBuilder sb = new StringBuilder(str.length());

        for(int i = 0; i < len; ++i) {
            char c = str.charAt(i);
            if(!Character.isWhitespace(c)) {
                    sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * thread safety
     */
    public static String md5(String str) {
        if (!hasLength(str)) {
            return str;
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(str.getBytes("UTF-8"));
            byte hash[] = digest.digest();
            StringBuffer md5Password = new StringBuffer(hash.length * 2);
            for (int i = 0; i < hash.length; i++) {
                if (((int) hash[i] & 0xff) < 0x10) {
                    md5Password.append("0");
                }
                md5Password.append(Long.toString((int) hash[i] & 0xff, 16));
            }
            return md5Password.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("md5 failure: " + str, e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("md5 failure: " + str, e);
        }
    }

    public static String md5(File file) {
        FileInputStream fis = null;
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            fis = new FileInputStream( file);
            byte[] buffer = new byte[8192];
            int length;
            while ((length = fis.read(buffer)) != -1) {
                digest.update(buffer, 0, length);
            }
            byte hash[] = digest.digest();
            StringBuffer md5Password = new StringBuffer(hash.length * 2);
            for (int i = 0; i < hash.length; i++) {
                if (((int) hash[i] & 0xff) < 0x10) {
                    md5Password.append("0");
                }
                md5Password.append(Long.toString((int) hash[i] & 0xff, 16));
            }
            return md5Password.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("md5 failure: ", e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("md5 failure: ", e);
        } catch (Exception e) {
            throw new RuntimeException("md5 failure: ", e);
        } finally {
            FileUtils.close( fis);
        }
    }

    public static String md5(byte[] bytes) {
        if (CollectionUtils.isEmpty(bytes)) {
            throw new IllegalArgumentException("md5 failure: 二进制码不能为空!");
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(bytes);
            byte hash[] = digest.digest();
            StringBuffer md5 = new StringBuffer(hash.length * 2);
            for (int i = 0; i < hash.length; i++) {
                if (((int) hash[i] & 0xff) < 0x10) {
                    md5.append("0");
                }
                md5.append(Long.toString((int) hash[i] & 0xff, 16));
            }
            return md5.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * thread safety
     */
    public static String toUnicode(String str) {
        if (!hasLength(str)) {
            return str;
        }
        StringBuffer ret = new StringBuffer();
        for (char c : str.toCharArray()) {
            if (c >= 19968 && c <= 171941) {
                ret.append("\\u" + Integer.toHexString(c));
            } else {
                ret.append(c);
            }
        }
        return ret.toString();
    }

    /**
     * thread safety
     * @throws JsonProcessingException
     */
    public static String toJson(Object obj) throws JsonProcessingException {
        if (obj == null) {
            return "{}";
        }
        // return JSONSerializer.toJSON(obj).toString();
        return mapper.writeValueAsString(obj);
    }

    public static String toJson(Object obj, JsonConfig config) throws JsonProcessingException {
        if (obj == null) {
            return "{}";
        }
        // return JSONSerializer.toJSON(obj, config).toString();
        return mapper.writeValueAsString(obj);
    }

    public static Map<String, Object> json2Map(String json) throws IOException {
        if (!hasLength(json)) {
            return null;
        }
        // JSONObject jsonObject = JSONObject.fromObject(json);
        JsonNode jsonObject = mapper.readTree(json);
        return json2Map(jsonObject);
    }

    private static  Map<String, Object> json2Map(JsonNode jsonNode) {
        Map<String, Object> map = new HashMap<String, Object>();
        Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String key = field.getKey();
            if (key instanceof String)  {
                JsonNode valueNode = field.getValue();
                if (valueNode.isNull()) {
                    map.put(key, null);
                } else if (valueNode instanceof JsonNode) {
                    map.put(key, json2Map(valueNode));
                } else if (valueNode instanceof ArrayNode) {
                    List<Map<String, Object>> list = new ArrayList<>();
                    for (int i = 0; i < valueNode.size(); i++) {
                        list.add(json2Map(valueNode.get(i)));
                    }
                    map.put(key, list);
                }
            } else {
                throw new IllegalArgumentException("json key must be string type!");
            }
        }
        // for (Object key : jsonNode.fields()) {
        //     if (key instanceof String) {
        //         Object value = jsonObject.get(key);
        //         if (value instanceof JSONNull) {
        //             value = null;
        //         } else if (value instanceof JSONObject) {
        //             value = json2Map((JSONObject) value);
        //         } else if (value instanceof JSONArray) {
        //             List<Map<String, Object>> list = new ArrayList<>();
        //             JSONArray array = (JSONArray)value;
        //             for (int i = 0; i < array.size(); i++) {
        //                 list.add( json2Map( array.getJSONObject( i)));
        //             }
        //             value = list;
        //         }

        //         map.put((String) key, value);
        //     } else {
        //         throw new RuntimeException("json key is not String: " + key);
        //     }
        // }
        return map;
    }

    public static List<Map<String, Object>> json2List(String json) throws IOException {
        if (!hasLength(json)) {
            return null;
        }

        // JSONArray jsonArray = JSONArray.fromObject(json);
        ArrayNode arrayNode = (ArrayNode) mapper.readTree(json);

        List< Map<String, Object>> list = new ArrayList<Map<String, Object>>(arrayNode.size());
        for (int i = 0; i < arrayNode.size(); i++) {
            // JSONObject jsonObject = jsonArray.getJSONObject(i);
            JsonNode jsonObject = arrayNode.get(i);
            Map<String, Object> map = json2Map(jsonObject);
            list.add(map);
        }
        return list;
    }

    /**
     * 生成n位随机数字
     * @param n 随机数的数字位数
     * @return n位数字组成的随机数字字符串
     */
    public static String getRandomNumStr(int n) {
        Random random = new Random();
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < n; i++) {
            randomStr.append(random.nextInt(10));
        }
        return randomStr.toString();
    }
    /**
     * 生成n位随机数字和字母
     * @param n 随机字符的位数
     * @return n位数字和字母组成的随机字符串
     */
    public static String getRandomCharStr(int n) {
        String codes = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < n; i++) {
            randomStr.append(codes.charAt(random.nextInt(62)));
        }
        return randomStr.toString();
    }



    public static void main(String[] args) {
//        System.out.println(md5("admin").toUpperCase());
//        System.out.println(md5("admin").equalsIgnoreCase("21232F297A57A5A743894AE4A801FC3"));

        //
        System.out.println( md5( "1042326805"));

        System.out.println( );


        System.out.println( ConvertUtils.toBinaryString( 8L, 36));

//        Map<String,String> params = new HashMap<>();
//        params.put( "Token", "845e7acc3f43d4573feef4be19cef3aa");
//
//        System.out.println( HttpUtils.postRequest( "https://crm.littlestar.cn/Wechat/Apicourse?Token=845e7acc3f43d4573feef4be19cef3aa", params));
//
//
//        Map<String,Object> map = json2Map( HttpUtils.postRequest( "https://crm.littlestar.cn/Wechat/Apicourse?Token=845e7acc3f43d4573feef4be19cef3aa", params));
//
//        List<Map<String,Object>> list = (List<Map<String,Object>>)map.get( "data");

//        System.out.println( HttpUtils.postRequest( "https://crm.littlestar.cn/Wechat/Apicourse?Token=845e7acc3f43d4573feef4be19cef3aa", "Token=845e7acc3f43d4573feef4be19cef3aa"));
    }


}
