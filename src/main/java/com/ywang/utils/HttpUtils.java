package com.ywang.utils;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.AbstractHttpEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;


import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class HttpUtils {

    private HttpUtils() {
    }

    public static void close(CloseableHttpClient client, CloseableHttpResponse response, HttpEntity entity) {
        try {
            EntityUtils.consume(entity);
        } catch (IOException e) {
        }
        try {
            if (response != null) {
                response.close();
            }
        } catch (IOException e) {
        }
        try {
            if (client != null) {
                client.close();
            }
        } catch (IOException e) {
        }
    }

    public static String encodeQueryString(String queryString) {
        if (!StringUtils.hasLength(queryString)) {
            return queryString;
        }
        StringBuffer ret = new StringBuffer();
        String[] queries = queryString.split("&");
        for (String query : queries) {
            int _idx = query.indexOf("=");
            String name = query.substring(0, _idx + 1);
            String value = query.substring(_idx + 1);
            value = encode( value);
            ret.append(name).append(value).append("&");
        }
        if (queries.length > 0) {
            ret.deleteCharAt(ret.length() - 1);
        }
        return ret.toString();
    }

    public static String encode(String param) {
        if (!StringUtils.hasLength( param)) {
            return param;
        }
        try {
            /**
             * URL编码格式:
             * 1 HTML4规范: 空格被编码成"+", 而字符串"+"被编码成%2B
             * 2 RFC-3986规范: 空格被编码成%20, 而字符串"+"被编码成%2B
             *
             * 参考资料:
             * 1 https://my.oschina.net/joymufeng/blog/620205
             */
            return URLEncoder.encode( param, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
        }

        return param;
    }


    private final static String PHONE_REG = "\\b(ip(hone|od)|android|opera m(ob|in)i"
            +"|windows (phone|ce)|blackberry"
            +"|s(ymbian|eries60|amsung)|p(laybook|alm|rofile/midp"
            +"|laystation portable)|nokia|fennec|htc[-_]"
            +"|mobile|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";

    private final static Pattern PHONE_PATTERN = Pattern.compile( PHONE_REG);

    public static boolean isFromMobile(HttpServletRequest request) {
        String userAgent = request.getHeader( "USER-AGENT").toLowerCase();
        if(StringUtils.isBlank( userAgent)) {
            return false;
        }
        Matcher matcher = PHONE_PATTERN.matcher( userAgent);
        return matcher.find();
    }


    public static InputStream getInputStream(String url) {
        try {
            URL _url = new URL( url);
            URLConnection conn = _url.openConnection();
            return conn.getInputStream();
        } catch (Exception e) {
            throw new RuntimeException( e);
        }
    }

    public static String getRequest(String url) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet( url);
            // 设置超时
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();//设置请求和传输超时时间
            httpGet.setConfig(requestConfig);
            httpResponse = httpClient.execute( httpGet);

            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException( e);
        } finally {
            HttpUtils.close( httpClient, httpResponse, entity);
        }
    }


    public static String postRequest(String url, String json) {
        if (!StringUtils.hasLength( url)) {
            throw new IllegalArgumentException( "url cannot be null or empty");
        }
        if (json == null) {
            json = "";
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( url);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( 60000 * 5).setConnectTimeout( 60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);

            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            AbstractHttpEntity postEntity = new StringEntity( json, "UTF-8");
            httpPost.setEntity( postEntity);
            httpResponse = httpClient.execute( httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException( e);
        } finally {
            close(httpClient, httpResponse, entity);
        }
    }

    public static String postRequest(String url, Map<String,String> params) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( url);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( 60000 * 5).setConnectTimeout( 60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);


            List<NameValuePair> pairs = new ArrayList <NameValuePair>();
            if (!CollectionUtils.isEmpty( params)) {
                for(String key : params.keySet()) {
                    pairs.add(new BasicNameValuePair( key, params.get( key)));
                }
            }

            AbstractHttpEntity postEntity = new UrlEncodedFormEntity( pairs, "UTF-8");
            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
//            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

            httpPost.setEntity( postEntity);
            httpResponse = httpClient.execute( httpPost);
            String result = "";
            entity = httpResponse.getEntity();
            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
                String text;
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException( e);
        } finally {
            close(httpClient, httpResponse, entity);
        }
    }

    public static void postRequest(String url, String json, OutputStream os) {
        if (!StringUtils.hasLength( url)) {
            throw new IllegalArgumentException( "url cannot be null or empty");
        }
        if (json == null) {
            json = "";
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( url);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( 60000 * 5).setConnectTimeout( 60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);

            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

            AbstractHttpEntity postEntity = new StringEntity( json, "UTF-8");
            httpPost.setEntity( postEntity);
            httpResponse = httpClient.execute( httpPost);


            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException( "HttpResponse Error:" + httpResponse.getStatusLine());
            }

            entity = httpResponse.getEntity();

            if (entity != null) {
                httpResponse.getEntity().writeTo( os);
            }
        } catch (Exception e) {
            throw new RuntimeException( e);
        } finally {
            FileUtils.close( os);
            close( httpClient, httpResponse, entity);
        }
    }


    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader( "x-forwarded-for");
        if (StringUtils.hasLength( ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if( ip.indexOf(",") != -1) {
                return ip.split(",")[0];
            }
            return ip;
        }
        String[] ipHeaders = new String[] { "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR", "X-Real-IP"};
        for (String header : ipHeaders) {
            ip = request.getHeader( header);

            if (StringUtils.hasLength( ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
        }

        return request.getRemoteAddr();
    }



    public static void main(String... args) throws Exception {

//        String url = "http://localhost:8080/heatmap.html";
//        String s = sourceCode(url);
//        System.out.println(s);

//        String url = "http://littlestar.douwifi.cn/demo/service-shortmessage.message_callback.json";
//        url = "http://localhost:8080/service-shortmessage.message_callback.json";
//
//       // 52
//        String json = "{\"code\":10000,\"MD5\":\"77005E33281F74556646B3363BBF492C\",\"count\":6,\"data\":[{\"taskid\":\"199749755892344540687\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:49\"},{\"taskid\":\"199749755892730424192\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:49\"},{\"taskid\":\"199749755893341215177\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:49\"},{\"taskid\":\"199749755893872011282\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:49\"},{\"taskid\":\"199749755890050214877\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:54\"},{\"taskid\":\"199749755890825516817\",\"phone\":\"18507497879\",\"status\":-1,\"statusinfo\":\"91008\",\"reporttime\":\"2019/6/8 11:34:54\"}]}";
//
//        Map<String,Object> params2 = new HashMap<>();
//        params2.put( "code", "10000");
//        params2.put( "MD5", "MD5");
//        params2.put( "count", "10");
//        params2.put( "data", new ArrayList<>());
//        url = "http://211.144.106.184:8081/rest/query/meetinglist/director";
////        params2.put( "meetingId", "6");
//
//        Map<String,String> params = new HashMap<>();
//        params.put( "latestUpdateTime", "2018-06-18 16:13:10");
//
//        System.out.println( postRequest( url, JSON.toJSONString( params)));
//        System.out.println( postRequest( url, json));


//        System.out.println( HttpUtils.class.getResource( "").getPath());

//        InputStream is = HttpUtils.getInputStream( "https://mmbiz.qpic.cn/mmbiz_jpg/INdG9Wtiafw7NogARyMXtsgzZ23MDsxcrZg6zqIagJ5PXsj8pzwQXzQMI4WQ1Vg5LS0VGVhY9HhxI2FOE1saNtQ/640?wx_fmt=jpeg");
//        OutputStream os = new FileOutputStream( "/Users/<USER>/Desktop/workspaces/test.jpeg");
//        FileUtils.transfer( is, os);



//        System.out.println( "http://211.144.106.184:8081/" + encodeQueryString( "/filesystem/meetingfiles/standingcommittee/6/1525/请假类型.txt"));
//            System.out.println("#"  + method.getResponseBodyAsString());



        String json = HttpUtils.postRequest( "http://ids.sjtu.edu.cn/classRoom/getByClassroomInfo?roomCode=LGXQ", (Map<String, String>) null);
        System.out.println(  json);
    }

}
