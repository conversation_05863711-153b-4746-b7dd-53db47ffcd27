package com.ywang.utils;

public final class InnerCodeUtils {

    private InnerCodeUtils() {
    }
    
    public static long parseId(String innerCode, int length) {
        if (!StringUtils.hasLength(innerCode)) {
            throw new IllegalArgumentException("innerCode cannot be null or empty!");
        }
        if (length <= 0) {
            throw new IllegalArgumentException("length must be greater than 0");
        }
        String strId = innerCode;
        if (innerCode.length() > length) {
            strId = innerCode.substring(innerCode.length() - length);
        }

        return ConvertUtils.lon(strId);
    }
    
    
    public static void main(String...args) {
        System.out.println(parseId("000366000369", 6));
    }
}
