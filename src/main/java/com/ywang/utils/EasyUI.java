package com.ywang.utils;

import com.ywang.sql.Page;

import java.util.*;

public final class EasyUI {

    private EasyUI(){
    }

    public final static String DATAGRID_ROWS = "rows";

    public final static String DATAGRID_FOOTER = "footer";

    public final static String DATAGRID_HEADER = "header";

    public final static String DATAGRID_TOTAL = "total";

    public final static String TREE_ID = "id";

    public final static String TREE_TEXT = "text";

    public final static String TREE_PARENT_ID = "parent_id";

    public final static String TREE_ICON_CLS = "iconCls";

    public final static String TREE_CHECKED = "checked";

    public final static String TREE_STATE = "state";

    public final static String TREE_ATTRIBUTES = "attributes";

    public static Map<String,Object> datagrid(List<Map<String,Object>> rows, Page page) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_ROWS, rows);
        datagrid.put(DATAGRID_TOTAL, page.getTotal());
        return datagrid;
    }

    public static Map<String,Object> datagrid1(List<?> rows, Page page) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_ROWS, rows);
        datagrid.put(DATAGRID_TOTAL, page.getTotal());
        return datagrid;
    }

    public static Map<String,Object> datagrid(List<Map<String,Object>> rows) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_ROWS, rows);
        return datagrid;
    }

    public static Map<String,Object> datagrid(List<Map<String,Object>> rows, List<Map<String,Object>> footer) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_ROWS, rows);
        datagrid.put(DATAGRID_FOOTER, footer);
        return datagrid;
    }

    public static Map<String,Object> datagrid(List<?> rows, Page page,List<?> footer) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_ROWS, rows);
        datagrid.put(DATAGRID_TOTAL, page.getTotal());
        datagrid.put(DATAGRID_FOOTER, footer);
        return datagrid;
    }

    public static Map<String,Object> datagrid(List<Map<String,Object>> header, List<Map<String,Object>> rows, List<Map<String,Object>> footer) {
        Map<String,Object> datagrid = new HashMap<String, Object>();
        datagrid.put(DATAGRID_HEADER, header);
        datagrid.put(DATAGRID_ROWS, rows);
        datagrid.put(DATAGRID_FOOTER, footer);
        return datagrid;
    }

    public static Map<String,Object> comboboxEditor(Collection<Map<String,Object>> list) {
        Map<String,Object> editor = new HashMap<String,Object>();
        editor.put("type", "combobox");
        Map<String,Object>options = new HashMap<String, Object>();
        List<Map<String,Object>> data = new ArrayList<>();
        for (Map<String,Object> _enum : list) {
            Map<String,Object> elem = new HashMap<String,Object>();
            elem.put("value", _enum.get("enum_value"));
            elem.put("text", _enum.get("enum_text"));
            data.add(elem);
        }
        options.put("data", data);
        options.put("editable", false);
        editor.put("options", options);
        return editor;
    }

    public static Collection<Map<String,Object>> tree(List<Map<String,Object>> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<Map<String, Object>>();
        }
        List<Map<String,Object>> _nodes = new ArrayList<Map<String, Object>>();
        for (Map<String,Object> node : nodes) {
            Map<String,Object> _node = new HashMap<String, Object>();
            Map<String,Object> _attributes = new HashMap<String, Object>();
            _node.put(TREE_ATTRIBUTES, _attributes);
            for (String key : node.keySet()) {
                if (TREE_ID.equals(key) || TREE_TEXT.equals(key) || TREE_PARENT_ID.equals(key) || TREE_ICON_CLS.equals(key)
                        || TREE_CHECKED.equals(key) || TREE_STATE.equals(key)) {
                    _node.put(key, node.get(key));
                } else {
                    _attributes.put(key, node.get(key));
                }
            }
            _nodes.add(_node);
        }
        return CollectionUtils.list2Tree(_nodes);

    }
}