package com.ywang.utils;


// JXL imports removed - using Apache POI instead
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * thread safety
 */
public final class FileUtils {

    private static String[] FILE_SIZE_SUFFIX = {"KB", "MB", "GB"};

    private FileUtils() {
    }

    /**
     * thread unsafety
     */
    public static void close(InputStream is) {
        if (is == null) {
            return;
        }
        try {
            is.close();
        } catch (IOException e) {
        }
    }

    public static void close(OutputStream os) {
        if (os == null) {
            return;
        }
        try {
            os.close();
        } catch (IOException e) {
        }
    }


    public static void close(Writer writer) {
        if (writer == null) {
            return;
        }
        try {
            writer.close();
        } catch (IOException e) {
        }
    }

    public static void close(Reader reader) {
        if (reader == null) {
            return;
        }
        try {
            reader.close();
        } catch (IOException e) {
        }
    }


    /**
     * Read Excel data using Apache POI (replaces JXL)
     * thread unsafety
     */
    public static List<String[]> getDataesFromExcel(InputStream is) throws Exception {
        return getDataesFromExcel(is, 0);
    }

    public static List<String[]> getDataesFromExcel(InputStream is, int sheetIndex) throws Exception {
        if (is == null) {
            return new ArrayList<String[]>(0);
        }

        List<String[]> result = new ArrayList<>();
        try (org.apache.poi.ss.usermodel.Workbook workbook =
             org.apache.poi.ss.usermodel.WorkbookFactory.create(is)) {

            org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(sheetIndex);
            if (sheet == null) {
                return result;
            }

            for (org.apache.poi.ss.usermodel.Row row : sheet) {
                String[] rowData = new String[row.getLastCellNum()];
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    org.apache.poi.ss.usermodel.Cell cell = row.getCell(i);
                    if (cell != null) {
                        switch (cell.getCellType()) {
                            case STRING:
                                rowData[i] = cell.getStringCellValue();
                                break;
                            case NUMERIC:
                                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                                    rowData[i] = ConvertUtils.date2Str(cell.getDateCellValue(), "yyyyMMddHHmmss");
                                } else {
                                    rowData[i] = String.valueOf(cell.getNumericCellValue());
                                }
                                break;
                            case BOOLEAN:
                                rowData[i] = String.valueOf(cell.getBooleanCellValue());
                                break;
                            case FORMULA:
                                rowData[i] = cell.getCellFormula();
                                break;
                            default:
                                rowData[i] = "";
                        }
                    } else {
                        rowData[i] = "";
                    }
                }
                result.add(rowData);
            }
        }
        return result;
    }

    public static String getFileType(String fileName) {
        if (!StringUtils.hasLength( fileName)) {
            throw new IllegalArgumentException( "file name cannot be null or empty");
        }
        int indexOf = fileName.lastIndexOf(".");
        if (indexOf <= 0) {
            throw new IllegalArgumentException( "file name is error: " + fileName);
        }
        return fileName.substring( indexOf + 1).toLowerCase();
    }

    public static File[] findFiles(File dir, String fileType) {
        if (dir == null) {
            throw new IllegalArgumentException( "directory cannot be null");
        }
        if (!dir.exists()) {
            throw new IllegalArgumentException( "file must be exists");
        }
        if (dir.isFile()) {
            throw new IllegalArgumentException( "file must be directory");
        }
        if (!StringUtils.hasLength( fileType)) {
            throw new IllegalArgumentException( "file type cannot be null or empty");
        }



        List<File> files = new ArrayList<>();
        _findFiles( files, dir, fileType);

        return files.toArray(new File[0]);
    }

    private static void _findFiles(List<File> files, File dir, String fileType) {
        for (File file : dir.listFiles()) {
//            System.out.println( file.getName() + "/" + fileType);
            if (file.isDirectory()) {
                _findFiles( files, file, fileType);
            } else if (file.getName().endsWith( fileType)) {
                files.add(file);
            }
        }
    }


    public static void copyFiles(File from, File to) {
        __moveOrCopy( from, to, false);
    }

    public static void moveFiles(File from, File to) {
        __moveOrCopy( from, to, true);
    }


    public static void deleteFile(File file) {
        try {
            if (file == null || !file.exists()) {
                return;
            }
            if (file.isDirectory()) {
                for (File subFile : file.listFiles()) {
                    deleteFile( subFile);
                }
            }
            file.delete();
        } catch (Exception e) {
            throw new RuntimeException( e);
        }
    }

    private static void __moveOrCopy(File from, File to, boolean isMove) {
        try {
            if (from == null) {
                throw new IllegalArgumentException("from cannot be null");
            }
            if (!from.exists()) {
                throw new IllegalArgumentException("from must be exists");
            }
            if (to == null) {
                throw new IllegalArgumentException("to cannot be null");
            }
            if (from.isDirectory() && (!to.exists() ||  to.isDirectory())) {
                if (!to.exists()) {
                    to.mkdirs();
                }
                for (File file : from.listFiles()) {
                    __moveOrCopy( file, new File( to.getAbsolutePath() + "/" + file.getName()), isMove);
                    if (isMove) {
                        from.delete();
                    }
                }
            }
            if (from.isFile() && (!to.exists() || to.isFile())) {
                FileUtils.transfer( new FileInputStream(from), new FileOutputStream(to));
                if (isMove) {
                    from.delete();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException( e);
        }
    }


    public static void zip(OutputStream outputStream, File... files) {
        if (CollectionUtils.isEmpty( files)) {
            throw new IllegalArgumentException( "files cannot be  null or empty");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException( "outputstream cannot be  null");
        }
        ZipOutputStream zos = null;
        FileInputStream fis = null;
        byte[] buffer = new byte[1024];
        try {
            zos = new ZipOutputStream( outputStream);
            for (File file : files) {

                zos.putNextEntry( new ZipEntry( file.getName()));
                fis = new FileInputStream( file);
                int length = -1;
                while ((length = fis.read(buffer)) != -1) {
                    zos.write(buffer, 0, length);
                }

                close( fis);
                zos.closeEntry();
            }
        } catch (Exception e) {
            throw new RuntimeException( e.getMessage(), e);

        } finally {
            close( fis);
            close( zos);
        }
    }

    public static String[] read(File file) {
        if (file == null) {
            throw new IllegalArgumentException( "file cannot be null");
        }
        if (!file.exists()) {
            throw new IllegalArgumentException( "file does not exist");
        }
        if (file.isDirectory()) {
            throw new IllegalArgumentException( "file cannot be directory");
        }
        BufferedReader reader = null;
        List<String> lines = new ArrayList<>();
        try {
            reader = new BufferedReader( new FileReader( file));
            String line = null;
            while ((line = reader.readLine()) != null) {
                lines.add( line);
            }

            return lines.toArray( new String[0]);
        } catch (Exception e) {
            throw new RuntimeException( e);
        } finally {
            close( reader);
        }

    }




    public String base64(byte[] bytes) {
        if (CollectionUtils.isEmpty( bytes)) {
            throw new IllegalArgumentException( "bytes cannot be null or empty!");
        }
        return Base64.encodeBase64String(bytes);
    }

    // JXL-based method removed - use getDataesFromExcel(InputStream, int) instead

    public static void transfer(InputStream is, OutputStream os) throws Exception {
        byte[] buffer = new byte[1024];
        try {
            int length = -1;
            while ((length = is.read(buffer)) != -1) {
                os.write(buffer, 0, length);
            }
        } finally {
            close( is);
            close( os);
        }
    }
    
    public static String descFileSize(long size) {
        return descFileSize(size / 1024.0, FILE_SIZE_SUFFIX[0]);
    }
    
    private static String descFileSize(double size, String suffix) {
        NumberFormat numberFormat = new DecimalFormat("0.00");
        if (size < 1024) {
            return numberFormat.format(size)  + suffix;
        }
        int idx = 0;
        for (String _suffix : FILE_SIZE_SUFFIX) {
            if (suffix.equals(_suffix)) {
                break;
            }
            idx++;
        }
        if (idx + 1 > FILE_SIZE_SUFFIX.length) {
            return numberFormat.format(size)  + suffix;
        }
        return descFileSize(size / 1024, FILE_SIZE_SUFFIX[idx + 1]);
    }

    public static byte[] decodeBase64(String strBase64) {
        if (!StringUtils.hasLength( strBase64)) {
            throw new RuntimeException( "base64 cannot be null or emprty!");
        }
        Base64 base64 = new Base64();
        return base64.decodeBase64( strBase64.getBytes());
    }

    
    public static void main(String...args) {
        File file = new File("E:\\workspace\\yaming_bak\\trunk\\web-app\\web\\plugin\\kendo");
        for (String fileName : file.list()) {
            if (fileName.endsWith(".js")) {
                System.out.println("<script type=\"text/javascript\" src=\"/plugin/kendo/" + fileName + "\"></script>");
            }




        }
    }

}
