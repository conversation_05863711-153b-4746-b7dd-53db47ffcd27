package com.ywang.utils;

import com.alibaba.druid.filter.config.ConfigTools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Druid 加解密
 *
 * <AUTHOR>
 * @date 2018/4/4
 */
public class DruidPasswordUtil {
    private static final Logger logger = LoggerFactory.getLogger(DruidPasswordUtil.class);

    public static final String MAP_PUBLIC_KEY = "publicKey";
    public static final String MAP_PRIVATE_KEY = "privateKey";
    public static final String MAP_PASSWORD = "password";

    /**
     * 加密
     *
     * @param password 明文密码
     * @return 加密后的公私钥及密文密码
     */
    public static Map<String, String> encrypt(String password) {
        Map<String, String> encryptResult = new HashMap<>(3);
        String[] arr;
        try {
            arr = ConfigTools.genKeyPair(512);
            encryptResult.put(MAP_PRIVATE_KEY, arr[0]);
            encryptResult.put(MAP_PUBLIC_KEY, arr[1]);
            encryptResult.put(MAP_PASSWORD, ConfigTools.encrypt(arr[0], password));
            return encryptResult;
        } catch (Exception e) {
            logger.error("druid encrypt error !", e);
        }
        return null;
    }

    public static String decrypt(String publicKey, String encryptPassword) {
        try {
            return ConfigTools.decrypt(publicKey, encryptPassword);
        } catch (Exception e) {
            logger.error("druid decrypt error !", e);
        }
        return null;
    }

    public static void main(String[] args){
        Map<String, String> encryptResult = encrypt("111111");
        System.out.println(encryptResult);
        String encryptPassword = "WJPm57xgprL6MbZZo661rzc9i2O7cE+82jwMtOQgJA7h770e2NBMUNLhDBs7wJwCtlsG0JcT6UD/eHz/wL8h7w==";
        String publicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKPJQeQzmgyX5h/6kIsS9W2DljtiOg8m9G/ADj/V1PHz8CR+AMG1ptyuhqWZiRxT8+slmpaJdvEPd0Iv2Wvv8/kCAwEAAQ==";
        System.out.println(decrypt(publicKey, encryptPassword));

    }

}
