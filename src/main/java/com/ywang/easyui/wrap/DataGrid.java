package com.ywang.easyui.wrap;



import java.util.*;

public class DataGrid {
    
    private final Collection<Map<String,Object>> rows;
    
    private final Integer total;
    
    public DataGrid(Collection<Map<String,Object>> rows) {
        this(rows, null);
    }

    public DataGrid(Collection<Map<String, Object>> rows, Integer total) {
        this.rows = Collections.unmodifiableCollection(rows);
        this.total = total;
    }

    public Collection<Map<String, Object>> getRows() {
        return rows;
    }

    public Integer getTotal() {
        return total;
    }
}
