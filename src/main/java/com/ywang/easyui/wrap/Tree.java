/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 16:46:35
 * @FilePath: /analysis_engine/src/main/java/com/ywang/easyui/wrap/Tree.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.easyui.wrap;


import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.ywang.utils.CollectionUtils;

public class Tree {

    private final static String[] ATTRIBUTE_NAMES = {"id", "text", "parent_id", "iconCls", "checked", "state", "attributes"};

    public static Collection<Map<String, Object>> valueOf(List<Map<String, Object>> nodes) {
        List<Map<String, Object>> tree = new ArrayList<>();
        for (Map<String, Object> node : nodes) {
            Map<String, Object> treeNode = new HashMap<>();
            Map<String, Object> nodeAttributes = new HashMap<>();
            treeNode.put("attributes", nodeAttributes);
            for (String key : node.keySet()) {
                if (CollectionUtils.contain(ATTRIBUTE_NAMES, key)) {
                    treeNode.put(key, node.get(key));
                } else {
                    nodeAttributes.put(key, node.get(key));
                }
            }
            tree.add(treeNode);
        }
        return list2Tree(tree);
    }

    public static Collection<Map<String, Object>> list2Tree(List<Map<String, Object>> list) {
        return list2Tree(list, "id", "parent_id", null, "children");
    }

    public static <K> Collection<Map<K, Object>> list2Tree(List<Map<K, Object>> list,
        K idKey, K parentIdKey, Object rootValue, K childrenKey) {

        if (idKey == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        if (parentIdKey == null) {
            throw new IllegalArgumentException("parent_id key cannot be null");
        }
        if (childrenKey == null) {
            throw new IllegalArgumentException("children key cannot be null");
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<Object, Map<K, Object>> tree = new LinkedHashMap<>();
        Map<Object, List<Map<K, Object>>> children = new HashMap<>();
        for (Map<K, Object> elem : list) {
            List<Map<K, Object>> child = children.get(elem.get(idKey));
            if (child == null) {
                child = new ArrayList<>();
                children.put(elem.get(idKey), child);
            }
            elem.put(childrenKey, (Object) child);
            Object parentId = elem.get(parentIdKey);
            if (parentId != null && !parentId.equals(rootValue)) {
                child = children.get(parentId);
                if (child == null) {
                    child = new ArrayList<>();
                    children.put(parentId, child);
                }
                child.add(elem);
            } else {
                Object id = elem.get(idKey);
                tree.put(id, elem);
                child = children.get(id);
                if (child == null) {
                    child = new ArrayList<>();
                    children.put(id, child);
                }
            }
        }
        return Collections.unmodifiableCollection(tree.values());
    }
}
