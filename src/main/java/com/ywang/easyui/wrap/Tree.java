package com.ywang.easyui.wrap;


import com.ywang.utils.CollectionUtils;

import java.util.*;

public class Tree {

    private final static String[] ATTRIBUTE_NAMES = {"id", "text", "parent_id", "iconCls", "checked", "state", "attributes"};

    public static Collection<Map<String, Object>> valueOf(List<Map<String, Object>> nodes) {
        List<Map<String, Object>> tree = new ArrayList<Map<String, Object>>();
        for (Map<String, Object> node : nodes) {
            Map<String, Object> treeNode = new HashMap<String, Object>();
            Map<String, Object> nodeAttributes = new HashMap<String, Object>();
            treeNode.put("attributes", nodeAttributes);
            for (String key : node.keySet()) {
                if (CollectionUtils.contain(ATTRIBUTE_NAMES, key)) {
                    treeNode.put(key, node.get(key));
                } else {
                    nodeAttributes.put(key, node.get(key));
                }
            }
            tree.add(treeNode);
        }
        return list2Tree(tree);
    }

    public static Collection<Map<String, Object>> list2Tree(List<Map<String, Object>> list) {
        return list2Tree(list, "id", "parent_id", null, "children");
    }

    public static <K, Object> Collection<Map<K, Object>> list2Tree(List<Map<K, Object>> list, K idKey, K parentIdKey, Object rootValue, K childrenKey) {
        if (idKey == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        if (parentIdKey == null) {
            throw new IllegalArgumentException("parent_id key cannot be null");
        }
        if (childrenKey == null) {
            throw new IllegalArgumentException("children key cannot be null");
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<Object, Map<K, Object>> tree = new LinkedHashMap<Object, Map<K, Object>>();
        Map<Object, List<Map<K, Object>>> children = new HashMap<Object, List<Map<K, Object>>>();
        for (Map<K, Object> elem : list) {
            List<Map<K, Object>> child = children.get(elem.get(idKey));
            if (child == null) {
                child = new ArrayList<Map<K, Object>>();
                children.put(elem.get(idKey), child);
            }
            elem.put(childrenKey, (Object) child);
            Object parentId = elem.get(parentIdKey);
            if (parentId != null && !parentId.equals(rootValue)) {
                child = children.get(parentId);
                if (child == null) {
                    child = new ArrayList<Map<K, Object>>();
                    children.put(parentId, child);
                }
                child.add(elem);
            } else {
                Object id = elem.get(idKey);
                tree.put(id, elem);
                child = children.get(id);
                if (child == null) {
                    child = new ArrayList<Map<K, Object>>();
                    children.put(id, child);
                }
            }
        }
        return Collections.unmodifiableCollection(tree.values());
    }
}
