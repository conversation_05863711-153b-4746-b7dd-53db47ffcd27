package com.ywang.easyui.taglibs;



import com.ywang.utils.StringUtils;

import java.util.Collection;
import java.util.Map;

public class EasyUITag {

    public static String combobox(String group, String options, String name, String style) {
//        Map<String, Object> _group = SysEnum.getInstance().getGroup(group);
//
//        StringBuffer combobox = new StringBuffer("<select class='easyui-combobox' name='" + name + "'" +
//                " data-options='" + (StringUtils.hasLength(options) ? options.replace("'", "\"") : "") + "'" +
//                " style='" + (StringUtils.hasLength(style) ? style.replace("'", "\"") : "") + "'>");
//        Collection<Map<String,Object>> enums = (Collection<Map<String,Object>>)_group.get("enums");
//        for (Map<String,Object> _enum : enums) {
//            combobox.append("<option value='" + _enum.get("enum_value") + "'>" + _enum.get("enum_text") + "</option>");
//        }
//        combobox.append("</select>");
//        return combobox.toString();

        return null;
    }

    public static String formatterCellByEnumInJavaScript(String group, String functionName) {
//        Map<String, Object> _group = SysEnum.getInstance().getGroup(group);
//
//        StringBuffer js = new StringBuffer("function " + functionName + "(value, row, index) {\n");
//        js.append("var mapper = {};\n");
//        Collection<Map<String,Object>> enums = (Collection<Map<String,Object>>)_group.get("enums");
//        for (Map<String,Object> _enum : enums) {
//            js.append("mapper['" + _enum.get("enum_value") + "'] = '" + _enum.get("enum_text") + "';\n");
//        }
//        js.append("if (mapper[value]) {\n");
//        js.append("return mapper[value];\n");
//        js.append("}");
//        js.append("return value;");
//        js.append("}");
//        return js.toString();

        return null;
    }
}
