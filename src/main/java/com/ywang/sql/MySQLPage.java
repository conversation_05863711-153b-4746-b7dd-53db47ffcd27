package com.ywang.sql;


import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public final class MySQLPage {

    private final OrderPage orderPage;

    private final Map<String,String> orderFields = new HashMap<String,String>();

    public MySQLPage(OrderPage orderPage, String[] orderFields) {
        this.orderPage = orderPage;

        if (CollectionUtils.isEmpty(orderFields)) {
            return;
        }
        for (String orderField : orderFields) {
            String[] arr = orderField.split("-");
            String order = arr[1];
            if (arr.length == 3) {
                order = arr[2];
            }
            String value = "ifnull(" + order + ",";
            if ("str".equals(arr[0])) {
                value = "if(isnull(" + order + ") or length(" + order + ")=0, '"
                        + Character.MAX_VALUE + "'," +order + ")";
            } else if ("date".equals(arr[0])) {
                value += "'3000-01-01')";
            } else if ("int".equals(arr[0])) {
                value += Integer.MAX_VALUE + ")";
            } else if ("lon".equals(arr[0])) {
                value += Long.MAX_VALUE + ")";
            } else if ("dou".equals(arr[0])) {
                value += Double.MAX_VALUE + ")";
            } else if ("bool".equals(arr[0])) {
                value += "3)";
            } else {
                throw new IllegalArgumentException("name formatter is error: " + orderField);
            }

            this.orderFields.put(arr[1].toLowerCase(), value);

        }
    }

    public OrderPage asOrderPage() {
        return orderPage;
    }

    public String getOrderBy() {
        if (CollectionUtils.isEmpty(orderPage.sorts)) {
            return "";
        }
        String orderBy = "";
        for (int i = 0; i < orderPage.sorts.length; i++) {
            if (!StringUtils.hasLength(orderPage.sorts[i])) {
                continue;
            }
            String sort = orderPage.sorts[i];
            if (orderFields.containsKey(sort.toLowerCase())) {
                sort = orderFields.get(sort.toLowerCase());
            }


            orderBy +=  sort + " " + orderPage.orders[i] + ",";
        }
        if (orderBy.length() == 0) {
            return "";
        }
        orderBy = orderBy.substring(0, orderBy.length() - 1);
        return " order by " + orderBy;
    }
}
