package com.ywang.sql;

// import org.springframework.data.redis.core.RedisTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface RedisStatement extends Statement {

    Object insertAndCache(String tableName, String redisKey, Map<String,Object> entity);

    <T> List<T> selectAndCache(String sql, RedisCallback<T> callback, Object... params);

    int updateAndCache(String sql, Collection<String> redisKeys, Object... params);

    int deleteAndCache(String sql, Collection<String> redisKeys, Object... params);

    // RedisTemplate getRedisTemplate();
}
