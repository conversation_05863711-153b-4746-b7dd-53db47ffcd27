package com.ywang.sql;


import java.util.List;

/**
 * Author: YunWang
 * Date:   2010-10-10
 */
public interface Statement {

    <T> List<T> select(String sql, Callback<T> callback, Object... params);

    Object insert(String sql, Object... params);

    int update(String sql, Object... params);

    int delete(String sql, Object... params);

    void execute(String sql);

    // batch
    int[] batch(String... sqls);

    int[] batchInsert(String sql, List<Object[]> params);

    int[] batchUpdate(String sql, List<Object[]> params);

    int[] batchDelete(String sql, List<Object[]> params);
}
