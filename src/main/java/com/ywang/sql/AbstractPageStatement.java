package com.ywang.sql;


import com.alibaba.fastjson.JSONObject;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;
import com.ywang.utils.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;

/**
 * Author: YunWang
 * Date:   2010-10-13
 */
public abstract class AbstractPageStatement implements PageStatement,RedisStatement {

    private final Logger LOGGER = Logger.getLogger( getClass());

    private final RedisStatement statement;

    public AbstractPageStatement(RedisStatement statement) {
        this.statement = statement;
    }

    public <T> List<T> select(String sql, Callback<T> callback, Object... params) {
        if (callback instanceof RedisCallback) {
            return statement.selectAndCache( sql, (RedisCallback)callback, params);
        }
        return statement.select(sql, callback, params);
    }

    public <T> List<T> selectAndCache(String sql, RedisCallback<T> callback, Object... params) {
        return statement.selectAndCache( sql, callback, params);
    }

    public Object insert(String sql, Object... params) {
        return statement.insert(sql, params);
    }

    public Object insertAndCache(String tableName, String redisKey, Map<String,Object> entity) {
        return statement.insertAndCache( tableName, redisKey, entity);
    }

    public int update(String sql, Object... params) {
        return statement.update( sql, params);
    }

    public int updateAndCache(String sql, Collection<String> redisKeys, Object... params) {
        return statement.updateAndCache( sql, redisKeys, params);
    }

    public int delete(String sql, Object... params) {
        return statement.delete( sql, params);
    }

    public int deleteAndCache(String sql, Collection<String> redisKeys, Object... params) {
        return statement.deleteAndCache( sql, redisKeys, params);
    }

    public int[] batch(String... sqls) {
        return statement.batch(sqls);
    }

    public int[] batchInsert(String sql, List<Object[]> params) {
        return statement.batchInsert(sql, params);
    }

    public int[] batchUpdate(String sql, List<Object[]> params) {
        return statement.batchUpdate(sql, params);
    }

    public int[] batchDelete(String sql, List<Object[]> params) {
        return statement.batchDelete(sql, params);
    }

    public void execute(String sql) {
        statement.execute( sql);
    }

    public <T> List<T> select(String sql, PageCallback<T> callback, Object... params) {
        String key = null;
        RedisTemplate redisTemplate = statement.getRedisTemplate();
        boolean cached = (redisTemplate != null) && (callback instanceof RedisCallback);
        String _sql = sql;
        if (callback.getPage() != null) {
            _sql = pageSql( sql, callback.getPage());
        }

        if (cached) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put( "sql", _sql);
            jsonObject.put( "params", params);
            key = "_sql." + StringUtils.md5( jsonObject.toJSONString());
            Map<String,Object> cache = (Map<String,Object>)redisTemplate.opsForValue().get( key);
            if (cache != null) {
                LOGGER.info( "Hit Cached: " + key);
                if (callback.getPage() != null) {
                    if (cache.containsKey( "total")) {
                        callback.getPage().setTotal( (Integer)cache.get( "total"));
                    } else {
                        count( _sql, callback, params);
                        cache.put( "total", callback.getPage().getTotal());

                        redisTemplate.opsForValue().set( key, cache);
                    }
                }
                return (List<T>)cache.get( "rows");
            }
        }

        List<T> rows = statement.select( _sql, callback, params);
        count(sql, callback, params);

        if (cached) {
            Map<String,Object> cache = new HashMap<>();
            cache.put( "rows", rows);
            if (callback.getPage() != null) {
                cache.put( "total", callback.getPage().getTotal());
            }
            RedisCallback redisCallback = (RedisCallback)callback;
            String[] tablesNames = redisCallback.getTableNames();
            LOGGER.info( "Table Names: " + Arrays.toString( tablesNames));
            if (!CollectionUtils.isEmpty( tablesNames)) {
                for (String tableName : tablesNames) {
                    redisTemplate.opsForSet().add( "_table." + tableName, key);
                }
                redisTemplate.opsForValue().set( key, cache);
                LOGGER.info( "Cache SUCCESS: " + key);
            } else {
                LOGGER.warn( "No Table Names SQL: " + sql);
            }
        }

        return rows;
    }

    protected abstract String pageSql(String sql, Page page);

    private void count(String sql, PageCallback callback, Object... params) {
        if (callback.getPage() != null) {
            List<Integer> ret = statement.select("select count(*) num from (" + sql + ") t", new IntegerCallback(), params);
            callback.getPage().setTotal( ret.get(0));
        }
    }


    public RedisTemplate getRedisTemplate() {
        return statement.getRedisTemplate();
    }

}
