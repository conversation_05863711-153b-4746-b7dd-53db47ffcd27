package com.ywang.sql;


import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;

public final class OrderPage {
    
    private final Page page;
    
    final String[] orders;

    final String[] sorts;

    public OrderPage(Page page, String[] sorts, String[] orders) {
        if (!CollectionUtils.isEmpty(orders) && !CollectionUtils.isEmpty(sorts) && sorts.length != orders.length) {
            throw new IllegalArgumentException(" order arguments is error ");
        }
        this.page = page;
        this.orders = orders;
        this.sorts = sorts;
    }
    
    public Page asPage() {
        return page;
    }
    
    public String getOrderBy() {
        if (CollectionUtils.isEmpty(sorts)) {
            return "";
        }
        String orderBy = "";
        for (int i = 0; i < sorts.length; i++) {
            if (!StringUtils.hasLength(sorts[i])) {
                continue;
            }
            orderBy +=  sorts[i] + " " + orders[i] + ",";
        }
        if (orderBy.length() == 0) {
            return "";
        }
        orderBy = orderBy.substring(0, orderBy.length() - 1);
        return " order by " + orderBy;
    }
}
