package com.ywang.sql;

import java.io.Serializable;

public final class Page implements Serializable {

    private final int size;

    private final int start;

    private int total;

    public Page(int size, int start) {
        this.size = size;
        if (start < 1) {
            throw new IllegalArgumentException("start start with 1");
        }
        this.start = start;
    }

    public Page(int size) {
        this.size = size;
        this.start = 1;
    }

     public Page() {
        this.size = 10;
        this.start = 1;
    }

    public int getSize() {
        return size;
    }

    public int getCurrendRecord() {
        return size * (start - 1);
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        if (total < 0) {
            throw new IllegalArgumentException("total count start with 0");
        }
        this.total = total;
    }

    public int getStart() {
        return start;
    }

    public int getCount() {
        return (getTotal() - 1) / getSize() + 1;
    }
}
