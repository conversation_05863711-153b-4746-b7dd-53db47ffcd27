package com.ywang.sql.support.callback;


import com.ywang.sql.Callback;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

public class DateCallback implements Callback<Date> {

    public Date with(int row, ResultSet rs) throws SQLException {
        if (rs.getTimestamp( 1) == null) {
            return null;
        }
        return new Date(rs.getTimestamp( 1).getTime());
    }
}
