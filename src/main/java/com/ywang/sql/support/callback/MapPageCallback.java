package com.ywang.sql.support.callback;

import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.Callback;
import com.ywang.sql.RedisCallback;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * Author: YunWang
 * Date:   2010-10-13
 */
public class MapPageCallback implements PageCallback<Map<String,Object>> {

    private final Callback<Map<String,Object>> callback;

    private final Page page;

    public MapPageCallback(Callback<Map<String,Object>> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public MapPageCallback(Page page) {
        this( new MapCallback(), page);
    }

    public MapPageCallback() {
        this( null);
    }

    public Map<String,Object> with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }

}
