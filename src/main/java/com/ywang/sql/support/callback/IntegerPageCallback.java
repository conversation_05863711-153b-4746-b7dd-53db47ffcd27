package com.ywang.sql.support.callback;

import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.Callback;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Author: YunWang
 * Date:   2010-11-12
 */
public class IntegerPageCallback implements PageCallback<Integer> {

    private final Callback<Integer> callback;

    private final Page page;

    public IntegerPageCallback(Callback<Integer> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public IntegerPageCallback(Page page) {
        this( new IntegerCallback(), page);
    }

    public IntegerPageCallback() {
        this( null);
    }

    public Integer with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }
}