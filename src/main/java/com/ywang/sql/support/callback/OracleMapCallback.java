package com.ywang.sql.support.callback;


import com.ywang.sql.Callback;
import com.ywang.utils.LogUtils;

import java.sql.*;
import java.util.*;

public class OracleMapCallback implements Callback<Map<String, Object>> {

    private boolean cached = false;

    private Map<String, Integer> keys = new HashMap<String, Integer>();

    public Map<String, Object> with(int row, ResultSet rs) throws SQLException {
        if (!cached) {
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String key = rsmd.getColumnLabel(i);
                if (key == null || key.length() == 0) {
                    key = rsmd.getColumnName(i);
                }
                if (rsmd.getColumnType(i) == Types.CHAR && rsmd.getColumnDisplaySize(i) == 1) {
                    keys.put(key, Types.BOOLEAN);
                } else {
                    keys.put(key, rsmd.getColumnType(i));
                }
            }
            cached = true;
        }
        Map<String, Object> ret = new HashMap<String, Object>();
        for (String key : keys.keySet()) {
            switch (keys.get(key)) {
                case Types.BOOLEAN:
                    String bool = rs.getString(key);
                    if (bool == null) {
                        ret.put(key.toLowerCase(), null);
                    } else {
                        ret.put(key.toLowerCase(), "1".equals(bool));
                    }
                    break;
                case Types.INTEGER:
                    ret.put(key.toLowerCase(), rs.getObject(key));
                    break;
                case Types.BIGINT:
                    ret.put(key.toLowerCase(), rs.getObject(key));
                    break;
                case Types.FLOAT:
                    Number val = (Number)rs.getObject(key);
                    if (val != null) {
                        ret.put(key.toLowerCase(), val.floatValue());
                    }
                    break;
                case Types.NUMERIC:
                case Types.DECIMAL:
                case Types.DOUBLE:
                    val = (Number)rs.getObject(key);
                    if (val != null) {
                        if (val.doubleValue() - val.longValue() == 0) {
                            ret.put(key.toLowerCase(), val.longValue());
                        } else {
                            ret.put(key.toLowerCase(), val.doubleValue());
                        }
                    }
                    break;
                case Types.CHAR:
                case Types.CLOB:
                case Types.VARCHAR:
                    ret.put(key.toLowerCase(), rs.getString(key));
                    break;
                case Types.TIME:
                case Types.TIMESTAMP:
                case Types.DATE:
                    Timestamp timesStamp = rs.getTimestamp(key);
                    if (timesStamp != null) {
                        ret.put(key.toLowerCase(), new java.util.Date(rs.getTimestamp(key).getTime()));
                    }
                    break;
                default:
                    LogUtils.DB_LOG.debug("sql type cannot be mapped: " + keys.get(key));
                    ret.put(key.toLowerCase(), rs.getObject(key));
            }
        }
        return ret;
    }
}
