package com.ywang.sql.support.callback;


import com.ywang.sql.Callback;
import com.ywang.utils.ReflectUtils;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.*;
import java.util.Date;

/**
 * Example: User user = db.selectUnique(sql, new EntityCallback<User>(User.class), schoolCode, studentId);
 * 对应关系: card_number -> setCardNumber() 方法
 * 需要提供一个无餐构造方法
 * @param <T>
 */
public class EntityCallback<T> implements Callback<T> {


    private final Class<T> targetClass;

    private final Method[] setMethods;

    public EntityCallback(Class<T> clazz) {
        targetClass = clazz;
        setMethods = ReflectUtils.getSetMethods( targetClass);
    }

    public EntityCallback() {
        ParameterizedType type = (ParameterizedType)getClass().getGenericSuperclass();
        targetClass = (Class<T>)type.getActualTypeArguments()[0];
        setMethods = ReflectUtils.getSetMethods( targetClass);
    }

    public T with(int row, ResultSet rs) throws SQLException{
        T ret = null;
        try {
            ret = targetClass.newInstance();
        } catch (Exception e) {
            throw new SQLException( e);
        }

        for (Method setMethod : setMethods) {
            try{
                String fieldName = ReflectUtils.getFieldName( setMethod);

                Type parameterType = setMethod.getGenericParameterTypes()[0];
                if (parameterType == int.class || parameterType == Integer.class) {
                    setMethod.invoke( ret, rs.getInt( fieldName));
                } else if (parameterType == long.class || parameterType == Long.class) {
                    setMethod.invoke( ret, rs.getLong( fieldName));
                } else if (parameterType == float.class || parameterType == Float.class) {
                    setMethod.invoke( ret, rs.getFloat( fieldName));
                } else if (parameterType == double.class || parameterType == Double.class) {
                    setMethod.invoke( ret, rs.getDouble( fieldName));
                } else if (parameterType == boolean.class || parameterType == Boolean.class) {
                    setMethod.invoke( ret, rs.getBoolean( fieldName));
                } else if (parameterType == String.class) {
                    setMethod.invoke( ret, rs.getString( fieldName));
                } else if (parameterType == Date.class) {
                    setMethod.invoke( ret, new Date(rs.getTimestamp( fieldName).getTime()));
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return ret;
    }


    private  T createInstance(Class<T> clazz) throws IllegalAccessException, InstantiationException {
        return clazz.newInstance();
    }


    public static void main(String... args) throws Exception {
//        new EntityCallback<User>(){};

    }
}
