package com.ywang.sql.support.callback;

import com.ywang.sql.RedisCallback;
import com.ywang.utils.StringUtils;
import org.apache.log4j.Logger;

import java.sql.*;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

public class MapRedisCallback implements RedisCallback<Map<String, Object>> {

    private final static Logger LOGGER = Logger.getLogger( MapRedisCallback.class);

    private boolean _cached = false;

    private final String redisKey;

    private Map<String, Integer> keys = new HashMap<String, Integer>();

    private Set<String> tableNames = new TreeSet<>();

    public MapRedisCallback(String redisKey) {
        this.redisKey = redisKey;
    }

    public MapRedisCallback() {
        this("");
    }

    public Map<String, Object> with(int row, ResultSet rs) throws SQLException {
        if (!_cached) {
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String key = rsmd.getColumnLabel(i);
                if (key == null || key.length() == 0) {
                    key = rsmd.getColumnName(i);
                }
                if (rsmd.getColumnType(i) == Types.CHAR && rsmd.getColumnDisplaySize(i) == 1) {
                    keys.put(key, Types.BOOLEAN);
                } else {
                    keys.put(key, rsmd.getColumnType(i));
                }
                if (StringUtils.hasLength(rsmd.getTableName(i))) {
                    tableNames.add(rsmd.getTableName(i));
                }
            }
            _cached = true;
        }
        Map<String, Object> ret = new HashMap<>();
        for (String key : keys.keySet()) {
            switch (keys.get(key)) {
                case Types.BOOLEAN:
                    String bool = rs.getString(key);
                    if (bool == null) {
                        ret.put(key, null);
                    } else {
                        ret.put(key, "1".equals(bool));
                    }
                    break;
                case Types.INTEGER:
                    ret.put(key, rs.getObject(key));
                    break;
                case Types.BIGINT:
                    ret.put(key, rs.getObject(key));
                    break;
                case Types.FLOAT:
                    Number val = (Number) rs.getObject(key);
                    if (val != null) {
                        ret.put(key, val.floatValue());
                    }
                    break;
                case Types.NUMERIC:
                case Types.DECIMAL:
                case Types.DOUBLE:
                    val = (Number) rs.getObject(key);
                    if (val != null) {
                        ret.put(key, val.doubleValue());
                    }
                    break;
                case Types.CHAR:
                case Types.CLOB:
                case Types.VARCHAR:
                    ret.put(key, rs.getString(key));
                    break;
                case Types.TIME:
                case Types.TIMESTAMP:
                case Types.DATE:
                    Timestamp timesStamp = rs.getTimestamp(key);
                    if (timesStamp != null) {
                        ret.put(key, new Date(rs.getTimestamp(key).getTime()));
                    } else {
                        ret.put(key, null);
                    }
                    break;
                default:
                    LOGGER.warn("sql type cannot be mapped: " + keys.get(key));
                    ret.put(key, rs.getObject(key));
            }
        }

        return ret;
    }

    public String[] getTableNames() {
        return tableNames.toArray(new String[0]);
    }

    public String getRedisKey() {
        return redisKey;
    }
}

