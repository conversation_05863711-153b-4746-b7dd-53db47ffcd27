package com.ywang.sql.support.callback;

import com.ywang.sql.Page;
import com.ywang.sql.PageRedisCallback;
import com.ywang.sql.RedisCallback;
import org.apache.log4j.Logger;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public class MapPageRedisCallback implements PageRedisCallback<Map<String,Object>> {

    private final static Logger LOGGER = Logger.getLogger( MapPageRedisCallback.class);

    private final RedisCallback<Map<String,Object>> callback;

    private final Page page;

    public MapPageRedisCallback(RedisCallback callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public MapPageRedisCallback(Page page, String redisKey) {
        this( new MapRedisCallback( redisKey), page);
    }

    public MapPageRedisCallback(RedisCallback callback) {
        this( callback, null);
    }

    public MapPageRedisCallback(Page page) {
        this( new MapRedisCallback(), page);
    }

    public MapPageRedisCallback(String redisKey) {
        this( new MapRedisCallback( redisKey), null);
    }

    public MapPageRedisCallback() {
        this( new MapRedisCallback(), null);
    }

    @Override
    public Map<String,Object> with(int row, ResultSet rs) throws SQLException {
        return callback.with( row, rs);
    }

    @Override
    public Page getPage() {
        return page;
    }

    @Override
    public String[] getTableNames() {
        return callback.getTableNames();
    }

    @Override
    public String getRedisKey() {
        return callback.getRedisKey();
    }
}
