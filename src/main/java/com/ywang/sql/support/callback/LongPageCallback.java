package com.ywang.sql.support.callback;

import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.Callback;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Author: YunWang
 * Date:   2010-11-12
 */
public class LongPageCallback implements PageCallback<Long> {

    private final Callback<Long> callback;

    private final Page page;

    public LongPageCallback(Callback<Long> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public LongPageCallback(Page page) {
        this( new LongCallback(), page);
    }

    public LongPageCallback() {
        this( null);
    }

    public Long with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }
}