package com.ywang.sql.support.callback;

import com.ywang.sql.Callback;
import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.RedisCallback;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public class MapPageCallbackForDM implements PageCallback<Map<String,Object>> {

    private final Callback<Map<String,Object>> callback;

    private final Page page;

    public MapPageCallbackForDM(Callback<Map<String,Object>> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public MapPageCallbackForDM(Page page) {
        this( new MapCallbackForDM(), page);
    }

    public MapPageCallbackForDM() {
        this( null);
    }

    public Map<String,Object> with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }
}