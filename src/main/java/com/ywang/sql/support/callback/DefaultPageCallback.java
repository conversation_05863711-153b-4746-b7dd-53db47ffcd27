package com.ywang.sql.support.callback;


import com.ywang.sql.Callback;
import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;

import java.sql.ResultSet;
import java.sql.SQLException;

public class DefaultPageCallback<T> implements PageCallback<T> {

    private final Callback<T> callback;

    private final Page page;

    public DefaultPageCallback(Callback<T> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    @Override
    public T with(int row, ResultSet rs) throws SQLException {
        return callback.with( row, rs);
    }

    @Override
    public Page getPage() {
        return page;
    }
}
