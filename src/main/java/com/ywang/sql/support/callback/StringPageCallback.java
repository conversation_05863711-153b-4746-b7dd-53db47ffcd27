package com.ywang.sql.support.callback;

import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.Callback;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Author: YunWang
 * Date:   2010-11-12
 */
public class StringPageCallback implements PageCallback<String> {

    private final Callback<String> callback;

    private final Page page;

    public StringPageCallback(Callback<String> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public StringPageCallback(Page page) {
        this( new StringCallback(), page);
    }

    public StringPageCallback() {
        this( null);
    }

    public String with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }
}