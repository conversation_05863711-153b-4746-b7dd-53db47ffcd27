package com.ywang.sql.support;


import com.ywang.sql.Callback;
import com.ywang.sql.Statement;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;
import com.ywang.utils.LogUtils;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class OracleStatememt implements Statement {

    private final DataSource dataSource;

    public OracleStatememt(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    public <T> List<T> select(String sql, Callback<T> callback, Object... params) {
        DbUtils.checkSql(sql, "select ");
        if (callback == null) {
            throw new IllegalArgumentException("callback cannot be null");
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<T> ret = new ArrayList<T>();
        try {
            long start = System.currentTimeMillis();
          //  System.out.println("Get CONN1...");
            conn = dataSource.getConnection();
         //   System.out.println("Get CONN1 SUCCESS");
            int concurrency = ResultSet.CONCUR_READ_ONLY;

            ps = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, concurrency);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            rs = ps.executeQuery();
            int row = 0;
            while (rs.next()) {
                T value = callback.with(row++, rs);
                if (value != null) {
                    ret.add(value);
                }
            }
            LogUtils.DB_LOG.info("SQL:" + sql + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return ret;
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(rs, ps, conn);
        }
    }

    public Object insert(String sql, Object... params) {
        DbUtils.checkSql(sql, "insert ");
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            long start = System.currentTimeMillis();
       //     System.out.println("Get CONN2...");
            conn = dataSource.getConnection();
      //      System.out.println("Get CONN2 SUCCESS");
            ps = conn.prepareStatement(sql, new int[]{1});
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            ps.executeUpdate();
            Object ret = DbUtils.getGeneratedKey(ps);
            LogUtils.DB_LOG.info("SQL:" + sql + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return ret;
        } catch (SQLException e) {
            DbUtils.rollback(conn);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, ps, conn);
        }
    }

    public int update(String sql, Object... params) {
        DbUtils.checkSql(sql, "update ");
        return executeUpdate(sql, params);
    }

    public int delete(String sql, Object... params) {
        DbUtils.checkSql(sql, "delete ");
        return executeUpdate(sql, params);
    }

    public void execute(String sql) {

    }

    private int executeUpdate(String sql, Object... params) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            long start = System.currentTimeMillis();
     //       System.out.println("Get CONN3...");
            conn = dataSource.getConnection();
 //           System.out.println("Get CONN3 SUCCESS");
            ps = conn.prepareStatement(sql);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            int ret = ps.executeUpdate();
            LogUtils.DB_LOG.info("SQL:" + sql + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return ret;
        } catch (SQLException e) {
            DbUtils.rollback(conn);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, ps, conn);
        }
    }

    public int[] batch(String... sqls) {
        if (CollectionUtils.isEmpty(sqls)) {
            return new int[0];
        }
        Connection conn = null;
        java.sql.Statement p = null;
        try {
            long start = System.currentTimeMillis();
      //      System.out.println("Get CONN4...");
            conn = dataSource.getConnection();
    //        System.out.println("Get CONN4 SUCCESS");
            p = conn.createStatement();

            for (String sql : sqls) {
                p.addBatch(sql);
            }
            int[] ret = p.executeBatch();
            LogUtils.DB_LOG.info("SQL:" + Arrays.toString(sqls) + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return ret;
        } catch (BatchUpdateException e) {
            return e.getUpdateCounts();
        } catch (SQLException e) {
            DbUtils.rollback(conn);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, p, conn);
        }
    }

    public int[] batchInsert(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "insert ");
        return batch(sql, params);
    }

    public int[] batchUpdate(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "update ");
        return batch(sql, params);
    }

    public int[] batchDelete(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "delete ");
        return batch(sql, params);
    }

    private int[] batch(String sql, List<Object[]> params) {
        if (CollectionUtils.isEmpty(params)) {
            throw new IllegalArgumentException("sql params cannot be empty");
        }
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            long start = System.currentTimeMillis();
            conn = dataSource.getConnection();
            ps = conn.prepareStatement(sql);

            int prevSize = params.get(0).length;
            for (int i = 0; i < params.size(); i++) {
                if (params.get(i) == null) {
                    throw new IllegalArgumentException("sql params is error");
                }
                if (prevSize != params.get(i).length) {
                    throw new IllegalArgumentException("sql params size can be equal");
                }
                for (int j = 0; j < prevSize; j++) {
                    DbUtils.setObject(ps, j + 1, params.get(i)[j]);
                }
                ps.addBatch();
            }
            int[] ret = ps.executeBatch();
            LogUtils.DB_LOG.info("SQL:" + sql + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return ret;
        } catch (BatchUpdateException e) {
            return e.getUpdateCounts();
        } catch (SQLException e) {
            DbUtils.rollback(conn);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, ps, conn);
        }
    }
}
