package com.ywang.sql.support;

import com.alibaba.fastjson.JSONObject;
import com.ywang.sql.RedisCallback;
import com.ywang.sql.RedisStatement;
import com.ywang.sql.Statement;
import com.ywang.sql.Callback;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;
import com.ywang.utils.StringUtils;
import org.apache.log4j.Logger;
// import org.springframework.data.redis.core.RedisTemplate;

import javax.sql.DataSource;
import java.util.*;
import java.sql.*;

/**
 * Author: YunWang
 * Date:   2010-10-10
 */
public final class DefaultStatememt implements RedisStatement {

    private final DataSource dataSource;

    // private RedisTemplate redisTemplate;

    private final static Logger LOGGER = Logger.getLogger( DefaultStatememt.class);

    public DefaultStatememt(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    // public void setRedisTemplate(RedisTemplate redisTemplate) {
    //     this.redisTemplate = redisTemplate;
    // }

    public <T> List<T> select(String sql, Callback<T> callback, Object... params) {
        DbUtils.checkSql(sql, "select ");
        if (callback == null) {
            throw new IllegalArgumentException("callback cannot be null");
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<T> rows = new ArrayList<T>();
        try {
            long start = System.currentTimeMillis();
            conn = dataSource.getConnection();
            conn.setAutoCommit(false);
            int concurrency = ResultSet.CONCUR_READ_ONLY;

            ps = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, concurrency);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            rs = ps.executeQuery();
            int row = 0;
            while (rs.next()) {
                T value = callback.with( row++, rs);
                if (value != null) {
                    rows.add(value);
                }
                if (concurrency == ResultSet.CONCUR_UPDATABLE) {
                    rs.updateRow();
                }
            }
            if (concurrency == ResultSet.CONCUR_UPDATABLE) {
                conn.commit();
            }

            LOGGER.info("SQL:" + sql + " runs: " + (System.currentTimeMillis() - start) / 1000 + "s");
            return rows;
        } catch (SQLException e) {
            System.out.println(sql);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(rs, ps, conn);
        }
    }

    public <T> List<T> selectAndCache(String sql, RedisCallback<T> callback, Object... params) {
        DbUtils.checkSql(sql, "select ");
        if (callback == null) {
            throw new IllegalArgumentException( "callback cannot be null");
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<T> rows = new ArrayList<T>();
        // boolean cached = (redisTemplate != null);
        boolean cached = false;
        String redisKey = null;
        try {
            if (cached) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put( "sql", sql);
                jsonObject.put( "params", params);
                redisKey = "_sql." + StringUtils.md5( jsonObject.toJSONString());

                // List<T> cacheRows = (List<T>)redisTemplate.opsForValue().get( redisKey);
                List<T> cacheRows = new ArrayList<>();
                if (cacheRows != null) {
                    LOGGER.info( "Hit Cached: " + redisKey);
                    return cacheRows;
                }
            }

            long start = System.currentTimeMillis();
            conn = dataSource.getConnection();
            conn.setAutoCommit( false);
            int concurrency = ResultSet.CONCUR_READ_ONLY;

            ps = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, concurrency);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            rs = ps.executeQuery();
            int row = 0;
            while (rs.next()) {
                T value = callback.with( row++, rs);
                if (value != null) {
                    rows.add( value);
                    if (cached && StringUtils.hasLength( callback.getRedisKey())) {
                        String redisKeyForRow = callback.getRedisKey() + "." + rs.getString( 1);
                        // redisTemplate.opsForValue().set( redisKeyForRow, value);
                        LOGGER.info( "Cache SUCCESS: " + redisKeyForRow);
                    }
                }
                if (concurrency == ResultSet.CONCUR_UPDATABLE) {
                    rs.updateRow();
                }
            }

            if (concurrency == ResultSet.CONCUR_UPDATABLE) {
                conn.commit();
            }
            if (cached) {
                String[] tablesNames = callback.getTableNames();
                LOGGER.info( "Table Names: " + Arrays.toString( tablesNames));
                if (!CollectionUtils.isEmpty( tablesNames)) {
                    for (String tableName : tablesNames) {
                        // redisTemplate.opsForSet().add( "_table." + tableName, redisKey);
                    }
                    // redisTemplate.opsForValue().set( redisKey, rows);
                    LOGGER.info( "Cache SUCCESS: " + redisKey);
                } else {
                    LOGGER.warn( "No Table Names SQL: " + sql);
                }
            }

            long runtime = (System.currentTimeMillis() - start) / 1000;
            if (runtime > 1) {
                LOGGER.info( "Query SQL:" + sql + " runs: " + runtime + "'s\nSQL Params: " + Arrays.toString( params));
            } else {
                LOGGER.debug( "Query SQL:" + sql + " runs: " + runtime + "'s\nSQL Params: " + Arrays.toString( params));
            }
            return rows;
        } catch (SQLException e) {
            System.out.println(sql);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(rs, ps, conn);
        }
    }

    public Object insert(String sql, Object... params) {
        DbUtils.checkSql(sql, "insert ");
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet generatedKey = null;

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(true);
            ps = conn.prepareStatement(sql, java.sql.Statement.RETURN_GENERATED_KEYS);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            ps.executeUpdate();
            return DbUtils.getGeneratedKey(ps);
        } catch (SQLException e) {
            System.out.println(sql);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(generatedKey, ps, conn);
        }
    }

    public Object insertAndCache(String tableName, String redisKey, Map<String,Object> entity) {
        if (StringUtils.isBlank( tableName)) {
            throw new IllegalArgumentException( "table name cannot be null or empty");
        }
        if (CollectionUtils.isEmpty( entity)) {
            throw new IllegalArgumentException( "entity cannot be null or empty");
        }

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet generatedKey = null;
        // boolean cached = (redisTemplate != null);
        boolean cached = false;

        try {
            long start = System.currentTimeMillis();
            conn = dataSource.getConnection();
            conn.setAutoCommit( true);
            List<Object> sqlParams = new ArrayList<>();
            String sql = DbUtils.insertSql( tableName, entity, sqlParams);
            ps = conn.prepareStatement(sql, java.sql.Statement.RETURN_GENERATED_KEYS);
            for (int i = 0; i < sqlParams.size(); i++) {
                DbUtils.setObject(ps, i + 1, sqlParams.get( i));
            }
            ps.executeUpdate();
            Object id = DbUtils.getGeneratedKey(ps);

            if (cached) {
                // Set keys = redisTemplate.opsForSet().members( "_table." + tableName);
                Set keys = new HashSet<>();
                if (!CollectionUtils.isEmpty( keys)) {
                    // redisTemplate.delete( keys);
                    LOGGER.info("Delete Cache[insert][_table." + tableName + "]: " + keys);
                }
                if (StringUtils.hasLength( redisKey)) {
                    // redisTemplate.opsForValue().set(redisKey + "." + id, entity);
                    LOGGER.info("Cache SUCCESS: " + redisKey + "." + id);
                }
            }

            long runtime = (System.currentTimeMillis() - start) / 1000;
            LOGGER.info( "INSERT SQL[" + id + "]:" + sql + " runs: " + runtime + "'s\nSQL Params: " + sqlParams);
            return id;
        } catch (SQLException e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(generatedKey, ps, conn);
        }
    }

    public int update(String sql, Object... params) {
        DbUtils.checkSql(sql, "update ");
        return executeUpdate(sql, false, null, params);
    }

    public int updateAndCache(String sql, Collection<String> redisKeys, Object... params) {
        DbUtils.checkSql(sql, "update ");
        return executeUpdate(sql,false, redisKeys, params);
    }

    public int delete(String sql, Object... params) {
        DbUtils.checkSql(sql, "delete ");
        return executeUpdate(sql, false, null, params);
    }

    public int deleteAndCache(String sql, Collection<String> redisKeys, Object... params) {
        DbUtils.checkSql(sql, "delete ");
        return executeUpdate(sql, false, redisKeys, params);
    }

    private int executeUpdate(String sql, boolean cached, Collection<String> redisKeys, Object... params) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(true);
            ps = conn.prepareStatement(sql);
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    DbUtils.setObject(ps, i + 1, params[i]);
                }
            }
            int updated = ps.executeUpdate();

            if (cached) {
                String tableName = DbUtils.getTableName( sql);
                if (StringUtils.hasLength( tableName)) {
                    // Set keys = redisTemplate.opsForSet().members( "_table." + tableName);
                    Set keys = new HashSet<>();
                    if (!CollectionUtils.isEmpty( keys)) {
                        // redisTemplate.delete( keys);
                        LOGGER.info("Delete Cache[execute][_table." + tableName + "]: " + keys);
                    }
                }
                if (!CollectionUtils.isEmpty( redisKeys)) {
                    // redisTemplate.delete( redisKeys);
                    LOGGER.info("Delete Cache[execute]: " + redisKeys);
                }
            }

            return updated;
        } catch (SQLException e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, ps, conn);
        }
    }

    public int[] batch(String... sqls) {
        if (CollectionUtils.isEmpty(sqls)) {
            return new int[0];
        }
        Connection conn = null;
        java.sql.Statement p = null;

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(true);
            p = conn.createStatement();

            for (String sql : sqls) {
                p.addBatch(sql);
            }

            return p.executeBatch();
        } catch (BatchUpdateException e) {
            return e.getUpdateCounts();
        } catch (SQLException e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, p, conn);
        }
    }

    public void execute(String sql) {
        Connection conn = null;
        java.sql.Statement p = null;

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(true);
            p = conn.createStatement();
            p.execute( sql);
        } catch (SQLException e) {
            System.out.println(sql);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, p, conn);
        }
    }

    public int[] batchInsert(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "insert ");
        return batch(sql, params);
    }

    public int[] batchUpdate(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "update ");
        return batch(sql, params);
    }

    public int[] batchDelete(String sql, List<Object[]> params) {
        DbUtils.checkSql(sql, "delete ");
        return batch(sql, params);
    }

    private int[] batch(String sql, List<Object[]> params) {
        if (CollectionUtils.isEmpty(params)) {
            throw new IllegalArgumentException("sql params cannot be empty");
        }
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit( false);
            ps = conn.prepareStatement(sql);

            int prevSize = params.get(0).length;
            for (int i = 0; i < params.size(); i++) {
                if (params.get(i) == null) {
                    throw new IllegalArgumentException("sql params is error");
                }
                if (prevSize != params.get(i).length) {
                    throw new IllegalArgumentException("sql params size can be equal");
                }
                for (int j = 0; j < prevSize; j++) {
                    DbUtils.setObject(ps, j + 1, params.get(i)[j]);
                }
                ps.addBatch();
            }
            int[] ret = ps.executeBatch();
            conn.commit();
            return ret;
        } catch (BatchUpdateException e) {
            e.printStackTrace();
            return e.getUpdateCounts();
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbUtils.release(null, ps, conn);
        }
    }

    // public RedisTemplate getRedisTemplate() {
    //     return redisTemplate;
    // }
}