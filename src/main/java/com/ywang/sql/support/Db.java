package com.ywang.sql.support;

import com.ywang.sql.*;
import com.ywang.sql.support.callback.MapCallback;
import com.ywang.sql.support.callback.MapPageCallback;
import com.ywang.sql.support.callback.MapPageRedisCallback;
import org.springframework.util.CollectionUtils;
import org.apache.log4j.Logger;
// import org.springframework.data.redis.core.RedisTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Map;
import java.util.List;

public class Db {

    private final static Logger LOGGER = Logger.getLogger( Db.class);

    private AbstractPageStatement statement = null;

    public Db() {
    }

    public void setStatement(AbstractPageStatement statement) {
        this.statement = statement;
    }

    public Db(AbstractPageStatement statement) {
        if (statement == null) {
            throw new IllegalArgumentException( "PageStatement cannot be null");
        }
        this.statement = statement;
    }

    public Map<String, Object> selectUnique(String sql, Object... params) {
        List<Map<String, Object>> ret = statement.select( sql, new MapPageCallback( new Page( 1)), params);
        return (CollectionUtils.isEmpty(ret) ? null : ret.get(0));
    }

    public <T> T selectUnique(String sql, final Callback<T> cb, Object... params) {
        List<T> rows = null;
        if (cb instanceof RedisCallback) {
            rows = statement.selectAndCache(sql, new PageRedisCallback<T>() {
                @Override
                public Page getPage() {
                    return new Page( 1);
                }

                @Override
                public T with(int row, ResultSet rs) throws SQLException {
                    return cb.with( row, rs);
                }

                @Override
                public String[] getTableNames() {
                    return ((RedisCallback<T>) cb).getTableNames();
                }

                @Override
                public String getRedisKey() {
                    return ((RedisCallback<T>) cb).getRedisKey();
                }
            }, params);
        } else {
            rows = statement.select(sql, new PageCallback<T>() {
                @Override
                public Page getPage() {
                    return new Page( 1);
                }

                @Override
                public T with(int row, ResultSet rs) throws SQLException {
                    return cb.with( row, rs);
                }
            }, params);
        }

        return (CollectionUtils.isEmpty( rows) ? null : rows.get(0));
    }

    public List<Map<String, Object>> select(String sql, Object... params) {
        return statement.select( sql, new MapCallback(), params);
    }

    public <T> List<T> select(String sql, Callback<T> cb, Object... params) {
        if (cb instanceof RedisCallback) {
            return statement.selectAndCache( sql, (RedisCallback)cb, params);
        }
        return statement.select(sql, cb, params);
    }

    public List<Map<String, Object>> select(String sql, Page page, Object... params) {
        return statement.select(sql, new MapPageCallback( page), params);
    }

    public List<Map<String, Object>> selectAndCache(String sql, Page page, Object... params) {
        return statement.selectAndCache(sql, new MapPageRedisCallback( page), params);
    }

    public List<Map<String, Object>> select(String sql, OrderPage page, Object... params) {
        return statement.select(sql, new MapPageCallback( page.asPage()), params);
    }

    public List<Map<String, Object>> selectAndCache(String sql, OrderPage page, Object... params) {
        return statement.selectAndCache(sql, new MapPageRedisCallback( page.asPage()), params);
    }

    public void execute(String sql) {
        statement.execute( sql);
    }


    public long insert(String sql, Object... params) {
        Object newId = statement.insert(sql, params);
        if (newId == null) {
            return 0;
        }
        return ((Number)newId).longValue();
    }

    public int update(String sql, Object... params) {
        Object newId = statement.update(sql, params);
        return ((Number) newId).intValue();
    }

    public int delete(String sql, Object... params) {
        return statement.delete(sql, params);
    }

    public int[] batchInsert(String sql, List<Object[]> params) {
        return statement.batchInsert(sql, params);
    }

    public int[] batchUpdate(String sql, List<Object[]> params) {
        return statement.batchUpdate(sql, params);
    }

    //Redis Cache
    // public RedisTemplate getRedisTemplate() {
    //     return statement.getRedisTemplate();
    // }

    //Redis Cache
    public long insertAndCache(String tableName, String redisKey, Map<String,Object> entity) {
        Object newId = statement.insertAndCache( tableName, redisKey, entity);
        if (newId == null) {
            return 0;
        }
        return ((Number)newId).longValue();
    }

    public int updateAndCache(String sql, Collection<String> redisKeys, Object... params) {
        return statement.updateAndCache( sql, redisKeys, params);
    }

    public int deleteAndCache(String sql, Collection<String> redisKeys, Object... params) {
        return statement.deleteAndCache( sql, redisKeys, params);
    }

//    public <T> T get(String sql, Object id, RedisCallback<T> callback) {
//        boolean cache = (statement instanceof RedisStatement) && callback != null && StringUtils.hasLength( callback.getKey());
//        if (cache) {
//            return ((RedisStatement)statement).get( sql, id, callback);
//        }
//        return selectUnique( sql, callback, id);
//    }
//
//    public <T> T delete(String sql, Object id, RedisCallback<T> callback) {
//        boolean cache = (statement instanceof RedisStatement) && callback != null && StringUtils.hasLength( callback.getKey());
//        if (cache) {
//            return ((RedisStatement)statement).get( sql, id, callback);
//        }
//        return selectUnique( sql, callback, id);
//    }
}
