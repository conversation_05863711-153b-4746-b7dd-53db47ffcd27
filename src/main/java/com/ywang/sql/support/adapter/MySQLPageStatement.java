package com.ywang.sql.support.adapter;

import com.ywang.sql.*;
import com.ywang.sql.support.DefaultStatememt;

import javax.sql.DataSource;

/**
 * Author: YunWang
 * Date:   2010-10-13
 */
public final class MySQLPageStatement extends AbstractPageStatement {

    public MySQLPageStatement(RedisStatement statement) {
        super( statement);
    }

    public MySQLPageStatement(DataSource dataSource) {
        super(new DefaultStatememt(dataSource));
    }

    protected String pageSql(String sql, Page page) {
        return (sql + " limit " + page.getCurrendRecord() + ", " + page.getSize());
    }
}
