package com.ywang.sql.support.adapter;

import com.ywang.sql.AbstractPageStatement;
import com.ywang.sql.Page;
import com.ywang.sql.RedisStatement;
import com.ywang.sql.Statement;
import com.ywang.sql.support.DefaultStatememt;

import javax.sql.DataSource;

/**
 * Created by hen<PERSON><PERSON> on 18/10/17.
 */
public class PostgreSQLPageStatement extends AbstractPageStatement {

    public PostgreSQLPageStatement(RedisStatement statement) {
        super(statement);
    }

    public PostgreSQLPageStatement(DataSource dataSource) {
        super(new DefaultStatememt( dataSource));
    }

    protected String pageSql(String sql, Page page) {
        return (sql + " limit " + page.getSize()  + " offset "  + page.getCurrendRecord());
    }
}