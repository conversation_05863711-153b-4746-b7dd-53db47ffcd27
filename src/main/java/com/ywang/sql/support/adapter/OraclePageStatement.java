package com.ywang.sql.support.adapter;

import com.ywang.sql.AbstractPageStatement;
import com.ywang.sql.Page;
import com.ywang.sql.RedisStatement;
import com.ywang.sql.Statement;
import com.ywang.sql.support.DefaultStatememt;

import javax.sql.DataSource;


public class OraclePageStatement  extends AbstractPageStatement {

    public OraclePageStatement(RedisStatement statement) {
        super( statement);
    }

    public OraclePageStatement(DataSource dataSource) {
        super(new DefaultStatememt(dataSource));
    }

    protected String pageSql(String sql, Page page) {
        return ( "SELECT * FROM "
                    + "(SELECT ROWNUM RN, t.*  FROM (" + sql  + ") t WHERE ROWNUM <=" + (page.getCurrendRecord() + page.getSize()) + ")"
                    + " WHERE RN >= " + page.getCurrendRecord());
    }
}
