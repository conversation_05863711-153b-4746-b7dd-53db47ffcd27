package com.ywang.restful.webservice;

import com.ywang.restful.RestfulRequest;
import com.ywang.restful.RestfulResponse;

/**
 * Created by hen<PERSON><PERSON> on 2019/11/23.
 */
public abstract class AbstractWebServiceResponse<T extends RestfulRequest, V extends RestfulResponse> implements RestfulResponse<T,V> {

    public T[] getRequests() {
        return null;
    }

    public void response(int i, V response) {
    }
}
