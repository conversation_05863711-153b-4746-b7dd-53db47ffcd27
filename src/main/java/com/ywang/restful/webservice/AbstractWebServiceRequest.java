package com.ywang.restful.webservice;

import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.util.Iterator;

import com.ywang.restful.RestfulPostRequest;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

public abstract class AbstractWebServiceRequest<T extends AbstractWebServiceResponse> implements RestfulPostRequest<T> {

    public T response(String xml) {
        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read( new ByteArrayInputStream( xml.getBytes( Charset.forName("UTF-8"))));
            Element root = document.getRootElement();
            Element resultElement = findResultElement( root);

            return doCreateResponse( resultElement.getStringValue());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    protected Element findResultElement(Element element) {
        return findResultElement( element, getResultNodeName());
    }

    protected Element findResultElement(Element element, String nodeName) {
        Iterator iterator = element.elementIterator();
        while (iterator.hasNext()) {
            Element child = (Element) iterator.next();
            if (nodeName.equals( child.getName())) {
                return child;
            }
            Element resultElement = findResultElement( child, nodeName);
            if (resultElement != null) {
                return resultElement;
            }
        }
        return null;
    }

    protected abstract T doCreateResponse(String json);

    protected abstract String getResultNodeName();
}