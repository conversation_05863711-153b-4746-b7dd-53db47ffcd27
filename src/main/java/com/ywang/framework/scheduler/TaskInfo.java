package com.ywang.framework.scheduler;



import java.lang.reflect.Method;

/**
 * Created by he<PERSON><PERSON><PERSON> on 17/4/2.
 */
public class TaskInfo {

    private final Object target;

    private final Method method;

    private final Class serviceClass;

    TaskInfo(Object target, Method method, Class serviceClass) {
        if (target == null) {
            throw new IllegalArgumentException("target cannot be null");
        }
        if (method == null) {
            throw new IllegalArgumentException("method cannot be null");
        }
//        if (method.getAnnotation(Scheduled.class) == null) {
//            throw new IllegalArgumentException("method must be task scheduled");
//        }
        this.target = target;
        this.method = method;
        this.serviceClass = serviceClass;
    }

    public Object getTarget() {
        return target;
    }

    public String getClassName() {
        return serviceClass.getName();
    }

    public String getMethodName() {
        return method.getName();
    }

    public String getCron() {
//        Scheduled scheduled = method.getAnnotation(Scheduled.class);
//        return scheduled.cron();
        return null;
    }

    public String getDescription() {
        return "";
    }
}
