package com.ywang.framework.scheduler;



import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ScheduleManager {

    private ScheduleManager() {}

    private static ScheduleManager INSTANCE = new ScheduleManager();

    private Map<Method, TaskInfo> registerTasks = new HashMap<>();

    public static ScheduleManager getInstance() {
        return INSTANCE;
    }

    public int registerTask(Object target, Class clazz, boolean updated) {
        if (target == null) {
            throw new IllegalArgumentException("target cannot be null");
        }
        int registerCount = 0;

//        for (Method method :clazz.getDeclaredMethods()) {
//            Scheduled scheduled = method.getAnnotation(Scheduled.class);
//            if (scheduled != null) {
//                if (registerTasks.containsKey(method) && !updated) {
//                    continue;
//                }
//                registerTasks.put(method, new TaskInfo(target, method, clazz)) ;
//                registerCount++;
//            }
//        }

        return registerCount;
    }

    public List<TaskInfo> getRegisterTasks() {
        return new ArrayList<>(registerTasks.values());
    }
}
