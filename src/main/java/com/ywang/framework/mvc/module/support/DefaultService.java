package com.ywang.framework.mvc.module.support;

import com.ywang.sql.OrderPage;
import com.ywang.sql.support.Db;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;
import com.ywang.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class DefaultService extends AbstractService<Map<String,Object>> {

    protected Db db;

    public void setDb(Db db) {
        this.db = db;
    }

    public long save(Map<String,Object> entity) {
        List params = new ArrayList();
        String sql = DbUtils.insertSql(doGetTableName(), entity, params);
        return db.insert(sql, params.toArray(new Object[0]));
    }
    
    public List<Map<String,Object>> list(String whereSql, Object...params) {
        return db.select(doGetQuerySql() + whereSql + doGetOrderSql(), params);
    }

    public List<Map<String,Object>> list(String whereSql, OrderPage page, Object...params) {
        String orderBy = doGetOrderSql();
        if (StringUtils.hasLength(page.getOrderBy())) {
            orderBy = page.getOrderBy();
        }

        return db.select(doGetQuerySql() + whereSql + orderBy, page.asPage(), params);
    }
    
    public Map<String,Object> view(long id) {
        return db.selectUnique(doGetQuerySql() + " and id=? ", id);
    }
    
    public int update(Map<String,Object> entity, String whereSql, Object...params) {
        List updateParams = new ArrayList();
        String sql = DbUtils.updateSql(doGetTableName(), entity, updateParams) + whereSql;

        if (!CollectionUtils.isEmpty(params)) {
            for (Object param : params) {
                updateParams.add(param);
            }
        }
        return db.update(sql, updateParams.toArray(new Object[0]));
    }

    public int delete(String whereSql, Object...params) {
        return db.delete("delete from " + doGetTableName() + " where 1=1 " + whereSql, params);
    }
    
    public int delete(long id) {
        return db.delete("delete from " + doGetTableName() + " where id=? ", id);
    }
    
    
    protected String doGetTableName() {
        return null;      
    }

    protected String doGetQuerySql() {
        return null;
    }
    
    protected String doGetOrderSql() {
        return "";
    }
}
