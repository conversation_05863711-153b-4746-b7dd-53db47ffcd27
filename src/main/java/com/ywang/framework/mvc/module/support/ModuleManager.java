package com.ywang.framework.mvc.module.support;

import com.ywang.framework.mvc.module.Module;
import com.ywang.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public final class ModuleManager {
    
    private ModuleManager() {
    };

    private static Map<String,Module> REGISTERED_MODULES = new HashMap<String, Module>();

    public static synchronized Module getInstance(String moduleName) {
        if (!StringUtils.hasLength(moduleName)) {
            throw new IllegalArgumentException("module name cannot be null");
        }

        Module module = REGISTERED_MODULES.get(moduleName);
        if (module == null) {
            module = new SimpleModule(moduleName);
        }
        return module;
    }

    public static synchronized void register(Module module) {
        if (module == null) {
            throw new IllegalArgumentException("module cannot be null");
        }
        if (REGISTERED_MODULES.containsKey(module.getModuleName())) {
            throw new IllegalArgumentException("不能重复构建Module对象：" + module.getModuleName());
        }
        REGISTERED_MODULES.put(module.getModuleName(), module);
    }
    
}
