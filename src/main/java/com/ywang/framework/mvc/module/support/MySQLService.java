package com.ywang.framework.mvc.module.support;


import com.ywang.sql.MySQLPage;
import com.ywang.sql.OrderPage;
import com.ywang.utils.StringUtils;

import java.util.List;
import java.util.Map;

public abstract class MySQLService extends DefaultService {

    public List<Map<String,Object>> list(String whereSql, OrderPage page, Object...params) {
        String orderBy = new MySQLPage(page, doGetOrderFields()).getOrderBy();
        if (!StringUtils.hasLength(orderBy)) {
            orderBy = doGetOrderSql();
        }

        return db.select(doGetQuerySql() + whereSql + orderBy, page.asPage(), params);
    }

    protected String[] doGetOrderFields() {
        return null;
    }
}
