/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 21:59:19
 * @FilePath: /analysis_engine/src/main/java/com/ywang/framework/mvc/module/Module.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.framework.mvc.module;

public interface Module {

    public Module getModule(String moduleName);

    public Module[] getModules();

    public <T extends Service> T getService(Class<T> clazz);

    public <T extends Service> T getService(Class<T> clazz, Module fromModule);

    public Service[] getServices();

    public String getModuleName();

    public void registerService(Service service);
}
