package com.ywang.framework.mvc.module;


public interface Module {

    public Module getModule(String moduleName);

    public Module[] getModules();

    public <T extends Service> T getService(Class<T> clazz);

    public <T extends Service> T getService(Class<T> clazz, Module fromModule);
    
    public Service[] getServices();
    
    public String getModuleName();

    public void registerService(Service service);
}
