package com.ywang.framework.mvc.module.support;


import com.ywang.framework.mvc.module.Module;
import com.ywang.framework.mvc.module.Service;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;

import java.util.HashSet;
import java.util.Set;

public abstract class AbstractService<T> implements Service<T> {

    private final Set<Module> modules = new HashSet<Module>();

    public <T extends Service> T getService(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("class cannot be null");
        }
        for (Module module : modules) {
            T service = module.getService(clazz);
            if (service != null) {
                return service;
            }
        }
        return null;
    }

    public Module getModule(String moduleName) {
        if (StringUtils.hasLength(moduleName)) {
            throw new IllegalArgumentException("module name cannot be null or empty");
        }

        for (Module module : modules) {
            if (moduleName.equals(module.getModuleName())) {
                return module;
            }
        }
        return null;
    }

    public Module[] getModules() {
        return modules.toArray(new Module[0]);
    }

    public void setModule(Module module) {
        registerModule(module);
        module.registerService(this);
    }

    public void setModuleList(Module... modules) {
        if (CollectionUtils.isEmpty(modules)) {
            throw new IllegalArgumentException("modules cannot be null");
        }
        for (Module module : modules) {
            setModule(module);
        }

    }

    public void registerModule(Module module) {
        if (module == null) {
            throw new IllegalArgumentException("module cannot be null");
        }

        Class moduleClass = module.getClass();
        for (Module _module : modules) {
            // 在添加Module的同时需要删除原有Module对象
            if (moduleClass.equals(moduleClass)) {
                modules.remove(_module);
                modules.add(module);
                return;
            }
        }
        modules.add(module);
    }
}
