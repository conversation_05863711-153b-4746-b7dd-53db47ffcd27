package com.ywang.framework.mvc.module.support;




import com.ywang.framework.mvc.module.Module;
import com.ywang.framework.mvc.module.Service;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public abstract class AbstractModule implements Module {

    protected final Map<Class<? extends Service>, Service> services = new HashMap<Class<? extends Service>, Service>();

    protected final Map<String, AbstractModule> modules = new HashMap<String, AbstractModule>();

    public Module getModule(String moduleName) {
        if (StringUtils.hasLength(moduleName)) {
            throw new IllegalArgumentException("module name cannot be null or empty");
        }
       
        return modules.get(moduleName);
    }

    public Module[] getModules() {
        return modules.values().toArray(new Module[0]);
    }

    public <T extends Service> T getService(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("class cannot be null");
        }
        //1. 根据类型直接返回Service对象
        T service = (T) services.get(clazz);
        if (service != null) {
            return service;
        }
        //2. 返回其子类Service对象
        for (Class _clazz : services.keySet()) {
            if (clazz.isAssignableFrom(_clazz)) {
                return (T) services.get(_clazz);
            }
        }
        //3. 从关联模块范围Service对象
        for (Module module : modules.values()) {
            if (module == this) {
                continue;
            }
            service = module.getService(clazz, this);
            if (service != null) {
                return service;
            }
        }
        return null;
    }

    public <T extends Service> T getService(Class<T> clazz, Module fromModule) {
        if (clazz == null) {
            throw new IllegalArgumentException("class cannot be null");
        }
        //1. 根据类型直接返回Service对象
        T service = (T) services.get(clazz);
        if (service != null) {
            return service;
        }
        //2. 返回其子类Service对象
        for (Class _clazz : services.keySet()) {
            if (clazz.isAssignableFrom(_clazz)) {
                return (T) services.get(_clazz);
            }
        }
        //3. 从关联模块范围Service对象
        for (Module module : modules.values()) {
            if (module == this) {
                continue;
            }
            if (module == fromModule) {
                continue;
            }
            service = module.getService(clazz, this);
            if (service != null) {
                return service;
            }
        }
        return null;
    }

    public Service[] getServices() {
        return services.values().toArray(new Service[0]);
    }

    public void setModule(AbstractModule module) {
        if (module == null) {
            throw new IllegalArgumentException("module cannot be null");
        }
        modules.put(module.getModuleName(), module);
    }

    public void setModuleList(AbstractModule... modules) {
        if (CollectionUtils.isEmpty(modules)) {
            throw new IllegalArgumentException("module cannot be null or empty");
        }
        for (AbstractModule module : modules) {
            setModule(module);
        }
    }

    public void setService(Service service) {
        registerService(service);
        service.registerModule(this);
    }

    public void setServices(Service... services) {
        if (CollectionUtils.isEmpty(services)) {
            throw new IllegalArgumentException("services cannot be null or empty");
        }
        for (Service service : services) {
            setService(service);
        }
    }

    public abstract String getModuleName();

    public void registerService(Service service) {
        if (service == null) {
            throw new IllegalArgumentException("service cannot be null");
        }
        Class serviceClass = service.getClass();
//        if (Proxy.isProxyClass(serviceClass)) {
//            String serviceName = service.toString();
//            serviceName = serviceName.substring(0, serviceName.indexOf("@"));
//            try {
//                serviceClass = Class.forName(serviceName);
//            } catch (Exception e) {
//            }
//        }
        services.put(serviceClass, service);
    }
}
