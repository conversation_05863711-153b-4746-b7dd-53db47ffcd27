package com.ywang.framework.mvc.module.support;

import com.ywang.sql.OrderPage;
import com.ywang.sql.support.Db;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;
import java.util.List;
import java.util.Map;


public abstract class ReportService extends AbstractService<Map<String,Object>> {

    protected Db db;

    public void setDb(Db db) {
        this.db = db;
    }

    public long save(Map<String,Object> entity) {
        throw new UnsupportedOperationException("Report Service do not supported save method");
    }

    public List<Map<String,Object>> list(String whereSql, Object...params) {
        if (whereSql == null) {
            whereSql = "";
        }
        List<Map<String,Object>> records = db.select(doGetQuerySql().replace("{whereSql}", whereSql) + doGetOrderSql(), params);

        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        int idx = 0;
        for (Map<String,Object> record : records) {
            calcRecord(record, idx++, records);
        }
        return records;
    }

    public List<Map<String,Object>> list(String whereSql, OrderPage page, Object...params) {
        if (whereSql == null) {
            whereSql = "";
        }
        String orderBy = doGetOrderSql();
        if (StringUtils.hasLength(page.getOrderBy())) {
            orderBy = page.getOrderBy();
        }
        List<Map<String,Object>> records = db.select(doGetQuerySql().replace("{whereSql}", whereSql)+ orderBy, page.asPage(), params);

        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        int idx = 0;
        for (Map<String,Object> record : records) {
            calcRecord(record, idx++, records);
        }
        return records;
    }

    public Map<String,Object> view(long id) {
        throw new UnsupportedOperationException("Report Service do not supported view method");
    }

    public int update(Map<String,Object> entity, String whereSql, Object...params) {
        throw new UnsupportedOperationException("Report Service do not supported update method");
    }

    public int delete(String whereSql, Object...params) {
        throw new UnsupportedOperationException("Report Service do not supported delete method");
    }

    public int delete(long id) {
        throw new UnsupportedOperationException("Report Service do not delete delete method");
    }


    protected String doGetQuerySql() {
        return null;
    }

    protected String doGetOrderSql() {
        return "";
    }

    protected void calcRecord(Map<String, Object> current, int index, List<Map<String, Object>> report) {

    }
}
