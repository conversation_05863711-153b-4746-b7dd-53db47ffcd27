package com.ywang.framework.mvc.module.support;


import com.ywang.framework.mvc.UploadFile;
import com.ywang.module.comm.service.UploadFileService;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;

import java.util.*;

public class DefaultUploadFileService extends DefaultService {

    public long save(Map<String, Object> entity) {
        List<String> removeKeys = new ArrayList<String>();
        Long uploadFileId = null;
        for (String key : entity.keySet()) {
            Object value = entity.get(key);
            if (value instanceof UploadFile) {
                removeKeys.add(key);
                UploadFile uploadFile = (UploadFile) value;
                uploadFile.setCreateTime((Date)entity.get("create_time"));
                uploadFile.setCreateUserName((String)entity.get("create_username"));

                UploadFileService uploadFileService = getService(UploadFileService.class);
                uploadFileId = uploadFileService.save(uploadFile);
            }
        }
        for (String key : removeKeys) {
            entity.remove(key);
        }
        if (uploadFileId != null) {
            entity.put(doGetUploadFileFieldName(), uploadFileId);
        }

        List params = new ArrayList();
        String sql = DbUtils.insertSql(doGetTableName(), entity, params);
        return db.insert(sql, params.toArray(new Object[0]));
    }

    public int update(Map<String, Object> entity, String whereSql, Object... params) {
        List<String> removeKeys = new ArrayList<String>();
        Long uploadFileId = null;
        for (String key : entity.keySet()) {
            Object value = entity.get(key);
            if (value instanceof UploadFile) {
                removeKeys.add(key);
                UploadFile uploadFile = (UploadFile) value;
                uploadFile.setCreateTime((Date)entity.get("update_time"));
                uploadFile.setCreateUserName((String)entity.get("update_username"));

                UploadFileService uploadFileService = getService(UploadFileService.class);
                uploadFileId = uploadFileService.save(uploadFile);
            }
        }
        for (String key : removeKeys) {
            entity.remove(key);
        }
        if (uploadFileId != null) {
            entity.put(doGetUploadFileFieldName(), uploadFileId);
        }

        List updateParams = new ArrayList();
        String sql = DbUtils.updateSql(doGetTableName(), entity, updateParams) + whereSql;
        if (!CollectionUtils.isEmpty(params)) {
            for (Object param : params) {
                updateParams.add(param);
            }
        }
        return db.update(sql, updateParams.toArray(new Object[0]));
    }

    protected String doGetUploadFileFieldName() {
        return "";
    }

}
