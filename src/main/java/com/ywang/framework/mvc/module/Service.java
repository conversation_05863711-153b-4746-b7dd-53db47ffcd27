package com.ywang.framework.mvc.module;


import com.ywang.sql.OrderPage;

import java.io.IOException;
import java.util.List;

/**
 *
 */
public interface Service<T> {

    public <T extends Service> T getService(Class<T> clazz);

    public Module getModule(String moduleName);

    public Module[] getModules();

    void registerModule(Module module);

    //增删改查
    public long save(T entity);

    public List<T> list(String whereSql, Object...params) throws IOException;

    public List<T> list(String whereSql, OrderPage page, Object...params) throws IOException;

    public T view(long id);

    public int update(T entity, String whereSql, Object...params);

    public int delete(String whereSql, Object...params);

    public int delete(long id);
}
