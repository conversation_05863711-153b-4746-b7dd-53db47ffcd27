/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-09 15:23:23
 * @FilePath: /analysis_engine/src/main/java/com/ywang/framework/mvc/Context.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.framework.mvc;

import com.ywang.framework.mvc.module.Service;
import com.ywang.framework.mvc.module.support.AbstractModule;
import com.ywang.framework.mvc.module.support.ModuleManager;
import com.ywang.framework.scheduler.ScheduleManager;
import com.ywang.module.rbac.service.AccountService;
import com.ywang.module.rbac.service.support.SimplePrivilegeService;
import com.ywang.module.sys.service.support.DbEnumManager;
import com.ywang.module.sys.service.support.DbSysConfigManager;
import com.ywang.module.sys.service.support.ErpRuleset;
import com.ywang.sql.support.Db;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.Date;


public final class Context implements ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {

    private Context() {
    }

    private static ApplicationContext SPRING_CONTEXT;

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SPRING_CONTEXT = applicationContext;
        System.out.println("spring_context -> " + SPRING_CONTEXT);
    }

    public void onApplicationEvent(ContextRefreshedEvent event) {

        System.out.println( new Date() + ": Context onApplicationEvent - " );
        ApplicationContext context = event.getApplicationContext();
        System.out.println(context.getDisplayName());
        ApplicationContext parentContext = context.getParent();
        System.out.println("parent context " + parentContext);
        if (parentContext != null ) {
            return;
        }

        try {
            //1. 检查模块关联及自动关联
            String[] beanNames = context.getBeanNamesForType(Service.class);
            for (String beanName : beanNames) {
                int idx = beanName.indexOf(".");
                String moduleName = beanName.substring(0, idx);
                AbstractModule module = (AbstractModule) ModuleManager.getInstance(moduleName);
                module.setService((Service) context.getBean(beanName));
                System.out.println(beanName + "[" + moduleName + "]:" + module.getServices().length);
            }
//
            //3. 注册Config,Enum
            String[] dbNames = context.getBeanNamesForType(Db.class);
            System.out.println(dbNames[0]);
            System.out.println(dbNames);
            Db db;
            if (CollectionUtils.isEmpty(dbNames)) {
                db = (Db) context.getBean(Constant.DEFAULT_DB_BEAN_NAME);
            } else {
                db = (Db) context.getBean(dbNames[0]);
            }

            DbSysConfigManager.registerConfig(db);
            DbEnumManager.registerEnum(db);

        } catch(Exception e) {
            System.out.println("ERROR: Context onApplicationEvent");
            e.printStackTrace();
        }
    }

    public static  ApplicationContext getApplicationContext() {
        return SPRING_CONTEXT;
    }

    public static <T> T getBean(Class<T> clazz, String defaultBeanName) {
        String[] beanNames = SPRING_CONTEXT.getBeanNamesForType( clazz);


        if (CollectionUtils.isEmpty( beanNames)) {
            return null;
        }
        if (StringUtils.isBlank( defaultBeanName)) {
            return (T)SPRING_CONTEXT.getBean( beanNames[0]);
        }

        for (String beanName : beanNames) {
            if (beanName.equals( defaultBeanName)) {
                return (T)SPRING_CONTEXT.getBean( beanName);
            }
        }
        System.out.println("getBean -> " + defaultBeanName);
        return (T)SPRING_CONTEXT.getBean( beanNames[0]);
    }

}
