package com.ywang.framework.mvc.view.support;



import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.module.Service;
import com.ywang.framework.mvc.view.ServiceView;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.sql.OrderPage;

import java.io.IOException;
import java.util.*;


public class EasyUIView<T extends Service<Map<String, Object>>> implements ServiceView<T, Map<String,Object>> {

    protected T service;

    public void setService(T service) {
        this.service = service;
    }

    public Forward create(ViewParams<?> params) {
        return doGetForward();
    }

    public Forward save(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String,Object> entity = params.getEntity(doGetEntityFields());
                Date now = new Date();
                entity.put("create_time", now);
//                entity.put("create_username", params.getUser().get("username"));
                entity.put("create_username", "db");
                entity.put("update_time", now);
//                entity.put("update_username", params.getUser().get("username"));
                entity.put("update_username", "db");
                service.save(entity);
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                e.printStackTrace();
                throw new UnknownException(new JsonForward(ret), e);
            }
            return new JsonForward(ret);
        }


        return new DefaultForward(null);
    }

    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            List<Object> whereParams = new ArrayList<>();
            OrderPage page = params.getPage();
            String whereSql = doGetWhereSql(params, whereParams);
            List<Map<String, Object>> datagrid = null;
            try {
                datagrid = service.list(whereSql, page, whereParams.toArray(new Object[0]));
            } catch (IOException e) {
                e.printStackTrace();
            }

            for (Map<String,Object> record : datagrid) {
                doProcessRecord( record);
            }
            return new JsonForward(new DataGrid(datagrid, page.asPage().getTotal()));
        }
        return doGetForward();
    }

    public Forward listAll(ViewParams<?> params) throws JsonProcessingException, IOException {
        if (params.isJson()) {
            List<Object> whereParams = new ArrayList<>();
            String whereSql = doGetWhereSql(params, whereParams);
            List<Map<String, Object>> datagrid  = service.list(whereSql, whereParams.toArray(new Object[0]));

            for (Map<String,Object> record : datagrid) {
                doProcessRecord( record);
            }
            return new JsonForward(datagrid);
        }
        return doGetForward();
    }

    public Forward view(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String, Object> entity = service.view(params.getId());
            return new JsonForward(entity);
        }
        return doGetForward();
    }

    public Forward edit(ViewParams<?> params) {
        return doGetForward();
    }

    public Forward update(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String,Object> entity = params.getEntity(doGetEntityFields());
                entity.put("update_time", new Date());
                entity.put("update_username", params.getUserName());
                service.update(entity, " and id=? ", params.getId());
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                throw new UnknownException(new JsonForward(ret), e);
            }
            return new JsonForward(ret);
        }
        return doGetForward();
    }

    public Forward delete(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                service.delete(params.getId());
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                throw new UnknownException(new JsonForward(ret), e);
            }
            return new JsonForward(ret);
        }
        return doGetForward();
    }

    protected String[] doGetEntityFields() {
        return null;
    }

    protected Forward doGetForward() {
        return new DefaultForward(null);
    }

    protected void doProcessRecord(Map<String,Object> record) {
    }

    protected String doGetWhereSql(ViewParams<?> params, List<Object> whereParams) {
        return params.whereSql( whereParams);
    }
}
