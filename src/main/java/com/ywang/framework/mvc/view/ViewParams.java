package com.ywang.framework.mvc.view;


import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.UploadFile;
import com.ywang.http.SessionUser;
import com.ywang.sql.OrderPage;
import com.ywang.sql.Page;
import com.ywang.utils.*;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.*;

public class ViewParams<E> implements Serializable {

    private final Map<String, List<E>> params;

    private final Map<String,Object> attributes;

    private final Map<String, Object> session;

    private final boolean isJson;

    private final String url;

    private final int userCount;

    private final boolean isFromMobile;

    private final String ip;

    private final String userAgent;

    private final String agentId;

    // "${pageContext.request.scheme}://${pageContext.request.serverName}:${pageContext.request.serverPort}/${pageContext.request.contextPath}"
    public String getURL() {
        return url;
    }

    public ViewParams(Map<String, List<E>> params, Map<String, Object> session, HttpServletRequest request) {
        this.params = params;
        this.session = session;
        isJson = request.getServletPath().endsWith(Constant.JSON_SUFFIX);
        url = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + "/" + request.getContextPath();
        isFromMobile = HttpUtils.isFromMobile( request);
        ip = HttpUtils.getIp( request);
        userAgent = request.getHeader("user-agent");

        @SuppressWarnings("unchecked")
        Map<String, HttpSession> sessions = (Map<String, HttpSession>) request.getSession().getServletContext().getAttribute(Constant.CONTEXT_SESSIONS_KEY);

        if (sessions == null) {
            userCount = 0;
        } else {
            userCount = sessions.size();
        }
        attributes = new HashMap<>();

        List<String> attributeNames = Collections.list( request.getAttributeNames());
        for (String attributeName : attributeNames) {
            attributes.put( attributeName, request.getAttribute( attributeName));
        }
        agentId = (String)request.getAttribute( "__agent_id");
    }


    public boolean isJson() {
        return isJson;
    }

    public boolean isFromMobile() {
        return isFromMobile;
    }

    public void printParams() {
        System.out.println( "Request Params: " + params);
    }


    public SessionUser getUser() {
        return (SessionUser) session.get(Constant.SESSION_USER_KEY);
    }

    public String getUserName() {
        SessionUser user = getUser();
        if (user == null) {
            return "none-user";
        }
        return user.getUserName();
    }


    public Map<String,Object> getSession() {
        return session;
    }

    public Boolean getBoolean(String name) {
        return getBoolean(name, null);
    }

    public Boolean getBoolean(String name, Boolean defValue) {
        return getValue(Boolean.class, name, defValue);
    }

    public Integer getInt(String name) {
        return getInt(name, null);
    }

    public Integer getInt(String name, Integer defValue) {
        return getValue(Integer.class, name, defValue);
    }

    public Long getLong(String name) {
        return getLong(name, null);
    }

    public Long getLong(String name, Long defValue) {
        return getValue(Long.class, name, defValue);
    }

    public Long getId() {
        return getLong("id", null);
    }

    public Double getDouble(String name) {
        return getDouble(name,null);
    }

    public Double getDouble(String name, Double defValue) {
        return getValue(Double.class, name, defValue);
    }

    public String getString(String name) {
        return getString(name, true, null);
    }


    public String agentId() {
        return (String)attributes.get( "__agent_id");
    }

    public String getIp() { return ip;}

    public String getUserAgent() { return userAgent; }

    public String getAgentId() {
        return agentId;
    }

    public String getString(String name, boolean isEscape, String defValue) {
        String str = getValue(String.class, name, defValue);
        if (StringUtils.hasLength(str) && isEscape) {
            return HtmlUtils.htmlEscape( str);
        }
        return str;
    }

    public Date getDate(String name) {
        return getDate(name, null);
    }

    public Date getDate(String name, Date defValue) {
        return getValue(Date.class, name, defValue);
    }

    public UploadFile getUploadFile(String name) {
        List<E> paramValues = params.get(name);
        if (CollectionUtils.isEmpty(paramValues)) {
            return null;
        }
        Object value = paramValues.get(0);
        if (value instanceof UploadFile) {
            return (UploadFile)value;
        }
        return null;
    }

    public String[] getStringArray(String name) {
        List<String> strings = getList(String.class, name);
        if (strings == null) {
            return null;
        }
        return strings.toArray(new String[0]);
    }

    public Integer[] getIntegerArray(String name) {
        List<Integer> integers = getList(Integer.class, name);
        if (integers == null) {
            return null;
        }
        return integers.toArray(new Integer[0]);
    }

    public Long[] getLongArray(String name) {
        List<Long> longs = getList(Long.class, name);
        if (longs == null) {
            return null;
        }
        return longs.toArray(new Long[0]);
    }

    public Double[] getDoubleArray(String name) {
        List<Double> doubles = getList(Double.class, name);
        if (doubles == null) {
            return null;
        }
        return doubles.toArray(new Double[0]);
    }

    public Date[] getDateArray(String name) {
        List<Date> dates = getList(Date.class, name);
        if (dates == null) {
            return null;
        }
        return dates.toArray(new Date[0]);
    }

    public UploadFile[] getUploadFileArray(String name) {
        List<E> paramValues = params.get(name);
        if (CollectionUtils.isEmpty(paramValues)) {
            return null;
        }
        List<UploadFile> ret = new ArrayList<UploadFile>(paramValues.size());
        for (Object value : paramValues) {
            if (value instanceof UploadFile) {
                ret.add((UploadFile)value);
            }
        }
        return ret.toArray(new UploadFile[0]);
    }


    public OrderPage getPage() {
//        Page page = new Page(getInt("rows", 30), getInt("page", 1));
        Page page = new Page(getInt("pageSize", 30), getInt("page", 1));
        String sort = getString("sort");
        String order = getString("order");
        String[] sorts = null;
        String[] orders = null;

        if (StringUtils.hasLength(sort)) {
            sorts = new String[]{sort};
            orders = new String[]{order};

            if (sort.indexOf(",") > 0) {
                sorts = sort.split(",");
                orders = order.split(",");
            }
        }

        return new OrderPage(page, sorts, orders);
    }

    private <T> T getValue(Class<T> clazz, String name, T defValue) {
        List<E> paramValues = params.get(name);
        if (CollectionUtils.isEmpty(paramValues)) {
            return defValue;
        }
        Object value = paramValues.get(0);
        if (value == null) {
            return defValue;
        }
        return convert(clazz, value.toString(), defValue);
    }

    private <T> List<T> getList(Class<T> clazz, String name) {
        List<E> paramValues = params.get(name);
        if (CollectionUtils.isEmpty(paramValues)) {
            return null;
        }
        List<T> ret = new ArrayList<T>(paramValues.size());
        for (Object value : paramValues) {
            if (value == null) {
                ret.add(null);
            } else {
                ret.add(convert(clazz, value.toString(), null));
            }
        }
        return ret;
    }

    private static <T> T convert(Class<T> clazz, String value, T defValue) {
        if (clazz == String.class) {
            return (T) value;
        }
        if (!StringUtils.hasLength(value)) {
            return defValue;
        }
        if (clazz == Date.class) {
            return (T) ConvertUtils.str2Date(value);
        }
        if (clazz == Boolean.class) {
            Object ret = ("true".equalsIgnoreCase(value) || "1".equals(value));
            return (T) ret;
        }
        try {
            Method valueOf = clazz.getMethod("valueOf", new Class[]{String.class});
            if (valueOf != null) {
                return (T) valueOf.invoke(null, value);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defValue;
    }

    public int getUserCount() {
        return userCount;
    }

    public Map<String, Object> getEntity(String[] names) {
        Map<String, Object> ret = new HashMap<String, Object>();
        for (String name : names) {
            String[] arr = name.split("-");
            if (CollectionUtils.isEmpty(arr) || arr.length < 1 || arr.length > 3) {
                throw new IllegalArgumentException("name formatter is error: " + name);
            }
            String paramType = arr[0];
            String paramName = arr[1];
            String defValue = (arr.length == 3 ? arr[2] : null);

            Object value = null;
            if ("str".equals(paramType)) {
                value = getString(paramName, true, defValue);
            } else if ("date".equals(paramType)) {
                value = getDate(paramName, ConvertUtils.str2Date(defValue));
            } else if ("int".equals(paramType)) {
                value = getInt(paramName, ConvertUtils.integer(defValue));
            } else if ("lon".equals(paramType)) {
                value = getLong(paramName, ConvertUtils.lon(defValue));
            } else if ("dou".equals(paramType)) {
                value = getDouble(paramName, ConvertUtils.dou(defValue));
            } else if ("bool".equals(paramType)) {
                value = getBoolean(paramName, ConvertUtils.bool(defValue));
            } else if ("uploadfile".equals(paramType)) {
                value = getUploadFile(paramName);
            } else {
                throw new IllegalArgumentException("name formatter is error: " + name);
            }

            if ("uploadfile".equals(paramType) && value == null) {
                continue;
            }
            ret.put(paramName, value);
        }
        return ret;
    }

    public Map<String, Object> getEntity(String[] names, boolean ignoreNull) {
        Map<String, Object> ret = new HashMap<String, Object>();
        for (String name : names) {
            String[] arr = name.split("-");
            if (CollectionUtils.isEmpty(arr) || arr.length < 1 || arr.length > 3) {
                throw new IllegalArgumentException("name formatter is error: " + name);
            }
            String paramType = arr[0];
            String paramName = arr[1];
            String defValue = (arr.length == 3 ? arr[2] : null);

            Object value = null;
            if ("str".equals(paramType)) {
                value = getString(paramName, true, defValue);
            } else if ("date".equals(paramType)) {
                value = getDate(paramName, ConvertUtils.str2Date(defValue));
            } else if ("int".equals(paramType)) {
                value = getInt(paramName, ConvertUtils.integer(defValue));
            } else if ("lon".equals(paramType)) {
                value = getLong(paramName, ConvertUtils.lon(defValue));
            } else if ("dou".equals(paramType)) {
                value = getDouble(paramName, ConvertUtils.dou(defValue));
            } else if ("bool".equals(paramType)) {
                value = getBoolean(paramName, ConvertUtils.bool(defValue));
            } else if ("uploadfile".equals(paramType)) {
                value = getUploadFile(paramName);
            } else {
                throw new IllegalArgumentException("name formatter is error: " + name);
            }

            if (ignoreNull && value == null) {
                continue;
            }

            if ("uploadfile".equals(paramType) && value == null) {
                continue;
            }
            ret.put(paramName, value);
        }
        return ret;
    }


    public String whereSqlForKendoUI(List whereParams) {
        StringBuffer whereSql = new StringBuffer(200);

        int index = 0;

        while (params.containsKey( "filter[filters][" + index + "][field]")) {
            String field = getString( "filter[filters][" + index + "][field]");
            String condition = getString( "filter[filters][" + index + "][operator]");
            String value = getString( "filter[filters][" + index + "][value]");
            index++;

            if (StringUtils.isBlank( value)) {
                continue;
            }
            if ("custom".equals( condition)) {
                whereSql.append( whereSql( field, "filter[filters][" + (index - 1) + "][value]", whereParams));
                continue;
            }

            if ("startswith".equals( condition)) {
                condition = "like ?";
                value = value + "%";
            } else if ("endwith".equals( condition)) {
                condition = "like ?";
                value = "%" + value;
            } else if ("contains".equals( condition)) {
                condition = "like ?";
                value = "%" + value + "%";
            } else if ("doesnotcontain".equals( condition)) {
                condition = "not like ?";
                value = "%" + value + "%";
            } else if ("eq".equals( condition)) {
                condition = "= ?";
            } else if ("neq".equals( condition)) {
                condition = "<> ?";
            } else if ("gte".equals( condition)) {
                condition = ">= ?";
            } else if ("gt".equals( condition)) {
                condition = "> ?";
            } else if ("lte".equals( condition)) {
                condition = "<= ?";
            } else if ("lt".equals( condition)) {
                condition = "> ?";
            } else if ("isnull".equals( condition) || "isempty".equals( condition) || "isnullorempty".equals( condition)) {
                condition = " is null";
                value = "<null>";
            } else if ("isnotnull".equals( condition) || "isnotempty".equals( condition) || "isnotnullorempty".equals( condition)) {
                condition = " is not null";
                value = "<null>";
            } else {
                continue;
            }
            whereSql.append( String.format(" and %s %s", field, condition));
            if (!"<null>".equals( value)) {
                whereParams.add(value);
            }
        }

        return whereSql.toString() + whereSql( whereParams);
    }


    public String whereSql(List whereParams) {
        StringBuffer whereSql = new StringBuffer(200);
        for (String name : params.keySet()) {
            System.out.println("whereSql -> " + whereSql);
            System.out.println("name -> " + name);
            whereSql.append( whereSql( name, name, whereParams));
        }

        return whereSql.toString();
    }


    private String whereSql(String name, String paramName, List whereParams) {
        String[] arr = name.split("-");
        String condition;
        Object value = null;
        if (name.startsWith("str-")) {
            condition = arr[2];
            String str = getString( paramName, true, "");
            if (str.length() > 0) {
                value = str;
            }
        } else if (name.startsWith("date-")) {
            condition = arr[2];
            value = getDate( paramName);
        } else if (name.startsWith("int-")) {
            condition = arr[2];
            value = getInt( paramName);
        } else if (name.startsWith("dou-")) {
            condition = arr[2];
            value = getDouble( paramName);
        } else if (name.startsWith("lon-")) {
            condition = arr[2];
            value = getLong( paramName);
        } else if (name.startsWith("bool-")) {
            condition = arr[2];
            value = getBoolean( paramName);
        } else {
            return "";
        }
        if (value == null) {
            return "";
        }
        if ("eq".equals(condition)) {
            condition = "=";
        } else if ("not_eq".equals( condition)) {
            condition = "<>";
        } else if ("lk".equals(condition)) {
            condition = "like";
            value = "%" + value + "%";
        } else if ("rlk".equals(condition)) {
            condition = "like";
            value = value + "%";
        } else if ("llk".equals(condition)) {
            condition = "like";
            value = "%" + value;
        } else if ("not_lk".equals(condition)) {
            condition = "not like";
            value = "%" + value + "%";
        } else if ("not_rlk".equals(condition)) {
            condition = "not like";
            value = value + "%";
        } else if ("not_llk".equals(condition)) {
            condition = "not like";
            value = "%" + value;
        } else if ("gt".equals(condition)) {
            condition = ">";
        } else if ("gteq".equals(condition)) {
            condition = ">=";
        } else if ("lt".equals(condition)) {
            condition = "<";
        } else if ("lteq".equals(condition)) {
            condition = "<=";
            if (value instanceof Date) {
                Calendar c = Calendar.getInstance();
                c.setTime((Date) value);
                if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) == 0) {
                    c.set(Calendar.HOUR, 0);
                    c.set(Calendar.MINUTE, 0);
                    c.set(Calendar.SECOND, 0);
                    c.set(Calendar.MILLISECOND, 0);
                    c.add(Calendar.DATE, +1);
                    value = c.getTime();
                    condition = "<";
                } else {
                    value = c.getTime();
                }

            }
        } else if ("bitAnd".equals( condition)) {
            condition = "&";
        } else if ("isnull".equals(condition)) {
            if ((Boolean)value) {
                return String.format(" and %s is null", arr[1]);
            }
            return String.format(" and %s is not null", arr[1]);
        } else {
            throw new RuntimeException("condition is error: " + condition);
        }
        System.out.println("where params add -> " + value);
        whereParams.add(value);
        return String.format(" and %s %s ?", arr[1], condition);
    }

    public <T> T setObjectValue(T obj) {
        if (obj == null) {
            return obj;
        }
        for (Method setter : ReflectUtils.getSetMethods( obj.getClass())) {
            try {
                String fieldName = ReflectUtils.getFieldName(setter);
                Class<?> fieldType = setter.getParameterTypes()[0];
                if (fieldType == String.class) {
                    setter.invoke(obj, getString(fieldName));
                } else if (fieldType == Date.class) {
                    setter.invoke(obj, getDate(fieldName));
                } else if (fieldType == int.class || fieldType == Integer.class) {
                    setter.invoke(obj, getInt(fieldName));
                } else if (fieldType == long.class || fieldType == Long.class) {
                    setter.invoke(obj, getLong(fieldName));
                } else if (fieldType == float.class || fieldType == Float.class) {
                    setter.invoke(obj, getDouble(fieldName));
                } else if (fieldType == double.class || fieldType == Double.class) {
                    setter.invoke(obj, getDouble(fieldName));
                } else if (fieldType == boolean.class || fieldType == Boolean.class) {
                    setter.invoke(obj, getBoolean(fieldName));
                }
            } catch (Exception e) {

            }
        }

        return obj;
    }
}