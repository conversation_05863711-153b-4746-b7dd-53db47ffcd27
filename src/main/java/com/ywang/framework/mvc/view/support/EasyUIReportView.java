/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 22:02:21
 * @FilePath: /analysis_engine/src/main/java/com/ywang/framework/mvc/view/support/EasyUIReportView.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.framework.mvc.view.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.module.Service;
import com.ywang.framework.mvc.view.ReportView;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.sql.OrderPage;

import java.io.IOException;
import java.util.*;


public class EasyUIReportView <T extends Service<Map<String, Object>>> implements ReportView<T, Map<String,Object>> {

    protected T service;

    public void setService(T service) {
        this.service = service;
    }


    public Forward list(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            List<?> whereParams = new ArrayList<>();
            OrderPage page = params.getPage();
            List<Map<String, Object>> datagrid = null;
            try {
                datagrid = service.list(params.whereSql(whereParams), page, whereParams.toArray());
            } catch (IOException e) {
                e.printStackTrace();
            }

            return new JsonForward(new DataGrid(datagrid, page.asPage().getTotal()));
        }
        return doGetForward();
    }

    public Forward listAll(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            List<?> whereParams = new ArrayList<>();
            List<Map<String, Object>> list = null;
            try {
                list = service.list(params.whereSql(whereParams), whereParams.toArray());
            } catch (IOException e) {
                e.printStackTrace();
            }
            return new JsonForward(list);
        }
        return doGetForward();
    }

    public Forward export(ViewParams<?> params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String, Object> entity = service.view(params.getId());
            return new JsonForward(entity);
        }
        return doGetForward();
    }

    protected Forward doGetForward() {
        return new DefaultForward(null);
    }
}
