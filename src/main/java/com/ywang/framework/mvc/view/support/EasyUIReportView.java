package com.ywang.framework.mvc.view.support;

import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.module.Service;
import com.ywang.framework.mvc.view.ReportView;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.sql.OrderPage;

import java.util.*;


public class EasyUIReportView <T extends Service<Map<String, Object>>> implements ReportView<T, Map<String,Object>> {

    protected T service;

    public void setService(T service) {
        this.service = service;
    }


    public Forward list(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            OrderPage page = params.getPage();
            List<Map<String, Object>> datagrid  = service.list(params.whereSql(whereParams), page, whereParams.toArray(new Object[0]));

            return new JsonForward(new DataGrid(datagrid, page.asPage().getTotal()));
        }
        return doGetForward();
    }

    public Forward listAll(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            List<Map<String, Object>> list  = service.list(params.whereSql(whereParams), whereParams.toArray(new Object[0]));

            return new JsonForward(list);
        }
        return doGetForward();
    }

    public Forward export(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> entity = service.view(params.getId());
            return new JsonForward(entity);
        }
        return doGetForward();
    }

    protected Forward doGetForward() {
        return new DefaultForward(null);
    }
}
