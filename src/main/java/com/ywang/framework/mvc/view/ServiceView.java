package com.ywang.framework.mvc.view;


import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.module.Service;

public interface ServiceView<T extends Service<K>, K> extends View<T, K> {

    public Forward create(ViewParams params);

    public Forward save(ViewParams params);

    public Forward view(ViewParams params);

    public Forward list(ViewParams params);

    public Forward listAll(ViewParams params);

    public Forward edit(ViewParams params);

    public Forward update(ViewParams params);

    public Forward delete(ViewParams params);
}
