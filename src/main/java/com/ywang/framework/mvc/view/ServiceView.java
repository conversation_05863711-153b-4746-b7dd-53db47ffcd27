package com.ywang.framework.mvc.view;


import java.io.IOException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.module.Service;

public interface ServiceView<T extends Service<K>, K> extends View<T, K> {

    public Forward create(ViewParams<?> params);
    public Forward save(ViewParams<?> params) throws JsonProcessingException;
    public Forward view(ViewParams<?> params) throws JsonProcessingException;
    public Forward list(ViewParams<?> params) throws JsonProcessingException;
    public Forward listAll(ViewParams<?> params) throws <PERSON>sonProcessingException, IOException;
    public Forward edit(ViewParams<?> params);
    public Forward update(ViewParams<?> params) throws JsonProcessingException;
    public Forward delete(ViewParams<?> params) throws JsonProcessingException;
}
