/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-19 22:00:41
 * @FilePath: /analysis_engine/src/main/java/com/ywang/framework/mvc/view/ReportView.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.framework.mvc.view;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.module.Service;


public interface ReportView <T extends Service<K>, K> extends View<T, K> {

    public Forward list(ViewParams<?> params) throws JsonProcessingException;
    public Forward listAll(ViewParams<?> params) throws JsonProcessingException;
    public Forward export(ViewParams<?> params) throws JsonProcessingException;
}
