package com.ywang.framework.mvc.view.support;

import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.module.Service;
import com.ywang.framework.mvc.view.ServiceView;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.sql.OrderPage;
import com.ywang.utils.ConvertUtils;

import java.util.*;


public abstract class PropertyGridView <T extends Service<Map<String, Object>>, K> implements ServiceView {

    protected T service;

    public void setService(T service) {
        this.service = service;
    }

    public Forward create(ViewParams params) {
        return doGetForward();
    }

    public Forward save(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String, Object> entity = params.getEntity(doGetEntityFields());

                Date now = new Date();
                entity.put("create_time", now);
                entity.put("create_username", params.getUserName());
                entity.put("update_time", now);
                entity.put("update_username", params.getUserName());

                doExtraProperties( entity);
                service.save(entity);
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                throw new UnknownException(new JsonForward(ret), e);
            }
            
            return new JsonForward(ret);
        }


        return new DefaultForward(null);
    }

    public Forward list(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            OrderPage page = params.getPage();
            List<Map<String, Object>> datagrid = service.list(params.whereSql(whereParams), page, whereParams.toArray(new Object[0]));

            for (Map<String,Object> record : datagrid) {
                doProcessRecord( record);
            }
            return new JsonForward(new DataGrid(datagrid, page.asPage().getTotal()));
        }
        return doGetForward();
    }

    public Forward listAll(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            List<Map<String, Object>> datagrid = service.list(params.whereSql(whereParams), whereParams.toArray(new Object[0]));

            for (Map<String,Object> record : datagrid) {
                doProcessRecord( record);
            }
            return new JsonForward(new DataGrid( datagrid));
        }
        return doGetForward();
    }

    public Forward view(ViewParams params) {
        if (params.isJson()) {
            List<Map<String, Object>> propertyGrid = doGetPropertyGrid();
            Map<String, Object> entity = null;
            if (params.getId() != null) {
                entity = service.view(params.getId());

                Map<String, Object> row = new HashMap<String, Object>();
                row.put("id", "id");
                row.put("name", "ID");
                row.put("value", params.getId());
                row.put("group", "基本信息");
                propertyGrid.add(0, row);
            }

            for (Map<String, Object> row : propertyGrid) {
                String id = (String) row.get("id");
                if (id == null) {
                    continue;
                }
                String[] arr = id.split("-");
                if (arr.length == 2) {
                    row.put("id", arr[1]);
                    if (entity != null) {
                        Object value = entity.get(arr[1]);
                        if (value instanceof Date) {
                            value = ConvertUtils.date2Str((Date)value, "yyyy-MM-dd");
                        }
                        row.put("value", value);
                    }
                }
            }

            return new JsonForward(new DataGrid(propertyGrid));
        }
        return doGetForward();
    }

    public Forward edit(ViewParams params) {
        return doGetForward();
    }

    public Forward update(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String, Object> entity = params.getEntity(doGetEntityFields());
                entity.put("update_time", new Date());
                entity.put("update_username", params.getUserName());

                doExtraProperties( entity);
                service.update(entity, " and id=? ", params.getId());
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                throw new UnknownException(new JsonForward(ret), e);
            }
            return new JsonForward(ret);
        }
        return doGetForward();
    }

    public Forward delete(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                service.delete(params.getId());
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
                throw new UnknownException(new JsonForward(ret), e);
            }
            return new JsonForward(ret);
        }
        return doGetForward();
    }

    protected String[] doGetEntityFields() {
        List<String> fields = new ArrayList<>();
        for (Map<String, Object> row : doGetPropertyGrid()) {
            if (row.containsKey("id")) {
                fields.add((String) row.get("id"));
            }
        }
        return fields.toArray(new String[0]);
    }

    protected abstract List<Map<String, Object>> doGetPropertyGrid();

    protected Forward doGetForward() {
        return new DefaultForward(null);
    }

    protected void doExtraProperties(Map<String,Object> entity) {

    }

    protected void doProcessRecord(Map<String,Object> record) {
    }

}


