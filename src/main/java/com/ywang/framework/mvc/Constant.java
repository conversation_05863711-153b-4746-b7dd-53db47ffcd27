package com.ywang.framework.mvc;

import java.net.InetAddress;

/**
 * 定义SpringMVC
 * 定义常量
 */
public final class Constant {

    private Constant(){
    }

    public static final String DEFAULT_DB_BEAN_NAME = "db";

    public static final String DEFAULT_ACTION_TYPE = ".htm";
    
    public static final String ACTION_TYPE = "action_type";

    public static final String SESSION_USER_KEY = "__sessionUser";

    public static final String SESSION_MP_USER_KEY = "__sessionMPUser";

    public static final String LOGIN_PATH = "/login";

    public static final String CONTEXT_SESSIONS_KEY = "context.sessions";

    public static final String DEFAULT_INDEX_PATH = "/comm/index.jsp";

    public static final String JSON_PATH = "/comm/json.json.jsp";

    public static final String JSON_SUFFIX = ".json";

    public static final String WEB_INF_PATH;

    public static final String WEB_ROOT_PATH;

    public static final String SERVER_IP;

    public static final String DEFAULT_PRIVILEGE_BEAN_NAME = "rbac.privilege";

    static {
        String path = Constant.class.getResource("").getFile();
        if (path.startsWith( "file:")) {
            path = path.substring( "file:".length());
        }
        WEB_INF_PATH = path.substring(0, path.indexOf("/WEB-INF/") + 9).replace("%20", " ");
        WEB_ROOT_PATH = WEB_INF_PATH.substring(0, WEB_INF_PATH.length() - "/WEB-INF/".length());
        String serverIp = "127.0.0.1";
        try {
            serverIp = InetAddress.getLocalHost().getHostAddress();
        } catch(Exception e) {
        }
        SERVER_IP = serverIp;
    }

}
