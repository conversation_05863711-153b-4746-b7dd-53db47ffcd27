package com.ywang.framework.mvc.controller.support;

import com.ywang.utils.FileUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;


public class DownloadExcel97Forward extends AbstractDownloadForward {

    public DownloadExcel97Forward(String fileName, File file) {
        super(fileName, file);
    }

    public void doDownload(HttpServletResponse response) {
        response.setHeader("Content-type", "application/vnd.ms-excel");

        File _file  = (File) file;
        InputStream is = null;
        OutputStream os = null;

        try {
            is = new FileInputStream(_file);
            os = response.getOutputStream();

            FileUtils.transfer(is, os);
            os.flush();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            FileUtils.close(is);
        }

    }
}
