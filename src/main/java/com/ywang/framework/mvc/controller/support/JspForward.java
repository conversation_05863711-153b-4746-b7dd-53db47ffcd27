package com.ywang.framework.mvc.controller.support;

import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.utils.HttpUtils;
import java.util.Map;


public class JspForward implements Forward {

    private final Map<String, Object> requestAttributes;

    private final Map<String, Object> sessionAttributes;

    private final String path;

    private final String queryString;

    private final boolean isRedirect;

    public JspForward(String path, Map<String, Object> requestAttributes) {
        this(path, null, requestAttributes, null, false);
    }

    public JspForward(String path, String queryString, Map<String, Object> requestAttributes) {
        this(path, queryString, requestAttributes, null, false);
    }

    public JspForward(String path, Map<String, Object> requestAttributes, boolean isRedirect) {
        this(path, null, requestAttributes, null, isRedirect);
    }

    public JspForward(String path, String queryString, Map<String, Object> requestAttributes, boolean isRedirect) {
        this(path, queryString, requestAttributes, null, false);
    }

    public JspForward(String path, Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        this(path, null, requestAttributes, sessionAttributes, false);
    }

    public JspForward(String path, String queryString, Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        this(path, queryString, requestAttributes, sessionAttributes, false);
    }

    public JspForward(String path, Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes, boolean isRedirect) {
        this(path, null, requestAttributes, sessionAttributes, isRedirect);
    }

    public JspForward(String path, String queryString, Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes, boolean isRedirect) {
        this.requestAttributes = requestAttributes;
        this.sessionAttributes = sessionAttributes;
        this.path = path;
        this.isRedirect = isRedirect;
        this.queryString = HttpUtils.encodeQueryString(queryString);
    }

    public Map<String, Object> getAttributesForRequest() {
        return requestAttributes;
    }


    public Map<String, Object> getAttributesForSession() {
        return sessionAttributes;
    }

    public String getPath() {
        return path;
    }

    public String getQueryString() {
        return queryString;
    }

    public boolean isRedirect() {
        return isRedirect;
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }
}
