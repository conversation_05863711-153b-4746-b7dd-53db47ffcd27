package com.ywang.framework.mvc.controller.support;


import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.FileUtils;
import com.ywang.utils.LogUtils;
import com.ywang.utils.StringUtils;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class DefaultDispatcher implements Dispatcher {

    private final HttpServletRequest request;

    private final HttpServletResponse response;

    private final String actionType;


    public DefaultDispatcher(HttpServletRequest request, HttpServletResponse response, String actionType) {
        this.request = request;
        this.response = response;
        this.actionType = actionType;
    }

    private void setAttributes(Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        if (!CollectionUtils.isEmpty(requestAttributes)) {
            for (String key : requestAttributes.keySet()) {
                request.setAttribute(key, requestAttributes.get(key));
            }
        }
        if (!CollectionUtils.isEmpty(sessionAttributes)) {
            HttpSession session = request.getSession();
            for (String key : sessionAttributes.keySet()) {
                session.setAttribute(key, sessionAttributes.get(key));

//                if (Constant.SESSION_USER_KEY.equals( key)) {
//                    ServletContext context = session.getServletContext();
//                    Map<String,HttpSession> sessions = (Map<String,HttpSession>)context.getAttribute(Constant.CONTEXT_SESSIONS_KEY);
//
//                    if (sessions == null) {
//                        sessions = new HashMap<>();
//                        context.setAttribute(Constant.CONTEXT_SESSIONS_KEY, sessions);
//                    }
//
//
//                    Map<String, Object> user = (Map<String, Object>) session.getAttribute(Constant.SESSION_USER_KEY);
//                    String username = (String)user.get("username");
//
//                    if (sessions.containsKey(username)) {
//                        sessions.get(username).invalidate();
//                        LogUtils.FRAMEWORK_LOG.info("用户踢出: " + username);
//                    }
//
//                    sessions.put(username, session);
//                    LogUtils.FRAMEWORK_LOG.info("用户登录: " + username);
//                }

            }
        }
    }

    public void dispatcher(JspForward forward) {
        setAttributes(forward.getAttributesForRequest(), forward.getAttributesForSession());
        if (forward.isRedirect()) {
            redirect(forward.getPath(), forward.getQueryString());
        } else {
            dispatcher(forward.getPath(), forward.getQueryString());
        }
    }

    public void dispatcher(AbstractDownloadForward forward) {
        OutputStream os = null;
        InputStream is = null;
        try {
            String agent = request.getHeader("USER-AGENT");
            String fileName = forward.getFileName();

            if (StringUtils.hasLength(fileName)) {
                agent = agent.toLowerCase();
                if (agent.indexOf("msie") > 0) {
                    fileName = URLEncoder.encode(fileName, "UTF-8");
                } else {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
                }
                response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            }

            forward.doDownload(response);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            FileUtils.close(os);
        }
    }

    public void dispatcher(DefaultForward forward) {
        setAttributes(forward.getAttributesForRequest(), forward.getAttributesForSession());
        String path = request.getServletPath();
        String url = Constant.DEFAULT_INDEX_PATH;
        if (!"/".equals(path)) {
            path = path.substring(0, path.lastIndexOf("."));
            int idx = path.indexOf("_");
            if (idx < 0) {
                url = "/comm" + path + ".jsp";
            } else {
                url = path.substring(0, idx) + path + ".jsp";
            }
        }

        dispatcher(url, null);
    }

    public void dispatcher(MultipleForward forward) {
        setAttributes(forward.getAttributesForRequest(), forward.getAttributesForSession());
        String path = request.getServletPath();
        String url = Constant.DEFAULT_INDEX_PATH;
        if (!"/".equals(path)) {
            path = path.substring(0, path.lastIndexOf("."));
            int idx = path.indexOf("_");
            if (idx < 0) {
                url = "/comm" + path + ".jsp";
            } else {
                url = path.substring(0, idx) + "/" + path.substring(idx + 1, +path.indexOf("_", idx + 1)) + path + ".jsp";
            }
        }

        dispatcher(url, null);
    }

    public void dispatcher(JsonForward forward) {
        request.setAttribute("json", forward.getJson());
        dispatcher("/comm/json.json.jsp", null);
    }

    public void dispatcher(JsonpForward forward) {
        request.setAttribute("json", forward.getJson());
        dispatcher("/comm/jsonp.json.jsp", null);
    }

    public void dispatcher(NoneForward forward) {
        setAttributes(forward.getAttributesForRequest(), forward.getAttributesForSession());
    }

    private void redirect(String path, String queryString) {
        String url = getUrl(path, queryString);
        try {
            response.sendRedirect(url);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private void dispatcher(String path, String queryString) {
        String url = getUrl(path, queryString);
        try {
            request.getRequestDispatcher(url).forward(request, response);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private String getUrl(String path, String queryString) {
        if (path.endsWith(".jsp")) {
            return path;
        }
        if("/authorize".equals(path)){
            return  path  + (StringUtils.hasLength(queryString) ? ("?" + queryString) : "");
        }
        String url = "";

        if (path.startsWith( "/")) {
            path = request.getContextPath() + path;
        }

        if (path.indexOf(".") > 0) {
            url = path  + (StringUtils.hasLength(queryString) ? ("?" + queryString) : "");
        } else if (!path.contains(  "?") ) {
            url = path  + actionType + (StringUtils.hasLength(queryString) ? ("?" + queryString) : "");
        } else {
            url = path;
        }


        return url;
    }
}
