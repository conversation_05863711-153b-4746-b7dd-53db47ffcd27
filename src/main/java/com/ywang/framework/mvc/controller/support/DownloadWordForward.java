package com.ywang.framework.mvc.controller.support;

import com.ywang.utils.FileUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;


public class DownloadWordForward extends AbstractDownloadForward {

    public DownloadWordForward(String fileName, byte[] file) {
        super(fileName, file);
    }

    public void doDownload(HttpServletResponse response) {
        response.setHeader("Content-type", "application/msword");

        InputStream is = null;
        OutputStream os = null;

        try {
            is = new ByteArrayInputStream((byte[]) file);
            os = response.getOutputStream();

            FileUtils.transfer(is, os);
            os.flush();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            FileUtils.close(os);
            FileUtils.close(is);
        }

    }
}