package com.ywang.framework.mvc.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.support.AopUtils;

import com.ywang.exception.ServiceRuntimeException;
import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.Context;
import com.ywang.framework.mvc.UploadFile;
import com.ywang.http.SessionUser;
import com.ywang.reflect.MethodParam;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.ConvertUtils;
import com.ywang.utils.HttpUtils;
import com.ywang.utils.StringUtils;


public class ServiceControllerFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(ServiceControllerFilter.class);

    @Override
    public void init(FilterConfig config) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        String path = req.getServletPath();
        if (path.startsWith("/")) {
            path = path.substring(1);
        }

        // 处理请求URL格式: service-beanId_method.json
        if (!(path.startsWith( "service-") && path.endsWith( ".json"))) {
            chain.doFilter(request, response);
            return;
        }

        //3. 业务处理
        try {
            Map<String,List<UploadFile>> uploadFileMap = new HashMap<>();
            path = path.substring( 8, path.length() - 5);
            int indexOf = path.indexOf( "_");
            String beanId = path.substring( 0, indexOf);
            String methodName = path.substring( indexOf + 1);

            if (ServletFileUpload.isMultipartContent(req)) {
                DiskFileItemFactory factory = new DiskFileItemFactory();
                ServletFileUpload fileUpload = new ServletFileUpload( factory);
                List<FileItem> items = fileUpload.parseRequest( req);

                if (!CollectionUtils.isEmpty( items)) {
                    for (FileItem item : items) {
                        if (!item.isFormField()) {
                            List<UploadFile> uploadFiles = uploadFileMap.get( item.getFieldName());

                            if (uploadFiles == null) {
                                uploadFiles = new ArrayList<>();
                                uploadFileMap.put( item.getFieldName(), uploadFiles);
                            }
                            UploadFile uploadFile = new UploadFile( item.getName(), item.get());
                            uploadFiles.add( uploadFile);
                        }
                    }
                }
            }

            Object bean = Context.getApplicationContext().getBean( beanId);
            Class<?> beanClass = bean.getClass();
            if (AopUtils.isCglibProxy( bean)) {
                Field field = beanClass.getDeclaredField("CGLIB$CALLBACK_0");
                field.setAccessible(true);
                Object dynamicAdvisedInterceptor = field.get( bean);

                Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
                advised.setAccessible(true);
                Object tmpBean = ((AdvisedSupport)advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();
                if (tmpBean != null) {
                    beanClass = tmpBean.getClass();
                } else {
                    log.error("ServiceControllerFilter bean is null");
                }
            }

            for (Method method : beanClass.getMethods()) {
                if (method.getName().equals( methodName)) {
                    Object[] params = new Object[method.getParameterCount()];

                    if (params.length == 1) {
                        Parameter methodParam = method.getParameters()[0];
                        params[0] = getMethodParameter( req, res, methodParam, uploadFileMap);
                    } else {
                        for (int i = 0; i < params.length; i++) {
                            Parameter methodParam = method.getParameters()[i];
                            params[i] = getMethodParameter( req, res, methodParam, uploadFileMap);
                        }
                    }

                    System.out.println( methodName + ": " + Arrays.toString( params));
                    Object ret = method.invoke( bean, params);

                    if (method.getReturnType() == String.class) {
                        String s = (String)ret;

                        Map<String,Object> map = new HashMap<>();
                        map.put( methodName, s);

                        ret = map;
                    }

                    if (method.getReturnType() != void.class) {
                        res.addHeader("Access-Control-Allow-Origin", "*");
                        request.setAttribute("json", StringUtils.toJson( ret));
                        request.getRequestDispatcher( "/comm/json.json.jsp").forward(request, response);
                    }
                }
            }

        } catch (InvocationTargetException e) {
            log.error("error", e.getTargetException());
            if (e.getTargetException() instanceof ServiceRuntimeException) {
                res.addHeader("Access-Control-Allow-Origin", "*");
                request.setAttribute("json", StringUtils.toJson( ((ServiceRuntimeException)e.getTargetException()).getErrorInfo()));
                request.getRequestDispatcher("/comm/json.json.jsp").forward(request, response);
            }
        } catch (Exception e) {
            log.error("error", e);
            chain.doFilter(request, response);
        }

    }


    private Object getMethodParameter(HttpServletRequest request, HttpServletResponse response, Parameter methodParam, Map<String,List<UploadFile>> uploadFileMap) {
        MethodParam param = methodParam.getAnnotation( MethodParam.class);
        Class<?> paramType = methodParam.getType();
        if (param != null) {
            if ( "SESSION-USERNAME".equals( param.name())) {
                return request.getSession().getAttribute( Constant.SESSION_USER_KEY);
            }
            if ( "REQUEST-REMOTE_HOST".equals( param.name())) {
                return request.getRemoteHost();
            }
            if ( "REQUEST-USER_AGENT".equals( param.name())) {
                return request.getHeader("user-agent");
            }
            if ( "REQUEST-IP".equals( param.name())) {
                return HttpUtils.getIp( request);
            }
            if ( "RESTFULAPI-POST".equals( param.name())) {
                try {
                    InputStream is = request.getInputStream();
                    StringBuilder json = new StringBuilder(500);
                    BufferedReader bufferedReader = new BufferedReader( new InputStreamReader(is, "utf-8"));
                    String line;
                    while ((line = bufferedReader.readLine()) != null) {
                        json.append( line);
                    }

                    return json.toString();
                } catch (IOException e) {
                    throw new RuntimeException( e);
                }
            }
            if (paramType == UploadFile.class) {
                List<UploadFile> uploadFiles = uploadFileMap.get( param.name());

                if (CollectionUtils.isEmpty( uploadFiles)) {
                    return null;
                }
                return uploadFiles.get( 0);
            }
            String paramValue = request.getParameter( param.name());
            System.out.println( param.name() + ":" + paramValue);
            if (paramType == String.class) {
                return paramValue;
            }
            if (paramType == long.class || paramType == Long.class) {
                return ConvertUtils.lon( paramValue);
            }
            if (paramType == int.class || paramType == Integer.class) {
                return ConvertUtils.integer( paramValue);
            }
            if (paramType == float.class || paramType == Float.class) {
                Double dou =  ConvertUtils.dou( paramValue);
                return (dou == null ? null : dou.floatValue());
            }
            if (paramType == double.class || paramType == Double.class) {
                return ConvertUtils.dou( paramValue);
            }
            if (paramType == boolean.class || paramType == Boolean.class) {
                return ConvertUtils.bool( paramValue, true);
            }
            if (paramType == Date.class) {
                return ConvertUtils.str2Date( paramValue);
            }
            if (paramType == String[].class) {
                return request.getParameterValues( param.name());
            }
            return paramValue;

        } else if (paramType == HttpServletResponse.class) {
            return response;
        } else if (paramType == SessionUser.class) {
            return request.getSession().getAttribute( Constant.SESSION_USER_KEY);
        }
        return null;
    }

    @Override
    public void destroy() {
    }
}
