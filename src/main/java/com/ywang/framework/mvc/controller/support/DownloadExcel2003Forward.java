package com.ywang.framework.mvc.controller.support;

import com.ywang.utils.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

public class DownloadExcel2003Forward extends AbstractDownloadForward {

    public DownloadExcel2003Forward(String fileName, HSSFWorkbook file) {
        super(fileName, file);
    }

    public void doDownload(HttpServletResponse response) {
        response.setHeader("Content-type", "application/vnd.ms-excel");


        HSSFWorkbook workbook = (HSSFWorkbook) file;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            workbook.write(os);
            os.flush();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
        }
    }
}