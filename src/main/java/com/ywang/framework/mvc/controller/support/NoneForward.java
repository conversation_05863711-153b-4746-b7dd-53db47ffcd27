package com.ywang.framework.mvc.controller.support;

import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.Dispatcher;

import java.util.Map;

public class NoneForward implements Forward {

    private final JspForward jspForward;

    public NoneForward() {
        this(null, null);
    }

    public NoneForward(Map<String, Object> requestAttributes) {
        this(requestAttributes, null);
    }

    public NoneForward(Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        jspForward = new JspForward(null, null, requestAttributes, sessionAttributes, false);
    }

    public Map<String, Object> getAttributesForRequest() {
        return jspForward.getAttributesForRequest();
    }


    public Map<String, Object> getAttributesForSession() {
        return jspForward.getAttributesForSession();
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }
}
