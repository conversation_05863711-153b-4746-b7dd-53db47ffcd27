package com.ywang.framework.mvc.controller.support;


import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;
import java.util.Map;

public class DefaultForward implements Forward {

    private final JspForward jspForward;

    public DefaultForward(Map<String, Object> requestAttributes) {
        this(requestAttributes, null);
    }

    public DefaultForward(Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        jspForward = new JspForward(null, null, requestAttributes, sessionAttributes, false);
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }

    public Map<String, Object> getAttributesForRequest() {
        return jspForward.getAttributesForRequest();
    }


    public Map<String, Object> getAttributesForSession() {
        return jspForward.getAttributesForSession();
    }
}
