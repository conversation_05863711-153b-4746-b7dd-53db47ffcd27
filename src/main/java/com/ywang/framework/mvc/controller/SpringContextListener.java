package com.ywang.framework.mvc.controller;

// import com.ywang.framework.mvc.Constant;
// import com.ywang.module.sys.service.support.DbEnumManager;
// import com.ywang.module.sys.service.support.DbSysConfigManager;
// import com.ywang.sql.support.Db;
// import com.ywang.utils.CollectionUtils;
import com.ywang.utils.LogUtils;
// import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoaderListener;
// import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContextEvent;

/**
 * SpringMVC -- Spring Context
 * 加载SpringContext
 */
public class SpringContextListener extends ContextLoaderListener {

    @Override
    public void contextInitialized(ServletContextEvent e) {
        try {
            LogUtils.FRAMEWORK_LOG.info("Spring Context initializing in SpringContextListener...");
            System.out.print("Spring Context initializing in SpringContextListener...");
            System.out.print("ServletContextEvent: " + e);
            super.contextInitialized(e);
            // ApplicationContext springContext = WebApplicationContextUtils.getRequiredWebApplicationContext(e.getServletContext());
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println( "%%%%%%%%%%%%%%%%Error");
        }
    }
}
