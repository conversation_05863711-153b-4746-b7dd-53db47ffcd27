package com.ywang.framework.mvc.controller.support;



import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.utils.StringUtils;


public class JsonForward implements Forward {

    private final String json;

    public JsonForward(Object json) {
        if (json instanceof String) {
            this.json = (String)json;
        } else {
            this.json = StringUtils.toJson(json);
        }
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }

    public String getJson() {
        return json;
    }
}
