package com.ywang.framework.mvc.controller.support;


import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;
import java.util.Map;

public class MultipleForward implements Forward {

    private final JspForward jspForward;

    public MultipleForward(Map<String, Object> requestAttributes) {
        this(requestAttributes, null);
    }

    public MultipleForward(Map<String, Object> requestAttributes, Map<String, Object> sessionAttributes) {
        jspForward = new JspForward(null, null, requestAttributes, sessionAttributes, false);
    }

    public Map<String, Object> getAttributesForRequest() {
        return jspForward.getAttributesForRequest();
    }

    public Map<String, Object> getAttributesForSession() {
        return jspForward.getAttributesForSession();
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }
}


