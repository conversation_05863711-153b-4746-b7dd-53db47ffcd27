package com.ywang.framework.mvc.controller;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.Context;
import com.ywang.http.SessionUser;
import com.ywang.module.rbac.service.support.SimplePrivilegeService;
import com.ywang.utils.CollectionUtils;


public class SpringPrivilegeFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(SpringPrivilegeFilter.class);
    private static SimplePrivilegeService PRIVILEGE_SERVICE;


    @Override
    public void init(FilterConfig config) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {

        HttpServletRequest req = (HttpServletRequest) request;
        String actionType = (String)req.getServletContext().getAttribute( Constant.ACTION_TYPE);
        String path = req.getServletPath();
        /**
         * 1. 判断动态请求连接
         * 1.1 不以actionType结尾的请求
         * 1.2 不以.json结尾的请求
         * 1.3 不以"/"请求
         *
         * 以上三种请求连接均属于静态连接
         */
        if (!path.endsWith(actionType) && !path.endsWith(Constant.JSON_SUFFIX) && !"/".equals(path) && !path.endsWith(".api")) {
            chain.doFilter(request, response);
            return;
        }

        SessionUser user = (SessionUser)req.getSession().getAttribute(Constant.SESSION_USER_KEY);


        if (PRIVILEGE_SERVICE == null) {
            String[] beanNames = Context.getApplicationContext().getBeanNamesForType(SimplePrivilegeService.class);
            if (CollectionUtils.isEmpty(beanNames)) {
                chain.doFilter(request, response);
                return;
            }
            PRIVILEGE_SERVICE = (SimplePrivilegeService) Context.getApplicationContext().getBean(beanNames[0]);
        }


        String url = "";
        if (path.startsWith("/")) {
            url = path.substring(1);
        }
        if (url.endsWith( actionType)) {
            url = url.substring(0, url.length() - actionType.length());
        }
        if (url.endsWith( Constant.JSON_SUFFIX)) {
            url = url.substring(0, url.length() - Constant.JSON_SUFFIX.length());
        }
        boolean hasPrivilege = PRIVILEGE_SERVICE.hasPrivilege( url, user);

        log.info("权限认证: " + path + " / " + user + ": " + hasPrivilege);

        if (hasPrivilege) {
            chain.doFilter(request, response);
            return;
        }

        if (path.endsWith(Constant.JSON_SUFFIX)) {
            request.setAttribute("json", "{message:'权限不足'}");
            request.getRequestDispatcher( "/comm/json.json.jsp").forward( request, response);
        } else {
//            request.setAttribute("json", "{message:'权限不足'}");
//            ((HttpServletResponse) response).sendRedirect( "/index.htm");
            request.getRequestDispatcher( "login.jsp").forward( request, response);
        }

    }

    @Override
    public void destroy() {

    }
}
