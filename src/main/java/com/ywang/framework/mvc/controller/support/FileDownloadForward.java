package com.ywang.framework.mvc.controller.support;

import com.ywang.utils.FileUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;


public class FileDownloadForward extends AbstractDownloadForward {

    public FileDownloadForward(String fileName, File file) {
        super(fileName, file);
    }

    public void doDownload(HttpServletResponse response) {
        response.setHeader("Content-type", "application/octet-stream");

        InputStream is = null;
        OutputStream os = null;

        try {
            is = new FileInputStream( (File)file);
            os = response.getOutputStream();

            FileUtils.transfer( is, os);
            os.flush();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            FileUtils.close( is);
        }

    }
}
