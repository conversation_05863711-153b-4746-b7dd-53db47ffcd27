package com.ywang.framework.mvc.controller.support;

import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.utils.StringUtils;

/**
 * Created by hen<PERSON><PERSON> on 18/8/8.
 */
public class JsonpForward implements Forward {

    private final String json;

    public JsonpForward(Object json) {
        this.json = StringUtils.toJson(json);
    }

    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }

    public String getJson() {
        return json;
    }

}
