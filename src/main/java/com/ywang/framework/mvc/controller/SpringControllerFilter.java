package com.ywang.framework.mvc.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import com.alibaba.fastjson.JSON;
import com.ywang.framework.mvc.Constant;
import com.ywang.framework.mvc.UploadFile;
import com.ywang.framework.mvc.controller.support.DefaultDispatcher;
import com.ywang.framework.mvc.exception.UnknownException;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.http.support.MultipartHttpServletRequest;


/**
 * SpringMVC -- Controller
 */
@Component
public final class SpringControllerFilter extends GenericFilterBean {

    private static final String ACTION_TYPE_PARAM_NAME = "action_type";
    private String actionType = Constant.DEFAULT_ACTION_TYPE;
    private static final Logger log = LoggerFactory.getLogger(SpringControllerFilter.class);

    @Resource private ApplicationContext context;
    public void initFilterBean(FilterConfig config) throws ServletException {
        //1. 动态请求后缀名
        String actionTypeLocal = config.getInitParameter(ACTION_TYPE_PARAM_NAME);
        if (StringUtils.hasLength(actionTypeLocal)) {
            this.actionType = actionTypeLocal;
        }
        System.out.println("#######  SpringControllerFilter initFilterBean, actionType: " + this.actionType);
        config.getServletContext().setAttribute(Constant.ACTION_TYPE, this.actionType);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {

        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        String path = req.getServletPath();
        if (!path.endsWith(actionType) && !path.endsWith(Constant.JSON_SUFFIX)
            && !"/".equals(path) && !path.endsWith(".api")) {
            chain.doFilter(request, response);
            return;
        }
        if (path.startsWith( "/service-")) {
            chain.doFilter(request, response);
            return;
        }

        // 附件处理
        Map<String, List<Object>> params = new HashMap<>();
        if (ServletFileUpload.isMultipartContent(req)) {
            try {
                FileItemFactory factory = new DiskFileItemFactory();
                ServletFileUpload upload = new ServletFileUpload(factory);
                Iterator<FileItem> it = upload.parseRequest(req).iterator();
                List<String> paramNames = new ArrayList<>();
                while (it.hasNext()) {
                    FileItem item = it.next();
                    List<Object> paramValues = params.get(item.getFieldName());
                    if (CollectionUtils.isEmpty(paramValues)) {
                        paramValues = new ArrayList<>();
                        params.put(item.getFieldName(), paramValues);
                    }
                    if (!item.isFormField()) {
                        if (StringUtils.hasLength(item.getName())) {
                            paramValues.add(new UploadFile(item.getName(), item.get()));
                        }
                    } else {
                        paramNames.add(item.getFieldName());
                        paramValues.add(item.getString(req.getCharacterEncoding()));
                    }
                }
                Map<String, String[]> _params = new HashMap<>();
                for (String paramName : paramNames) {
                    _params.put(paramName, (String[]) params.get(paramName).toArray());
                }
                req = new MultipartHttpServletRequest(_params, req);
            } catch (UnsupportedEncodingException | FileUploadException e) {
                log.error(e.getMessage());
            }
        } else  if (path.endsWith(".api")) {
            InputStream is = request.getInputStream();
            StringBuilder json = new StringBuilder( 500);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is, "utf-8"));
            String text;
            while ((text = bufferedReader.readLine()) != null) {
                json.append( text);
            }

            Map<String, Object> _params = JSON.parseObject( json.toString());

            if (_params == null) {
                _params = new HashedMap<>();
            }

            for (String name : _params.keySet()) {
                List<Object> paramValues = new ArrayList<>();
                Object value = _params.get(name);

                if (value instanceof Collection) {
                    paramValues.addAll((Collection<?>)value);
                } else {
                    paramValues.add( value);
                }

                params.put((String) name, paramValues);
            }

        } else {
            Map<String, String[]> _params = req.getParameterMap();
            for (String name : _params.keySet()) {
                List<Object> paramValues = new ArrayList<>();
                Collections.addAll(paramValues, (String[]) _params.get(name));
                params.put((String) name, paramValues);
            }
        }


        HttpSession httpSession = req.getSession();
        Map<String, Object> session = new HashMap<>();
        for (Object attrName : Collections.list(httpSession.getAttributeNames())) {
            session.put((String) attrName, httpSession.getAttribute((String) attrName));
        }
        ViewParams<Object> viewParams = new ViewParams<>(params, session, req);

        String url = "";
        if (path.startsWith("/")) {
            url = path.substring(1);
        }
        if (url.endsWith( actionType)) {
            url = url.substring(0, url.length() - actionType.length());
        }

        req.setAttribute("__menu_id", url);
        //3. 业务处理
        try {
            String strParams = JSON.toJSONString( params);

            if (strParams.length() > 100) {
                strParams = strParams.substring(0, 100);
            }
            log.info("Request URL[" + req.getAttribute( "__agent_id") + "]: " +
                req.getServletPath() + ", params: " +
                strParams + ", user: " +
                viewParams.getUserName());

            //3.1 解析URL
            String[] msdr = parseUrl(path);

            //3.2 获取View对象
            String viewId = (msdr[0] + "." + msdr[1]);
            if (".".equals(viewId)) {
                viewId = "index";
            }
            //3.3 当Bean不存在时,改为静态请求
            if (!context.containsBean("view-" + viewId)) {
                chain.doFilter(request, response);
            }
            Object view = context.getBean("view-" + viewId);
            Forward forward = null;
            Method method;
            method = view.getClass().getMethod(msdr[2], new Class[]{ViewParams.class});
            try {
                forward = (Forward) method.invoke(view, new Object[]{viewParams});
            } catch (InvocationTargetException e) {
                if (e.getTargetException() instanceof UnknownException) {
                    forward = ((UnknownException) e.getTargetException()).getForward();
                } else {
                    throw e;
                }
            }

            //3.4 JSON跨域请求处理
            if (forward != null) {
                res.addHeader("Access-Control-Allow-Origin", "*");
                forward.forward(new DefaultDispatcher(req, res, actionType));
            }

        } catch (IOException | IllegalAccessException | IllegalArgumentException | NoSuchMethodException
                | SecurityException | InvocationTargetException | ServletException | BeansException e) {
            log.error(e.getMessage());
            chain.doFilter(request, response);
        }
    }

    /**
     * URL协议(msdr)：module service do result
     */
    private String[] parseUrl(String path) {
        String msdr;
        String defaultPath = "index";
        if (!StringUtils.hasLength(path)) {
            throw new IllegalArgumentException(String.format("web path[%s] must obey msmr1!", path));
        }
        // 默认路径
        path = ("/".equals(path) ? "/" + defaultPath + actionType : path);
        String[] paths = path.split("/");
        if (paths.length > 2) {
            throw new IllegalArgumentException(String.format("web path[%s] must obey msmr2!", path));
        }
        msdr = paths[1];

        String _path = msdr.substring(0, msdr.lastIndexOf("."));

        String[] ret = {"", "", defaultPath, ""};
        String[] arr = _path.split("_");
        switch (arr.length) {
            case 1:
                ret[2] = arr[0];
                break;
            case 2:
                ret[1] = arr[0];
                ret[2] = arr[1];
                break;
            case 4:
                ret[3] = arr[3];
            case 3:
                ret[0] = arr[0];
                ret[1] = arr[1];
                ret[2] = arr[2];
                break;
            default:
                throw new IllegalArgumentException(String.format("web path[%s] must obey msmr3!", path));
        }
        return ret;
    }

    @Override
    public void destroy() {
    }


}
