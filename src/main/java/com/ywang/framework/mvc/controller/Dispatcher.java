/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-08 20:06:41
 * @FilePath: /analysis_engine/src/main/java/com/ywang/framework/mvc/controller/Dispatcher.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.framework.mvc.controller;




import com.ywang.framework.mvc.controller.support.*;


public interface Dispatcher {
    public void dispatcher(JspForward forward);

    public void dispatcher(DefaultForward forward);

    public void dispatcher(MultipleForward forward);

    public void dispatcher(JsonForward forward);

    public void dispatcher(NoneForward forward);

    public void dispatcher(AbstractDownloadForward forward);

    public void dispatcher(JsonpForward forward);
}
