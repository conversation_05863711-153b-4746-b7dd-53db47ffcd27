package com.ywang.framework.mvc.controller.support;


import com.ywang.framework.mvc.controller.Dispatcher;
import com.ywang.framework.mvc.controller.Forward;

import javax.servlet.http.HttpServletResponse;


public abstract class AbstractDownloadForward implements Forward  {

    protected final String fileName;

    protected final Object file;
    
    
    public AbstractDownloadForward(String fileName, Object file) {
        this.fileName = fileName;
        this.file = file;
    }
    
    public void forward(Dispatcher dispatcher) {
        dispatcher.dispatcher(this);
    }

    public String getFileName() {
        return fileName;
    }

    public Object getFile() {
        return file;
    }

    public abstract void doDownload(HttpServletResponse response);
}
