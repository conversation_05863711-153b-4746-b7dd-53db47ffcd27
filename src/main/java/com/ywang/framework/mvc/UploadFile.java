package com.ywang.framework.mvc;

import com.ywang.utils.StringUtils;

import java.util.Date;

public final class UploadFile {
    
    private final String fileName;
    
    private final String fileType;
    
    private final byte[] content;

    private final long size;

    private final String md5;
    
    private String memo;
    
    private String createUserName;
    
    private Date createTime;

    public UploadFile(String fileName, byte[] content) {
        this(fileName.substring(0, fileName.lastIndexOf(".")), fileName.substring(fileName.lastIndexOf(".") + 1), content);
    }

    public UploadFile(String fileName, String fileType, byte[] content) {
        this.fileName = fileName;
        this.fileType = fileType.toLowerCase();
        this.content = content;
        this.size = content.length;
        this.md5 = StringUtils.md5(content);
    }


    public String getFileName() {
        return fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public byte[] getContent() {
        return content;
    }

    public String getMd5() {
        return md5;
    }

    public long getSize() {
        return size;
    }

    public String getMemo() {
        return memo;
    }

    public String getFullName() {
        return fileName + "." + fileType;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
