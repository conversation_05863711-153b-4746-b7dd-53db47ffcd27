package com.ywang.module.comm.service.support;


import com.ywang.framework.mvc.UploadFile;
import com.ywang.framework.mvc.module.support.AbstractService;
import com.ywang.module.comm.service.UploadFileService;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.Callback;
import com.ywang.sql.OrderPage;
import com.ywang.sql.Page;
import com.ywang.sql.PageCallback;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.LongCallback;
import com.ywang.utils.DbUtils;
import com.ywang.utils.FileUtils;
import com.ywang.utils.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DefaultUploadFileService extends AbstractService<UploadFile> implements UploadFileService {

    public final static String SYSTEM_STORE = "system";

    public final static String DATABASE_STORE = "database";
    
    private Db db;

    public void setDb(Db db) {
        this.db = db;
    }

    public long save(UploadFile uploadFile) {
        List params = new ArrayList();

        // 检查系统是否已经保存上传文件，当系统已保存直接返回文件id
        Long id = db.selectUnique("select id from " + doGetTableName() + " where md5=? ", new LongCallback(), uploadFile.getMd5());
        if (id != null) {
            return id;
        }
        SysConfigManager config = SysConfigManager.getInstance();
        Map<String,Object> uploadFileMap = new HashMap<String, Object>();
        if (SYSTEM_STORE.equals(config.getValue("resource.store_type"))) {
            String path = config.getValue("resource.win.store_path") + uploadFile.getMd5();
            try {
                FileUtils.transfer(new ByteArrayInputStream(uploadFile.getContent()), new FileOutputStream(path));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            uploadFileMap.put("path", path);
        }
        uploadFileMap.put("file_name", uploadFile.getFileName());
        uploadFileMap.put("file_type", uploadFile.getFileType());
        uploadFileMap.put("file_size", uploadFile.getSize());
        uploadFileMap.put("md5", uploadFile.getMd5());
        uploadFileMap.put("memo", uploadFile.getMemo());
        uploadFileMap.put("create_time", uploadFile.getCreateTime());
        uploadFileMap.put("create_username", uploadFile.getCreateUserName());
        uploadFileMap.put("store_type", config.getValue("resource.store_type"));


        String sql = DbUtils.insertSql(doGetTableName(), uploadFileMap, params);
        return db.insert(sql, params.toArray(new Object[0]));
    }

    public List<UploadFile> list(String whereSql, Object... params) {
        return db.select(doGetQuerySql() + whereSql + doGetOrderSql(), new UploadFileCallback(), params);
    }

    public List<UploadFile> list(String whereSql, OrderPage page, Object... params) {
        String orderBy = doGetOrderSql();
        if (StringUtils.hasLength(page.getOrderBy())) {
            orderBy = page.getOrderBy();
        }

        return db.select(doGetQuerySql() + whereSql + orderBy, new UploadFilePageCallback(), page.asPage(), params);
    }

    public int delete(String whereSql, Object... params) {
        return db.delete("delete from " + doGetTableName() + " where 1=1 " + whereSql, params);
    }

    public UploadFile view(long id) {
        return db.selectUnique(doGetQuerySql() + " and id=? ", new UploadFileCallback(), id);
    }

    public int update(UploadFile entity, String whereSql, Object... params) {
        throw new UnsupportedOperationException("UploadFile不支持更新操作");
    }

    public int delete(long id) {
        return db.delete("delete from " + doGetTableName() + " where id=? ", id);
    }

    protected String doGetTableName() {
        return "comm_uploadfile";
    }

    protected String doGetQuerySql() {
        return null;
    }

    protected String doGetOrderSql() {
        return "";
    }
}

class UploadFileCallback implements Callback<UploadFile> {

    public UploadFile with(int row, ResultSet rs) throws SQLException {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }
}

class UploadFilePageCallback implements PageCallback<UploadFile> {

    private final Callback<UploadFile> callback;

    private final Page page;

    public UploadFilePageCallback(Callback<UploadFile> callback, Page page) {
        this.callback = callback;
        this.page = page;
    }

    public UploadFilePageCallback(Page page) {
        this( new UploadFileCallback(), page);
    }

    public UploadFilePageCallback() {
       this( null);
    }

    public UploadFile with(int row, ResultSet rs) throws SQLException {
        return callback.with(row, rs);
    }

    public Page getPage() {
        return page;
    }
}
