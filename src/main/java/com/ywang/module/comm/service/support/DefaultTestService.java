package com.ywang.module.comm.service.support;


import com.ywang.framework.mvc.module.support.DefaultService;
import com.ywang.module.comm.service.TestService;

import java.util.*;

public class DefaultTestService extends DefaultService implements TestService {

    public void save(boolean isTrue) {
        if (isTrue) {
            db.insert("insert into test(s) values(?)", new Date().toLocaleString());
        } else {
            db.insert("insert into test2(s) values(?)", new Date().toLocaleString());
        }
    }


    public static void main(String...args) {
        SortedMap<Integer,Integer> sortedMap = new TreeMap<>();

        for (int i = 0; i < 10; i++) {
            sortedMap.put( i, i);
        }
        System.out.println( sortedMap);

        for (int i = 5; i < sortedMap.size(); i++) {
            sortedMap.remove( sortedMap.firstKey());
        }
        System.out.println( sortedMap);
    }
}
