package com.ywang.module.database.view;


import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.sql.OrderPage;
import com.ywang.sql.support.Db;
import com.ywang.utils.StringUtils;

import java.util.*;

public class KendouiView {


    private Db db;

    public void setDb(Db db) {
        this.db = db;
    }

    public static final String DEFAULT_IDENTITY_KEY = "id";

    public static final String DEFAULT_TREE_PARENT_ID_KEY = "parent_id";

    public static final String DEFAULT_TREE_ROOT_VALUE = "0";

    public static final String DEFAULT_TREE_TEXT_KEY = "text";

    public static final String DEFAULT_TREE_ITEMS_KEY = "items";

    public Forward grid(ViewParams params) {
        String fields = params.getString("fields");
        String tableName = params.getString("table_name");
        String orderBy = params.getString("order_by");
        String jsonField = params.getString("json_field");


        if (!StringUtils.hasLength(tableName)) {
            return new JsonForward( null);
        }

        if (!StringUtils.hasLength(fields)) {
            fields = "*";
        }
        List sqlParams = new ArrayList<>();
        String sql = "select " + fields + " from " + tableName + " where 1=1 " + params.whereSql( sqlParams) + params.whereSqlForKendoUI( sqlParams);

        if (StringUtils.hasLength(orderBy)) {
            sql += " order by " + orderBy;
        }

        OrderPage page = params.getPage();
        List<Map<String,Object>> records = db.select(sql, page, sqlParams.toArray(new Object[0]));


        for (Map<String,Object> record : records) {
            String json = (String)record.get( jsonField);
            if (StringUtils.hasLength( json)) {
                Map<String,Object> jsonMap = StringUtils.json2Map( json);
                for (String key : jsonMap.keySet()) {
                    record.put( key, jsonMap.get( key));
                }
            }
        }

        Map<String,Object> grid = new HashMap<>();
        grid.put("total", page.asPage().getTotal());
        grid.put("rows", records);

        return new JsonForward( grid);
    }

    public Forward treeList(ViewParams params) {
        String fields = params.getString("fields");
        String tableName = params.getString("table_name");
        String orderBy = params.getString("order_by");
        String jsonField = params.getString("json_field");
        String parentField = params.getString( "parent_field", true, DEFAULT_TREE_PARENT_ID_KEY);



        if (!StringUtils.hasLength(tableName)) {
            return new JsonForward( null);
        }

        if (!StringUtils.hasLength(fields)) {
            fields = "*";
        }


        List sqlParams = new ArrayList<>();
        String sql = "select " + fields + " from " + tableName + " where 1=1 " + params.whereSql( sqlParams);

        if (StringUtils.hasLength(orderBy)) {
            sql += " order by " + orderBy;
        }

        List<Map<String,Object>> records = db.select(sql, sqlParams.toArray(new Object[0]));


        for (Map<String,Object> record : records) {
            String json = (String)record.get( jsonField);
            if (StringUtils.hasLength( json)) {
                Map<String,Object> jsonMap = StringUtils.json2Map( json);
                for (String key : jsonMap.keySet()) {
                    record.put( key, jsonMap.get( key));
                }
            }
            record.put( "parentId", record.remove( parentField));
            record.put( "_id", record.get( "id"));
        }

        return new JsonForward( records);
    }

    public Forward treeView(ViewParams params) {
        String tableName = params.getString("table_name");
        String parentField = params.getString( "parent_field", true, "parent_id");

        String textField = params.getString("text_field", true, DEFAULT_TREE_TEXT_KEY);
        String jsonField = params.getString("json_field");

        String orderBy = params.getString("order_by");


        if (!StringUtils.hasLength(tableName)) {
            return new JsonForward( null);
        }

        String fields = DEFAULT_IDENTITY_KEY + "," + textField + " as " + DEFAULT_TREE_TEXT_KEY + "," + parentField + " as " + DEFAULT_TREE_PARENT_ID_KEY;
        List sqlParams = new ArrayList<>();
        String sql = "select " + fields + " from " + tableName + " where 1=1 " + params.whereSql( sqlParams);

        sql += " order by " + DEFAULT_TREE_PARENT_ID_KEY;
        if (StringUtils.hasLength(orderBy)) {
            sql += "," + orderBy;
        }

        List<Map<String,Object>> records = db.select(sql, sqlParams.toArray(new Object[0]));

        Map<Object, Map<String,Object>> treeView = new LinkedHashMap<>();
        List<Map<String,Object>> ret = new ArrayList<>();
        for (Map<String,Object> record : records) {
            String json = (String)record.get( jsonField);
            if (StringUtils.hasLength( json)) {
                Map<String,Object> jsonMap = StringUtils.json2Map( json);
                for (String key : jsonMap.keySet()) {
                    record.put( key, jsonMap.get( key));
                }
            }
            Object parentId = record.get( DEFAULT_TREE_PARENT_ID_KEY);

            System.out.println( parentId);
            if (DEFAULT_TREE_ROOT_VALUE.equals( parentId + "")) {
                ret.add( record);
            }

            Map<String,Object> parentNode = treeView.get( parentId);
            if (parentNode != null) {
                List<Map<String,Object>> items = (List<Map<String,Object>>)parentNode.get( DEFAULT_TREE_ITEMS_KEY);

                if (items == null) {
                    items = new ArrayList<>();
                    parentNode.put( DEFAULT_TREE_ITEMS_KEY, items);
                }
                items.add( record);
            }

            treeView.put( record.get( DEFAULT_IDENTITY_KEY), record);


        }

        return new JsonForward( ret);
    }


    public Forward tree(ViewParams params) {
        String fields = params.getString("fields");
        String tableName = params.getString("table_name");
        String orderBy = params.getString("order_by");
        String jsonField = params.getString("json_field");
        String parentField = params.getString( "parent_field");
        String idField = params.getString("id_field");


        if (!StringUtils.hasLength(tableName)) {
            return new JsonForward( null);
        }

        if (!StringUtils.hasLength(fields)) {
            fields = "*";
        }
        if (StringUtils.hasLength( parentField)) {
            fields = fields.replace( parentField, parentField + " as parentId ");
        }

        List sqlParams = new ArrayList<>();
        String sql = "select " + fields + " from " + tableName + " where 1=1 " + params.whereSql( sqlParams);

        if (StringUtils.hasLength(orderBy)) {
            sql += " order by " + orderBy;
        }

        List<Map<String,Object>> records = db.select(sql, sqlParams.toArray(new Object[0]));

        Map<Object,List<Map<String,Object>>> nodeMapping = new HashMap<>();
        List<Map<String,Object>> trees = new ArrayList<>();
        for (Map<String,Object> record : records) {
            String json = (String)record.get( jsonField);
            if (StringUtils.hasLength( json)) {
                Map<String,Object> jsonMap = StringUtils.json2Map( json);
                for (String key : jsonMap.keySet()) {
                    record.put( key, jsonMap.get( key));
                }
            }
            record.put( "_id", record.get( "id"));

            Object parentKey = record.get( "parentId");
            List<Map<String,Object>> items = new ArrayList<>();
            nodeMapping.put( record.get( "organize_id"), items);
            record.put("items", items);

            if (parentKey == null) {
                trees.add( record);
            } else {
                items = nodeMapping.get( parentKey);
                items.add( record);
            }

        }

        return new JsonForward( trees);
    }



}
