package com.ywang.module.sys.service.support;


import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.MapPageCallbackForDM;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.StringUtils;

import java.util.*;


public class DbSysConfigManager extends SysConfigManager {

    private final Db db;

    private Map<Object, Map<String, Object>> configs = new HashMap<Object, Map<String, Object>>();

    private static SysConfigManager INSTANCE;

    private DbSysConfigManager(Db db) {
        if (db == null) {
            throw new IllegalArgumentException("db cannot be null");
        }
        this.db = db;

        if (INSTANCE != null) {
            throw new RuntimeException("Config Class is Singleton");
        }
        refresh();
        INSTANCE = this;
    }

    public static synchronized void registerConfig(Db db) {
        if (INSTANCE != null) {
            return;
        }
        new DbSysConfigManager(db);
    }

    public synchronized void refresh() {
        this.configs.clear();
        List<Map<String, Object>> configs = db.select("select id,config_name as name,config_value as value,group_name as group_name from comm_sys_config a order by sort ", new MapPageCallbackForDM());
        if (configs.isEmpty()) {
            System.out.println("No config found in database");
        }
        // for (Map<String,Object> config : configs) {
        //     System.out.println( "#######" + config);
        // }
        this.configs = CollectionUtils.list2Map(configs, "name");
        setValue("param.os_name", System.getProperties().getProperty("os.name"));
    }

    public static SysConfigManager getInstance() {
        return INSTANCE;
    }

    public String getValue(String key) {
        return getValue(key, null);
    }

    public String getValue(String key, String defValue) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return defValue;
        }
        Object value = config.get("value");
        if (value == null) {
            return defValue;
        }
        return value.toString();
    }


    public void setValue(String key, String value) {
        Map<String, Object> config = configs.get(key);
        if (config == null) {
            return;
        }
        db.update("update comm_sys_config set config_value=?,update_time=? where id=? ", value, new Date(), config.get("id"));
        config.put("value", value);
    }


    public Map<String, Object> getProperty(String key) {
        if (!StringUtils.hasLength(key)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        return Collections.unmodifiableMap(configs.get(key));
    }

    public List<Map<String,Object>> getProperties(String group) {
        if (!StringUtils.hasLength( group)) {
            throw new IllegalArgumentException("key cannot be null or empty");
        }
        List<Map<String,Object>> configs = new ArrayList<>();
        for (Map<String,Object> config : this.configs.values()) {
            if (config.get( "group_name").equals( group)) {
                configs.add( config);
            }
        }
        return Collections.unmodifiableList( configs);
    }
}
