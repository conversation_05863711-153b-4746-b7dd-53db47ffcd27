package com.ywang.module.sys.service;


import com.ywang.framework.mvc.module.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CalendarService extends Service<Map<String,Object>> {

    public List<Map<String,Object>> getDaysOfMonth(int year, int month);
    
    public List<Map<String,Object>> holidaysByYears(int year, String[] holidayNames);
    
    public Map<String,Object> view(Date date);
    
    public int getWorkdays(Date start, Date end);
}
