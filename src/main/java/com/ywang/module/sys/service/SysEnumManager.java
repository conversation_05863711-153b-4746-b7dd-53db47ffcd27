package com.ywang.module.sys.service;


import com.ywang.module.sys.service.support.DbEnumManager;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public abstract class SysEnumManager<T> {

    public static SysEnumManager getInstance() {
        return DbEnumManager.getInstance();
    }

    public abstract void refresh();
    
    public abstract List<T> getGroup(String group);

    public abstract Collection<T> getEnums(String group);

    public abstract T getEnum(String group, String strEnum);

    public abstract void setEnumText(String group, String strEnum, String text);
}
