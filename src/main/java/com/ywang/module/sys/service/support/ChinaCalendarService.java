package com.ywang.module.sys.service.support;

import com.ywang.framework.mvc.module.support.DefaultService;
import com.ywang.module.sys.service.CalendarService;
import com.ywang.sql.support.callback.IntegerCallback;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DateUtils;

import java.util.*;


public class ChinaCalendarService extends DefaultService implements CalendarService {

    public List<Map<String, Object>> getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, 1);
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 7);
        } else {
            calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - calendar.get(Calendar.DAY_OF_WEEK) + 1);
        }
        Date start = DateUtils.clear(calendar.getTime());
        Date end = new Date(start.getTime() + DateUtils.ONE_DATE * 7 * 6);
        return db.select("select calendar_year,calendar_month,calendar_date,is_workday,memo,day_of_week " +
                " from comm_sys_calendar where calendar>=? and calendar <?  order by calendar",
                start, end);
    }

    public Map<String, Object> view(Date date) {
        return db.selectUnique(
                "select calendar,calendar_year,calendar_month,calendar_date,is_workday,memo,week_of_year,day_of_week,week_of_year " +
                        " from comm_sys_calendar where calendar=?", DateUtils.clear(date));
    }


    public List<Map<String, Object>> holidaysByYears(int year, String[] holidayNames) {
        StringBuffer sql = new StringBuffer("select memo holiday,min(calendar) start_time,max(calendar) end_time,count(calendar) num " +
                " from comm_sys_calendar where calendar_year =? and is_workday =? and memo in (");
        if (CollectionUtils.isEmpty(holidayNames)) {
            return null;
        }
        List params = new ArrayList();
        params.add(year);
        params.add(false);
        for (String holidayName : holidayNames) {
            sql.append("?,");
            params.add(holidayName);
        }
        sql.deleteCharAt(sql.length() - 1).append(") group by memo ");
        return db.select(sql.toString(), params.toArray(new Object[0]));
    }

    public int getWorkdays(Date start, Date end) {
        if (start == null || end == null) {
            return -1;
        }
        return db.selectUnique("select count(*) from comm_sys_calendar where is_workday=? and calendar>=? and calendar<=?",
                new IntegerCallback(), true, DateUtils.clear(start), DateUtils.clear(end));
    }

    protected String doGetTableName() {
        return "comm_sys_calendar";
    }


}
