/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 01:10:34
 * @FilePath: /analysis_engine/src/main/java/com/ywang/module/sys/service/support/DefaultConfigService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.module.sys.service.support;


import com.ywang.framework.mvc.module.support.DefaultService;


import com.ywang.module.sys.service.ConfigService;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.utils.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class DefaultConfigService extends DefaultService implements ConfigService {

    public List<Map<String, Object>> list(String whereSql, Object... params) throws IOException {
        String sql = "select id,name easyui_key,title name,value,easyui_editor editor," +
                " (select title from comm_sys_config where id=a.parent_id) easyui_group " +
                " from comm_sys_config a where parent_id is not null ";

        List<Map<String, Object>> configs = db.select(sql + whereSql + " order by sort", params);
        for (Map<String, Object> config : configs) {
            config.put("key", config.remove("easyui_key"));
            config.put("group", config.remove("easyui_group"));
            String editor = (String) config.get("editor");
            if (StringUtils.hasLength(editor) && editor.startsWith("{")) {
                config.put("editor", StringUtils.json2Map(editor));
            }
        }
        return configs;
    }

    public void update(String name, String value) {
        SysConfigManager.getInstance().setValue(name, value);
    }
}
