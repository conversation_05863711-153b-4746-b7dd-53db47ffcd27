package com.ywang.module.sys.service.support;


import com.ywang.module.sys.service.SysEnumManager;
import com.ywang.module.sys.vo.EnumItem;
import com.ywang.sql.support.Db;
import com.ywang.utils.StringUtils;

import java.util.*;

public class DbEnumManager extends SysEnumManager<EnumItem> {

    private final Db db;

    private static DbEnumManager INSTANCE;

    private final Map<String,List<EnumItem>> groups = new HashMap<>();

    private final Map<String, EnumItem> enums = new HashMap<>();

    private DbEnumManager(Db db) {
        if (db == null) {
            throw new IllegalArgumentException("db cannot be null");
        }
        this.db = db;
        if (INSTANCE != null) {
            throw new RuntimeException("Enum Class is Singleton");
        }
        refresh();
        INSTANCE = this;
    }

    public static synchronized void registerEnum(Db db) {
        if (INSTANCE != null) {
            return;
        }
        new DbEnumManager(db);
    }

    public synchronized void refresh() {
        this.enums.clear();
        this.groups.clear();

        List<Map<String, Object>> enums =
                db.select("select id,enum_group,enum_value,enum_name from comm_sys_enum order by enum_group,sort ");

        for (Map<String, Object> enumMap : enums) {
            String group = (String) enumMap.get( "enum_group");
            String name = (String)enumMap.get( "enum_name");
            String value = (String)enumMap.get( "enum_value");

            EnumItem _enum = new EnumItem( group, name, value);

            List<EnumItem> groups = this.groups.get( group);

            if (groups == null) {
                groups = new ArrayList<>();
                this.groups.put( group, groups);
            }
            groups.add( _enum);
            this.enums.put( group + "$" + name, _enum);
        }

    }

    public static DbEnumManager getInstance() {
        return INSTANCE;
    }


    public List<EnumItem> getGroup(String group) {
        throw new RuntimeException( "method is error");
    }


    public Collection<EnumItem> getEnums(String group) {
        if (!StringUtils.hasLength(group)) {
            throw new IllegalArgumentException("enum group cannot be null or empty");
        }
        return Collections.unmodifiableCollection( (Collection<EnumItem>)groups.get(group));
    }


    public EnumItem getEnum(String group, String name) {
        return findEnum( group, name);

    }

    public void setEnumText(String group, String strEnum, String name) {
        throw new RuntimeException( "method is error");
    }


    private EnumItem findEnum(String group, String name) {
        if (!StringUtils.hasLength( group)) {
            throw new IllegalArgumentException("enum group cannot be null or empty");
        }
        if (!StringUtils.hasLength( name)) {
            throw new IllegalArgumentException("enum name cannot be null or empty");
        }
        return enums.get( group + "$" + name);
    }
}
