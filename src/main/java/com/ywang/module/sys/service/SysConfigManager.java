package com.ywang.module.sys.service;


import com.ywang.module.sys.service.support.DbSysConfigManager;

import java.util.List;
import java.util.Map;

public abstract class SysConfigManager {

    public static SysConfigManager getInstance() {
        return DbSysConfigManager.getInstance();
    }

    public abstract void refresh();

    public abstract String getValue(String key);
    
    public abstract String getValue(String key, String defValue);
    
    public abstract void setValue(String key, String value);
    
    public abstract Map<String,Object> getProperty(String key);

    public abstract List<Map<String, Object>> getProperties(String group);
}
