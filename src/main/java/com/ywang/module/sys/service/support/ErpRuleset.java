package com.ywang.module.sys.service.support;

import com.ywang.module.sys.service.RulesetManger;
import com.ywang.sql.support.Db;
import com.ywang.sql.support.callback.StringCallback;
import com.ywang.utils.ConvertUtils;
import com.ywang.utils.StringUtils;

import java.util.Date;


public class ErpRuleset implements RulesetManger {

    private final Db db;

    private static ErpRuleset INSTANCE;

    private ErpRuleset(Db db) {
        if (db == null) {
            throw new IllegalArgumentException("db cannot be null");
        }
        this.db = db;
        if (INSTANCE != null) {
            throw new RuntimeException("Enum Class is Singleton");
        }
        INSTANCE = this;
    }

    public static synchronized void registerRulesetManager(Db db) {
        if (INSTANCE != null) {
            return;
        }
        new ErpRuleset(db);
    }

    public static ErpRuleset getInstance() {
        return INSTANCE;
    }

    public String generateTradeSerialNumber(Date now) {
        if (now == null) {
            throw new IllegalArgumentException("date cannot be null");
        }
        String year = ConvertUtils.date2Str( now, "yy");
        if ("18".equals(year)) {
            year = "17";
        }
        String serialNumber = db.selectUnique("select max(serial_number) from erp_om_trade where serial_number like ?",
                new StringCallback(), year + "%");
        if (!StringUtils.hasLength(serialNumber)) {
            return year + "-00001";
        }
        int i = ConvertUtils.integer(serialNumber.substring(3)) + 1;

        return year + "-" + StringUtils.lpad(i + "", '0', 5);
    }
}
