package com.ywang.module.sys.view.support;


import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.NoneForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIView;
import com.ywang.module.sys.service.ConfigService;
import com.ywang.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class DefaultConfigView extends EasyUIView {

    public Forward update(ViewParams params) {
        if (params.isJson()) {
            ConfigService configService = (ConfigService)service;
            String name = params.getString("name");
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", false);
            if (!StringUtils.hasLength(name)) {
                ret.put("error", "保存错误，键值不能为空");
            } else {
                try {
                    configService.update(name, params.getString("value"));
                    ret.put("success", true);  
                } catch (Exception e) {
                    ret.put("error", e.getMessage());
                }
            }

            return new JsonForward(ret);
        }
        return new NoneForward();
    }
}
