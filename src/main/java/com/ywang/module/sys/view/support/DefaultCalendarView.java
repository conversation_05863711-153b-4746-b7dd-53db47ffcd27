package com.ywang.module.sys.view.support;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.controller.support.NoneForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIMultipleView;
import com.ywang.module.sys.service.CalendarService;
import com.ywang.module.sys.view.CalendarView;

import java.util.*;

public class DefaultCalendarView extends EasyUIMultipleView implements CalendarView {

    private final String[] entityFields = {"bool-is_workday", "str-memo"};

    public Forward list(ViewParams params) throws JsonProcessingException {
        if (params.isJson()) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DATE, 1);
            calendar.set(Calendar.YEAR, params.getInt("year", calendar.get(Calendar.YEAR)));
            calendar.set(Calendar.MONTH, params.getInt("month", calendar.get(Calendar.MONTH)));
            CalendarService calendarService = (CalendarService)service;
            List<Map<String,Object>> days = calendarService.getDaysOfMonth(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH));

            return new JsonForward(days);
        }
        return doGetForward();
    }

    public Forward update(ViewParams params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String,Object> entity = params.getEntity(doGetEntityFields());
                entity.put("update_time", new Date());
                entity.put("update_username", params.getUserName());
                service.update(entity, " and calendar=? ", params.getString("calendar"));
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return doGetForward();
    }


    public Forward holidays(ViewParams params) {
        if (params.isJson()) {
//            CalendarService calendarService = (CalendarService)service;
//            SysEnum _enum = SysEnum.getInstance();
//            List<String> holidayNames = new ArrayList<String>();
//            for (Map<String,Object> holiday : _enum.getEnums("calendar.holidays")) {
//                holidayNames.add((String)holiday.get("enum_text"));
//            }
//
//            Calendar calendar = Calendar.getInstance();
//            int year = params.getInt("year", calendar.get(Calendar.YEAR));
//
//            List<Map<String,Object>> holidays = calendarService.holidaysByYears(year, holidayNames.toArray(new String[0]));
//
//            return new JsonForward(holidays);
        }
        return doGetForward();
    }


    protected String[] doGetEntityFields() {
        return entityFields;
    }

    public Forward init(ViewParams params) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2010, 0, 1);
        Map<String, Integer> map = new LinkedHashMap<String, Integer>();
        map.put("calendar_year", Calendar.YEAR);
        map.put("calendar_month", Calendar.MONTH);
        map.put("calendar_date", Calendar.DATE);
        map.put("week_of_year", Calendar.WEEK_OF_YEAR);
        map.put("week_of_month", Calendar.WEEK_OF_MONTH);

        map.put("day_of_year", Calendar.DAY_OF_YEAR);
        map.put("day_of_month", Calendar.DAY_OF_MONTH);
        map.put("day_of_week", Calendar.DAY_OF_WEEK);
        map.put("day_of_week_in_month", Calendar.DAY_OF_WEEK_IN_MONTH);
        Date now = new Date();

        for (int i = 0; i < 365 * 50; i++) {
            Map<String, Object> entity = new HashMap<String, Object>();
            entity.put("calendar", calendar.getTime());
            entity.put("is_workday", false);
            for (String key : map.keySet()) {
                entity.put(key, calendar.get(map.get(key)));
            }
            entity.put("create_time", now);
            entity.put("create_username", params.getUserName());
            entity.put("update_time", now);
            entity.put("update_username", params.getUserName());
            service.save(entity);
            calendar.add(Calendar.DATE, 1);
        }
        Map<String, Object> entity = new HashMap<String, Object>();
        entity.put("calendar", now);
        entity.put("is_workday", false);
        for (String key : map.keySet()) {
            entity.put(key, calendar.get(map.get(key)));
        }
        entity.put("create_time", now);
        entity.put("create_username", params.getUserName());
        entity.put("update_time", now);
        entity.put("update_username", params.getUserName());
//        service.save(entity);

        return new NoneForward();
    }

    public static void main(String... args) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2013, 8, 1);
        for (int i = 1; i < 31; i++) {
            calendar.set(Calendar.DATE, i);
            System.out.println(calendar.getTime().toLocaleString());
            Map<String, Integer> map = new LinkedHashMap<String, Integer>();
            map.put("YEAR", Calendar.YEAR);
            map.put("MONTH", Calendar.MONTH);
            map.put("WEEK_OF_YEAR", Calendar.WEEK_OF_YEAR);
            map.put("WEEK_OF_MONTH", Calendar.WEEK_OF_MONTH);
            map.put("DATE", Calendar.DATE);
            map.put("DAY_OF_YEAR", Calendar.DAY_OF_YEAR);
            map.put("DAY_OF_WEEK", Calendar.DAY_OF_WEEK);
            map.put("DAY_OF_WEEK_IN_MONTH", Calendar.DAY_OF_WEEK_IN_MONTH);

            for (String s : map.keySet()) {
                System.out.println(s + ":" + calendar.get(map.get(s)));
            }

        }
    }
}
