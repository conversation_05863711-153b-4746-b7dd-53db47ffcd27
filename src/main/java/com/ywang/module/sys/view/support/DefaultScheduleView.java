package com.ywang.module.sys.view.support;

import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIMultipleView;
import com.ywang.framework.scheduler.ScheduleManager;
import com.ywang.framework.scheduler.TaskInfo;
import com.ywang.module.sys.view.ScheduleView;
import com.ywang.utils.ConvertUtils;

import java.util.*;


public class DefaultScheduleView extends EasyUIMultipleView implements ScheduleView {

    public Forward list(ViewParams params) {
        if (params.isJson()) {
            ScheduleManager scheduleManager = ScheduleManager.getInstance();
            List<TaskInfo> registerTasks = scheduleManager.getRegisterTasks();

            List<Map<String,Object>> tasks = new ArrayList<Map<String,Object>>();

            for (TaskInfo taskInfo : registerTasks) {
                Map<String,Object> task = ConvertUtils.entityToMap( taskInfo);
                task.remove("target");
                tasks.add( task);
            }

            return new JsonForward(new DataGrid( tasks));
        }
        return doGetForward();
    }

    public Forward console(ViewParams params) {

        return doGetForward();
    }
}
