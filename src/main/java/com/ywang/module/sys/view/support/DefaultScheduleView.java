/*
 * @Author: sneaker <EMAIL>
 * @Date: 2023-08-10 14:13:28
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 01:11:14
 * @FilePath: /analysis_engine/src/main/java/com/ywang/module/sys/view/support/DefaultScheduleView.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.module.sys.view.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.easyui.wrap.DataGrid;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIMultipleView;
import com.ywang.framework.scheduler.ScheduleManager;
import com.ywang.framework.scheduler.TaskInfo;
import com.ywang.module.sys.view.ScheduleView;
import com.ywang.utils.ConvertUtils;

import java.util.*;


public class DefaultScheduleView extends EasyUIMultipleView implements ScheduleView {

    public Forward list(ViewParams params) throws JsonProcessingException {
        if (params.isJson()) {
            ScheduleManager scheduleManager = ScheduleManager.getInstance();
            List<TaskInfo> registerTasks = scheduleManager.getRegisterTasks();

            List<Map<String,Object>> tasks = new ArrayList<Map<String,Object>>();

            for (TaskInfo taskInfo : registerTasks) {
                Map<String,Object> task = ConvertUtils.entityToMap( taskInfo);
                task.remove("target");
                tasks.add( task);
            }

            return new JsonForward(new DataGrid( tasks));
        }
        return doGetForward();
    }

    public Forward console(ViewParams params) {

        return doGetForward();
    }
}
