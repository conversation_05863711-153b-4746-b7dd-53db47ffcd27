package com.ywang.module.rbac.service.support;



import com.ywang.module.rbac.dao.PrivilegeCallback;
import com.ywang.module.rbac.vo.AbstractPrivilege;
import com.ywang.module.rbac.vo.LoginPrivilege;
import com.ywang.module.rbac.vo.User;
import com.ywang.sql.support.Db;

import java.util.List;


public class SimplePrivilegeService {

    private Db db;

    private AbstractPrivilege privilege;

    public void setDb(Db db) {
        this.db = db;
    }

    public void init() {
        List<AbstractPrivilege> privileges =
                db.select("select url,has_login,role_code,type from comm_rbac_privilege ", new PrivilegeCallback());

        AbstractPrivilege father = null;
        for (AbstractPrivilege privilege : privileges) {
            if (father == null) {
                this.privilege = privilege;
            } else {
                father.setPrivilegeChain( privilege);
            }
            father = privilege;
        }
        father.setPrivilegeChain( new LoginPrivilege());
    }



    public boolean hasPrivilege(String url, User user) {
        if (privilege == null) {
            return true;
        }
        return privilege.hasPrivilege( url, user);
    }


}
