package com.ywang.module.rbac.service.support;


import com.ywang.framework.mvc.module.support.DefaultService;
import com.ywang.module.rbac.service.RoleService;
import com.ywang.sql.support.callback.LongCallback;
import com.ywang.utils.CollectionUtils;
import com.ywang.utils.DbUtils;
import com.ywang.utils.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


public class DefaultRoleService extends DefaultService implements RoleService {

    public void getRoleNames(Map<String, Object>... users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        List<Map<String, Object>> roles = list("");
        for (Map<String, Object> user : users) {
            Long userRoleCode = (Long) user.get("role_code");
            String roleNames = "";
            if (userRoleCode == null) {
                userRoleCode = 0L;
            }
            for (Map<String, Object> role : roles) {
                Long roleCode = (Long) role.get("role_code");

                if (roleCode == null) {
                    continue;
                }
                if ((roleCode & userRoleCode) > 0) {
                    roleNames += role.get("role_name") + ",";
                }
            }
            if (StringUtils.hasLength(roleNames)) {
                roleNames = roleNames.substring(0, roleNames.length() - 1);
            }
            user.put("role_names", roleNames);
        }
    }

    public long save(Map<String, Object> entity) {
        long maxRoleCode = db.selectUnique("select max(role_code) from comm_rbac_role ", new LongCallback());
        entity.put("role_code", maxRoleCode * 2);
        List params = new ArrayList();
        String sql = DbUtils.insertSql(doGetTableName(), entity, params);
        return db.insert(sql, params.toArray(new Object[0]));
    }

    public Map<String, Object> view(long id) {
        String sql = doGetQuerySql() + " and  role_code=? ";
        return db.selectUnique(sql, id);
    }

    public int delete(long id) {
        if (id == 1) {
            throw new IllegalArgumentException("错误，无法删除系统角色");
        }
        int count = db.delete("delete from " + doGetTableName() + " where role_code=? and is_system_role=?", id, false);
        if (count == 0) {
            throw new IllegalArgumentException("错误，无法删除系统角色");
        }
        Date now = new Date();
        db.update("update comm_rbac_permission set role_code=(role_code-?),update_time=? where bitand(role_code,?) > 0", id, now, id);
        db.update("update comm_rbac_user set role_code=(role_code-?),update_time=? where bitand(role_code,?) > 0", id, now, id);

        return count;
    }

    protected String doGetTableName() {
        return "comm_rbac_role";
    }

    protected String doGetQuerySql() {
        return "select role_code,role_name,memo,sort from comm_rbac_role where 1=1 ";
    }

    protected String doGetOrderSql() {
        return " order by sort ";
    }
}
