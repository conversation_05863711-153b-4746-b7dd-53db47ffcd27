/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-17 01:19:15
 * @FilePath: /analysis_engine/src/main/java/com/ywang/module/rbac/service/support/DefaultUserService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.module.rbac.service.support;


import com.ywang.framework.mvc.module.support.DefaultService;
import com.ywang.module.rbac.service.RoleService;
import com.ywang.module.rbac.service.UserService;
import com.ywang.sql.OrderPage;

import java.io.IOException;
import java.util.*;

public class DefaultUserService extends DefaultService implements UserService {

    public Map<String, Object> login(String username) {
        return db.selectUnique("select id,username,password,access_level,role_code,ifnull(real_name,username) real_name from comm_rbac_user where username=? and password=? ",
                username);
    }


    public List<Map<String, Object>> list(String whereSql, OrderPage page, Object... params) throws IOException {
        List<Map<String, Object>> users = super.list(whereSql, page, params);
        RoleService roleService = getService(RoleService.class);
        roleService.getRoleNames(users.toArray(new Map[0]));
        return users;
    }

    public Map<String, Object> view(long id) {
        Map<String,Object> user = db.selectUnique("select id,employee_id,username,real_name,role_code,email,sex,mobile,memo from comm_rbac_user where id=? ", id);
//        Map<String,Object> user = db.selectUnique("select a.employee_id,a.id,a.username,a.real_name,a.role_code,a.email,a.sex,a.mobile,a.memo from comm_rbac_user a " +
//                "left join hr_resource_employee b on a.employee_id=b.id where a.id=? ", id);
        Long roleCode = (Long)user.get("role_code");
        roleCode = (roleCode == null ? 0L : roleCode);
        List<Long> roleCodes = new ArrayList<Long>();
        for (int i = 0; i < Long.toBinaryString(roleCode).length(); i++) {
            long num = (long)Math.pow(2, i);
            if ((roleCode & num) > 0) {
                roleCodes.add(num);
            }
        }
        user.put("role_code", roleCodes);
        return user;
    }

    public int delete(long id) {
        if (id <= 2000) {
            throw new IllegalArgumentException("错误，无法删除系统用户");
        }
        return super.delete(id);
    }

    protected String doGetTableName() {
        return "comm_rbac_user";
    }

    protected String doGetQuerySql() {
        return "select id,username,real_name,role_code,email,sex,mobile from comm_rbac_user where 1=1 ";
//        return "select b.employee_name,a.id,a.username,a.real_name,a.role_code,a.email,a.sex,a.mobile from comm_rbac_user a " +
//                "left join hr_resource_employee b on a.employee_id=b.id where 1=1 ";
    }

    protected String doGetOrderSql() {
        return " order by real_name ";
    }
}
