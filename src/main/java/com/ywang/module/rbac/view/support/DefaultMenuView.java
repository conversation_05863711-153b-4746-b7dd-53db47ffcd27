package com.ywang.module.rbac.view.support;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.ywang.easyui.wrap.DataGrid;
import com.ywang.easyui.wrap.Tree;
import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIView;
import com.ywang.module.rbac.service.RoleService;
import com.ywang.module.rbac.view.MenuView;

import java.io.IOException;
import java.util.*;

public class DefaultMenuView extends EasyUIView implements MenuView {

    private final String[] entityFields = {"lon-role_code"};

    public Forward listAll(ViewParams params) throws IOException {
        RoleService roleService = (RoleService) service.getService(RoleService.class);
        List<Map<String, Object>> roles = roleService.list("");
        if (params.isJson()) {
            List whereParams = new ArrayList();
            String whereSql = params.whereSql(whereParams);
            whereSql += " and is_menu=? ";
            whereParams.add(true);
            List<Map<String, Object>> permissions = service.list(whereSql, whereParams.toArray(new Object[0]));
            for (Map<String, Object> permission : permissions) {
                permission.put("_parentId", permission.get("parent_id"));
                long roleCode = (Long) permission.get("role_code");
                for (Map<String, Object> role : roles) {
                    long _roleCode = (Long) role.get("role_code");
                    permission.put("role_" + _roleCode, ((roleCode & _roleCode) > 0));
                }
                long childrenNum = (Long) permission.get("children_num");
                permission.put("iconCls", (childrenNum > 0 ? "icon-menu" : "icon-gears"));
            }
            return new JsonForward(new DataGrid(permissions));
        }

        Map<String, Object> requestAttributes = new HashMap<String, Object>();
        requestAttributes.put("roles", roles);
        return new DefaultForward(requestAttributes);
    }

    public Forward update(ViewParams params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                //1. 重置所有菜单
                Map<String, Object> menu = new HashMap<String, Object>();
                menu.put("is_menu", false);
                menu.put("update_time", new Date());
                menu.put("update_username", params.getUserName());
                service.update(menu, "");
                //2. 设置菜单
                int length = params.getLongArray("id[]").length;
                if (length > 0) {
                    menu.put("is_menu", true);
                    String whereSql = " and id in (";
                    for (int i = 0; i < length; i++) {
                        whereSql += "?,";
                    }
                    whereSql = whereSql.substring(0, whereSql.length() - 1) + ")";
                    service.update(menu, whereSql, params.getLongArray("id[]"));
                }




            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }

    public Forward updateRoleCode(ViewParams params) throws JsonProcessingException {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Date now = new Date();
                Map<String, Object> entity = params.getEntity(doGetEntityFields());
                entity.put("update_time",now);
                entity.put("update_username", params.getUserName());
                service.update(entity, " and inner_code like ? and is_menu=? ", params.getString("inner_code") + "%", true);
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }

    public Forward tree(ViewParams params) throws IOException {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            String whereSql = params.whereSql(whereParams);
            List<Map<String, Object>> permissions = service.list(whereSql, whereParams.toArray(new Object[0]));
            for (Map<String, Object> permission : permissions) {
                long childrenNum = (Long) permission.get("children_num");
                permission.put("iconCls", (childrenNum > 0 ? "icon-menu" : "icon-gears"));
                Boolean isMenu = (Boolean) permission.get("is_menu");
                permission.put("checked", (isMenu != null && isMenu));
                permission.put("text", permission.get("title"));
            }
            return new JsonForward(Tree.valueOf(permissions));
        }
        return new DefaultForward(null);
    }

    protected String[] doGetEntityFields() {
        return entityFields;
    }
}
