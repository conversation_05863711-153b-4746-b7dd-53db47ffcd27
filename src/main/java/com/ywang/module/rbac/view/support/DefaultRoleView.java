package com.ywang.module.rbac.view.support;



import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIView;
import com.ywang.module.rbac.view.RoleView;

import java.util.*;

public class DefaultRoleView extends EasyUIView implements RoleView {

    private final String[] entityFields = {"str-role_name", "str-memo", "lon-sort"};

    public Forward combobox(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            List<Map<String, Object>> roles = service.list(params.whereSql(whereParams), whereParams.toArray(new Object[0]));

            return new JsonForward(roles);
        }
        return new DefaultForward(null);
    }

    public Forward save(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String, Object> entity = params.getEntity(doGetEntityFields());
                Date now = new Date();
                entity.put("is_system_role", false);
                entity.put("create_time", now);
                entity.put("create_username", params.getUserName());
                entity.put("update_time", now);
                entity.put("update_username",params.getUserName());
                service.save(entity);
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }

    public Forward update(ViewParams params) {
        if (params.isJson()) {
            Map<String, Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String, Object> entity = params.getEntity(doGetEntityFields());
                entity.put("update_time", new Date());
                entity.put("update_username", params.getUserName());
                service.update(entity, " and role_code=? ", params.getLong("role_code"));
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }

    public String[] doGetEntityFields() {
        return entityFields;
    }
}
