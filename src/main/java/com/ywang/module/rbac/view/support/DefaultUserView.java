package com.ywang.module.rbac.view.support;



import com.ywang.framework.mvc.controller.Forward;
import com.ywang.framework.mvc.controller.support.DefaultForward;
import com.ywang.framework.mvc.controller.support.JsonForward;
import com.ywang.framework.mvc.view.ViewParams;
import com.ywang.framework.mvc.view.support.EasyUIView;

import com.ywang.module.rbac.view.UserView;
import com.ywang.module.sys.service.SysConfigManager;
import com.ywang.utils.StringUtils;

import java.util.*;


public class DefaultUserView extends EasyUIView implements UserView {

    private final String[] entityFields = {"str-username", "bool-sex", "int-access_level-1", "str-real_name",
            "str-mobile", "str-email", "str-memo", "lon-employee_id"};

    public Forward save(ViewParams params) {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                SysConfigManager config = SysConfigManager.getInstance();
                Date now = new Date();
                Map<String,Object> entity = params.getEntity(doGetEntityFields());
                entity.put("password", StringUtils.md5(config.getValue("rbac.default_password")));
                long roleCodes = 0;
                for (long roleCode : params.getLongArray("role_code")) {
                    roleCodes += roleCode;
                }

                entity.put("role_code", roleCodes);
                entity.put("create_time", now);
                entity.put("create_username", params.getUserName());
                entity.put("update_time", now);
                entity.put("update_username", params.getUserName());
                service.save(entity);
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }
    
    
    public Forward update(ViewParams params) {
        if (params.isJson()) {
            Map<String,Object> ret = new HashMap<String, Object>();
            ret.put("success", true);
            try {
                Map<String,Object> entity = params.getEntity(doGetEntityFields());
                long roleCodes = 0;
                for (long roleCode : params.getLongArray("role_code")) {
                    roleCodes += roleCode;
                }
                entity.put("role_code", roleCodes);
                entity.put("update_time", new Date());
                entity.put("update_username", params.getUserName());
                service.update(entity, " and id=? ", params.getId());
            } catch (Exception e) {
                ret.put("success", false);
                ret.put("error", e.getMessage());
            }
            return new JsonForward(ret);
        }
        return new DefaultForward(null);
    }

    public Forward combobox(ViewParams params) {
        if (params.isJson()) {
            List whereParams = new ArrayList();
            List<Map<String, Object>> users = service.list(params.whereSql(whereParams), whereParams.toArray(new Object[0]));

            return new JsonForward(users);
        }
        return new DefaultForward(null);
    }

    public String[]  doGetEntityFields() {
        return entityFields;
    }
}
