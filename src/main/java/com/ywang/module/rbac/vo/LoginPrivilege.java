package com.ywang.module.rbac.vo;

import java.util.HashMap;
import java.util.Map;

public class LoginPrivilege implements Privilege {

    public boolean hasPrivilege(String url, User user) {
        return hasPrivilege( url, user, new HashMap<>());
    }

    public boolean hasPrivilege(String url, User user, Map<String,Object> extAttr) {
        extAttr.put( "privilege", this);
        return user != null;
    }
}
