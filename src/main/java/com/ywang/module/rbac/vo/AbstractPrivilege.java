package com.ywang.module.rbac.vo;


import com.ywang.utils.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public abstract class AbstractPrivilege implements Privilege, Serializable {

    protected String url;

    protected long roleCode;

    protected boolean hasLogin;

    protected Map<String,Object> extAttr = new HashMap<>();

    protected Privilege privilegeChain;

    public Privilege getPrivilegeChain() {
        return privilegeChain;
    }

    public void setPrivilegeChain(Privilege privilegeChain) {
        this.privilegeChain = privilegeChain;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(long roleCode) {
        this.roleCode = roleCode;
    }

    public boolean isHasLogin() {
        return hasLogin;
    }

    public void setHasLogin(boolean hasLogin) {
        this.hasLogin = hasLogin;
    }

    public <T> T setExtAttr(String key, T value) {
        return (T)extAttr.put( key, value);
    }

    public <T> T getExtAttr(String key, Class<T> clazz) {
        return (T)extAttr.get( key);
    }


    public boolean hasPrivilege(String url, User user) {
        return hasPrivilege( url, user, new HashMap<>());
    }

    public boolean hasPrivilege(String url, User user, Map<String,Object> extAttr) {
        extAttr.put( "privilege", this);
        if (url == null) {
            return false;
        }
        if (doCheckPrivilege(url, user)) {
            return (hasLogin ? (user != null) : true);
        }
        if (privilegeChain == null) {
            return false;
        }

        return privilegeChain.hasPrivilege( url, user, extAttr);
    }

    protected abstract boolean doCheckPrivilege(String url, User user);
}
