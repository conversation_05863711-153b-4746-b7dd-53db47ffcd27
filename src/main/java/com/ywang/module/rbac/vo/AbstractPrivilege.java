/*
 * @Author: sneaker <EMAIL>
 * @Date: 2024-11-07 13:41:52
 * @LastEditors: sneaker <EMAIL>
 * @LastEditTime: 2024-11-14 11:15:55
 * @FilePath: /analysis_engine/src/main/java/com/ywang/module/rbac/vo/AbstractPrivilege.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.ywang.module.rbac.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public abstract class AbstractPrivilege implements Privilege, Serializable {

    protected String url;

    protected long roleCode;

    protected boolean hasLogin;

    protected Map<String,Object> extAttr = new HashMap<>();

    protected Privilege privilegeChain;

    public Privilege getPrivilegeChain() {
        return privilegeChain;
    }

    public void setPrivilegeChain(Privilege privilegeChain) {
        this.privilegeChain = privilegeChain;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(long roleCode) {
        this.roleCode = roleCode;
    }

    public boolean isHasLogin() {
        return hasLogin;
    }

    public void setHasLogin(boolean hasLogin) {
        this.hasLogin = hasLogin;
    }

    public <T> T setExtAttr(String key, T value) {
        return (T)extAttr.put( key, value);
    }

    public <T> T getExtAttr(String key, Class<T> clazz) {
        return (T)extAttr.get( key);
    }


    public boolean hasPrivilege(String url, User user) {
        return hasPrivilege( url, user, new HashMap<>());
    }

    public boolean hasPrivilege(String url, User user, Map<String,Object> extAttr) {
        extAttr.put( "privilege", this);
        if (url == null) {
            return false;
        }
        if (doCheckPrivilege(url, user)) {
            return (hasLogin ? (user != null) : true);
        }
        if (privilegeChain == null) {
            return false;
        }

        return privilegeChain.hasPrivilege( url, user, extAttr);
    }

    protected abstract boolean doCheckPrivilege(String url, User user);
}
