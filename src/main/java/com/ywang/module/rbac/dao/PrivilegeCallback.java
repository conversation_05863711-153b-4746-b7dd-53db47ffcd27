package com.ywang.module.rbac.dao;



import com.ywang.module.rbac.vo.AbstractPrivilege;

import com.ywang.module.rbac.vo.EqualPrivilege;
import com.ywang.module.rbac.vo.StartWithPrivilege;
import com.ywang.sql.Callback;

import java.sql.ResultSet;
import java.sql.SQLException;


public class PrivilegeCallback implements Callback<AbstractPrivilege> {

    @Override
    public AbstractPrivilege with(int row, ResultSet rs) throws SQLException {
        String type = rs.getString( "type");
        AbstractPrivilege privilege = null;
        if ("equal".equals( type)) {
            privilege = new EqualPrivilege();
        } else  if ("startwith".equals( type)) {
            privilege = new StartWithPrivilege();
        }

        privilege.setUrl( rs.getString( "url"));
        privilege.setHasLogin( rs.getBoolean( "has_login"));
        privilege.setRoleCode( rs.getLong( "role_code"));

        return privilege;
    }
}
