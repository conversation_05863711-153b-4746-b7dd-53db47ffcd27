<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
       </style>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{/*background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;*/font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
        .title{
            padding:5px;
            font-size: 14px;
        }
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3" style="width:100%;margin-right:0"><a href="javascript:step(0)" class="step" for="qcReport">
                    <span class="number">&nbsp;</span>
                    <span class="desc"> QC报告</span>
                </a></li>
                <%--<li class="span3"><a href="javascript:step(1)" class="step"   for="qcFile">
                    <span class="number">2</span>
                    <span > QC文件</span>
                </a></li>--%>
            </ul>

        </div>

        <div id="tabContent" style="background: #fff">
            <div id="qcReport">
                <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
                    <div  class="am-u-sm-12" style="padding: 0px; border-width: 0px;">
                       <%-- <div class="title" >11111</div>--%>
                        <div id="qc-report-grid1" ></div>
                    </div>
                   <%-- <div class="am-u-sm-6" style="padding: 0px; border-width: 0px;">
                        &lt;%&ndash;<div class="title" >22222</div>&ndash;%&gt;
                        <div id="qc-report-grid2" ></div>
                    </div>--%>
                </div>

            </div>

            <div id="qcFile" style="display: none">
                <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
                    <div id="qc-file-grid"></div>
                </div>
            </div>
        </div>

    </div>

</div>
</div>

<div id="dialog2"></div>
<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".k-widget").height( height - 56).find( ".widget-body").height( height - 56);
    }
</script>
<%@include file="/include/include-footer.jsp"%>
<script type="text/x-kendo-template" id="row-file_name">
    #= file_name #
</script>
<script type="text/javascript">
    var rowIndex=1;
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        }

        return "";
    }



    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");

        var showId = $this.attr("for");
        $("#tabContent > div").hide();
        $("#"+showId).show();

        if("qcFile"==showId){
            var loading = $this.attr("loading");
            if(!loading){
                $this.attr("loading","loading");
                resizeContent();
                var $grid = $("#qc-file-grid").kendoGrid({
                    dataSource: {
                        type: "json",
                        <%--read: "${CTX}/project_file_view.json?project_no="+"${project_no}",--%>
                        transport: {
                            read: "${CTX}/project_report_viewQCFile.json?id=${param['id']}",
                            dataType: "json"
                        },
                        pageSize: 40,
                        serverPaging: true,
                        serverFiltering: true,
                        schema: {
                            "total" : "total",
                            "data" : "rows",
                            model: {
                                id: "id"
                            }
                        }
                    },
                    height: $(window).height() - 133,
                    groupable: false,
                    sortable: false,
                    selectable: "true",
                    /* pageable: {
                         refresh: true,
                         pageSizes: true,
                         buttonCount: 5
                     },*/
                    filterable: {
                        extra: false
                    },
                    //toolbar: kendo.template($("#toolbar").html()),
                    columns: [
                        {title:"序号",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template : "#= rowIndex++ #",},
                        /*{field:"sample_name",title:"样本编号",width:"80px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        */{title:"文件名",width:"340px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},template: kendo.template($("#row-file_name").html())},
                        {field:"file_size",title:"文件大小",width:"80px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                        {field:"create_time",title:"文件最后修改时间",width:"120px",template : "#= last_modify_time?kendo.toString(new Date(last_modify_time), 'yyyy-MM-dd HH:mm'):'--' #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {field:"file_status",title:"文件状态",width:"120px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        { command: [
                            {text: "查看",click: viewFile,visible: function(dataItem) {return dataItem.file_status=="已生成"}},
                        ], title: " ", width: "70px", headerAttributes:{style:"text-align:center;vertical-align:middle"}}
                    ]
                });


                $("#upload-file", $grid).on("change", function() {
                    var $file = $(this);
                    if ($file.val().length == 0) {
                        return;
                    }

                    var $form = $("#upload-file-form", $grid);

                });
            }
        }
    }
</script>


<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
<%--                <input type="hidden" name="project_no" value="${param["project_no"]}" />--%>
                <input type="hidden" name="sample_name" value="${param["sample_name"]}" />
            </form>
        </div>
        <%--<div  class="am-form-group am-form-file" id="file_edit" >--%>
        <%--<i class="am-icon-pencil"></i> 编辑--%>
        <%--</div>--%>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>


<script type="text/javascript">
    var counter = 1;
    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if(!size) return "--";

        if(!idx)  idx = 0;
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }
    $(function(){

        var $grid = $("#qc-report-grid1").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/project_report_viewQC2.json?id=${param['id']}&s=0",
                    dataType: "json"
                },
                /*  group: {
                      field: "样本编号",
                      dir: "asc"
                  },*/
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() -56,
            groupable: false,
            sortable: false,
            selectable: "true",
            /* pageable: {
                 refresh: true,
                 pageSizes: true,
                 buttonCount: 5
             },*/
            filterable: {
                extra: false
            },
            //toolbar: kendo.template($("#toolbar").html()),
            columns: [
                {field:"",title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: "#=counter++#"},
                {field:"qc_name",title:"检测项目",width:"140px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left;"}},
                /*{field:"cancer_kind",title:"癌症种类",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},*/
                {"title":"${project.sample_name_0}",headerAttributes:{style:"text-align:center;vertical-align:middle"},columns:[
                    {field:"qc_value",title:"检测结果",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right;border-width: 0;"},
                        template : "#= (qc_qualified==0?'<div style=\"color: red\">'+qc_value+'</div>':qc_value)#"
                    },
                    {field:"qc_reference",title:"参考值",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center;border-width: 0;"}},
                ]},
                {"title":"${project.sample_name_1}",headerAttributes:{style:"text-align:center;vertical-align:middle"},columns:[
            /*{field:"qc_name1",title:"检测项目",width:"140px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left;"}},
             *//*{field:"cancer_kind",title:"癌症种类",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},*/
                {field:"qc_value1",title:"检测结果",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right;border-width: 0;"},
                    template : "#= (qc_qualified1==0?'<div style=\"color: red\">'+qc_value1+'</div>':qc_value1)#"
                },
                {field:"qc_reference1",title:"参考值",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center;border-width: 0;"}},
                ]}
           ]
        });




        var showType = "${param['type']}";
        if(showType){
            step(showType);
        } else {
            step(0);
        }
    });


    $("#backBtn").click(function(){
        history.go(-1);
    });

    function viewFile(e){
            var row = this.dataItem( $(e.currentTarget).closest("tr"));
            <%--window.open("${CTX}/project_cancer_viewLog.htm?type=stdout&id=${id}&metaId="+row.id);--%>
            $("#dialog2").kendoWindow({
                width: "800px",
                height:"560px",
                scrollable: true,
                content: "${CTX}/project_cancer_viewLog.htm?filePath="+row.file_path,
                iframe: true,
                visible: false,
                actions: ["Refresh", "Maximize", "Close"],
            }).data("kendoWindow").center().open();

    }

</script>

</body>

</html>