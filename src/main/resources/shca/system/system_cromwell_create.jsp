<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div class="widget am-cf am-u-sm-12" style="padding:0">
                <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">
                    <form id="cromwell-form"  action="${CTX}/system_cromwell_create.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">
                        <input type="hidden" name="parent_id" value="69" />
                        <div class="am-form-group">
                            <label for="cancer_kind" class="am-u-sm-1 am-form-label">癌症种类<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="cancer_kind" name="cancer_kind" data-placeholder="请选择癌症种类" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <c:forEach items="${kindList}" var="kind">
                                    <option value="${kind['name']}">${kind['value']}</option>
                                </c:forEach>
                                </select>
                                <small></small>
                            </div>
                        </div>



                        <div class="am-form-group">
                            <label for="cancer_script" class="am-u-sm-1 am-form-label">脚本名称<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="cancer_script" id="cancer_script" value="" placeholder="请输入脚本名称地址" />
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cancer_script" class="am-u-sm-1 am-form-label"> 脚本类型<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <label for="script_step_1" class="am-radio"  style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="script_step" id="script_step_1"  type="radio" value="0" checked>
                                    单样本分析
                                </label>
                                <label for="script_step_2" class="am-radio"  style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="script_step" id="script_step_2"  type="radio" value="1">
                                    对比分析
                                </label>
                                <label for="script_step_3" class="am-radio"  style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="script_step" id="script_step_3"  type="radio" value="2">
                                    评估报告
                                </label>
                                <label for="script_step_4" class="am-radio"  style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="script_step" id="script_step_4"  type="radio" value="3">
                                    文件上传
                                </label>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cancer_script" class="am-u-sm-1 am-form-label">默认脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <label for="auto_script_1" class="am-radio"  style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="auto_script" id="auto_script_1" class="weui-switch-cp__input" type="radio" value="1" checked>
                                    是
                                </label>
                                <label for="auto_script_2" class="am-radio"   style="display: inline-block;line-height: 1.6;margin-top:5px;width:150px;">
                                    <input name="auto_script" id="auto_script_2" class="weui-switch-cp__input" type="radio" value="0">
                                    否
                                </label>
                                <small></small>
                            </div>
                        </div>


                        <div class="am-form-group">
                            <label for="cromwell_workflow_source" class="am-u-sm-1 am-form-label">测序脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="cromwell_workflow_source" id="cromwell_workflow_source" value="" placeholder="请输入测序脚本路径" readonly />
                                <div class="am-form-group am-form-file" style="margin-top:4px;width: 120px">
                                    <button type="button" class="am-btn">
                                        <i class="am-icon-cloud-upload" data-am-modal="{target:'#toolbar'}"></i> 上传测序脚本
                                    </button>
                                    <input class="upload-image-form-file"  id="upload-workflow_source-file" type="file" name="upload-workflow_source-file" >
                                </div>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cromwell_workflow_dependencies" class="am-u-sm-1 am-form-label">依赖脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="cromwell_workflow_dependencies" id="cromwell_workflow_dependencies" value="" placeholder="请输入依赖脚本路径" readonly/>
                                <div class="am-form-group am-form-file" style="margin-top:4px;width: 120px">
                                    <button type="button" class="am-btn">
                                        <i class="am-icon-cloud-upload" data-am-modal="{target:'#toolbar'}"></i> 上传依赖脚本
                                    </button>

                                    <input  class="upload-image-form-file"  id="upload-workflow_dependencies-file" type="file" name="upload-workflow_dependencies-file">
                                </div>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cromwell_workflow_dependencies" class="am-u-sm-1 am-form-label">参数配置<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <textarea type="text"  rows="6" class="tpl-form-input" name="cromwell_inputs" id="cromwell_inputs"  placeholder="请输入参数配置" ></textarea>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <div class="am-u-sm-12 am-u-sm-push-1">
                                <a id="createBtn" class="blue-btn" style="display:inline-block;text-align:justify;text-align-last:justify;">
                                    创建 <i class="icon-angle-right"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>
<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
                <input type="hidden" name="project_no" value="${param["project_no"]}" />
            </form>
        </div>
        <%--<div  class="am-form-group am-form-file" id="file_edit" >--%>
        <%--<i class="am-icon-pencil"></i> 编辑--%>
        <%--</div>--%>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>
<script type="text/x-kendo-template" id="row-file_name">
    #= file_name.replace( /B2000617B/g, "<span class='warning'>${param["project_no"]}</span>") #
<%--    #= name.replace( /B2000617B/g, "<span class='warning'>${param["project_no"]}</span>") #--%>
</script>
<%@include file="/include/include-footer.jsp"%>
<script type="text/javascript"  src="${CTX}/plugin/jquery-weui-3/js/jquery-weui.js"></script>
<script type="text/javascript">
    $("select.chosen").chosen();
    $("input.kendo-datetimepicker").kendoDateTimePicker({
        // display month and year in the input
        format: "yyyy-MM-dd HH:mm",
        // specifies that DateInput is used for masking the input element
        dateInput: false,
    });


    $(function(){

        //选择文件
        $("#upload-workflow_source-file").on("change", function() {
            var $file = $(this);
            if ($file.val().length == 0) {
                return;
            }
            $("#cromwell_workflow_source").val($file[0].files[0].name);
        });

        $("#upload-workflow_dependencies-file").on("change", function() {
            var $file = $(this);
            if ($file.val().length == 0) {
                return;
            }
            //console.log($file);
            $("#cromwell_workflow_dependencies").val($file[0].files[0].name);
        });
    });

    $("#createBtn").click(function(){
        try{
            var $button = $(this);
            if ($button.attr("disabled") == "disabled"){
                return;
            };


            var cancer_kind = $("#cancer_kind").val()
            if(cancer_kind == undefined || cancer_kind == ""){
                alert("请选择癌症种类")
                return false;
            }

            var cancer_script = $("#cancer_script").val();
            if(cancer_script == undefined || cancer_script == ""){
                alert("请输入脚本名称地址")
                return false;
            }

            var cromwell_workflow_source = $("#cromwell_workflow_source").val();
            if(cromwell_workflow_source == undefined || cromwell_workflow_source == ""){
                alert("请输入测序脚本路径")
                return false;
            }

            var cromwell_workflow_dependencies = $("#cromwell_workflow_dependencies").val();
            if(cromwell_workflow_dependencies == undefined || cromwell_workflow_dependencies == ""){
                alert("请输入依赖脚本路径")
                return false;
            }

            $button.button( "提交申请中...");
            $button.attr("disabled", true);

            $("#cromwell-form").ajaxSubmit({
                dataType: "json",
                success: function (ret) {
                    if(ret.errcode==0){
                        alert("保存成功");
                        window.location.href="${CTX}/system_cromwell_list.htm"
                    } else {
                        alert(ret.errmsg);
                    }
                }
            });

        }catch (e){
            alert(e);
        }
    });


</script>

</body>

</html>