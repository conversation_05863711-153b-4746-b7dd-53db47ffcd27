<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
        <div id="project-grid"></div>
    </div>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>
<div id="sample-cat-dialog" style="padding:0;overflow:hidden">
    <div id="step-container-0" class="am-u-sm-12 am-u-md-12 step-container" style="margin:0;padding:0 1px;border-radius:0;display:inline;">
        <div class="widget am-cf am-u-sm-12" style="padding:0">
            <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">

                <form id="sample-merge-form"  action="${CTX}/ftp_file_merge.json" method="post" class="am-form tpl-form-border-form" style="padding-top:0;">
                    <input type="hidden" name="id" value="" />
                    <input type="hidden" name="append_file" value="" />
                    <input type="hidden" name="rawdata" value="" />
                    <div class="am-form-group">
                        <label for="sample_name" class="am-u-sm-2 am-form-label">样本号<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-9">
                            <input type="text" class="tpl-form-input" name="sample_name" id="sample_name" value="" placeholder="请输入样本号" />
                            <small></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="original_file" class="am-u-sm-2 am-form-label">原样本文件<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-9">
                            <select id="original_file" name="original_file" data-placeholder="请选择原样本文件" class="chosen" style="width:500px;">
                                <option value=""></option>
                                <c:forEach var="file" items="${original_files}"><option id="original_file-${file["md5"]}" value="${file["store_path"]}">${file["file_name"]}</option></c:forEach>
                            </select>
                            <small></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="sample_name" class="am-u-sm-2 am-form-label">Nova<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-9">
                            <input type="text" class="tpl-form-input" name="nova" id="nova" value="" placeholder="请输入样本号" readonly="readonly" />
                            <small>系统自动加1</small>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        文件名：<input id="file_name" class="filter-input" type="text" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;" />
        <div id="search-btn" class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-search"></i> 查询
        </div>
    </div>
    <div class="toolbar">

    </div>
</script>


<script type="text/javascript">

    $("select.chosen").chosen({search_contains:true});
    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }



    $(function(){
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/ftp_file_list.json?str-directory-eq=cat",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                pageSizes: true,
                buttonCount: 5
            },
            filterable: {
                extra: false
            },
            toolbar: kendo.template($("#toolbar").html()),
            columns: [
                // {field:"project_no",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"file_name",title:"文件名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"store_path",title:"存储路径",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"file_size",title:"文件大小",width:"80px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"sample_name",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"rawdata",title:"Rawdata",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"nova",title:"Nova号",filterable:false,width:"60px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"memo",title:"状态",filterable:false,width:"120px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:left"}},
                {title:"上传时间",width:"120px",template : "#= kendo.toString(new Date(modified_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {command:[{text: "合并样本", click: merge},], title: " ", width: "120px"  }
            ]
        });

        $("#search-btn", $grid).click(function () {
            $grid.data( "kendoGrid").dataSource.read({
                "str-file_name-lk": $("#file_name", $grid).val()
            });
        });

        var $dialog = $("#sample-cat-dialog").kendoDialog({
            width: "680px",
            height:"500px",
            title: "样本合并",
            visible: false,
            actions: [{
                text: '合并',
                primary: true,
                action: function() {
                    $("#sample-merge-form").ajaxSubmit({
                        dataType: "json",
                        success: function(ret) {
                            if (ret.errcode != 0) {
                                alert( "[" + ret.errcode + "]: " + ret.errmsg);
                                return;
                            }
                            $grid.data( "kendoGrid").dataSource.read({
                            });
                            $dialog.close();
                        }
                    });

                }
            },{
                text: '关闭',
                primary: false,
                action: function(){ $dialog.close();}
            }],
            closable: false
        }).data("kendoDialog");



        function merge(e) {
            var row = this.dataItem( $(e.currentTarget).closest("tr"));
            $("#sample_name").val( row["sample_name"] + "01");
            var $option = $("#original_file-" + row["original_file"]);
            var $select = $("#original_file");

            $("input[name=id]").val( row["id"]);
            $("input[name=append_file]").val( row["store_path"]);
            $("input[name=nova]").val( row["max_nova"]);
            $("input[name=rawdata]").val( row["rawdata"]);
            $select.val( $option.attr( "value"));
            $select.trigger("liszt:updated");
            $dialog.open();
        }
    });
</script>

</body>

</html>