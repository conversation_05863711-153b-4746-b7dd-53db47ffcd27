<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<jsp:include page="/include/include-taglib.jsp" />
<!-- <jsp:include page="/include/include-taglib.jsp" /> -->
<style>
    .theme-white .tpl-header-fluid{background:#0051a3;border:0 none;}
    .theme-white .tpl-header-navbar a{color:#fff;}
    .theme-white .tpl-header-switch-button{background:#0051a3;border:0 none;color:#fff;}
    .theme-white .left-sidebar{background: #F0F1F3;}
    .theme-white .tpl-header-logo{background:#0051a3 url(${CTX}/shca/image/logo.png?v=1) no-repeat center; }
    @media screen and (max-width: 1024px) {
        .tpl-header-navbar-welcome {
            display: list-item !important;
        }
        .tpl-content-wrapper {
            margin-left: 240px !important;
        }

        .tpl-header-fluid {
            margin-left: 240px !important;
        }
        .tpl-header-logo {
            float: left;
            width: 240px !important;
        }
    }
</style>
<header>
    <!-- logo -->
    <div class="am-fl tpl-header-logo" style="padding-left:1px;">

    </div>
    <!-- 右侧内容 -->
    <div class="tpl-header-fluid" style="padding-left:1px;">
        <!-- 侧边切换 -->
        <span style="height:57px;line-height:70px;font-size: 14px;color:#fff">V1.0506</span>
        <%--<div class="am-fl tpl-header-switch-button am-icon-list">--%>
                    <%--<span>--%>

                <%--</span>--%>
        <%--</div>--%>
        <!-- 搜索 -->
        <div class="am-fl tpl-header-search">
            <form class="tpl-header-search-form" action="javascript:;">
                <%--  <button class="tpl-header-search-btn am-icon-search"></button>
                  <input class="tpl-header-search-box" type="text" placeholder="搜索内容...">--%>
            </form>
        </div>
        <!-- 其它功能-->
        <div class="am-fr tpl-header-navbar">
            <ul>
                <!-- 欢迎语 -->
                <li class="am-text-sm tpl-header-navbar-welcome">
                    <a href="javascript:;">欢迎你, <span>${sessionScope["__sessionUser"].name}</span> </a>
                </li>



                <!-- 退出 -->
                <li class="am-text-sm">
                    <a href="${CTX}/logout.htm">
                        <span class="am-icon-sign-out"></span> 退出
                    </a>
                </li>
            </ul>
        </div>
    </div>
</header>
<!-- 风格切换 -->
<%--<div class="tpl-skiner">--%>
<%--<div class="tpl-skiner-toggle am-icon-cog">--%>
<%--</div>--%>
<%--<div class="tpl-skiner-content">--%>
<%--<div class="tpl-skiner-content-title">--%>
<%--选择主题--%>
<%--</div>--%>
<%--<div class="tpl-skiner-content-bar">--%>
<%--<span class="skiner-color skiner-white" data-color="theme-white"></span>--%>
<%--<span class="skiner-color skiner-black" data-color="theme-black"></span>--%>
<%--</div>--%>
<%--</div>--%>
<%--</div>--%>