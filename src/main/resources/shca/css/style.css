/*
Template Name: Admin Lab Dashboard build with Bootstrap v2.3.1
Template Version: 1.2
Author: <PERSON><PERSON><PERSON>
Website: http://thevectorlab.net/
*/

/* body styles */
body {
    color: #000;
    font-family: 'Arial';
    padding: 0px !important;
    margin: 0px !important;
    font-size:13px;
}

/* fix for font awesome icon classes */
[class^="icon-"], [class*=" icon-"],[class^="icon-"]:hover, [class*=" icon-"]:hover {
    background: none !important;
}

/* hide by default, used to handle elements to show only for ie8*/
.visible-ie8 {
    display: none;
}

@font-face {
    font-family: 'MyriadPro-Bold';
    src: url('../font/myriadprobold.eot');
    src: url('../font/myriadprobold.eot?#iefix') format('embedded-opentype'),
    url('../font/myriadprobold.woff') format('woff'),
    url('../font/myriadprobold.ttf') format('truetype'),
    url('../font/myriadprobold.svg#myriadprobold') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'MyriadPro-It';
    src: url('../font/myriadproit.eot');
    src: url('../font/myriadproit.eot?#iefix') format('embedded-opentype'),
    url('../font/myriadproit.woff') format('woff'),
    url('../font/myriadproit.ttf') format('truetype'),
    url('../font/myriadproit.svg#myriadproit') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'MyriadPro-Light';
    src: url('../font/myriadprolight.eot');
    src: url('../font/myriadprolight.eot?#iefix') format('embedded-opentype'),
    url('../font/myriadprolight.woff') format('woff'),
    url('../font/myriadprolight.ttf') format('truetype'),
    url('../font/myriadprolight.svg#myriadprolight') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'MyriadPro-Regular';
    src: url('../font/myriadproregular.eot');
    src: url('../font/myriadproregular.eot?#iefix') format('embedded-opentype'),
    url('../font/myriadproregular.woff') format('woff'),
    url('../font/myriadproregular.ttf') format('truetype'),
    url('../font/myriadproregular.svg#myriadproregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

/* general typography*/
h3 small, h2 small, h5 small {
    color: #868686;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: normal;
}


h1.block, h2.block, h3.block, h4.block, h5.block, h6.block {
    padding-bottom: 10px;
}

.page-title {
    font-size: 28px;
    display: block;
    font-weight: normal !important;
    margin: 13px 0px 8px 0px;
    font-family: 'MyriadPro-Light';
}
.page-title small {
    font-size: 14px;
}

/* general tools */
img.center {
    text-align:center;
}
.phone-margin-top-5:before {
    display: block;
    margin-top: 5px;
}
.no-padding {
    padding: 0px !important;
}
.no-margin {
    margin: 0px !important;
}
.no-bottom-space {
    padding-bottom:0px !important;
    margin-bottom: 0px !important;
}
.no-top-space {
    padding-top:0px !important;
    margin-top: 0px !important;
}
.block-margin-bottom-5 {
    display: inline-block;
    margin-bottom: 5px;
}
.hide {
    display: none;
}
.bold {
    font-weight:bold;
}

.fix-margin {
    margin-left: 0px !important
}

.border {
    border: 1px solid #ddd
}

.small {
    font-size: 11px !important;
}

.btn-top-space {
    margin-top: 5px !important;
}

.italic {
    font-style: italic !important;
}

i.big {
    font-size: 20px;
}

i.warning {
    color: #E74955;
}

i.critical {
    color: #22878E;
}

i.normal {
    color: #A5D16C;
}

hr {
    margin: 15px 0;
    border: 0;
    border-top: 1px solid #E0DFDF;
    border-bottom: 1px solid #FEFEFE;
}

i.icon, a.icon {
    color: #999;
}

a.icon:hover {
    text-decoration: none;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    -ms-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
    opacity: .4;
}

a.icon.huge i{
    font-size: 16px !important;
}

.space5 {
    display: block;
    height: 5px !important;
    clear: both;
}

.space7 {
    height: 7px !important;
    clear: both;
}

.space10 {
    height: 10px !important;
    clear: both;
}

.space12 {
    height: 12px !important;
    clear: both;
}

.space15 {
    height: 15px !important;
    clear: both;
}

.space20 {
    height: 20px !important;
    clear: both;
}

.mtop5 {
    margin-top: 5px
}

.mtop7 {
    margin-top: 7px
}

.mtop10 {
    margin-top: 10px
}

.no-text-shadow {
    text-shadow: none !important;
}

.notify-row {
    float: left;
    padding: 5px;
}

/*fix outlines on click*/
a,a:focus, a:hover, a:active {
    outline: 0;
}

/*logo*/
#header .brand {
    background:#333333;
    margin-top: 0px !important;
    opacity:0.6;
    filter:alpha(opacity=60);
    padding: 18px 35.5px;
    width: 144px;
}

/* header nav bar*/
#header.navbar {
    min-width: 0px;
}
#header .navbar .nav > li > a{
    padding:0px !important;
}

.navbar .nav > li > a {
    padding: 10px;
}

#header .navbar-inner .nav > li {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

#header .navbar-inner li.dropdown .dropdown-toggle i {
    font-size: 20px;
}

#header .navbar-inner li.dropdown .dropdown-toggle .label {
    position: relative;
    top:-3px;
    font-size: 9px !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    margin-top: 0px !important;
    display: inline-block !important;
}

#header .navbar-inner .nav .dropdown-toggle:hover, .navbar-inner .nav .dropdown.open .dropdown-toggle {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

#header.navbar-inverse .btn-navbar {
    margin-top: 6px;
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #e9ebec;
    background-image: -moz-linear-gradient(top, #e9ebec, #caced1);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e9ebec), to(#caced1));
    background-image: -webkit-linear-gradient(top, #e9ebec, #caced1);
    background-image: -o-linear-gradient(top, #e9ebec, #caced1);
    background-image: linear-gradient(to bottom, #e9ebec, #caced1);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffcaced1', endColorstr='#ffcaced1', GradientType=0);
    border-color: #caced1 #caced1 black;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
}

#header.navbar-inverse .btn-navbar:hover {
    background-color: #caced1;
}

#header .navbar-inner {
    top:0px;
    width: 100%;
    margin: 0px !important;
    margin-bottom: -2px !important;
    border-top: 0px !important;
    border-left: 0px !important;
    border-right: 0px !important;
    padding: 0px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

#header.navbar-inverse .navbar-inner {
    height: 60px;
    border:none !important;
}

#header.navbar-inverse .divider-vertical {
    height: 40px;
}

#header .navbar-search {
    margin-left: 110px;
}

#header .top-nav .dropdown-menu {
    margin-top: 3px;
}

.top-menu {
    padding: 5px;
}

.navbar .nav .dropdown-toggle .caret {
    margin-top: 12px;
}

.top-nav .nav > li > a > img {
    border-radius: 20px;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
}

.navbar-inverse .navbar-search .search-query {
    background-color: #FFFFFF;
    border: 0 none;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
    color: #333333;
    outline: 0 none;
    padding: 5px 15px;
    text-shadow: none;

}

/* main container */
#container {

}

.fixed-top #container {
    margin-top: 60px;
}

/* i8 fix for form input height in fluid rows */
.ie8 .row-fluid [class*="span"] {
    min-height: 20px !important;
}

/* body container */
#login-body {
    background-color: #f7f7f7  !important;
	background-image: url("../img/body-bg.png") !important;
}

#main-content {
    margin-top: 0px;
    margin-left: 215px;
    min-height: 800px;
    background: #f7f7f7 url("../img/body-bg.png");
}

/* page container */
.sidebar-toggler {
    -webkit-border-radius: 15px 0px 0px 15px;
    -moz-border-radius: 15px 0px 0px 15px;
    border-radius: 15px 0px 0px 15px;
    cursor: pointer;
    display: block;
    float: right;
    margin-top: 12px;
    width: 23px;
    height: 25px;
    background: #e9ebec url("../img/body-bg.png");
}

.sidebar-toggler:before {
    margin: 2px 2px 7px 8px;
    display: block;
    font-size: 18px;
    font-family: FontAwesome;
    height: auto;
    content: "\f104";
    font-weight: 300;
    text-shadow:none;
}

.sidebar-toggler.closed:before,
.sidebar-closed .sidebar-toggler:before {
    margin: 2px 2px 7px 10px;
    content: "\f105";
}

.sidebar-closed > #sidebar > ul {
    display: none;
}

.sidebar-closed #main-content {
    margin-left: 25px;
}

.sidebar-closed #sidebar {
    margin-left: -190px;
}

/* sidebar menu */

[class^="icon-"], [class*=" icon-"] {
    margin-top: 0;
}

ul.sidebar-menu span.icon-box  {
    background: url("../img/side-bar-list-bg.png") !important;
    padding: 20px;
    margin-right: 10px;
}

#sidebar .navbar-search {
    border: 0px;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}

#sidebar.closed {
    display: none;
}

#sidebar > ul {
    list-style: none;
    margin: 0;
    padding: 0;
    margin: 0;
    padding: 0;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
}

#sidebar > ul > li {
    display: block;
    margin: 0 0 1px 0;
    padding: 0;
    border: 0px;
    background: url("../img/side-bar-list-bg.png");
    line-height: 30px;
}

#sidebar > ul > li > a {
    display: block;
    position: relative;
    margin: 0;
    border: 0px;
    padding: 0px 15px 0px 0;
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 0px !important;
    text-decoration: none;
    font-size: 14px;
    font-weight: normal;
}

#sidebar > ul > li a i {
    color:#eaeaea;
    font-size: 18px;
}

#sidebar > ul > li.active > a{
    border: none;
}


#sidebar > ul > li.active > a .arrow {
    margin-right: 1px;
}

#sidebar > ul > li.active > a .arrow.open {
    margin-right: 0px;
}


#sidebar ul > li > a .arrow {
    float: right;
    margin-top: 27px;
    margin-right: 5px;
    width: 0;
    height: 0;
    border-left: 4px solid #f3f3f3;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

#sidebar > ul > li > a .arrow.open {
    float: right;
    margin-top: 27px;
    margin-right: 3px;
    width: 0;
    height: 0;
    border-top: 5px solid #f3f3f3;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
}

#sidebar > ul > li > ul.sub {
    display: none;
    list-style: none;
    clear: both;
    margin: 0px;
}

#sidebar > ul > li.active > ul.sub {
    display: block;
}

#sidebar > ul > li > ul.sub > li {
    background: none !important;
    padding: 0px;
}

#sidebar > ul > li > ul.sub > li > a {
    display: block;
    position: relative;
    padding: 10px 10px 10px 60px;
    color: #ccc;
    text-decoration: none;
    text-shadow: 0 1px 1px #000;
    font-size: 13px;
    font-weight: normal;
}

#sidebar > ul > li > ul.sub > li.active > a, #sidebar > ul > li > ul.sub > li > a:hover {

    background: url("../img/submenu_hover.png") !important;
}

/* ie8, ie9 fixes */
.ie8 #sidebar .search-query, .ie8 #header .search-query {
    padding-top: 7px !important;
    padding-bottom: 5px !important;
}

.ie9 #sidebar .search-query, .ie9 #header .search-query {
    padding-bottom: 0px !important;
    height: 24px;
}

.ie9 #sidebar > ul > li.active > a .triangle {
    right: -1px;
}

/* ie10 fixes */
.ie10 #header .search-input-area > i, .ie10 #sidebar .search-input-area > i {
    top:-2px !important;
}

/* ie8 fixes */
.ie8 #sidebar {
    position: absolute;
    width: 215px;
}

.ie8 #sidebar ul{
    margin-top:47px;
    width: 215px;
}

/* footer container */
#footer {
    padding: 8px 20px 5px 20px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    /*background: url("../img/top-bg.jpg");*/
}

#footer .go-top {
    display: block;
    font-size: 12px;
    text-decoration: none;
    color: #fff;
    cursor: pointer;
    margin-top: -3px;
    margin-right: 0px;
    margin-bottom: 0px;
    font-size: 16px;
    background-color: #111;
    opacity: 0.8;
    padding: 3px 5px 2px 6px;
    opacity:0.4;
    filter: alpha(opacity = 40);
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
}

/* custom wells */
.well {
    background-color: #fafafa;
    border: 1px solid #ddd;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}

.well.mini {
    padding: 7px !important;
}


.tab-content {
    padding: 0px;
    overflow: hidden;
}

/* general form */
form legend {
    margin: 15px 0px 10px 0px !important;
}

.form-actions {
    background-color: #fff ;
}

.widget-body.form form {
    margin: 0px !important;
    padding: 0px !important;
}

.widget-body.form .control-group:last-child {
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;
}

.widget-body.form .form-actions{
    margin-left:-15px !important;
    margin-right:-15px !important;
    margin-top: 20px !important;
    margin-bottom: -15px !important;
    margin-top: 20px;
    padding-left: 195px;
    -webkit-border-radius: 0px 0px 4px 4px;
    -moz-border-radius: 0px 0px 4px 4px;
    border-radius: 0px 0px 4px 4px;
}

.widget-body .dataTables_info, .widget-body .dataTables_paginate {
    margin-top: 5px !important;
    padding-bottom: 0px !important;
    margin-bottom: -4px !important;
}

.widget-body .table {
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;
}

.widget-title > h4, .breadcrumb > li> a:hover, .chats li.in .name {
    color: #4C4C4C;
    text-shadow: 0 1px 1px #FFFFFF;
}

/* custom form input error states with icons */
.input-icon input {
    padding-right: 25px !important;
}

.input-icon .input-error, .input-icon .input-warning, .input-icon .input-success {
    display: inline-block !important;
    position: relative !important;
    top: 4px;
    right: 25px !important;
    font-size: 16px;
}

.input-icon .input-error {
    color:#B94A48;
}
.input-icon .input-warning {
    color: #C09853;
}
.input-icon .input-success {
    color: #468847;
}

/* custom breadcrumb */
.breadcrumb {
    background: none;
    margin-left: -15px;
}
.breadcrumb > li {
    height: 33px;
    line-height: 33px;
    background: url("../img/bread-crumb-bg.jpg") repeat-x;
    float: left;
    margin-bottom: 25px;
    padding: 0  0 0 8px;
}
.breadcrumb > li> a{
    color: #737373;
}
.breadcrumb > li> a:hover{
    text-decoration: none;
}

.breadcrumb > li > .divider {
    display: inline-block;
    padding: 0px;
    width: 33px;
    height: 33px;
    line-height: 33px;
    background: url("../img/bread-crumb-divider.jpg") no-repeat;
}
.breadcrumb > li > .divider-last {
    display: inline-block;
    padding: 0px;
    width: 19px;
    height: 33px;
    line-height: 33px;
    background: url("../img/bread-crumb-last.jpg") no-repeat;
}
.breadcrumb .tooltip {
    text-shadow:none !important;
}

/*general search  */
.breadcrumb > li.search-wrap {
    background: none !important;
    float: right;
}

.breadcrumb .search-input-area {
    float: right;
    position: relative;
    width: 94%;
}

.search-input-area input.search-query {
    border: 0px !important;
    -webkit-box-shadow: 0 0px 5px #ccc;
    -moz-box-shadow: 0 0px 5px #ccc;
    box-shadow: 0 0px 5px #ccc;
    padding-left: 8px;
    padding-right: 20px;
}
.search-input-area input:focus.search-query {
    outline: 0;
    box-shadow: 0 0 8px rgba(82, 168, 236, 0.6) !important;
    -webkit-box-shadow: 0 0 8px rgba(82, 168, 236, 0.6) !important;
    -moz-box-shadow: 0 0 8px rgba(82, 168, 236, 0.6) !important;
}

.breadcrumb .search-input-area > i, #sidebar .search-input-area > i {
    cursor: pointer;
    display: inline-block !important;
    font-size: 18px;
    position: absolute !important;
    right: -10px !important;
    top: 7px !important;
}

/* widget container */
.sortable .widget .widget-title {
    cursor: move;
}

.sortable-box-placeholder {
    background-color: #f5f5f5;
    border: 1px dashed #DDDDDD;
    display: block;
    margin-top: 0px !important;
    margin-left: 1%;
    margin-right: 0.6%;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}
.sortable-box-placeholder * {
    visibility:hidden;
}

.widget {
    background:#fff;
    clear: both;
    margin-bottom: 20px;
    margin-top: 0;
    -webkit-box-shadow: 0 0px 5px #ddd;
    -moz-box-shadow: 0 0px 5px #ddd;
    box-shadow: 0 0px 5px #ddd;
}

.widget-title {
    background: #f2f2f2;
    background-image: -webkit-gradient(linear, 0 0%, 0 100%, from(#ffffff), to(#f2f2f2));
    background-image: -webkit-linear-gradient(top, #ffffff 0%, #f2f2f2 100%);
    background-image: -moz-linear-gradient(top, #ffffff 0%, #f2f2f2 100%);
    background-image: -ms-linear-gradient(top, #ffffff 0%, #f2f2f2 100%);
    background-image: -o-linear-gradient(top, #ffffff 0%, #f2f2f2 100%);
    /*background-image: -linear-gradient(top, #ffffff 0%, #f2f2f2 100%);*/
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f2f2f2',GradientType=0 );
    height: 36px;
    border-bottom: 1px solid #fff;
}

.widget-title > h4 {
    float: left;
    font-size: 13px;
    font-weight: bold;
    padding: 12px 11px 10px 15px;
    line-height: 12px;
    margin: 0;
}

.widget-title > h4 i {
    font-size: 14px;
    margin-right: 2px;
}

.widget-title span.tools {
    border-left: 1px solid #E0DEDE;
    float: right;
    margin: 2px 0 0;
    padding: 6px 5px 6px 10px;
}

.widget-title span.tools > a {
    display: inline-block;
    margin-right: 5px;
    color: #979797;
    font-size: 14px;
    text-decoration: none;
}

.widget-title span.tools > a:hover {
    text-decoration: none;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    -ms-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
    opacity: .6;
}

.widget-title .btn-group {
    margin-right:5px;
    margin-top: -2px;
}

.widget-title .btn-group .caret {
    margin-top: 8px;
    margin-left: 3px;
}

.widget-body {
    padding: 15px 15px;
    -webkit-border-radius: 0px 0px 3px 3px;
    -moz-border-radius: 0px 0px 3px 3px;
    border-radius: 0px 0px 3px 3px;
}

/* charts & stats */
.chart, .pie, .bars {
    height: 300px;
}

.stat {
    margin: 0px;
    padding: 0px;
}

.item-list.table .percent {
    width: 30px;
    float: right;
    margin-right: 10px;
    margin-top: 3px;
}

.item-list.table .title {
    padding-top: -5px;
}

.stat .title {
    margin-left: 10px;
    margin-right: 10px;
    font-size1: 13px;
}

.stat.good .percent  {
    color: #52e136;
    font-size: 16px;
    font-weight: bold;
}

.stat.bad .percent {
    color: #d12610;
    font-size: 16px;
    font-weight: bold;
}

.stat.ok .percent {
    color: #37b7f3;
    font-size: 16px;
    font-weight: bold;
}

/* general list for item with picture */
ul.item-list li .pic {
    height: 50px;
    width: 50px;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
    -webkit-border-radius: 2px !important;
    -moz-border-radius: 2px !important;
    border-radius: 2px !important;
}
ul.item-list {
    margin: 0px;
    list-style: none;
}
ul.item-list li {
    padding: 5px 0;
    list-style: none;
    border-top: 1px solid white;
    border-bottom: 1px solid #EBEBEB;
    font-size: 12px;
}
ul.item-list li:first-child {
    border-top: none;
    border-bottom: 1px solid #EBEBEB;
}
ul.item-list li:last-child {
    border-top: none;
    border-bottom: none;
}
ul.item-list li .label {
    margin-right: 5px;
}

/* general purpose block with css3 gradient background */
.block {
    line-height: 18px;
    margin: 0 0 20px 0;
    padding: 10px;
    text-align: center;
}

/* metro dashboard stats */
.metro-overview-cont {
    padding-top:0px;
    margin-bottom: 15px;
}
.metro-overview {
    clear: both;
    padding: 10px 10px 0px 10px;
    margin: 0px;
    margin-bottom: 5px;
    box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -moz-box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -webkit-box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
}

.metro-overview .display {
    margin-right: 5px;
    float: left;
    font-size: 30px;
    color: #fff;
}

.metro-overview .percent {
    color: #fff;
    font-size: 12px;
}

.metro-overview .details {
    color:#fff;
    text-align:right;
}

.metro-overview .details .title {
    color: #fff;
    font-size: 12px;
    font-weight: normal;
    margin-bottom: 6px;
}
.metro-overview .details .title i {
    color: #fff;
    margin-right: 2px;
}
.metro-overview .details .numbers {
    color: #fff;
    font-size: 20px;
    margin-bottom: 6px;
}
.metro-overview .progress {
    height: 5px;
    margin-bottom:10px !important;
}

/* mini chart and bar containers  */
.bar-chart {display: none}
.line-chart {display: none}

/* custom label and badges */
.notify-row .badge {
    position: absolute;
    top: -5px;
    z-index: 100;
    right: 1px;
}
.badge {
    -webkit-border-radius: 10px!important;
    -moz-border-radius: 10px !important;
    border-radius: 10px !important;
    padding: 2px 6px;
}
.label {
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 0px !important;
    text-shadow: none !important;
    padding: 5px !important;
}

.label-success, .badge-success {
    background-color: #a5d16c;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #a5d16c), color-stop(100%, #a5d16c));
    background-image: -webkit-linear-gradient(top, #a5d16c, #a5d16c);
    background-image: -moz-linear-gradient(top, #a5d16c, #a5d16c);
    background-image: -ms-linear-gradient(top, #a5d16c, #a5d16c);
    background-image: -o-linear-gradient(top, #a5d16c, #a5d16c);
    background-image: linear-gradient(top, #a5d16c, #a5d16c);
}

.label-warning, .badge-warning {
    background-color: #fcb322;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #fcb322), color-stop(100%, #fcb322));
    background-image: -webkit-linear-gradient(top, #fcb322, #fcb322);
    background-image: -moz-linear-gradient(top, #fcb322, #fcb322);
    background-image: -ms-linear-gradient(top, #fcb322, #fcb322);
    background-image: -o-linear-gradient(top, #fcb322, #fcb322);
    background-image: linear-gradient(top, #fcb322, #fcb322);
}

.label-important, .badge-important {
    background-color: #e74955;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #e74955), color-stop(100%, #e74955));
    background-image: -webkit-linear-gradient(top, #e74955, #e74955);
    background-image: -moz-linear-gradient(top, #e74955, #e74955);
    background-image: -ms-linear-gradient(top, #e74955, #e74955);
    background-image: -o-linear-gradient(top, #e74955, #e74955);
    background-image: linear-gradient(top, #e74955, #e74955);
}

.label-info, .badge-info {
    background-color: #32c2cd;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #32c2cd), color-stop(100%, #32c2cd));
    background-image: -webkit-linear-gradient(top, #32c2cd, #32c2cd);
    background-image: -moz-linear-gradient(top, #32c2cd, #32c2cd);
    background-image: -ms-linear-gradient(top, #32c2cd, #32c2cd);
    background-image: -o-linear-gradient(top, #32c2cd, #32c2cd);
    background-image: linear-gradient(top, #32c2cd, #32c2cd);
}

.label-mini {
    font-size: 11px;
}

/*progress bar*/
.progress {
    height: 10px;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

/*pre loader list */
.list_items > li {
    padding: 0 16px 12px 0;
}
/*pagination */
.pagination {
    margin: 10px 0;
}

/*slider*/
.slider {
    margin-bottom: 40px;
    margin-top: 20px;
}
.slider label {
    cursor: text;
}
.jslider .jslider-value {
    border: 1px solid #CDCDCD;
    border-radius: 10px !important;
    -moz-border-radius: 10px !important;
    -webkit-border-radius: 10px !important;
}
.jslider .jslider-value, .jslider .jslider-label, .jslider .jslider-scale ins {
    font-size: 12px !important;
}
.jslider .jslider-bg i {
    height: 6px !important;
}
.jslider .jslider-value {
    padding: 3px 5px 0 !important;
    top: -28px !important;
}
.jslider_round_plastic .jslider-pointer {
    height: 23px !important;
    margin-left: -12px !important;
    width: 23px !important;
}
.jslider .jslider-pointer {
    background-position: 0 -58px !important;
    top: -6px !important;
}
.jslider .jslider-pointer {
    background-position: 0 -58px !important;
    top: -6px !important;
}
.jslider .jslider-pointer-hover {
    top: -8px !important;
    background-position: -22px -56px !important;
}
.jslider .jslider-value {padding: 3px 5px !important; }

/*font awesome icon style*/

.icon-style-list ul.unstyled li {
    /*border: 1px solid #EAEAEA;*/
    font-family: arial;
    line-height: 30px;
    margin-bottom: 10px;
    padding: 0 10px;
    font-size: 13px;
    background: #eeeeee;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
}

.icon-style-list ul.unstyled li i {
    font-size: 16px;
    padding-right: 5px;
}

.icon-style-list h3, .icon-style-list h4 {
    font-family: Arial;
}

ul.icons {
    list-style-type: none;
    text-indent: -0.75em;
    margin-left: 25px;
}
/*alpha listing*/
.upper-alpha {
    list-style: upper-alpha;
}
/*roman list*/
.roman-list {
    list-style: upper-roman;
}
/*glyphicons icon style*/
.the-icons {
    list-style: none;
    margin-left: 0;
}

.the-icons li {
    float: left;
    line-height: 25px;
    width:20%;
    line-height: 30px;
}

/*buttons style*/

.btn{
    border-radius: 0;
    box-shadow:0 0 3px rgba(0, 0, 0, 0.2) inset;
    -moz-box-shadow:0 0 3px rgba(0, 0, 0, 0.2) inset;
    -webkit-box-shadow:0 0 3px rgba(0, 0, 0, 0.2) inset;
    background: #e8e8e8;
    outline: none;
}
.btn:hover{
    border-radius: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
}
.btn-group > .btn:first-child,
.btn-group > .btn:last-child,
.btn-group > .dropdown-toggle,
.btn-group-vertical > .btn:first-child,
.btn-group-vertical > .btn:last-child {
    border-radius: 0px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
}

.btn-primary{
    background: #2fade7;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary.disabled, .btn-primary[disabled] {
    background-color: #1DA4E7;
}

.btn-success{
    background: #87bb33;
}
.btn-success:hover, .btn-success:active, .btn-success.active, .btn-success.disabled, .btn-success[disabled],
.btn-group.open .btn-success.dropdown-toggle{
    background-color: #70BB2E;
}
.btn-info{
    background: #22c0cb;
}
.btn-info:hover, .btn-info:active, .btn-info.active, .btn-info.disabled, .btn-info[disabled],
.btn-group.open .btn-info.dropdown-toggle{
    background-color: #15B4CB;
}
.btn-warning{
    background: #fb9800;
}
.btn-warning:hover, .btn-warning:active, .btn-warning.active, .btn-warning.disabled, .btn-warning[disabled],
.btn-group.open .btn-warning.dropdown-toggle{
    background-color: #FB8E13;
}
.btn-danger{
    background: #dc5d3a;
}
.btn-danger:hover, .btn-danger:active, .btn-danger.active, .btn-danger.disabled, .btn-danger[disabled],
.btn-group.open .btn-danger.dropdown-toggle{
    background-color: #DC4E3B;
}
.btn-inverse{
    background: #484848;
}
.btn-inverse:hover, .btn-inverse:active, .btn-inverse.active, .btn-inverse.disabled, .btn-inverse[disabled],
.btn-group.open .btn-inverse.dropdown-toggle{
    background-color: #292929;
}
.btn-link {
    background: none;
    box-shadow: none;
}

.input-prepend .add-on:first-child, .input-prepend .btn:first-child,
.input-prepend.input-append .add-on:first-child, .input-prepend.input-append .btn:first-child,
.input-prepend.input-append .add-on:last-child, .input-prepend.input-append .btn:last-child,
.input-append .add-on:last-child, .input-append .btn:last-child, .input-append .btn-group:last-child > .dropdown-toggle{
    border: 1px solid #ddd;
    border-radius: 0px;
}

.uneditable-input {
    width: 135px;
}

.switch-form input {
    width: 110px;
}


/*form element */

textarea, select, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .uneditable-input, .fileupload-new .input-append .btn-file {
    border: 1px solid #ddd;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.17) inset;
    transition: border 0.2s linear 0s, box-shadow 0.2s linear 0s;
    border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
}

textarea:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .uneditable-input:focus {
    background: #fff;
    border:1px solid #DDDDDD;
    box-shadow: none;
}

.form-horizontal .control-label {
    text-align: left;
}

/* icon buttons */
.icon-btn {
    height: 70px;
    width: 50px;
    margin: 10px 0px 10px 0px;
    padding: 16px 0px 0px 0px;
    font-size: 10px;
    background-color: #fff !important;
    -webkit-box-shadow: 0 0px 5px #ddd !important;
    -moz-box-shadow: 0 0px 5px #ddd!important;
    box-shadow: 0 0px 5px #ddd !important;
    display:block !important;
    color: #646464 !important;
    text-align: center;
    cursor: pointer;
    position: relative;
    -webkit-transition: all 0.3s ease !important;
    -moz-transition: all 0.3s ease !important;
    -ms-transition: all 0.3s ease !important;
    -o-transition: all 0.3s ease !important;
    transition: all 0.3s ease !important;
}

.ie8 .icon-btn:hover {
    filter: none !important;
}

.icon-btn:hover {
    background: #fff !important;
    text-decoration: none !important;
    box-shadow: none !important;
    color: #444 !important;
    -webkit-transition: all 0.3s ease !important;
    -moz-transition: all 0.3s ease !important;
    -ms-transition: all 0.3s ease !important;
    -o-transition: all 0.3s ease !important;
    transition: all 0.3s ease !important;
    box-shadow:0px 0px 0px #fff , 0 0 1px rgba(0, 0, 0, .6) inset !important;
}

.icon-btn i {
    font-size: 20px;
    color: #777 !important;
}

.icon-btn div {
    margin-top: 5px;
    margin-bottom: 20px;
    font-size: 12px !important;
    font-family: Arial;
}

.icon-btn .badge {
    position: absolute;
    font-size: 10px !important;
    top: 26px;
    right: -8px;
    height: 14px;
    padding: 3px 7px;
    color: white !important;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    text-shadow: none;
    font-family: Arial;
}

.icon-btn:hover .badge {
    -webkit-transition: all 0.3s ease !important;
    -moz-transition: all 0.3s ease !important;
    -ms-transition: all 0.3s ease !important;
    -o-transition: all 0.3s ease !important;
    transition: all 0.3s ease !important;
}

.icon-btn i {
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
}

.icon-btn:hover i {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    color: #fff;
    opacity: 1;
}

/* custom dropdown */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    padding: 0px 0;
    margin: 2px 0 0;
    list-style: none;
    text-shadow: none;
    background-color: #fcfcfc;
    border: 1px solid rgba(0, 2, 1, 0.2);
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    -webkit-box-shadow: 0 0px 0px rgba(0, 2, 1, 0.4);
    -moz-box-shadow: 0 0px 0px rgba(0, 2, 1, 0.4);
    box-shadow: 0 0px 0px rgba(0, 2, 1, 0.4);
    -webkit-background-clip: padding-box;
    -moz-background-clipp: padding;
    background-clip: padding-box;
    padding: 0px 0;
    margin:0px;
    list-style: none;
    text-shadow: none;
}

.dropdown-menu.opens-left {
    margin-top: 2px;
    margin-left: -88px;
}

.ie8 .dropdown-menu.opens-left {
    margin-left: -82px;
}

.dropdown-menu.extended {
    top:40px;
    min-width: 160px !important;
    max-width: 300px !important;
    width: 233px !important;
}

.dropdown-menu.extended li a{
    display: block;
    padding: 5px 10px !important;
    clear: both;
    font-weight: normal;
    line-height: 20px;
    white-space: normal !important;
}

.dropdown-menu.extended .arrow{
    top:-14px;
    left: 10px;
    position: absolute;
    margin-top: 6px;
    margin-right: 12px;
    width: 0;
    height: 0;
    border-bottom: 8px solid #f3f3f3;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
}

.dropdown-menu.extended li i{
    margin-right: 3px;
}

.dropdown-menu.extended li a{
    padding: 10px !important;
    background-color: #fafafa !important;
}

.dropdown-menu.extended li p{
    padding: 10px;
    background-color: #eee;
    margin: 0px;
    color: #666;
}

.dropdown-menu.extended li a{
    padding: 7px 0 5px 0px;
    list-style: none;
    border-top: 1px solid white !important;
    border-bottom: 1px solid #EBEBEB !important;
    font-size: 12px;
}
.dropdown-menu.extended li:first-child a {
    border-top: none;
    border-bottom: 1px solid #EBEBEB !important;
}
.dropdown-menu.extended li:last-child a {
    border-top: 1px solid white !important;
    border-bottom: 1px solid #EBEBEB !important;
}

.dropdown-menu.inbox li a .photo img {
    float: left;
    height: 40px;
    width: 40px;
    margin-right: 4px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
}

.dropdown-menu.inbox li a .subject {
    display: block;
}

.dropdown-menu.inbox li a .subject .from {
    font-size: 12px;
    font-weight: bold;
}

.dropdown-menu.inbox li a .subject .time {
    font-size: 11px;
    font-weight: bold;
    font-style: italic;
    position: absolute;
    right: 5px;
}

.dropdown-menu.inbox li a .message {
    display: block !important;
    font-size: 11px;
}

/* star rating */
.rating {
    unicode-bidi: bidi-override;
    direction: rtl;
    font-size: 30px;
}
.rating span.star,
.rating span.star {
    font-family: FontAwesome;
    font-weight: normal;
    font-style: normal;
    display: inline-block;
}
.rating span.star:hover,
.rating span.star:hover {
    cursor: pointer;
}
.rating span.star:before,
.rating span.star:before {
    content: "\f006";
    padding-right: 5px;
    color: #999999;
}
.rating span.star:hover:before,
.rating span.star:hover:before,
.rating span.star:hover ~ span.star:before,
.rating span.star:hover ~ span.star:before {
    content: "\f005";
    color: #87bb33;
}



/* adjust uniform components */
.radio, .checkbox {
    padding-left: 0px !important;
}

.controls > .radio,
.controls > .checkbox {
    display: inline-block;
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 0px !important;
    margin-right: 15px !important;
}

.controls > .radio.line,
.controls > .checkbox.line {
    display: block;
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 5px !important;
}

.controls .text {
    display: block;
    margin-top: 5px;
}

.checkbox  div.checker {
    margin-right: 2px !important;
}

.uploader {
    margin-top: 2px !important;
}

/* item block  */
.item {
    overflow: hidden;
    display: block;
}
.item:hover .zoom-icon{
    opacity:0.5;
    filter: alpha(opacity = 50);
}

/* zoom icon overlay on images */
.zoom {
    cursor: pointer;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 5;
}

.zoom .zoom-icon {
    background-image:url("../img/overlay-icon.png");
    background-color: #222;
    background-repeat: no-repeat;
    background-position: 50%;
    position: absolute;
    width: inherit;
    height: inherit;
    opacity: 0;
    filter: alpha(opacity = 0);
    z-index: 6;
    top:0;
}

/* logo page */
#logo {
    width: 247px;
    margin: 0 auto;
    padding: 15px;
    text-align: center;
}

.login-header {
    background: url("../img/top-bg.jpg") repeat-x;
    border-width: 0;
    height: 60px;
    text-align: center;
}

#login {

    background:#e3e3e3;
    box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -moz-box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -webkit-box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    width: 370px;
    margin: 150px auto 0;
    padding: 20px;
}

.login-btn {
    background: url("../img/login-btn.jpg");
    border: none;
    box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -moz-box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -webkit-box-shadow: 1px 0 1px #FFFFFF, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    font-size: 16px;
    color: #fff;
    text-shadow: none;
    padding: 10px 0;
}

.login-btn:hover {
    opacity: 0.8;
    color: #fff;
}

#login .control-group {
    margin-bottom: -11px;
}

#login .form-actions {
    padding: 20px;
    margin-left: -20px;
    margin-right: -20px;
    margin-bottom: 0px;
    -webkit-border-radius: 0px 0px 4px 4px;
    -moz-border-radius: 0px 0px 4px 4px;
    border-radius: 0px 0px 4px 4px;
    margin-bottom: -37px;
}

#login input {
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}
#login input[type=checkbox] {
    margin-top: -3px;
}
#login span.add-on {
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    padding: 10px;
    color: #828283;
    text-shadow: none;
}

#login input {
    padding: 10px;
}

#login i {
    color: #999 !important;
}

#login #forget-password {
    font-size: 11px;
}

#login-copyright {
    text-align: center;
    width: 250px;
    margin: 0 auto;
    padding: 10px 10px 0 10px;
    color: #999;
    font-size: 11px;
}

.control-wrap {
    width: 266px;
    display: inline-block;
    margin-bottom: 15px;
}

#login .lock {
    width: 100px;
    float: left;
    margin-top: 28px;
    font-size: 110px;
    vertical-align: middle;
}

#login .lock i {
    vertical-align: middle;
}

#forgotform #input-email {
    width: 312px;
}

/* style switcher */
#theme-change {
    position: fixed;
    width: 20px;
    height: 22px;
    overflow: hidden;
    top:70px;
    right: 0px;
    white-space: nowrap;
    padding: 5px 10px 5px 8px;
    background-color: #dcdcdc;
    z-index: 100;
    color: #737373;
    -webkit-border-radius: 5px 0px 0px 5px;
    -moz-border-radius: 5px 0px 0px 5px;
    border-radius: 5px 0px 0px 5px;
}

#theme-change > i {
    font-size: 18px;
    cursor: pointer;
    display: inline-block;
    margin-right: 2px;
    margin-top: 2px;
}

#theme-change:hover, #theme-change > i:hover {
    color: #737373;
}

#theme-change label {
    display: inline-block !important;
}

#theme-change .text {
    margin-right: 2px;
    font-weight: bold;
    font-size: 14px;
}

#theme-change .settings {
    display: none;
}

#theme-change .colors {
}

#theme-change .checker {
    display: inline-block !important;
    margin-top:-1px;
}

#theme-change .colors span {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin: 2px 1px -7px 1px;
    border: 2px solid #ddd;
    cursor: pointer;
}

#theme-change .layout {
    width: 100px;
    margin-top: 7px;
    margin-left: 63px;
    margin-bottom: 5px;
    display: block;
}

#theme-change .colors span.active, #theme-change .colors span:hover {
    border: 2px solid white;
}

#theme-change .colors .color-default {
    background-color: #30c0cb;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
}
#theme-change .colors .color-purple {
    background-color: #7265ae;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
}
#theme-change .colors .color-gray {
    background-color: #4d4d4d;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
}
#theme-change .colors .color-navy-blue {
    background-color: #263849;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
}


/* Circle stats admin lab */

.circle-wrap {
    text-align: center;
    margin-bottom: 20px;
    opacity: 1
}

.turquoise-color {
    background: #4cc5cd;
}

.red-color {
    background: #e17f90;
}

.green-color {
    background: #a8c77b;
}

.gray-color {
    background: #b9baba;
}

.purple-color {
    background: #c8abdb;
}

.blue-color {
    background: #93c4e4;
}

.stats-circle {
    width: 100px;
    height: 100px;
    border-radius: 70px;
    -moz-border-radius: 70px;
    -webkit-border-radius: 70px;
    display: inline-block;
    text-align: center;
    color: #fff;
    margin-bottom: 10px;
    cursor: pointer;

    box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -moz-box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
    -webkit-box-shadow: 1px 0px 1px #fff, 0 0 3px rgba(0, 0, 0, 0.2) inset;
}

.circle-wrap p {
    color: #888888;
    font-size: 13px;
    margin: 0;
}

.circle-wrap p strong {
    display: block;
    font-size: 18px;
    color: #777
}

.stats-circle i{
    display: inline-block;
    font-size: 3.5em;
    margin-top: 26px;
    text-shadow: 1px 1px 1px #999;
    opacity: .6;
}

.stats-circle i {
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
}

.stats-circle:hover i {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    color: #fff;
    opacity: 1;
}

.stats-circle span {
    display: block;
}

/* Square stats */

.square-state {

}

.square-state .icon-btn {
    margin-top: 0;
    margin-bottom: 20px;
}


/* Circle stats */
.knobify {
    border: 0 !important;
    width: 0px;
}
.ie8 .knobify {
    display: none;
}

.circle-stats {
    position: relative;
    margin: 10px 0px 10px 0px;
}

.circle-stat {
    padding:2px;
}

.circle-stat canvas {
}
.circle-stat:before,
.circle-stat:after {
    display: table;
    line-height: 0;
    content: "";
}
.circle-stat:after {
    clear: both;
}

.circle-stat .visual {
    display: inline-block;
    position: relative;
}

.circle-stat .details {
    display:block;
    margin-left: 5px;
    padding-top: 7px;
    clear: both;
    text-align: center;
}

.circle-stat .details .title {
    padding: 0px !important;
    font-size: 13px;
    text-shadow: 0 1px rgba(232, 232, 232, 0.75);
    color: #777;
}

.ie8 .circle-stat .details .title {
    margin-top:5px !important;
}
.ie8 .circle-stat .details {
    padding-top: 0px !important;
    margin-bottom: 5px !important;
}

.circle-stat .details .title i {
    margin-top:2px !important;
    color: #52e136;
    font-size: 16px;
}

.circle-stat .details .title i.down {
    color: #b63625;
}

.circle-stat .details .number {
    margin: 0px !important;
    margin-bottom: 5px !important;
    font-size: 18px;
    padding: 0px;
    font-weight: bold;
    text-shadow: 0 1px rgba(244, 244, 244, 0.85);
    color: #777;
}


/*circle state icon place */

.circle-state-overview .span3:first-child {margin-left: 20px;}

.circle-state-icon {
    background: url("../img/body-bg.png") repeat scroll 0 0 #F7F7F7;
    border-radius: 60px 60px 60px 60px;
    font-size: 38px;
    height: 38px;
    left: 15px;
    opacity: 1;
    padding: 15px;
    position: absolute;
    top: 15px;
    width: 38px;
    z-index: 10;
}
.circle-state-icon .turquoise-color {
    color: #4cc5cd;
}

.circle-state-icon .red-color {
    color: #e17f90;
}

.circle-state-icon .green-color {
    color: #a8c77b;
}

.circle-state-icon .gray-color {
    color: #b9baba;
}

.circle-state-icon .purple-color {
    color: #c8abdb;
}

.circle-state-icon .blue-color {
    color: #93c4e4;
}

/*map stats*/

.map-stat {
    margin: 20px;
    display: block;
}
.map-stat:before,
.map-stat:after {
    display: table;
    line-height: 0;
    content: "";
}
.map-stat:after {
    clear: both;
}

.map-stat .visual {
    width: 70px;
    height: 60px;
    margin-right: 5px;
    display: block;
    float: left;
}

.map-stat .visual i{
    margin-top: 15px;
    display: block;
    font-size: 68px;
    color: #4d4d4d;
}

.map-stat .details {
    display: block;
    float: left;
    margin-left: 5px;
    padding-top: 0px;
}

.map-stat .details .title {
    margin: 0px 0px 5px 0px !important;
    padding: 0px !important;
    font-size: 12px;
    color: #878787;
}

.map-stat .details .title i {
    margin-top:2px !important;
    color: #52e136;
    font-size: 16px;
}

.map-stat .details .title i.down {
    color: #b63625;
}

.map-stat .details .number {
    margin: 0px !important;
    margin-bottom: 7px !important;
    font-size: 42px;
    padding: 0px;
    font-weight: bold;
    color: #35d1fe;
}

/*scroller padding*/
.scroller {
    padding-right: 10px;
}


/*jqvmap changes*/
.jqvmap-zoomin {
    background-color: #666 !important;
}
.jqvmap-zoomout {
    background-color: #666 !important;
}
.vmaps {
    position: relative;
    overflow: hidden;
    height: 300px;
}


/* google maps */
.gmaps {
    height: 300px;
    width: 100%;
}

/* important!  bootstrap sets max-width on img to 100% which conflicts with google map canvas*/
.gmaps img {
    max-width: none;
}

#gmap_static div{
    background-repeat: no-repeat !important;
    background-position: 50% 50% !important;
    height:100%;
    display:block;
    height: 300px;
}

#gmap_routes_instructions {
    margin-top: 10px;
    margin-bottom: 0px;
}

/* advance tables*/
.table-advance {
    margin-bottom: 10px !important;
}

.table-advance thead {
    color: #999;
}

.table-advance thead tr th{
    background-color: #DDD;
    color: #666;
}

.table-advance div.success, .table-advance div.info,
.table-advance div.important, .table-advance div.warning, .table-advance div.danger {
    position: absolute;
    margin-top:5px;
    float: left;
    width: 10px;
    height: 10px;
    margin-right: 20px !important;
}

.table-advance tr td {
    border-left-width: 0px;
}
.table-advance tr td:first-child {
    border-left-width: 1px !important;
}

.table-advance tr td.highlight:first-child a {
    margin-left: 15px;
}

.table-advance td.highlight div.success {
    border-left: 10px solid #A5D16C;
}

.table-advance td.highlight div.info {
    border-left: 10px solid #87ceeb;
}

.table-advance td.highlight div.important {
    border-left: 10px solid #f02c71;
}

.table-advance td.highlight div.warning {
    border-left: 10px solid #fdbb39;
}

.table-advance td.highlight div.danger {
    border-left: 10px solid #e23e29;
}

/*gritter changes*/
.gritter-close {
    left:auto !important;
    right: 3px !important;
}

/* calendar and calendar form */
.external-event {
    display: inline-block !important;
    cursor:move;
    margin-bottom: 5px !important;
    margin-right: 5px !important;
}

/* fix full calendar title */

.has-toolbar .fc-header-right {
    padding-right: 50px !important;
}
.fc-header-title h2 {
    font-size: 13px !important;
    line-height: 20px;
    color: #111;
}
.event-form-title {
    margin-top:0px;
    margin-bottom: 13px;
    font-size: 13px !important;
    line-height: 20px;
    color: #111;
}

.fc-event-skin {
    border: 0px !important;
    background-color: inherit !important;
}

.fc-event.label {
    text-shadow:none !important;
    padding: 4px 4px !important;
}

.label-default  {
    background-color: #999 !important;
}

/* fix calendar title for ie8 and ie9 */
.ie8 .label-success, .ie9 .label-success {
    background-color: #5fd02c !important;
}

.ie8 .label-warning, .ie9 .label-warning {
    background-color: #fcb322 !important;
}
.ie8 .label-important, .ie9 .label-important {
    background-color: #ed4e2a !important;
}
.ie8 .label-info, .ie9 .label-info {
    background-color: #57b5e3 !important;
}
/* hide chosen search box */
.event_priority_chzn .chzn-search {
    display: none !important;
}

/* portlet tabs */
.widget-tabs .nav-tabs {
    position: relative;
    margin-top: -52px;
}

.widget-tabs .nav-tabs > li {
    float: right;
    /*border-top: 3px solid transparent;*/
}

.widget-tabs .nav-tabs {
    border-bottom: none;
    margin-right: 5px;
}

.widget-tabs .nav-tabs > li > a {
    padding-top: 9px;
    padding-bottom: 10px;
    line-height: 16px;
    margin-left: 0px;
    margin-right: 0px;
    border-left: none !important;
    border-right: none !important;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
}

.widget-tabs .nav-tabs > li:last-child > a {
    border-right:0;
}

.widget-tabs .nav-tabs > .active {
    border-top: 2px solid #4C4C4C;
}
.widget-tabs .nav-tabs > li {
    margin-left: -1px;
}
.widget-tabs .nav-tabs > li > a:hover, .widget-tabs .nav-tabs > li > a.active {
    margin-bottom: 0px;
    border-bottom: 0;
    margin-left: 0px;
    margin-right: 0px;
    border-left: 0;
    border-right: 0;
    background-color: #fff;
}
.widget-tabs .nav-tabs > .active > a  {
    color: #555555;
    cursor: default;
    background-color: #fff;
}
.widget-tabs .widget-body.form {
    padding: 0px;
}

.widget-tabs .widget-body.form .tab-pane {
    padding: 15px;
    padding-top: 0px;
}

.widget-tabs .widget-body.form .nav-tabs {
    margin-top: -37px;
}
/*TodoList*/

.todo-list {
    margin: 0px;
    padding: 0px;
    list-style: none;
}

.todo-list li {
    padding: 1px 0 8px 0px;
    margin-bottom: 5px;
    border-bottom: 1px solid #EBEBEB;
}

.todo-list li:first-child {
    border-top: none;
    border-bottom: 1px solid #EBEBEB;
}

.todo-list li:last-child {
    border-top: none;
    border-bottom: none;
}

.todo-list li:before,
.todo-list li:after {
    display: table;
    line-height: 0;
    content: "";
}

.todo-list li:after {
    clear: both;
}

.todo-list .col1 {
    float:left;
    width:100%;
    clear: both;
}

.todo-list .col2 {
    float:left;
    width:120px;
    margin-left:-120px;
    text-align: right;
}

.todo-list .col1 .cont {
    float:left;
    margin-right:120px;
    overflow:hidden;
}

/*time line chat*/
.timeline-messages:before {
    background: rgba(0, 0, 0, 0.1);
    bottom: 0;
    left: 58px;
    top: 0;
    width: 2px;
}
.timeline-messages:before, .msg-time-chat:before, .msg-time-chat .text:before {
    content: "";
    left: 20px;
    position: absolute;
    top: -2px;
}
.timeline-messages, .msg-time-chat {
    position: relative;
}
.msg-time-chat:first-child:before {
    margin-top: 16px;
}
.msg-time-chat:before {
    background:#CCCCCC;
    border: 2px solid #FAFAFA;
    border-radius: 100px;
    -moz-border-radius: 100px;
    -webkit-border-radius: 100px;
    height: 10px;
    margin: 23px 0 0 47px;
    width: 10px;
}
.msg-time-chat:hover:before {
    background: #A5D16C;
}
.msg-time-chat:first-child {
    padding-top: 0;
}
.message-img {
    border-radius: 30px;
    -moz-border-radius: 30px;
    -webkit-border-radius: 30px;
    float: left;
    margin-right: 30px;
    overflow: hidden;
}
.message-img img {
    display: block;
    height: 44px;
    width: 44px;
}
.message-body {
    margin-left: 74px;
}
.msg-time-chat .text {
    background: #fbfbfb;
    border: 1px solid #E5E5E5;
    padding: 10px;
}
.msg-time-chat p {
    margin: 0;
}
.msg-time-chat .attribution {
    color: #666666;
    font-size: 11px;
    margin: 0px 0 5px;
}
.msg-time-chat {
    overflow: hidden;
    padding:8px 0;
}

.msg-in a{
    color: #22878E;
}
.msg-out a{
    color: #B14C4C;
}


/*Chats*/

.chats {
    margin:0;
    padding: 0;
    margin-top: -15px;
    margin-right: 10px;
}

.chats li {
    list-style: none;
    padding: 8px 0 5px;
    margin: 7px auto;
    font-size: 12px;
}

.chats li img.avatar {
    height: 45px;
    width: 45px;
    -webkit-border-radius: 50% !important;
    -moz-border-radius: 50% !important;
    border-radius: 50% !important;
}

.chats li.in img.avatar {
    float: left;
    margin-right: 10px;
    margin-top: 0px;
}

.chats li .name {
    font-size: 13px;
    font-weight: 400;
}

.chats li .datetime {
    color:#adadad;
    font-size: 13px;
    font-weight: 400;
}

.chats li.out img.avatar {
    float: right;
    margin-left: 10px;
    margin-top: 0px;
}

.chats li .message {
    display: block;
    padding: 5px;
    position: relative;
}

.chats li.in .message {
    text-align: left;
    margin-left: 65px;
}

.chats li.in .message .arrow {
    display: block;
    position: absolute;
    top: 8px;
    left: -8px;
    width: 0;
    height: 0;

    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

.chats li.out .message .arrow {
    display: block;
    position: absolute;
    top: 8px;
    right: -8px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #b14c4c;
}

.chats li.out .message {
    border-right: 2px solid #b14c4c;
    margin-right: 65px;
    text-align: right;
}

.chats li.out .name,
.chats li.out .datetime  {
    text-align: right;
}

.chats li .message .body {
    display: block;
}

.chat-form {
    margin-top: 15px;
    padding: 10px;
    background-color: #eee;
    clear: both;
}

.chat-form .input-cont {
    margin-right: 55px;
}

.chat-form .input-cont input {
    margin-bottom: 0px;
}

.chat-form .input-cont input{
    border: 1px solid #ddd;
    width: 94%;
    margin-top:0;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

.chat-form .input-cont input {
    background-color: #fff !important;
}

.chat-form .input-cont input:focus{
    border: 1px solid #2FADE7 !important;
}

.chat-form .btn-cont {
    margin-top: -38px;
    position: relative;
    float: right;
}

.chat-form .btn-cont .btn {
    border-left: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    margin-top: 8px;
}

/*normal chat*/
.normal-chat .message {
    border: 1px solid #eaeaea;
    background: #fbfbfb;
    border-left: 1px solid #eaeaea !important;
    border-right: 1px solid #eaeaea !important;
    padding: 10px !important;
    border-radius: 5px;
}
.normal-chat li img.avatar {
    border-radius: 5px !important;
    -moz-border-radius: 5px !important;
    -webkit-border-radius: 5px !important;
    height: 45px;
    width: 45px;
}

.normal-chat li.in img.avatar, .normal-chat li.out img.avatar {
    margin-top: 0px;
}
.normal-chat li.in .message .arrow {
    border-right: 8px solid #eaeaea !important;
}
.normal-chat li.in .message .arrow {
    border-bottom: 8px solid transparent;
    border-top: 8px solid transparent;
    display: block;
    height: 0;
    left: -8px;
    position: absolute;
    top: 15px;
    width: 0;
}
.normal-chat li.out .message .arrow {
    border-left: 8px solid #eaeaea !important;
}
.normal-chat li.out .message .arrow {
    border-bottom: 8px solid transparent;
    border-top: 8px solid transparent;
    display: block;
    position: absolute;
    right: -8px;
    top: 15px;
}

.normal-chat li.in .name {
    color: #FB9800 !important;
}
.normal-chat li.out .name {
    color: #2FADE7 !important;
}
.normal-chat li .datetime {
    color: #ADADAD;
    font-size: 11px !important;
    font-weight: 400;
}

/* Input icons */

/* input with right aligned and colored icons */
.input-icon input {
    padding-right: 25px !important;
}

.input-icon .input-info,
.input-icon .input-error,
.input-icon .input-warning,
.input-icon .input-success {
    display: inline-block !important;
    position: relative !important;
    top: 3px;
    right: 25px !important;
    font-size: 16px;
}

.input-icon .input-info {
    color:#27a9e3;
}
.input-icon .input-error {
    color:#B94A48;
}
.input-icon .input-warning {
    color: #C09853;
}
.input-icon .input-success {
    color: #468847;
}

/* input with left aligned icons */
.input-icon.left i {
    color: #ccc;
    display: block !important;
    position: absolute !important;
    z-index: 1;
    margin: 6px 2px 4px 10px;
    width: 16px;
    height: 16px;
    border1: 1px solid #ddd;
    font-size: 16px;
    text-align: center;
}

.input-icon.left input {
    padding-left: 33px !important;
}
/* Modify tags input plugin css */
div.tagsinput {
    height: 40px !important;
    margin: 0 !important;
    padding: 5px !important;
    overflow: auto !important;
}

div.tagsinput span.tag {
    background: #22C0CB !important;
    color: #fff !important;
    border: 0 !important;
    padding: 3px 6px !important;
    margin-bottom: 4px !important;

}

div.tagsinput input {
    padding: 3px 6px !important;
}

div.tagsinput span.tag a {
    color: #fff !important;
}

div.tagsinput .not_valid {
    color: #fff !important;
    padding: 3px 6px !important;
    background-color: #e02222 !important;
}

/* File uploader plugin css changes */

.fileupload .close {
    position: relative;
    top:4px !important;
}

/*Form wizard*/

.form-wizard .progress {
    margin-bottom: 30px;
}

.form-wizard .steps {
    padding: 10px 0;
    margin-bottom: 15px;
}

.form-wizard .steps .navbar-inner {
    background-color: #fff !important;
    background-image: none !important;
    filter:none !important;
    border: 0px;
    box-shadow: none !important;
}

.form-wizard .steps .navbar-inner li a {
    background-color: #eee !important;
    background-image: none !important;
    filter:none !important;
    border: 0px;
    box-shadow: none !important;
    border-radius:30px;
    -moz-border-radius: 30px;
    -webkit-border-radius: 30px;
}

.form-wizard .step:hover {
    text-decoration: none;
}

.form-wizard .step .number {
    background-color: #ccc;
    display: inline-block;
    font-size: 16px;
    font-weight: 300;
    padding: 12px 25px !important;
    margin-right: 10px;
    -webkit-border-radius: 30px 0 0 30px !important;
    -moz-border-radius: 30px 0 0 30px !important;
    border-radius: 30px 0 0 30px !important;
}

.form-wizard .navbar .nav > li > a {
    padding: 0;
}

.form-wizard .step .desc {
    display: inline-block;
    font-size: 14px;
    font-weight: 300;
}

.form-wizard .active .step .number {
    background-color: #A5D16C;
    color: #fff;
}

.form-wizard .active .step .desc {
    font-weight: 400;
}

.form-wizard .step i {
    display: none;
}

.form-wizard .done .step .number {
    background-color: #E74955;
    color: #fff;
}

.form-wizard .done .step .desc {
    font-weight: 400;
}

.form-wizard .done .step i {
    font-size: 12px;
    font-weight: normal;
    color: #999;
    display: inline-block;
}

.form-wizard .tab-pane h4 {
    margin-bottom: 20px !important;
}

.form-wizard .tab-pane .chzn-container {
    position: absolute !important;
}

/*Pricing table*/

.pricing-table {
    border: 1px solid #ddd;
}

.pricing-head {
    text-align: center;
}
.pricing-title {
    text-align: center;
    padding: 0px 0 30px 0;
}
.pricing-head h3 {
    background:  #797979;
    border-bottom: 1px solid rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 18px;
    font-weight: 300;
    margin: 0;
}
.green .pricing-head h3 {
    background:  #93bf40;
    border-bottom: 1px solid #598011;
    color: #fff;
    font-size: 18px;
    font-weight: 300;
    margin: 0;
}
.pricing-head h3 span, .pricing-head h4 span {
    display: block;
    font-size: 12px;
    font-style: italic;
    margin-top: 5px;
}
.pricing-head h4 {
    background:#797979;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 54px;
    font-weight: 300;
    padding: 25px 0 10px 0;
    margin:0;
}
.green .pricing-head h4 {
    background:#93bf40;
    border-top: 1px solid #b6e858;
    color: #fff;
    font-size: 54px;
    font-weight: 300;
    padding: 25px 0 10px 0;
    margin:0;
}

.pricing-head span.note {
    display: inline;
    font-size: 17px;
    line-height: 0.8em;
    position: relative;
    top: -28px;
}

.pricing-table:hover {
    border-color: #93bf40;
}

.pricing-table ul {
    margin: 15px 0px;
    padding: 0px;
    list-style: none;
    text-align: center;
}

.pricing-table ul li {
    border-bottom: 1px dotted #CCCCCC;
    margin: 0 2em;
    padding: 1em 0;
    text-align: center;
}

.pricing-table ul li i {
    position: absolute;
    margin-right: 0px;
    margin-top: -2px;
    margin-left: -17px;
    color: #35aa47;
    font-size: 16px;
}
.price-actions {
    border-top: 1px solid #DDDDDD;
    padding: 1.15em;
    text-align: center;
}

.pricing-table.selected {
    background-color: #35aa47;
}

.pricing-table.selected:hover {
    border-color: #ddd;
}

.pricing-table.selected .desc {
    border-bottom-color: #fff;
}

.pricing-table.selected h3,
.pricing-table.selected .desc,
.pricing-table.selected ul li,
.pricing-table.selected ul li i,
.pricing-table.selected .rate {
    color: #fff;
}


/* Date tables plugin changes */
.dataTable {
    clear: both;
    margin-top: 5px;
}

.dataTables_filter label {
    line-height: 32px !important;
}

.dataTables_paginate,
.dataTables_filter {
    float: right;
}


/*faq list*/
ul.faq-list li {
    line-height: 30px;
}
ul.faq-list li a{
    background: #eee;
    margin-bottom: 1px;
    color: #868686;
}
ul.faq-list li a:hover, ul.faq-list li a.active{
    margin-bottom: 1px;
    color: #fff;
}


/*mail-btn*/

.mail-btn .btn {
    background-image: -moz-linear-gradient(center top , #FDFDFD 0%, #EAEAEA 100%);
    box-shadow: none;
}
.mail-btn .btn:hover, .mail-btn .btn:focus {
    background: none;
    box-shadow: none;
}

.mail-btn .btn-group > .btn + .btn {
    margin-left: 0;
}

/*alert*/
.alert {
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

/*error*/

.error-page {display: block; text-align: center;}

.error-page h1 {color: #adafb2; font-size: 30px; font-weight: bold; line-height: 50px; padding: 20px 0}

.error-page h1 strong {color: #adafb2; font-size: 60px; font-weight: bold}

.error-page p {color: #adafb2; font-size: 20px; font-weight: bold;}

/*profile*/
.profile-pic {
    display: block;
    margin-bottom: 10px;
}
.nav-stacked > li > a {
    border-radius: 0!important;
    -moz-border-radius: 0!important;
    -webkit-border-radius: 0!important;
}

/*gallery*/

.thumbnail {
    border:none;
    border-radius:0;
    box-shadow: none;
    line-height: 20px;
    padding: 5px 0;
}

/*accordion-group*/

.accordion-group a {
    text-decoration: none;
}

.accordion-group {
    border-radius: 0!important;
    -moz-border-radius: 0!important;
    -webkit-border-radius: 0!important;
}

/*tab*/

.tabbable-custom .nav-tabs > li > a {
    border-radius: 0!important;
    -moz-border-radius: 0!important;
    -webkit-border-radius: 0!important;
}

/*lock*/

#lock-body {
    background: url("../img/bg-grey.jpg") !important;
}

.lock-header {
    margin-top: 50px;
    text-align: center;
}

#lock {
    background: #fff;
    margin: 50px auto 0;
    padding: 10px 0;
    width: 420px;
}

.lock-title {
    text-align: center;
}

.lock-title i, .lock-title h3 {
    display: inline-block;
    margin: 0;
    padding: 0;
}

.lock-title i {
    font-size: 25px;
    padding: 0 5px;
}

.lock-avatar-row {
    display: block;
    text-align: center;
    margin-top: 20px;
    padding: 20px;
}

.lock-avatar-row img {
    border-radius: 300px;
    -moz-border-radius: 300px;
    -webkit-border-radius: 300px;
}

.lock-identity {
    text-align: center;
    display: block;
    width: 100%;
}

.lock-identity h3 {
    margin: 20px 0 0 0;
    padding: 0;
    line-height: 22px;
}

.lock-form-row {
    display: block;
    margin-top: 20px;
}
.lock-form-row i {
    text-shadow: none;
    color: #fff;
}
.lock-form-row .tarquoise{
    margin-left: 10px;
    box-shadow: none;
}

/*coming soon*/

#coming-body {
    background: url("../img/bg-denim.png") !important;
}

.coming-soon {
    margin-top: 50px;
    text-align: center;
    color: #c6c6c6;
}

.coming-soon h1 label{
    color: #2fbfca;
    display: inline;
    font-size: 40px;
}
.coming-soon h1 span{
    color: #facc5f;
    font-size: 60px;
}
.coming-soon h1 {
    font-family: 'Alex Brush', cursive;
}

.coming-soon .input-append {
    width: 100%;
}

.coming-soon .input-append .submit-btn{
    padding: 2px 10px;
    border-radius: 0;
    text-shadow: none;
    border: none;
    height: 37px;
    margin: 0 10px;
}
.coming-soon .input-append .submit-btn:hover{
    background: #000;
    color: #d1d3d3;
    transition-duration: 500ms;
    transition-property: width, background;
    transition-timing-function: ease;

}

.circles {
    display: block;
    height: 152px;
    margin: 50px auto 50px !important;
    width: 800px;
}

.email-address {
    width: 40%;
    height: 27px !important;
}

.twt-txt {
    color: #c6c6c6;
}


ul.social-link, ul.social-link li {
    display: inline-block;
}

ul.social-link li {
    list-style: none;
    margin: 0 5px;
    line-height: 33px;
}

ul.social-link li a {
    background: #fff;
    padding: 10px;
    border-radius: 30px;
    -moz-border-radius: 30px;
    -webkit-border-radius: 30px;
    color: #000;
    text-decoration: none;
}

ul.social-link li a:hover {
    color: #fff;
    transition-duration: 400ms;
    transition-property: width, background;
    transition-timing-function: ease;
}

/*tree view*/

.widget-body > .actions {
    float: right;
    margin: 6px 0 0;
    padding: 3px 5px 2px;
}

/*blog*/

.blog img {margin-bottom: 10px; width: 100%}

.blog h2, .blog h2 a{
    color: #868686 !important;
    font-size: 22px;
    font-weight: normal;
    margin: 0;
    padding: 0;
    line-height: normal;
}

.blog .date {
    display: block;
    text-align: center;
    width: 60px;
    margin-bottom: 10px;
}

.blog .date p{
    margin: 0;
    padding: 0px;
}

.blog .date .day {
    font-size: 13px;
}

.blog .date .month {
    color: #fff;
    font-size: 13px;
}

.blog ul {
    float: left;
    margin: 0 0px 10px 0;
    padding: 5px;
    background: #f4f4f5;
    border-radius: 0;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1) inset;
}

.blog ul li { float: left; margin-right: 20px; list-style: none}

.blog ul li a { color: #868686; text-decoration: none;}

.blog .btn {
    background: #767676;
}

.blog-side-bar h2 {
    color: #3b3431;
    text-transform: uppercase;
    font-size: 14px;
    margin: 0;
    padding: 0;
    line-height: normal;
}

.blog-side-bar ul {
    margin-top: 15px;
}
.blog-side-bar ul li {
    border-bottom: 1px solid #dfdfdf;
}

.blog-side-bar ul li:last-child {
    border-bottom: none;
}

.blog-side-bar ul li a {
    color: #837f7e;
    display: block;
    line-height: 35px;
    text-decoration: none;
}

.blog-side-bar h5, .blog-side-bar h5 a {
    margin: 0;
    padding: 0;
    color: #4a4341;
    line-height: normal;
}

.blog-side-bar ul.tag {
    width: 100%;
    display: inline-block;
}

.blog-side-bar ul.tag li {
    float: left;
    margin-right: 10px;
    border-bottom: none;
    margin-bottom: 10px;
    line-height: 18px;
}

.blog-side-bar ul.tag li a {
    color: #fff;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1) inset;
    padding: 0px 6px;

}

.blog-side-bar ul.tag li a:hover {
    background: #767676;
    color: #fff;
    transition-duration: 500ms;
    transition-property: width, background;
    transition-timing-function: ease;
}

.show-right {
    float: right !important;
}

.post-comment .color-red {
    color: #FF0000;
}

/*invoice-list*/


.invoice-list h5 {
    text-transform: uppercase;
}

.invoice-block {
    text-align: right;
}

ul.amounts li {
    border: 1px solid #eaeaea;
    padding:5px 10px;
    margin-bottom: 5px;
    background: #f1f1f1;
}

/*about us*/

.about-us h3 {
    text-align: center;
    padding-bottom: 30px;
}
.about-us h4 {
    padding-bottom: 10px;
}
.about-us h5 {
    padding-bottom: 5px;
    margin: 0;
}

.team-member, .team-member span{
    text-align: center;
}
.team-member span{
    color: #b6b6b6;
    text-transform: uppercase;
    display: block;
}

.team-member h3 {
    margin: 0;
    padding: 0;
}

.team-member img{
    border: 10px solid #e3e3e3;
    border-radius: 500px;
    -moz-border-radius: 500px;
    -webkit-border-radius: 500px;
}

.team-member ul {
    display: inline-block;
    margin: 10px 0;
}
.team-member ul li {
    display: inline-block;
}
.team-member ul li a {
    color: #d9d9d9;
    font-size: 35px;
    text-decoration: none;
}

.team-member p {
    text-align: left;
}

/*contact us*/
.contact-us h3 {
    text-align: center;
    padding-bottom:0px;
    margin:0;
}

.contact-us .feedback p {
    text-align: center;
}

.one-half {
    margin-right: 21px;
}

.feedback .control-group  {
    margin-bottom: 15px;
}

