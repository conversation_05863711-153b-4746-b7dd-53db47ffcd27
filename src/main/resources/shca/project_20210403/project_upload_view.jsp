<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}
    </style>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
        .am-u-sm-1{
            width:10%
        }
        .am-u-sm-11{
            width:90%
        }
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu2.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
<%--        <c:if test="${project==1}">--%>
<%--            <ul id="statusTab" class="am-tabs-nav am-nav am-nav-tabs">--%>
<%--                <li class="am-active" status="${sample_name_0_id}"><a>测序样本1</a></li>--%>
<%--                <li status="${sample_name_1_id}"><a>测序样本2</a></li>--%>
<%--            </ul>--%>
<%--        </c:if>--%>
        <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3"><a href="javascript:step(0)" class="step" for="infoForm">
                    <span class="number">1</span>
                    <span class="desc"> 项目信息</span>
                </a></li>
                <li class="span3"><a href="javascript:step(1)" class="step"   for="dataList">
                    <span class="number">2</span>
                    <span > 样本数据</span>
                </a></li>

<%--                <li class="span3"><a href="javascript:step(2)" class="step" for="qcReport" >--%>
<%--                    <span class="number">3</span>--%>
<%--                    <span > QC报告</span>--%>
<%--                </a></li>--%>
            </ul>

        </div>
     <%--   <ul id="contentTab" class="am-tabs-nav am-nav am-nav-tabs">
            &lt;%&ndash; wait_rawdata,running_step0,wait_match,running_step1,running_step2,finished,failured&ndash;%&gt;
            <li for="infoForm" class="am-active"><a>样本信息</a></li>
            <li for="dataList"><a >样本数据</a></li>
        </ul>--%>
        <div id="tabContent" style="background: #fff">

                <div  id="infoForm" class="widget am-cf am-u-sm-12" style="padding:0;display: none;">
                    <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">
                <form id="upload-form"   method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">
                    <div class="am-form-group">
                        <label class="am-u-sm-1 am-form-label">项目编号<span style="color:red"></span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" value="${cancer.project_no}" readonly />
                                <small></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-1 am-form-label">项目名称<span style="color:red"></span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <input type="text" class="tpl-form-input" value="${cancer.project_name}" readonly />
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-1 am-form-label">癌症种类<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <%--<input type="text" class="tpl-form-input" value="${cancer.cancer_kind}" readonly />--%>
                               <%-- <select id="cancer_kind" name="cancer_kind" data-placeholder="请选择癌症种类" class="chosen" style="width:160px;">
                                    <option value="${cancer.cancer_kind}">${cancer.cancer_kind}</option>

                                </select>--%>
                                <div id="cancer_kind_chzn" class="chzn-container chzn-container-single chzn-container-single-nosearch chzn-container-active" style="width: 160px;cursor: not-allowed" disabled="disabled">
                                    <a href="javascript:void(0)" class="chzn-single chzn-default" tabindex="-1"><span>${cancer.cancer_kind}</span><div><b></b></div></a>
                                </div>
                                <small></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-1 am-form-label">测序样本1<span style="color:red"></span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <input type="text" class="tpl-form-input" value="${cancer.sample_name_0}" readonly />
                            <small></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-1 am-form-label">测序样本2<span style="color:red"></span><span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11">
                            <input type="text" class="tpl-form-input" value="${cancer.sample_name_1}" readonly />
                            <small></small>
                        </div>
                    </div>


                    <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">测序脚本<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11" >

                                <input type="text" class="tpl-form-input" value="${cancer.cancer_script_0}" readonly />
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">脚本状态<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11 " >
                            <div id="cancer_kind_chzn" class="chzn-container chzn-container-single chzn-container-single-nosearch chzn-container-active" style="width: 160px;cursor: not-allowed" disabled="disabled">
                                <a href="javascript:void(0)" class="chzn-single chzn-default" tabindex="-1"><span>${cancer.status}</span><div><b></b></div></a>
                            </div>
                            <%-- <input type="text" class="tpl-form-input" value="" readonly />--%>
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">任务执行时间<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11" >
                            <span class="k-widget k-datetimepicker k-header kendo-datetimepicker" style="width: 200px; border-width: 0px; height: 28px;">
                                <span class="k-picker-wrap k-state-default">
                                    <c:if test="${cancer.start==null}">
                                        <input class="kendo-datetimepicker k-input"  value="-- -- -- " readonly
                                               type="text" style="width: 100%; border-width: 0px; height: 28px;" data-role="datetimepicker" role="combobox" aria-expanded="false" aria-disabled="false">
                                    </c:if>
                                    <c:if test="${cancer.start!=null}">
                                        <input class="kendo-datetimepicker k-input"  value="<fmt:formatDate value="${cancer.start}" pattern="yyyy-MM-dd HH:mm"/>" readonly
                                               type="text" style="width: 100%; border-width: 0px; height: 28px;" data-role="datetimepicker" role="combobox" aria-expanded="false" aria-disabled="false">
                                    </c:if>

                                    <span unselectable="on" class="k-select" style="line-height: 28px; padding: 0 2px 0 4px">
                                        <span class="k-link k-link-date" aria-label="Open the date view">
                                            <span unselectable="on" class="k-icon k-i-calendar"></span>
                                        </span>
                                        <span class="k-link k-link-time" aria-label="Open the time view">
                                            <span unselectable="on" class="k-icon k-i-clock"></span>
                                        </span>
                                    </span>
                                </span>
                            </span>
                            &emsp;至&emsp;
                            <span class="k-widget k-datetimepicker k-header kendo-datetimepicker" style="width: 200px; border-width: 0px; height: 28px;">
                                <span class="k-picker-wrap k-state-default">
                                    <c:if test="${cancer.end==null}">
                                        <input class="kendo-datetimepicker k-input"  value="-- -- -- " readonly
                                               type="text" style="width: 100%; border-width: 0px; height: 28px;" data-role="datetimepicker" role="combobox" aria-expanded="false" aria-disabled="false">
                                    </c:if>
                                    <c:if test="${cancer.end!=null}">
                                        <input class="kendo-datetimepicker k-input"  value="<fmt:formatDate value="${cancer.end}" pattern="yyyy-MM-dd HH:mm"/>" readonly
                                               type="text" style="width: 100%; border-width: 0px; height: 28px;" data-role="datetimepicker" role="combobox" aria-expanded="false" aria-disabled="false">
                                    </c:if>

                                    <span unselectable="on" class="k-select" style="line-height: 28px; padding: 0 2px 0 4px">
                                        <span class="k-link k-link-date" aria-label="Open the date view">
                                            <span unselectable="on" class="k-icon k-i-calendar"></span>
                                        </span>
                                        <span class="k-link k-link-time" aria-label="Open the time view">
                                            <span unselectable="on" class="k-icon k-i-clock"></span>
                                        </span>
                                    </span>
                                </span>
                            </span>
                            <%--<input type="text" class="tpl-form-input" value="${cancer.cancer_script_1}" readonly />--%>
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">任务执行时长<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11" >

                            <input id="date" type="text" class="tpl-form-input" value="" readonly />
                            <small></small>
                        </div>
                    </div>

                  <%--  <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">测序脚本2<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11" >

                                <input type="text" class="tpl-form-input" value="${cancer.cancer_script_1}" readonly />
                            <small></small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label  class="am-u-sm-1 am-form-label">测序脚本3<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                        <div class="am-u-sm-11" >
                            <input type="text" class="tpl-form-input" value="${cancer.cancer_script_2}" readonly />
                            <small></small>
                        </div>
                    </div>--%>



                      <div class="am-form-group">
                            <label for="description" class="am-u-sm-1 am-form-label">描述 <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <textarea class="form-field" rows="5" id="description" name="description" readonly></textarea>
                                <small></small>
                            </div>
                        </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-12 am-u-sm-push-1" style="left:10%">
                            <a id="backBtn" class="blue-btn" style="display:inline-block;text-align:justify;text-align-last:justify;">
                                返回
                            </a>
                        </div>
                    </div>
                </form>
                    </div>

            </div>
            <div id="dataList" style="display: none">
                <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
                    <div id="project-file-grid"></div>
                </div>
            </div>

            <div id="qcReport" style="display: none">
                <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
                    <div id="qc-report-grid"></div>
                </div>
            </div>
        </div>

    </div>

</div>
</div>
<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 76).find( ".widget-body").height( height - 56 - 76);
    }
</script>
<%@include file="/include/include-footer.jsp"%>
<script type="text/x-kendo-template" id="row-file_name">
    <%--    #= file_name.replace( /${param["project_no"]}/g, "<span class='warning'>${param["project_no"]}</span>") #--%>
<%--    #= file_name.replace( /${sample_name_0}/g, "<span class='warning'>${sample_name_0}</span>") #--%>
    #= file_name #
</script>
<script type="text/javascript">
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        }

        return "";
    }



    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");

        var showId = $this.attr("for");
        $("#tabContent > div").hide();
        $("#"+showId).show();

        if("dataList"==showId){
            var loading = $this.attr("loading");
            if(!loading){
                $this.attr("loading","loading");
                resizeContent();
                var $grid = $("#project-file-grid").kendoGrid({
                    dataSource: {
                        type: "json",
                        <%--read: "${CTX}/project_file_view.json?project_no="+"${project_no}",--%>
                        transport: {
                            read: "${CTX}/project_upload_view.json?id=${param['id']}",
                            dataType: "json"
                        },
                        pageSize: 40,
                        serverPaging: true,
                        serverFiltering: true,
                        schema: {
                            "total" : "total",
                            "data" : "rows",
                            model: {
                                id: "id"
                            }
                        }
                    },
                    height: $(window).height() - 133,
                    groupable: false,
                    sortable: false,
                    selectable: "true",
                   /* pageable: {
                        refresh: true,
                        pageSizes: true,
                        buttonCount: 5
                    },*/
                    filterable: {
                        extra: false
                    },
                    //toolbar: kendo.template($("#toolbar").html()),
                    columns: [
                        {field:"seq_no",title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {field:"sample_name",title:"样本号",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {title:"文件名",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},template: kendo.template($("#row-file_name").html())},
                        /*{field:"cancer_kind",title:"癌症种类",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},*/
                        {field:"sample_type",title:"样本类型",width:"60px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {field:"store_path",title:"存储路径",width:"280px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                        {field:"file_size",title:"文件大小",width:"80px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                        {field:"create_username",title:"创建人",width:"80px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                        {field:"create_time",title:"上传时间",width:"120px",template : "#= kendo.toString(new Date(create_time.time), 'yyyy-MM-dd HH:mm') #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                    ]
                });


                $("#upload-file", $grid).on("change", function() {
                    var $file = $(this);
                    if ($file.val().length == 0) {
                        return;
                    }

                    var $form = $("#upload-file-form", $grid);

                });
            }
        }
    }
</script>


<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
<%--                <input type="hidden" name="project_no" value="${param["project_no"]}" />--%>
                <input type="hidden" name="sample_name" value="${param["sample_name"]}" />
            </form>
        </div>
        <%--<div  class="am-form-group am-form-file" id="file_edit" >--%>
        <%--<i class="am-icon-pencil"></i> 编辑--%>
        <%--</div>--%>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>


<script type="text/javascript">
    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if(!idx)  idx = 0;
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }
    $(function(){
        var start = "${cancer.start.time}";
        var end = "${cancer.end.time}";
        if(start != "" && end != ""){
            $("#date").val(descRuntime(${cancer.end.time-cancer.start.time}));
        } else if(start != ""){
            $("#date").val(descRuntime(new Date().getTime()-${cancer.start.time}));
        } else {
            $("#date").val("-- --");
        }

        /*$("select.chosen").chosen({
            disable_search:true
        });
        $("select.chosen").attr("disabled","disabled");
        $("#cancer_kind_chzn").attr("disabled","disabled");*/
        //tab切换
        <%--$("#statusTab li").click(function(){--%>
        <%--    $("#statusTab li").removeClass("am-active");--%>
        <%--    $(this).addClass("am-active");--%>
        <%--    window.location.replace("${CTX}/project_cancerfile_view.htm?id="+$(this).attr("status")+"&sample_name_0_id="+"${sample_name_0_id}"+"&sample_name_1_id="+"${sample_name_1_id}"+"&project="+1);--%>
        <%--});--%>
        $("#contentTab li").click(function(){
            $("#contentTab li").removeClass("am-active");
            $(this).addClass("am-active");

           var showId = $(this).attr("for");
            $("#tabContent > div").hide();
           $("#"+showId).show();
        });

        var showType = "${param['type']}";
        if(showType){
            step(showType);
        } else {
            step(0);
        }
    });


    $("#backBtn").click(function(){
        history.go(-1);
    });



</script>

</body>

</html>