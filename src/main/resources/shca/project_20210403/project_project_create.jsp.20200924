<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">
       <%-- <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3"><a href="javascript:step(0)" class="step active">
                    <span class="number">1</span>
                    <span class="desc"> 填写项目基本信息</span>
                </a></li>
                <li class="span3"><a href="javascript:step(1)" class="step active">
                    <span class="number">2</span>
                    <span> 选择样本文件</span>
                </a></li>
                <li class="span3"><a href="javascript:step(2)" class="step">
                    <span class="number">3</span>
                    <span class="desc"> 配置脚本参数</span>
                </a></li>
            </ul>
        </div>--%>
        <div id="step-container-0" class="am-u-sm-12 am-u-md-12 step-container" style="margin:0;padding:0 1px;border-radius:0;display:inline;">
            <div class="widget am-cf am-u-sm-12" style="padding:0">
                <div class="widget-body am-fr" id="widget-form" style="overflow-y:auto;padding-bottom: 18px;overflow-x:hidden">
<%--                    <form id="upload-form"  action="${CTX}/fs_upload_upload.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">--%>
                    <form id="upload-form"  action="${CTX}/project_project_create.json" method="post" enctype="multipart/form-data" class="am-form tpl-form-border-form" style="padding-top:0;">
                        <div class="am-form-group">
                            <label for="project_no" class="am-u-sm-1 am-form-label">项目编号<span style="color:red">*</span><span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="project_no" id="project_no" value="" placeholder="请输入项目编号" />
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="project_name" class="am-u-sm-1 am-form-label">项目名称<span style="color:red"></span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="project_name" id="project_name" value="" placeholder="请输入项目名称" />
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cancer_kind" class="am-u-sm-1 am-form-label">癌症种类<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="cancer_kind" name="cancer_kind" data-placeholder="请选择癌症种类" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <option value="breast_cancer">乳腺癌</option>
                                    <option value="colorectal_cancer">结直肠癌</option>
                                    <option value="gastric_cancer">胃癌</option>
                                    <option value="hepatocellular_cancer">肝癌</option>
                                    <option value="ctDNA_pan-cancer">ctDNA泛癌</option>
                                    <option value="ovarian_cancer">卵巢癌</option>
                                    <option value="pan-cancer_tissue">泛癌组织</option>
                                    <option value="urinary">泌尿</option>
                                </select>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="sample_name_0" class="am-u-sm-1 am-form-label">测序样本1<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="sample_name_0" id="sample_name_0" value="" placeholder="请输入测序样本1" />
                                <%--<select id="sample_name_0" name="sample_name_0" data-placeholder="请选择样本1" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <c:forEach items="${sampleList}" var="sample">
                                        <option value="${sample.sample_name}">${sample.sample_name}</option>
                                    </c:forEach>
                                </select>--%>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="sample_name_1" class="am-u-sm-1 am-form-label">测序样本2<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <input type="text" class="tpl-form-input" name="sample_name_1" id="sample_name_1" value="" placeholder="请输入测序样本2" />
                                <%--<select id="sample_name_1" name="sample_name_1" data-placeholder="请选择样本2" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <c:forEach items="${sampleList}" var="sample">
                                        <option value="${sample.sample_name}">${sample.sample_name}</option>
                                    </c:forEach>
                                </select>--%>
                                <small></small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="cancer_script_0" class="am-u-sm-1 am-form-label">测序脚本<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <select id="cancer_script_0" name="cancer_script_0" data-placeholder="请选择测序脚本" class="chosen" style="width:160px;">
                                    <option value=""></option>
                                    <c:forEach items="${cancer_script}" var="list" varStatus="id">
                                        <option value="${list.cancer_script}">${list.cancer_script}</option>
                                    </c:forEach>
<%--                                    <option value="breast_cancer">乳腺癌</option>--%>
                                </select>
                                <small></small>
                            </div>
                        </div>

                       <%-- <div class="am-form-group">
                            <label for="run_time" class="am-u-sm-1 am-form-label">执行时间<span style="color:red">*</span> <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11 ">
                                <label class="am-radio" style="line-height: 1.6;margin-top:5px"><input type="radio" id="run_time" name="run_time" value="manual" checked>手动执行</label>
                                <label class="am-radio-inline" style="line-height: 1.6;margin-top:5px;margin-right:18px;"><input type="radio" name="run_time" value="auto" >自动执行</label><input class="kendo-datetimepicker" type="text" style="width:180px;border-width:0;height:28px;" />
                                <small></small>
                            </div>
                        </div>--%>


                        <div class="am-form-group">
                            <label for="description" class="am-u-sm-1 am-form-label">描述 <span class="tpl-form-line-small-title"></span></label>
                            <div class="am-u-sm-11">
                                <textarea class="form-field" rows="6" id="description" name="description" placeholder="请输入描述"></textarea>
                                <small></small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div class="am-u-sm-12 am-u-sm-push-1">
                                <a id="createBtn" class="blue-btn" style="display:inline-block;text-align:justify;text-align-last:justify;">
                                    创建 <i class="icon-angle-right"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div id="step-container-1" class="am-u-sm-12 am-u-md-12 step-container" style="margin:0;padding:0 1px;border-radius:0;display: none">
            <div id="project-file-grid"></div>
        </div>
    </div>

</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
</script>
<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        <div class="am-form-group am-form-file">
            <i class="am-icon-upload"></i> 上传样本
            <form id="upload-file-form" action="${CTX}/fs_upload_upload.json" class="am-form" method="post" enctype="multipart/form-data">
                <input id="upload-file" type="file" name="file" multiple="multiple" />
                <input type="hidden" name="project_no" value="${param["project_no"]}" />
            </form>
        </div>
        <div id="chooseFile"  class="am-form-group am-form-file" >
        <i class="am-icon-pencil"></i> 选取样本
        </div>
        <div id="delete-file-btn" class="am-form-group am-form-file">
            <i class="am-icon-trash-o"></i> 删除
        </div>
    </div>
    <div class="toolbar">
        <input id="filter-input" type="text" placeholder="文件名称检索" />
    </div>
</script>
<script type="text/x-kendo-template" id="row-file_name">
    #= file_name.replace( /B2000617B/g, "<span class='warning'>${param["project_no"]}</span>") #
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/javascript">
    var id;

    $("select.chosen").chosen();
    $("input.kendo-datetimepicker").kendoDateTimePicker({
        // display month and year in the input
        format: "yyyy-MM-dd HH:mm",
        // specifies that DateInput is used for masking the input element
        dateInput: false,
    });


    var FILE_SIZE = ["B", "KB", "MB", "GB"]
    function descFileSize(size, idx) {
        if (size <= 1024) {
            return size.toFixed( 2)  + FILE_SIZE[idx];
        }
        if (idx == FILE_SIZE.length) {
            return size.toFixed( 2) + FILE_SIZE[idx];
        }
        return descFileSize( size / 1024, idx + 1);
    }

    $(function(){

    });



    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        $step.eq( index).addClass( "active");

        for (var i = 0; i < index; i++) {
            $step.eq( i).addClass( "active").find( "span").eq( 1).addClass( "desc");
        }

        $(".step-container").css( "display", "none");
        console.log( $("#step-container-" + index).length)
        $("#step-container-" + index).css( "display", "block");
    }


    $("#sample_name_0").change(function () {
        var sample_name = $("#sample_name_0").val();
        authentication(sample_name);
        // var code = authentication(sample_name);
        // console.log(code);
        // if (code != 0 || code != "0"){
        //     alert("测序样本1已存在，请重新输入");
        // }
    });

    $("#sample_name_1").change(function () {
        var sample_name = $("#sample_name_1").val();
        authentication(sample_name);
    });

    function authentication(sample_name){
        $.ajax({
            type: "POST",
            dataType: "json",
            url: "${CTX}/project_project_sample.json",
            data: {
                "sample_name":sample_name,
            },
            success: function (ret) {
                console.log(ret.errcode);
                if(ret.errcode != 0){
                    alert("测序样本已存在，请重新输入");
                }
            }
        });
    }

    $("#createBtn").click(function(){
        try{
            var $button = $(this);
            if ($button.attr("disabled") == "disabled"){
                return;
            };

            var project_no = $("#project_no").val();
            if(project_no == undefined && project_no.replace(/(^\s*)|(\s*$)/g,"") ==""){
                alert("项目编号不能为空！");
                return false;
            }

            var project_name = $("#project_name").val();
            if(project_name == undefined && project_name.replace(/(^\s*)|(\s*$)/g,"") ==""){
                alert("项目名称不能为空！");
                return false;
            }

            var sample_name_0 = $("#sample_name_0").val();
            if(sample_name_0 == undefined || sample_name_0 == ""){
                alert("请输入样本1")
                return false;
            }
            authentication(sample_name_0);
            // var code1 = authentication(sample_name_0);
            // if (code1 == 1000){
            //     alert("测序样本1已存在，请重新输入");
            //     return false;
            // }

            var sample_name_1 = $("#sample_name_1").val();
            if(sample_name_1 == undefined || sample_name_1 == ""){
                alert("请输入样本2")
                return false;
            }
            authentication(sample_name_1);
            // var code2 = authentication(sample_name_1);
            // if (code2 == 1000){
            //     alert("测序样本2已存在，请重新输入");
            //     return false;
            // }

            var cancer_script_0 = $("#cancer_script_0").val();
            if(cancer_script_0 == undefined || cancer_script_0 == ""){
                alert("请选择测序脚本")
                return false;
            }

            $button.button( "提交申请中...");
            $button.attr("disabled", true);

            $("#upload-form").ajaxSubmit({
                dataType: "json",
                success: function (ret) {
                    if(ret.errCode==0){
                        alert("保存成功");
                        window.location.href="${CTX}/project_project_list.htm";
                    } else {
                        alert(ret.errMsg);
                    }
                }
            });


        }catch (e){
            alert(e);
        }
    })






</script>

</body>

</html>