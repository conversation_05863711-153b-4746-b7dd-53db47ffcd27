<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .warning {color: #F37B1D}
        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}
    </style>

</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->

    <div class="tpl-content-wrapper">

        <div id="project-file-grid"></div>

    </div>

</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="row-file_name">
    #= index++  #
</script>
<script type="text/javascript">
     var index = 1;

     var FILE_SIZE = ["B", "KB", "MB", "GB"]
     function descFileSize(size, idx) {
         if(!idx)  idx = 0;
         if (size <= 1024) {
             return size.toFixed( 2)  + FILE_SIZE[idx];
         }
         if (idx == FILE_SIZE.length) {
             return size.toFixed( 2) + FILE_SIZE[idx];
         }
         return descFileSize( size / 1024, idx + 1);
     }

    $(function(){
        var $grid = $("#project-file-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/project_cancer_report.json?id="+"${param['id']}",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 56,
            groupable: false,
            sortable: false,
            selectable: "true",
            filterable: {
                extra: false
            },
            columns: [
                {title:"序号",width:"40px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-file_name").html())},
                {field:"file_name",title:"文件名",width:"200px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                {field:"file_size",title:"文件大小",width:"80px",template : "#= descFileSize( file_size, 0) #",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:right"}},
                {field:"store_path",title:"存储路径",width:"480px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"}},
                { command: [{text: "查看", click: viewFile }], title: " ", width: "60px"  },
            ]
        });

    });



    function viewFile(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.open("${CTX}/project_cancer_reportFile.htm?id="+row.id);
    }



</script>

</body>

</html>