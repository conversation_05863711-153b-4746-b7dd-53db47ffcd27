<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>基因测序</title>
    <%@include file="/include/include-head.jsp"%>
    <link rel="stylesheet" href="${CTX}/shca/css/style.css" />
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        #project-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        .filter-input {
            width: 380px;
            box-sizing: border-box;
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #d9d9d9;
            margin-top: -4px;
        }

        .am-nav-tabs>li>a {margin:0;padding:10px 18px;font-weight:600;font-size:1.2em;color:#666;width: 120px;text-align: center}
        .am-nav-tabs>li.am-active>a, .am-nav-tabs>li.am-active>a:focus, .am-nav-tabs>li.am-active>a:hover{color:#41CA6E;}
        .am-progress-bar,.am-progress-bar-success,.am-progress-bar-danger{line-height:18px;}


    </style>
    <style>
        .theme-white .tpl-form-border-form .am-form-label {font-size: 14px;}
        .theme-white .tpl-form-border-form .am-radio-inline,.theme-white .tpl-form-border-form .am-checkbox-inline  {line-height: 1.6;margin-top:5px;}
        h2{font-size: 18px;margin:0;padding:0;border-width:0;border-left:4px solid #0e90d2;padding-left:8px;}
        a.white-btn{color:#333;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#fff,#e6e6e6)}
        a.white-btn:hover{color:#333;background-color:#f5f5f5;}
        a.blue-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#08c,#04c)}
        a.blue-btn:hover{color:#fff;background-color:#006dcc;}
        a.green-btn{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,0.25);display:inline-block;padding:4px 12px;
            margin:0;font-size:14px;line-height:20px;text-align:center;vertical-align:middle;cursor:pointer;border-radius:4px;border:1px solid #ccc;
            text-decoration:none;background-image:linear-gradient(to bottom,#62c462,#51a351)}
        a.green-btn:hover{color:#fff;background-color:#5bb75b;}
        i.icon-angle-right{display:inline-block;width:14px;height:14px;margin-top:1px;line-height:14px;vertical-align:text-top;}

        .row-fluid{display:flex;}
        .row-fluid .span3{background:#eee;border-radius:500px;width:33%;margin-right:2em;}
        .form-wizard li a {color:#666;font-weight:800;}
        .form-wizard li a span{font-size:14px;font-weight:300}
        .form-wizard li a.active span{font-size:14px;font-weight:600}
        .form-wizard .active .number{background-color:#41CA6E;color:#fff;}
        .form-wizard .active .desc{background:url(${CTX}/shca/image/check.png) no-repeat left;text-indent:2em;font-weight:600}


        #project-file-grid .k-grid-toolbar {padding:10px 8px;font-size:14px;height:25px}
        .btnContainer {display: inline-block;padding-left: 4px;  }
        .btnContainer .am-form-group {display:inline-block;margin: 0 8px 0 0;  }
        .toolbar {float: right;}
        #filter-input{width:380px;box-sizing:border-box;padding:6px;border-radius:3px;border:1px solid #d9d9d9;margin-top:-4px;}
    </style>
</head>

<body class="theme-white" data-type="index">
<div class="am-g tpl-g">
    <!-- 头部 -->
    <%@include file="/shca/include/include-header.jsp"%>


    <!-- 侧边导航栏 -->
    <%@include file="/shca/include/include-menu.jsp"%>

    <!-- 内容区域 -->
   <%-- <div class="tpl-content-wrapper">
        <div class="form-wizard am-u-sm-12 am-u-md-12" style="margin:0;padding:15px;border-radius:0;background-color:#FFF">
            <ul class="row-fluid">
                <li class="span3"><a href="javascript:step(0)" class="step active">
                    <span class="number">1</span>
                    <span class="desc"> 全部</span>
                </a></li>
                <li class="span3"><a href="javascript:step(1)" class="step"  status="running_step">
                    <span class="number">2</span>
                    <span > 执行中</span>
                </a></li>
                <li class="span3"><a href="javascript:step(2)" class="step"  status="wait">
                    <span class="number">3</span>
                    <span> 排队中</span>
                </a></li>
                <li class="span3"><a href="javascript:step(3)" class="step"  status="finished">
                    <span class="number">4</span>
                    <span > 执行完成</span>
                </a></li>
                <li class="span3"><a href="javascript:step(4)" class="step"  status="failure">
                    <span class="number">5</span>
                    <span > 执行失败</span>
                </a></li>

            </ul>

        </div>
        <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div id="project-grid"></div>
        </div>
    </div>--%>
    <div class="tpl-content-wrapper">
        <ul id="statusTab" class="am-tabs-nav am-nav am-nav-tabs">
           <%-- wait_rawdata,running_step0,wait_match,running_step1,running_step2,finished,failured--%>
            <li class="am-active" ><a>全部</a></li>
            <li status="running_step"><a >执行中</a></li>
            <li status="wait"><a >排队中</a></li>
            <li status="finished"><a>执行完成</a></li>
            <li status="failed"><a>执行失败</a></li>
        </ul>

         <div class="am-u-sm-12 am-u-md-12" style="margin:0;padding:0 1px;border-radius:0;">
            <div id="project-grid"></div>
         </div>
    </div>
    <%--    <div class="tpl-content-wrapper">
            <div class="row-content am-cf" style="padding:18px 8px">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="widget am-cf am-u-sm-12" style="padding:0">

                        <div class="widget-body am-fr" style="padding:0">
                            <div id="grid-profile"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>--%>
</div>
</div>

<script type="text/javascript">
    function resizeContent() {
        var height = $(window).height();
        $(".step-container").height( height - 56);
        $(".widget").height( height - 56).find( ".widget-body").height( height - 56 - 46);
    }
    /*function resizeContent() {
        var height = $(window).height();
        $(".widget").height( height - 56 - 36).find( ".widget-body").height( height - 56 - 36 - 46);
    }*/
    function step(index) {
        var $step = $( ".row-fluid .step").removeClass( "active");
        $step.find( "span.desc").removeClass( "desc");
        var $this= $step.eq( index);
        $this.addClass( "active").find( "span").eq( 1).addClass( "desc");


        /*for (var i = 0; i < index; i++) {
                    $step.eq( i).addClass( "active").find( "span").eq( 1).addClass( "desc");
                }*/
       /* $(".step-container").css( "display", "none");
        console.log( $("#step-container-" + index).length)
        $("#step-container-" + index).css( "display", "block");*/

        var grid = $("#project-grid").data("kendoGrid");
        var dataSource1 = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "${CTX}/project_cancer_list.json"+($this.attr("status")?"?str-status-rlk="+$this.attr("status"):""),
                    dataType: "json"
                }
            },
            pageSize: 40,
            serverPaging: true,
            serverFiltering: true,
            schema: {
                "total" : "total",
                "data" : "rows",
                model: {
                    id: "id"
                }
            }
        });
        grid.setDataSource(dataSource1);
    }
</script>
<%@include file="/include/include-footer.jsp"%>

<script type="text/x-kendo-template" id="toolbar">
    <div class="btnContainer">
        样本号：<input id="sample_name" class="filter-input" type="text" style="width:220px;padding:4px;margin-left:4px;margin-right:12px;" />
        <%--测序时间：<input id="start_time" class="kendo-datepicker" type="text" style="width:160px;border-width:0" />
        至 <input id="end_time" class="kendo-datepicker" type="text" style="width:160px;border-width:0" />--%>
        <div id="query"  class="am-form-group" style="font-size:14px;margin-left:12px;">
            <i class="am-icon-search"></i> 查询
        </div>
    </div>
    <div class="toolbar">

    </div>
</script>
<script type="text/x-kendo-template" id="row-progress">

    <div class="am-progress" style="margin-bottom:0;height:18px;">
        # if(status == "finished") { # <div class="am-progress-bar am-progress-bar-success"  style="width:100%">#= total_step# / #= total_step#</div>
        # } else if(status == "wait_running") {# <div class="am-progress-bar"  style="width:100%;background-color:white;color:black">0 / #= total_step#</div>
        # } else if(status == "running") {# <div class="am-progress-bar am-progress-bar-success"  style="width:#= parseInt( current_step / total_step * 100)#%">#= current_step# / #= total_step#</div>
        # } else if(status == "failed") {# <div class="am-progress-bar am-progress-bar-danger"  style="width:#= parseInt( current_step / total_step * 100)#%">#= current_step# / #= total_step#</div>
        # }#
    </div>
</script>

<script type="text/x-kendo-template" id="row-start-end">
    #if(status == "finished"){#
    #=   kendo.toString( new Date( start.time), 'MM-dd HH:mm') + " 至 " +  kendo.toString( new Date( end.time), 'HH:mm')  #
    #} else  {
        if(start!=null){ #
            #=   kendo.toString( new Date( start.time), 'MM-dd HH:mm') + " 至 --:--"   #
        #} else {}
    } #
</script>

<script type="text/x-kendo-template" id="row-runtime">
    #if(start!=null && end !=null){#
    #=   descRuntime (end.time-start.time)  #
    #} else {  } #
</script>


<script type="text/javascript">
    var ONE_MINUTE = 60 * 1000;
    var ONE_HOUR = 60 * ONE_MINUTE;
    var ONE_DATE = 24 * ONE_HOUR;
    var ONE_MONTH = 30 * ONE_DATE;
    function descRuntime(runtime) {
        if (runtime == null) {
            return "";
        }
        if (runtime < ONE_MINUTE) {
            var cnt =  Math.round( runtime / 1000);
            return cnt + "秒";
        }
        if (runtime < ONE_HOUR) {
            var cnt =  Math.round( runtime / ONE_MINUTE);
            var minus = runtime - cnt * ONE_MINUTE;
            if (minus <= 0) {
                return cnt + "分钟";
            }
            minus = Math.round( minus / 1000);
            return cnt + "分" + minus + "秒";
        }
        if (runtime < ONE_DATE) {
            var cnt =  Math.round( runtime / ONE_HOUR);
            var minus = runtime - cnt * ONE_HOUR;
            if (minus <= 0) {
                return cnt + "小时";
            }
            minus = Math.round( minus / ONE_MINUTE);
            return cnt + "小时" + minus + "分";
        }

        return "";
    }
    $(function(){
        //tab切换
        $("#statusTab li").click(function(){
            $("#statusTab li").removeClass("am-active");
            $(this).addClass("am-active");

            $(".btnContainer input").val("");//置空查询条件

            var grid = $("#project-grid").data("kendoGrid");
            var dataSource1 = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: "${CTX}/project_cancer_list.json"+($(this).attr("status")?"?str-status-rlk="+$(this).attr("status"):""),
                        dataType: "json"
                    }
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            });
            grid.setDataSource(dataSource1);
        });

        //列表初始化
        var $grid = $("#project-grid").kendoGrid({
            dataSource: {
                type: "json",
                transport: {
                    read: "${CTX}/project_cancer_list.json",
                    dataType: "json"
                },
                pageSize: 40,
                serverPaging: true,
                serverFiltering: true,
                schema: {
                    "total" : "total",
                    "data" : "rows",
                    model: {
                        id: "id"
                    }
                }
            },
            height: $(window).height() - 110,
            groupable: false,
            sortable: false,
            pageable: {
                refresh: true,
                pageSizes: true,
                buttonCount: 5
            },
            filterable: {
                extra: false
            },
            toolbar: kendo.template($("#toolbar").html()),
            columns: [
                // {field:"project_no",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"sample_name",title:"样本号",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {field:"cancer_kind",title:"癌症种类",filterable:false,width:"80px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"测序时间",width:"160px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-start-end").html())},
                {title:"测序时长",width:"100px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"},template: kendo.template($("#row-runtime").html()) },
                {field:"project_no",title:"项目编号",filterable:false, width:"100px",headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}},
                {title:"执行状况",width:"200px",filterable:false,headerAttributes:{style:"text-align:center;vertical-align:middle"},attributes:{style:"text-align:center"}, template: kendo.template($("#row-progress").html())},
                { command: [{text: "样本详情", click: viewFile },{text: "脚本详情", click: showScript },{text: "QC报告",click:viewQC },{text: "样本报告",click:viewReport }/*,{text: "启动", click: startApproval }*/], title: " ", width: "260px"  },//{text: "撤回流程", click: cancelApproval }
            ]
        });

        //日期控件初始化
        $("input.kendo-datepicker", $grid).kendoDatePicker({
            // display month and year in the input
            format: "yyyy-MM-dd",
            // specifies that DateInput is used for masking the input element
            dateInput: false,
        });

        //列表工具条查询按钮事件
        $("#query").click(function () {
            query();
        })

    });

    function query() {
        var sampleName = $("#sample_name").val();
       /* var startTime = $("#start_time").val();
        var endTime = $("#end_time").val();

        if(startTime&&endTime){

            if (endTime<startTime) {
                alert("结束时间不能小于开始时间");
                return false;
            }
        }*/

        var grid = $("#project-grid").data("kendoGrid");
        grid.dataSource.query({
            "str-sample_name-lk" : sampleName,
            page:1,
            pageSize:40
        });
    }


    function startApproval(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        console.log(row);
        alert( "开发中");
        return;
    }

    function viewFile(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.location.href = "${CTX}/project_file_list.htm?project_no=" + row.project_no;--%>
        window.location.href = "${CTX}/project_cancerfile_view.htm?id="+row.id;
    }

    function showScript(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        window.location.href = "${CTX}/project_cancer_script.htm?id="+row.id;
    }

    function viewQC(e) {
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.location.href = "${CTX}/project_file_list.htm?project_no=" + row.project_no;--%>
        window.location.href = "${CTX}/project_cancerfile_view.htm?id="+row.id +"&type=2";
    }

    function viewReport(e){
        var row = this.dataItem( $(e.currentTarget).closest("tr"));
        <%--window.location.href = "${CTX}/project_file_list.htm?project_no=" + row.project_no;--%>
        window.location.href = "${CTX}/project_cancer_report.htm?id="+row.id;
    }
</script>

</body>

</html>