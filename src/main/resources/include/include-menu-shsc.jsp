<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ include file="include-taglib.jsp"%>
<style>
    .left-sidebar {
        padding-top: 5px !important;
    }
</style>
<div class="left-sidebar">
    <!-- 菜单 -->
    <ul class="sidebar-nav">
        <li class="sidebar-nav-link">
            <c:if test="${sessionScope.role eq 'mp'}">
            <a href="javascript:;" class="sidebar-nav-sub-title">
                <i class="am-icon-database sidebar-nav-link-logo"></i> 治安地图管理
                <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
            </a>
            <ul class="sidebar-nav sidebar-nav-sub">
                <li class="sidebar-nav-link">
                    <a id="mp_map_consolePolice" href="${CTX}/mp_map_consolePolice.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span> 派出所
                    </a>
                </li>
                <li class="sidebar-nav-link">
                    <a id="mp_map_consoleDog" href="${CTX}/mp_map_consoleDog.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span> 养犬登记证办证点
                    </a>
                </li>
                <li class="sidebar-nav-link">
                    <a id="mp_map_consolePrevention" href="${CTX}/mp_map_consolePrevention.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>宠物防疫点
                    </a>
                </li>

            </ul>
            </c:if>
			<a href="javascript:;" class="sidebar-nav-sub-title">
                <i class="am-icon-database sidebar-nav-link-logo"></i> 行政处罚管理
                <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
            </a>
            <ul class="sidebar-nav sidebar-nav-sub">

                <li class="sidebar-nav-link">
                    <a id="xzcf_punish_list" href="${CTX}/xzcf_punish_list.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>EXCEL导入
                    </a>
                </li>

            </ul>
            <a href="javascript:;" class="sidebar-nav-sub-title">
                <i class="am-icon-database sidebar-nav-link-logo"></i> 举报管理
                <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
            </a>
            <ul class="sidebar-nav sidebar-nav-sub">

                <li class="sidebar-nav-link">
                    <a id="tip_offs_list" href="${CTX}/tip_offs_list.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>举报信息列表
                    </a>
                </li>
                <li class="sidebar-nav-link">
                    <a id="register_approve_list" href="${CTX}/register_approve_list.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>申请举报人列表
                    </a>
                </li>

            </ul>

            <c:if test="${sessionScope.role eq 'pdnet'}">
            <a href="javascript:;" class="sidebar-nav-sub-title">
                <i class="am-icon-database sidebar-nav-link-logo"></i> 问题感知管理
                <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
            </a>
            <ul class="sidebar-nav sidebar-nav-sub">

                <li class="sidebar-nav-link">
                    <a id="event_all_console" href="${CTX}/event_all_console.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>待提交网格中心
                    </a>
                </li>
                <li class="sidebar-nav-link">
                    <a id="event_all_list" href="${CTX}/event_all_list.htm">
                        <span class="am-icon-angle-right sidebar-nav-link-logo"></span>已提交网格中心
                    </a>
                </li>
            </ul>
            </c:if>
            <c:if test="${sessionScope.role eq 'manager'}">
                <a href="javascript:;" class="sidebar-nav-sub-title">
                    <i class="am-icon-database sidebar-nav-link-logo"></i> 问题感知情况
                    <span class="am-icon-chevron-down am-fr am-margin-right-sm sidebar-nav-sub-ico"></span>
                </a>
                <ul class="sidebar-nav sidebar-nav-sub">

                    <li class="sidebar-nav-link">
                        <a id="police_statistics_list" href="${CTX}/community_eventList_list.htm">
                            <span class="am-icon-angle-right sidebar-nav-link-logo"></span>问题感知统计列表
                        </a>
                    </li>

                </ul>
            </c:if>
        </li>






    </ul>
</div>
<script type="text/javascript">
    $('.sidebar-nav-sub-title').on('click', function() {
        $(this).siblings('.sidebar-nav-sub')
                .slideToggle( 80)
                .end()
                .find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');
        console.log("1 click")
    })


//    //选中菜单
//    $("#mp_map_admin_police").addClass( "active").parents( ".sidebar-nav-sub").css( "display", "block").prev( ".sidebar-nav-sub-title").find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');
    $("#${__menu_id}").addClass( "active").parents( ".sidebar-nav-sub").css( "display", "block").prev( ".sidebar-nav-sub-title").find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');

    //单击菜单
//    var curType;
    <%--clickMenu = function(type){--%>
        <%--if(curType){--%>
            <%--$("#mp_map_admin_"+curType).removeClass("active");--%>
        <%--}--%>
        <%--$("#mp_map_admin_"+type).addClass("active").parents( ".sidebar-nav-sub").css( "display", "block").prev( ".sidebar-nav-sub-title").find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');--%>
        <%--curType = type;--%>
        <%--console.log("clickMenu:", type);--%>
        <%--if(type=="excel"){--%>
            <%--window.location.href="${CTX}/xzcf_punish_list.htm"--%>
        <%--}else{--%>
            <%--clickMenuConsole(type);--%>
        <%--}--%>
    <%--};--%>
</script>