{"version": 3, "sources": ["messages/kendo.messages.zh-CN.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "previewInput", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "edit<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogOk", "dialogCancel", "cleanFormatting", "createTable", "createTableHint", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "exportAs", "import", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "additionalValue", "additionalOperator", "logic", "FilterMultiCheck", "checkAll", "selectedItemsFormat", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteDependencyConfirmation", "deleteTaskWindowTitle", "deleteTaskConfirmation", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "GanttTimeline", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "PivotSettingTarget", "PivotConfigurator", "measures", "measures<PERSON>abel", "columnsLabel", "rowsLabel", "fieldsLabel", "RecurrenceEditor", "recurrenceEditorTitle", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "MobileRecurrenceEditor", "endTitle", "repeatTitle", "headerTitle", "patterns", "repeatBy", "dayOfMonth", "dayOfWeek", "every", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "timeline", "timelineWeek", "timelineWorkWeek", "timelineMonth", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "retry", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "showList<PERSON><PERSON>on", "showCalendarButton", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "scale", "fit", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "rangeDisabledDialog", "incompatibleRangesDialog", "noFillDirectionDialog", "duplicateSheetNameDialog", "overflowDialog", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "linkDialog", "url", "removeLink", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "matches", "doesnotmatch", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "toggleGridlines", "saveAs", "sort", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "hyperlink", "view", "errors", "openUnsupported", "shiftingNonblankCells", "insertColumnWhenRowIsSelected", "insertRowWhenColumnIsSelected", "filterRangeContainingMerges", "sortRangeContainingMerges", "cantSortMultipleSelection", "cantSortNullRef", "cantSortMixedCells", "validationError", "cannotModifyDisabled", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "dragHandleTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeList", "noRows", "loading", "requestFailed", "createchild", "TreeView", "PanelBar", "Upload", "localization", "resume", "clearSelectedFiles", "uploadSelectedFiles", "invalidFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusPaused", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "mobile", "<PERSON><PERSON><PERSON>", "pullTemplate", "releaseTemplate", "refreshTemplate", "ListView", "loadMoreText", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YAEF,SAAWG,EAAGC,GAGNC,MAAMC,GAAGC,kBACTF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACtDE,MAAS,KACTC,OAAU,KACVC,QAAW,MACXC,WAAc,OACdC,aAAgB,cAKxBX,MAAMC,GAAGW,cACTZ,MAAMC,GAAGW,YAAYT,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,YAAYT,UAAUC,QAAQC,UAClDE,MAAS,KACTC,OAAU,KACVC,QAAW,MACXC,WAAc,OACdC,aAAgB,cAKxBX,MAAMC,GAAGY,aACTb,MAAMC,GAAGY,WAAWV,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGY,WAAWV,UAAUC,QAAQC,UACjDS,cAAiB,OACjBC,eAAkB,OAClBC,OAAU,KACVC,QAAW,MACXC,KAAQ,KACRC,SAAY,MACZC,KAAQ,KACRC,OAAU,QAKlBrB,MAAMC,GAAGqB,SACTtB,MAAMC,GAAGqB,OAAOnB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,OAAOnB,UAAUC,QAAQC,UAC7CkB,KAAQ,KACRC,OAAU,KACVC,UAAa,MACbC,cAAiB,MACjBC,YAAe,KACfC,UAAa,KACbC,cAAiB,OACjBC,YAAe,MACfC,aAAgB,MAChBC,YAAe,OACfC,oBAAuB,SACvBC,kBAAqB,SACrBC,OAAU,OACVC,QAAW,OACXC,WAAc,OACdC,OAAU,OACVC,YAAe,OACfC,WAAc,OACdC,WAAc,SACdC,SAAY,QACZC,SAAY,QACZC,gBAAmB,SACnBC,SAAY,QACZC,gBAAmB,SACnBC,YAAe,KACfC,WAAc,KACdC,UAAa,OACbC,UAAa,QACbC,MAAS,KACTC,YAAe,QACfC,cAAiB,sBACjBC,WAAc,OACdC,eAAkB,OAClBC,QAAW,QACXC,YAAe,QACfC,YAAe,QACfC,gBAAmB,iCACnBC,WAAc,oBACdC,cAAiB,6BACjBC,kBAAqB,SACrBC,gBAAmB,SACnBC,aAAgB,QAChBC,WAAc,aACdC,YAAe,aACfC,eAAkB,SAClBC,UAAa,OACbC,eAAkB,OAClBC,SAAY,OACZC,YAAe,OACfC,oBAAuB,YACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,IACzBC,SAAY,KACZC,aAAgB,KAChBC,gBAAmB,OACnBC,YAAe,OACfC,gBAAmB,sBACnBC,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfC,UAAa,MACbC,aAAgB,MAChBC,YAAe,OACfC,SAAY,KACZC,QAAW,MACXC,iBAAoB,OACpBC,QAAW,KACXC,QAAW,KACXC,MAAS,IACTC,OAAU,IACVC,MAAS,KACTC,YAAe,QACfC,YAAe,SACfC,WAAc,SACdC,UAAa,KACbC,WAAc,MACdC,SAAY,MACZC,GAAM,KACNC,OAAU,KACVC,YAAe,OACfC,gBAAmB,OACnBC,SAAY,OACZC,0BAA6B,WAC7BC,UAAa,MACbC,YAAe,OACfC,WAAc,MACdC,aAAgB,OAChBC,eAAkB,OAClBC,cAAiB,OACjBC,gBAAmB,OACnBC,kBAAqB,OACrBC,iBAAoB,OACpBC,gBAAmB,OACnBC,kBAAqB,OACrBC,iBAAoB,OACpBC,YAAe,OACfvG,QAAW,IACXwG,KAAQ,IACRC,eAAkB,UAClBC,SAAY,KACZC,SAAU,QAKlB5H,MAAMC,GAAG4H,cACT7H,MAAMC,GAAG4H,YAAY1H,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4H,YAAY1H,UAAUC,QAAQC,UAClDiD,WAAc,OACdE,QAAW,OACXE,YAAe,QACfD,YAAe,QACfK,kBAAqB,SACrBV,YAAe,QACfQ,WAAc,oBACdD,gBAAmB,iCACnBE,cAAiB,6BACjBiE,cAAiB,aACjBC,OAAU,QAKlB/H,MAAMC,GAAG+H,aACThI,MAAMC,GAAG+H,WAAW7H,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,WAAW7H,UAAUC,QAAQC,UACjD4H,OAAU,IACVC,QAAW,IACXlH,OAAU,KACVmH,MAAS,KACTC,SAAY,SAKpBpI,MAAMC,GAAG+H,aACThI,MAAMC,GAAG+H,WAAW7H,UAAUC,QAAQiI,UAClCvI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,WAAW7H,UAAUC,QAAQiI,WACjDC,QACIC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,KAClBC,SAAY,MACZC,OAAU,KACVC,UAAa,KACbC,QAAW,OACXC,WAAc,QACdC,cAAiB,KACjBC,iBAAoB,MAExBC,QACIZ,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBU,MACIjB,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBW,OACIlB,GAAM,KACNC,IAAO,MACPK,OAAU,KACVC,UAAa,SAMzB9I,MAAMC,GAAGyJ,aACT1J,MAAMC,GAAGyJ,WAAWvJ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,WAAWvJ,UAAUC,QAAQC,UACjDsJ,KAAQ,QACRC,MAAS,QACT3B,OAAU,IACVC,QAAW,IACXlH,OAAU,KACVmH,MAAS,KACT0B,IAAO,KACPC,GAAM,KACNC,YAAe,YACf3B,SAAY,MACZ4B,MAAS,IACTC,gBAAmB,MACnBC,mBAAsB,OACtBC,MAAS,OACT3J,OAAU,QAKlBR,MAAMC,GAAGyJ,aACT1J,MAAMC,GAAGyJ,WAAWvJ,UAAUC,QAAQiI,UAClCvI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,WAAWvJ,UAAUC,QAAQiI,WACjDC,QACIC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,KAClBC,SAAY,MACZC,OAAU,KACVC,UAAa,KACbC,QAAW,OACXC,WAAc,QACdC,cAAiB,KACjBC,iBAAoB,MAExBC,QACIZ,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBU,MACIjB,GAAM,KACNC,IAAO,MACPY,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,KACNV,OAAU,KACVC,UAAa,MAEjBW,OACIlB,GAAM,KACNC,IAAO,MACPK,OAAU,KACVC,UAAa,SAMzB9I,MAAMC,GAAGmK,mBACTpK,MAAMC,GAAGmK,iBAAiBjK,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmK,iBAAiBjK,UAAUC,QAAQC,UACvDgK,SAAY,KACZlC,MAAS,KACTnH,OAAU,KACV+G,OAAU,KACVvH,OAAU,KACV8J,oBAAuB,iBAK/BtK,MAAMC,GAAGsK,QACTvK,MAAMC,GAAGsK,MAAMpK,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,MAAMpK,UAAUC,QAAQC,UAC5CmK,SACIC,SAAY,QACZC,OAAU,OACVC,YAAe,QACfC,aAAgB,QAChBC,IAAO,UAEXrK,OAAU,KACVsK,4BAA+B,SAC/BC,6BAAgC,iBAChCC,sBAAyB,OACzBC,uBAA0B,eAC1BC,QAAW,KACXC,QACIC,aAAgB,OAChBC,YAAe,OACfC,IAAO,OACPC,gBAAmB,OACnBC,UAAa,KACbC,qBAAwB,OACxBC,gBAAmB,OACnBC,MAAS,OACT/B,MAAS,OACTgC,YAAe,OAEnBC,KAAQ,KACRC,OACIC,IAAO,MACPT,IAAO,OACPU,MAAS,MACTL,MAAS,OACTM,KAAQ,MACRC,KAAQ,UAMpBlM,MAAMC,GAAGkM,gBACTnM,MAAMC,GAAGkM,cAAchM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkM,cAAchM,UAAUC,QAAQC,UACpDyL,OACIC,IAAO,MACPE,KAAQ,MACRD,MAAS,MACTE,KAAQ,MACRP,MAAS,OACTL,IAAO,WAMnBtL,MAAMC,GAAGmM,OACTpM,MAAMC,GAAGmM,KAAKjM,UAAUC,QAAQC,SAC5BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,KAAKjM,UAAUC,QAAQC,UAC3CgM,UACI7L,OAAU,KACV8L,WAAc,KACdC,OAAU,KACVrB,QAAW,KACXsB,KAAQ,KACRC,MAAS,WACT5B,IAAO,SACPgB,KAAQ,KACRa,OAAU,KACVC,OAAU,MAEdC,UACIC,aAAgB,OAChBC,aAAgB,eAChBC,cAAiB,QAErBC,UAAa,QACbC,2BAA8B,MAKtCjN,MAAMC,GAAGiN,YACTlN,MAAMC,GAAGiN,UAAU/M,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiN,UAAU/M,UAAUC,QAAQC,UAChD8M,MAAS,2BAKjBnN,MAAMC,GAAGmN,iBACTpN,MAAMC,GAAGmN,eAAejN,UAAUC,QAC9BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmN,eAAejN,UAAUC,SAC7CiN,YAAe,KACfC,cAAiB,QAKzBtN,MAAMC,GAAGsN,cACTvN,MAAMC,GAAGsN,YAAYpN,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,YAAYpN,UAAUC,QAAQC,UAClDmN,MAAS,KACTC,KAAQ,KACRC,KAAQ,KACRC,OAAU,OACVC,QAAW,KACXC,WAAc,QAKtB7N,MAAMC,GAAG6N,QACT9N,MAAMC,GAAG6N,MAAM3N,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6N,MAAM3N,UAAUC,QAAQC,UAC5C0N,SAAY,KACZC,QAAW,wBACXb,MAAS,QACTc,KAAQ,MACRC,GAAM,YACNC,aAAgB,MAChBC,MAAS,KACTC,SAAY,MACZC,KAAQ,MACRC,KAAQ,KACRC,QAAW,KACXC,UAAa,WAKrBzO,MAAMC,GAAGyO,YACT1O,MAAMC,GAAGyO,UAAUvO,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyO,UAAUvO,UAAUC,QAAQC,UAChDsO,cAAiB,YACjBC,aAAgB,WAChBC,UAAa,cAKrB7O,MAAMC,GAAG6O,iBACT9O,MAAMC,GAAG6O,eAAe3O,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6O,eAAe3O,UAAUC,QAAQC,UACrDsJ,KAAQ,QACR7I,cAAiB,OACjBC,eAAkB,OAClBgO,aAAgB,OAChB/N,OAAU,KACVgO,QAAW,UACXpF,MAAS,QACTzB,MAAS,KACT8G,GAAM,KACNzO,OAAU,KACV6H,WACIK,SAAY,KACZC,eAAkB,KAClBF,WAAc,MACdG,SAAY,MACZL,GAAM,KACNC,IAAO,UAMnBxI,MAAMC,GAAGiP,qBACTlP,MAAMC,GAAGiP,mBAAmB/O,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiP,mBAAmB/O,UAAUC,QAAQC,UACzD8M,MAAS,aAKjBnN,MAAMC,GAAGkP,oBACTnP,MAAMC,GAAGkP,kBAAkBhP,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkP,kBAAkBhP,UAAUC,QAAQC,UACxD+O,SAAY,YACZnO,QAAW,WACXwG,KAAQ,WACR4H,cAAiB,IACjBC,aAAgB,IAChBC,UAAa,IACbC,YAAe,QAKvBxP,MAAMC,GAAGwP,mBACTzP,MAAMC,GAAGwP,iBAAiBtP,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwP,iBAAiBtP,UAAUC,QAAQC,UACvDqP,sBAAyB,WACzBC,aACIC,MAAS,KACTC,OAAU,MACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEdJ,QACIK,YAAe,KACfC,SAAY,OAEhBL,OACII,YAAe,KACfC,SAAY,MAEhBJ,QACIG,YAAe,KACfE,SAAY,MACZD,SAAY,MAEhBH,SACIE,YAAe,KACfE,SAAY,MACZD,SAAY,KACZpE,IAAO,OAEXkE,QACIC,YAAe,KACfE,SAAY,MACZD,SAAY,KACZjC,GAAM,OAEV5C,KACI+E,MAAS,KACTC,YAAe,KACfV,MAAS,KACTW,MAAS,MACTC,WAAc,MACdC,GAAM,OAEVC,iBACItC,MAAS,KACTuC,OAAU,KACVC,MAAS,KACTC,OAAU,KACVtC,KAAQ,OAEZuC,UACI/E,IAAO,IACPgF,QAAW,MACXC,QAAW,SAMvBhR,MAAMC,GAAGgR,yBACTjR,MAAMC,GAAGgR,uBAAuB9Q,UAAUC,QAAQC,SAC9CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgR,uBAAuB9Q,UAAUC,QAAQC,UAC7DG,OAAU,KACVmM,OAAU,KACVuE,SAAY,OACZC,YAAe,OACfC,YAAe,OACf9F,KACI+F,UACIzB,MAAS,KACTW,MAAS,QACTE,GAAM,SAEVb,MAAS,KACTW,MAAS,QACTE,GAAM,SAEVX,OACIK,SAAY,IAEhBN,QACIM,SAAY,IAEhBJ,QACII,SAAY,IAEhBH,SACIG,SAAY,GACZmB,SAAY,OACZC,WAAc,KACdC,UAAa,KACbtB,YAAe,OACfuB,MAAS,IACT1F,IAAO,KAEXkE,QACIE,SAAY,GACZmB,SAAY,OACZC,WAAc,KACdC,UAAa,KACbtB,YAAe,OACfuB,MAAS,IACTzF,MAAS,IACTD,IAAO,QAMnB/L,MAAMC,GAAGyR,YACT1R,MAAMC,GAAGyR,UAAUvR,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyR,UAAUvR,UAAUC,QAAQC,UAChDsR,OAAU,KACVnI,KAAQ,KACRoI,MAAS,KACTC,KAAQ,KACRC,YAAe,OACfC,YAAe,SACfC,MAAS,KACTnH,IAAO,SACPgB,KAAQ,KACRrL,OAAU,KACV0K,QAAW,KACX+G,kBAAqB,OACrB3D,KAAQ,KACRD,SAAY,KACZ6D,cAAiB,sBACjBC,eAAkB,sBAClBvF,UACIE,aAAgB,gBAEpBhB,OACIC,IAAO,MACPE,KAAQ,MACRmG,SAAY,QACZC,OAAU,OACVrG,MAAS,MACTsG,SAAY,MACZC,aAAgB,SAChBC,iBAAoB,WACpBC,cAAiB,UAErBC,oBACIT,kBAAqB,WACrBU,uBAA0B,SAC1BC,mBAAsB,WACtBC,gBAAmB,WACnBC,qBAAwB,SACxBC,iBAAoB,WACpBC,gBAAmB,qBACnBC,cAAiB,sBAErB9H,QACIvB,MAAS,OACT+B,MAAS,OACTL,IAAO,OACP4H,YAAe,OACfC,YAAe,KACfC,OAAU,KACVC,SAAY,KACZC,cAAiB,OACjBC,YAAe,OACfC,kBAAqB,eACrBC,oBAAuB,OACvBC,qBAAwB,OACxBC,cAAiB,OACjBC,WAAc,MACdvI,YAAe,SAM3BrL,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAASyT,gBAChD9T,MAAM6T,YAAYxT,SAASyT,cACvBhU,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAASyT,eACtCC,WAAc,OACdC,cAAiB,MACjBC,wBAA2B,QAC3BC,sBAAyB,QACzBC,eAAkB,MAClBC,WAAc,MACdC,UAAa,MACbC,YAAe,MACfC,aAAgB,MAChBC,UAAa,MACbC,MAAS,QACTC,YAAe,UACfnU,MAAS,KACTC,OAAU,QAIlBR,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAASsU,UAChD3U,MAAM6T,YAAYxT,SAASsU,QACvB7U,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAASsU,SACtCpU,MAAS,KACTsL,KAAQ,KACRrL,OAAU,KACVoU,OAAU,KACVC,MAAS,KACTC,OAAU,KACVC,OAAU,KACVC,mBACIpL,MAAS,QACTqL,YACI9L,OAAU,KACV+L,SAAY,KACZ1L,KAAQ,OAGhB2L,kBACIvL,MAAS,MAEbwL,gBACIxL,MAAS,MAEbyL,eACIzL,MAAS,MAEb0L,iBACI1L,MAAS,KACT2L,SACIC,aAAgB,MAChB3T,cAAiB,OACjBE,aAAgB,MAChBC,YAAe,OACfyT,SAAY,OACZC,YAAe,OACfC,YAAe,SAGvBC,aACIhM,MAAS,QACT2L,SACIM,WAAc,OACdC,kBAAqB,OACrBC,gBAAmB,OACnBC,QAAW,SAGnBC,cACIrM,MAAS,OACT2L,SACIW,YAAe,SACfC,WAAc,QACdC,cAAiB,QACjBC,SAAY,SAGpBC,oBACIC,KAAQ,gBACR3M,MAAS,SAEb4M,kBACI5M,MAAS,OACT6M,YAAe,gBACfC,UAAa,QACbC,UACIC,IAAO,MACPzN,OAAU,KACVoN,KAAQ,KACR/M,KAAQ,KACRqN,OAAU,QACVC,KAAQ,MAEZC,WACIC,YAAe,KACfC,SAAY,KACZC,QAAW,KACXC,WAAc,MACdC,QAAW,KACXC,WAAc,MACdC,qBAAwB,OACxBC,kBAAqB,QAEzBC,kBACIR,YAAe,QACfC,SAAY,QACZC,QAAW,cACXC,WAAc,eACdC,QAAW,QACXC,WAAc,SACdC,qBAAwB,UACxBC,kBAAqB,UACrBV,OAAU,WAEdY,QACId,SAAY,KACZe,SAAY,KACZC,IAAO,MACPC,IAAO,MACP5N,MAAS,IACT2B,MAAS,KACTL,IAAO,KACPuM,cAAiB,SACjBC,YAAe,OACfC,YAAe,OACfC,SAAY,WACZtB,UAAa,SACbD,YAAe,SACfwB,YAAe,OACfC,eAAkB,SAClBC,mBAAsB,UAE1BC,cACIC,UAAa,QACbC,YAAe,UAGvBC,gBACI3O,MAAS,QACT6N,QACIe,MAAS,KACTC,IAAO,QACPC,SAAY,MACZC,WAAc,OACdC,WAAc,OACdC,UAAa,OACbC,QAAW,MACXC,YAAe,OACfC,MAAS,KACTC,WAAc,MACdC,OAAU,OACVC,aAAgB,KAChBC,WAAc,OAGtBC,oBACIC,aAAgB,kBAEpBC,qBACID,aAAgB,gBAEpBE,0BACIF,aAAgB,SAEpBG,uBACIH,aAAgB,YAEpBI,0BACIJ,aAAgB,WAEpBK,gBACIL,aAAgB,8BAEpBM,mBACIhQ,MAAS,QACT0P,aAAgB,2BAChB7B,QACIoC,QAAW,MACXC,OAAU,MACVC,SAAY,QAGpBC,4BACIV,aAAgB,mBAEpBW,YACIrQ,MAAS,KACT6N,QACIlB,KAAQ,OACR2D,IAAO,OACPC,WAAc,YAM9Bna,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAAS+Z,aAChDpa,MAAM6T,YAAYxT,SAAS+Z,WACvBta,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAAS+Z,YACtCtZ,cAAiB,WACjBC,eAAkB,WAClBsZ,cAAiB,OACjBC,kBAAqB,OACrB/Z,MAAS,KACTwH,OAAU,KACVwS,aAAgB,UAChBpS,MAAS,KACTqS,OAAU,MACVC,aAAgB,IAChB5Q,IAAO,KACPC,GAAM,KACNzB,WACIC,QACII,SAAY,OACZC,eAAkB,QAClBF,WAAc,QACdG,SAAY,QACZ8R,QAAW,OACXC,aAAgB,SAEpBnR,MACIjB,GAAO,OACPC,IAAO,QACPe,GAAO,OACPF,GAAO,QAEXF,QACIZ,GAAM,OACNC,IAAO,QACPY,IAAO,SACPC,GAAM,OACNC,IAAO,SACPC,GAAM,YAMtBvJ,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAASua,cAChD5a,MAAM6T,YAAYxT,SAASua,YACvB9a,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAASua,aACtCnG,MAAS,QACTC,YAAe,UACfnU,MAAS,KACTC,OAAU,QAIlBR,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAASwa,UAChD7a,MAAM6T,YAAYxT,SAASwa,QACvB/a,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAASwa,SACtC5V,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfe,UAAa,KACb2U,kBACItF,aAAgB,MAChB3T,cAAiB,OACjBE,aAAgB,MAChBC,YAAe,OACfyT,SAAY,OACZC,YAAe,OACfC,YAAe,QAEnBoF,gBAAmB,OACnBxZ,KAAQ,KACRyZ,QAAW,KACXJ,aACInG,MAAS,QACTC,YAAe,WAEnBuG,KAAQ,KACRC,IAAO,KACP5V,aAAgB,OAChBD,UAAa,OACb8V,YAAe,gBACfxT,SAAY,SACZ3G,OAAU,KACVoa,WAAc,KACdvY,SAAY,KACZwY,OAAU,UACVC,aACIC,UAAa,KACbhF,KAAQ,KACRpN,OAAU,KACVqS,QAAW,MACXC,UAAa,KACbvG,SAAY,KACZ1L,KAAQ,KACRqI,KAAQ,KACR6J,SAAY,OACZC,SAAY,MACZC,YAAe,WAEnBC,sBAAyB,SACzBC,sBAAyB,SACzBC,OAAU,OACVC,eACI9F,YAAe,SACfC,WAAc,QACdC,cAAiB,QACjBC,SAAY,QAEhB7U,OAAU,KACVya,MAAS,QACTC,cACIrG,WAAc,OACdC,kBAAqB,OACrBC,gBAAmB,OACnBC,QAAW,QAEfmG,KAAQ,QACRC,MAAS,KACTC,aACIC,KAAQ,KACRC,KAAQ,MAEZC,gBAAmB,QACnBC,OAAU,SACVC,KAAQ,KACRC,QAAW,OACXC,SAAY,OACZC,aACIC,aAAgB,aAChBC,cAAiB,aACjBC,aAAgB,eAChBC,cAAiB,gBAErBC,UAAa,OACbC,SAAY,OACZ1b,UAAa,MACb2b,WAAc,UACdC,UAAa,QAIrBrd,MAAM6T,aAAe7T,MAAM6T,YAAYxT,SAASid,OAChDtd,MAAM6T,YAAYxT,SAASid,KACvBxd,EAAEQ,QAAO,EAAMN,MAAM6T,YAAYxT,SAASid,MACtCC,QACIC,gBAAmB,8BACnBC,sBAAyB,yCACzBC,8BAAiC,gBACjCC,8BAAiC,gBACjCC,4BAA+B,qBAC/BC,0BAA6B,mBAC7BC,0BAA6B,cAC7BC,gBAAmB,gBACnBC,mBAAsB,sBACtBC,gBAAmB,sBACnBC,qBAAwB,gBAE5BC,MACIC,KAAQ,KACRC,OAAU,KACVC,KAAQ,SAMpBte,MAAMC,GAAGse,SACTve,MAAMC,GAAGse,OAAOpe,UAAUC,QACtBN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGse,OAAOpe,UAAUC,SACrCoe,oBAAuB,KACvBC,oBAAuB,KACvBC,gBAAmB,OAK3B1e,MAAMC,GAAG0e,UACT3e,MAAMC,GAAG0e,QAAQxe,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0e,QAAQxe,UAAUC,QAAQC,UAC9Cue,OACIhK,OAAU,KACViK,OAAU,KACVC,SAAY,KACZC,WAAc,OACdC,aAAgB,OAChBC,cAAiB,SACjBC,gBAAmB,aAM/Blf,MAAMC,GAAGkf,WACTnf,MAAMC,GAAGkf,SAAShf,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkf,SAAShf,UAAUC,QAAQC,UAC/C+e,OAAU,QACVC,QAAW,SACXC,cAAiB,QACjBzK,MAAS,KACTxI,UACIG,KAAQ,KACRG,OAAU,KACVL,WAAc,KACdC,OAAU,KACVgT,YAAe,OACfrU,QAAW,KACXuB,MAAS,WACT5B,IAAO,aAMnB7K,MAAMC,GAAGuf,WACTxf,MAAMC,GAAGuf,SAASrf,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuf,SAASrf,UAAUC,QAAQC,UAC/Cgf,QAAW,SACXC,cAAiB,QACjBzK,MAAS,QAKjB7U,MAAMC,GAAGwf,WACTzf,MAAMC,GAAGwf,SAAStf,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwf,SAAStf,UAAUC,QAAQC,UAC/Cgf,QAAW,SACXC,cAAiB,QACjBzK,MAAS,QAKjB7U,MAAMC,GAAGyf,SACT1f,MAAMC,GAAGyf,OAAOvf,UAAUC,QAAQuf,aAC9B7f,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyf,OAAOvf,UAAUC,QAAQuf,cAC7CjT,OAAU,OACVlM,OAAU,KACVqU,MAAS,KACTD,OAAU,KACVpH,MAAS,KACToS,OAAU,KACVC,mBAAsB,KACtBC,oBAAuB,KACvBhY,cAAiB,aACjBiY,aAAgB,WAChBC,gBAAmB,SACnBC,eAAkB,QAClBC,cAAiB,QACjBC,aAAgB,QAChBC,sBAAyB,SACzBC,mBAAsB,OACtBC,qBAAwB,QACxBC,mBAAsB,QACtBC,mBAAsB,QACtBC,qBAAwB,eAKhCzgB,MAAMC,GAAGygB,YACT1gB,MAAMC,GAAGygB,UAAUvgB,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGygB,UAAUvgB,UAAUC,QAAQC,UAChDsgB,SAAY,YACZC,QAAW,cACXjJ,IAAO,oBACPC,IAAO,oBACPiJ,KAAQ,gBACRC,MAAS,mBACT5G,IAAO,iBACP1Q,KAAQ,iBACRuX,YAAe,sBAKvB/gB,MAAMC,GAAG+gB,WACThhB,MAAMC,GAAG+gB,SAAS3gB,SACdP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+gB,SAAS3gB,UAC7Bgf,QAAW,YAKnBrf,MAAMC,GAAGghB,SACTjhB,MAAMC,GAAGghB,OAAO9gB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGghB,OAAO9gB,UAAUC,QAAQuf,cAC7CuB,MAAS,QAKjBlhB,MAAMC,GAAGkhB,WACTnhB,MAAMC,GAAGkhB,SAAShhB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkhB,SAAShhB,UAAUC,QAAQC,UAC/C+gB,iBAAoB,MAK5BphB,MAAMC,GAAGohB,QACTrhB,MAAMC,GAAGohB,MAAMlhB,UAAUC,QAAQC,SAC7BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGohB,MAAMlhB,UAAUC,QAAQuf,cAC5C5K,OAAU,QAKlB/U,MAAMC,GAAGqhB,UACTthB,MAAMC,GAAGqhB,QAAQnhB,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqhB,QAAQnhB,UAAUC,QAAQuf,cAC9C5K,OAAU,KACVvU,OAAU,QAKlBR,MAAMC,GAAGshB,SACTvhB,MAAMC,GAAGshB,OAAOphB,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGshB,OAAOphB,UAAUC,QAAQuf,cAC7C5K,OAAU,KACVvU,OAAU,QAKlBR,MAAMC,GAAGuhB,YACTxhB,MAAMC,GAAGuhB,UAAUrhB,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuhB,UAAUrhB,UAAUC,QAAQC,UAChD6L,KAAQ,IACRF,MAAS,IACTD,IAAO,IACPgF,QAAW,IACX0Q,KAAQ,IACRC,OAAU,IACV/Q,OAAU,IACVgR,UAAa,WAOrB3hB,MAAM4hB,OAAO3hB,GAAG4hB,WAChB7hB,MAAM4hB,OAAO3hB,GAAG4hB,SAAS1hB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAM4hB,OAAO3hB,GAAG4hB,SAAS1hB,UAAUC,QAAQC,UACtDyhB,aAAgB,OAChBC,gBAAmB,OACnBC,gBAAmB,YAK3BhiB,MAAM4hB,OAAO3hB,GAAGgiB,WAChBjiB,MAAM4hB,OAAO3hB,GAAGgiB,SAAS9hB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAM4hB,OAAO3hB,GAAGgiB,SAAS9hB,UAAUC,QAAQC,UACtD6hB,aAAgB,SAChBJ,aAAgB,OAChBC,gBAAmB,OACnBC,gBAAmB,aAIhCG,OAAOniB,MAAMoiB", "file": "kendo.messages.zh-CN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n/* JS for All Kendo UI Components Simplified Chinese (zh-CN) Language Pack | Written by IKKI | 2018-02-03 */\n(function ($, undefined) {\n\n    /* FlatColorPicker messages */\n    if (kendo.ui.FlatColorPicker) {\n        kendo.ui.FlatColorPicker.prototype.options.messages =\n            $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n                \"apply\": \"确定\",\n                \"cancel\": \"取消\",\n                \"noColor\": \"无颜色\",\n                \"clearColor\": \"清除颜色\",\n                \"previewInput\": \"颜色十六进制代码\"\n            });\n    }\n\n    /* ColorPicker messages */\n    if (kendo.ui.ColorPicker) {\n        kendo.ui.ColorPicker.prototype.options.messages =\n            $.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n                \"apply\": \"确定\",\n                \"cancel\": \"取消\",\n                \"noColor\": \"无颜色\",\n                \"clearColor\": \"清除颜色\",\n                \"previewInput\": \"颜色十六进制代码\"\n            });\n    }\n\n    /* ColumnMenu messages */\n    if (kendo.ui.ColumnMenu) {\n        kendo.ui.ColumnMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n                \"sortAscending\": \"升序排列\",\n                \"sortDescending\": \"降序排列\",\n                \"filter\": \"筛选\",\n                \"columns\": \"字段列\",\n                \"done\": \"完成\",\n                \"settings\": \"列设置\",\n                \"lock\": \"锁定\",\n                \"unlock\": \"解锁\"\n            });\n    }\n\n    /* Editor messages */\n    if (kendo.ui.Editor) {\n        kendo.ui.Editor.prototype.options.messages =\n            $.extend(true, kendo.ui.Editor.prototype.options.messages,{\n                \"bold\": \"粗体\",\n                \"italic\": \"斜体\",\n                \"underline\": \"下划线\",\n                \"strikethrough\": \"删除线\",\n                \"superscript\": \"上标\",\n                \"subscript\": \"下标\",\n                \"justifyCenter\": \"水平居中\",\n                \"justifyLeft\": \"左对齐\",\n                \"justifyRight\": \"右对齐\",\n                \"justifyFull\": \"两端对齐\",\n                \"insertUnorderedList\": \"插入无序列表\",\n                \"insertOrderedList\": \"插入有序列表\",\n                \"indent\": \"增加缩进\",\n                \"outdent\": \"减少缩进\",\n                \"createLink\": \"插入链接\",\n                \"unlink\": \"删除链接\",\n                \"insertImage\": \"插入图片\",\n                \"insertFile\": \"插入文件\",\n                \"insertHtml\": \"插入HTML\",\n                \"viewHtml\": \"源代码编辑\",\n                \"fontName\": \"请选择字体\",\n                \"fontNameInherit\": \"（默认字体）\",\n                \"fontSize\": \"请选择字号\",\n                \"fontSizeInherit\": \"（默认字号）\",\n                \"formatBlock\": \"格式\",\n                \"formatting\": \"格式\",\n                \"foreColor\": \"文字颜色\",\n                \"backColor\": \"文字背景色\",\n                \"style\": \"样式\",\n                \"emptyFolder\": \"文件夹为空\",\n                \"editAreaTitle\": \"在可编辑区域可按 F10 跳转工具栏。\",\n                \"uploadFile\": \"上传文件\",\n                \"overflowAnchor\": \"更多功能\",\n                \"orderBy\": \"排序方式：\",\n                \"orderBySize\": \"按大小排序\",\n                \"orderByName\": \"按名称排序\",\n                \"invalidFileType\": \"你上传的文件格式 {0} 是无效的，支持的文件类型为：{1}\",\n                \"deleteFile\": \"你确定要删除【{0}】这个文件吗？\",\n                \"overwriteFile\": \"当前文件夹已存在文件名为【{0}】的文件，是否覆盖？\",\n                \"directoryNotFound\": \"文件夹未找到\",\n                \"imageWebAddress\": \"图片链接地址\",\n                \"imageAltText\": \"图片占位符\",\n                \"imageWidth\": \"图片宽度（单位px）\",\n                \"imageHeight\": \"图片高度（单位px）\",\n                \"fileWebAddress\": \"文件链接地址\",\n                \"fileTitle\": \"文件标题\",\n                \"linkWebAddress\": \"链接地址\",\n                \"linkText\": \"链接文字\",\n                \"linkToolTip\": \"链接提示\",\n                \"linkOpenInNewWindow\": \"在新窗口中打开链接\",\n                \"dialogUpdate\": \"更新\",\n                \"dialogInsert\": \"插入\",\n                \"dialogButtonSeparator\": \"或\",\n                \"dialogOk\": \"确定\",\n                \"dialogCancel\": \"取消\",\n                \"cleanFormatting\": \"清除格式\",\n                \"createTable\": \"创建表格\",\n                \"createTableHint\": \"创建一个 {0} 行 {1} 列的表格\",\n                \"addColumnLeft\": \"在左侧插入列\",\n                \"addColumnRight\": \"在右侧插入列\",\n                \"addRowAbove\": \"在上方插入行\",\n                \"addRowBelow\": \"在下方插入行\",\n                \"deleteRow\": \"删除行\",\n                \"deleteColumn\": \"删除列\",\n                \"tableWizard\": \"表格向导\",\n                \"tableTab\": \"表格\",\n                \"cellTab\": \"单元格\",\n                \"accessibilityTab\": \"可访问性\",\n                \"caption\": \"标题\",\n                \"summary\": \"摘要\",\n                \"width\": \"宽\",\n                \"height\": \"高\",\n                \"units\": \"单位\",\n                \"cellSpacing\": \"单元格间距\",\n                \"cellPadding\": \"单元格内边距\",\n                \"cellMargin\": \"单元格外边距\",\n                \"alignment\": \"对齐\",\n                \"background\": \"背景色\",\n                \"cssClass\": \"样式表\",\n                \"id\": \"ID\",\n                \"border\": \"边框\",\n                \"borderStyle\": \"边框样式\",\n                \"collapseBorders\": \"合并边框\",\n                \"wrapText\": \"文字换行\",\n                \"associateCellsWithHeaders\": \"关联表头与单元格\",\n                \"alignLeft\": \"左对齐\",\n                \"alignCenter\": \"居中对齐\",\n                \"alignRight\": \"右对齐\",\n                \"alignLeftTop\": \"左上对齐\",\n                \"alignCenterTop\": \"中上对齐\",\n                \"alignRightTop\": \"右上对齐\",\n                \"alignLeftMiddle\": \"左中对齐\",\n                \"alignCenterMiddle\": \"居中对齐\",\n                \"alignRightMiddle\": \"右中对齐\",\n                \"alignLeftBottom\": \"左下对齐\",\n                \"alignCenterBottom\": \"中下对齐\",\n                \"alignRightBottom\": \"右下对齐\",\n                \"alignRemove\": \"移除对齐\",\n                \"columns\": \"列\",\n                \"rows\": \"行\",\n                \"selectAllCells\": \"选择所有单元格\",\n                \"exportAs\": \"导出\",\n                \"import\": \"导入\"\n            });\n    }\n\n    /* FileBrowser messages */\n    if (kendo.ui.FileBrowser) {\n        kendo.ui.FileBrowser.prototype.options.messages =\n            $.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n                \"uploadFile\": \"上传文件\",\n                \"orderBy\": \"排序方式\",\n                \"orderByName\": \"按名称排序\",\n                \"orderBySize\": \"按大小排序\",\n                \"directoryNotFound\": \"文件夹未找到\",\n                \"emptyFolder\": \"文件夹为空\",\n                \"deleteFile\": \"你确定要删除【{0}】这个文件吗？\",\n                \"invalidFileType\": \"你上传的文件格式 {0} 是无效的，支持的文件类型为：{1}\",\n                \"overwriteFile\": \"当前文件夹已存在文件名为【{0}】的文件，是否覆盖？\",\n                \"dropFilesHere\": \"将文件拖拽到此处上传\",\n                \"search\": \"搜索\"\n            });\n    }\n\n    /* FilterCell messages */\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n                \"isTrue\": \"是\",\n                \"isFalse\": \"否\",\n                \"filter\": \"筛选\",\n                \"clear\": \"清空\",\n                \"operator\": \"运算符\"\n            });\n    }\n\n    /* FilterCell operators */\n    if (kendo.ui.FilterCell) {\n        kendo.ui.FilterCell.prototype.options.operators =\n            $.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n                \"string\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"startswith\": \"开头是\",\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"endswith\": \"结尾是\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\",\n                    \"isempty\": \"空字符串\",\n                    \"isnotempty\": \"非空字符串\",\n                    \"isnullorempty\": \"无值\",\n                    \"isnotnullorempty\": \"有值\"\n                },\n                \"number\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"gte\": \"大于等于\",\n                    \"gt\": \"大于\",\n                    \"lte\": \"小于等于\",\n                    \"lt\": \"小于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"date\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"gte\": \"晚于等于\",\n                    \"gt\": \"晚于\",\n                    \"lte\": \"早于等于\",\n                    \"lt\": \"早于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"enums\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                }\n            });\n    }\n\n    /* FilterMenu messages */\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n                \"info\": \"筛选条件：\",\n                \"title\": \"筛选条件：\",\n                \"isTrue\": \"是\",\n                \"isFalse\": \"否\",\n                \"filter\": \"筛选\",\n                \"clear\": \"清空\",\n                \"and\": \"并且\",\n                \"or\": \"或者\",\n                \"selectValue\": \"-= 请选择 =-\",\n                \"operator\": \"运算符\",\n                \"value\": \"值\",\n                \"additionalValue\": \"附加值\",\n                \"additionalOperator\": \"附加运算\",\n                \"logic\": \"筛选逻辑\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* FilterMenu operator messages */\n    if (kendo.ui.FilterMenu) {\n        kendo.ui.FilterMenu.prototype.options.operators =\n            $.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n                \"string\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"startswith\": \"开头是\",\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"endswith\": \"结尾是\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\",\n                    \"isempty\": \"空字符串\",\n                    \"isnotempty\": \"非空字符串\",\n                    \"isnullorempty\": \"无值\",\n                    \"isnotnullorempty\": \"有值\"\n                },\n                \"number\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"gte\": \"大于等于\",\n                    \"gt\": \"大于\",\n                    \"lte\": \"小于等于\",\n                    \"lt\": \"小于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"date\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"gte\": \"晚于等于\",\n                    \"gt\": \"晚于\",\n                    \"lte\": \"早于等于\",\n                    \"lt\": \"早于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                },\n                \"enums\": {\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\",\n                    \"isnull\": \"为空\",\n                    \"isnotnull\": \"非空\"\n                }\n            });\n    }\n\n    /* FilterMultiCheck messages */\n    if (kendo.ui.FilterMultiCheck) {\n        kendo.ui.FilterMultiCheck.prototype.options.messages =\n            $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n                \"checkAll\": \"全选\",\n                \"clear\": \"清空\",\n                \"filter\": \"筛选\",\n                \"search\": \"搜索\",\n                \"cancel\": \"取消\",\n                \"selectedItemsFormat\": \"已选择 {0} 条数据\"\n            });\n    }\n\n    /* Gantt messages */\n    if (kendo.ui.Gantt) {\n        kendo.ui.Gantt.prototype.options.messages =\n            $.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n                \"actions\": {\n                    \"addChild\": \"新增子任务\",\n                    \"append\": \"新增任务\",\n                    \"insertAfter\": \"插入到后面\",\n                    \"insertBefore\": \"插入到前面\",\n                    \"pdf\": \"导出 PDF\"\n                },\n                \"cancel\": \"取消\",\n                \"deleteDependencyWindowTitle\": \"删除从属任务\",\n                \"deleteDependencyConfirmation\": \"你确定要删除这项从属任务吗？\",\n                \"deleteTaskWindowTitle\": \"删除任务\",\n                \"deleteTaskConfirmation\": \"你确定要删除这项任务吗？\",\n                \"destroy\": \"删除\",\n                \"editor\": {\n                    \"assingButton\": \"资源分配\",\n                    \"editorTitle\": \"编辑任务\",\n                    \"end\": \"结束时间\",\n                    \"percentComplete\": \"完成进度\",\n                    \"resources\": \"资源\",\n                    \"resourcesEditorTitle\": \"资源编辑\",\n                    \"resourcesHeader\": \"资源名称\",\n                    \"start\": \"开始时间\",\n                    \"title\": \"任务标题\",\n                    \"unitsHeader\": \"百分比\"\n                },\n                \"save\": \"保存\",\n                \"views\": {\n                    \"day\": \"日视图\",\n                    \"end\": \"任务结束\",\n                    \"month\": \"月视图\",\n                    \"start\": \"任务开始\",\n                    \"week\": \"周视图\",\n                    \"year\": \"年视图\"\n                }\n            });\n    }\n\n    /* GanttTimeline messages */\n    if (kendo.ui.GanttTimeline) {\n        kendo.ui.GanttTimeline.prototype.options.messages =\n            $.extend(true, kendo.ui.GanttTimeline.prototype.options.messages,{\n                \"views\": {\n                    \"day\": \"日视图\",\n                    \"week\": \"周视图\",\n                    \"month\": \"月视图\",\n                    \"year\": \"年视图\",\n                    \"start\": \"任务开始\",\n                    \"end\": \"任务结束\"\n                }\n            });\n    }\n\n    /* Grid messages */\n    if (kendo.ui.Grid) {\n        kendo.ui.Grid.prototype.options.messages =\n            $.extend(true, kendo.ui.Grid.prototype.options.messages,{\n                \"commands\": {\n                    \"cancel\": \"取消\",\n                    \"canceledit\": \"取消\",\n                    \"create\": \"新增\",\n                    \"destroy\": \"删除\",\n                    \"edit\": \"编辑\",\n                    \"excel\": \"导出 Excel\",\n                    \"pdf\": \"导出 PDF\",\n                    \"save\": \"保存\",\n                    \"select\": \"选择\",\n                    \"update\": \"更新\"\n                },\n                \"editable\": {\n                    \"cancelDelete\": \"取消删除\",\n                    \"confirmation\": \"你确定要删除这条数据吗？\",\n                    \"confirmDelete\": \"确定删除\"\n                },\n                \"noRecords\": \"无相关数据\",\n                \"expandCollapseColumnHeader\": \"\"\n            });\n    }\n\n    /* Groupable messages */\n    if (kendo.ui.Groupable) {\n        kendo.ui.Groupable.prototype.options.messages =\n            $.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n                \"empty\": \"将字段列名称拖拽到此处可进行该列的分组显示\"\n            });\n    }\n\n    /* NumericTextBox messages */\n    if (kendo.ui.NumericTextBox) {\n        kendo.ui.NumericTextBox.prototype.options =\n            $.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n                \"upArrowText\": \"增加\",\n                \"downArrowText\": \"减少\"\n            });\n    }\n\n    /* MediaPlayer messages */\n    if (kendo.ui.MediaPlayer) {\n        kendo.ui.MediaPlayer.prototype.options.messages =\n            $.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n                \"pause\": \"暂停\",\n                \"play\": \"播放\",\n                \"mute\": \"静音\",\n                \"unmute\": \"取消静音\",\n                \"quality\": \"画质\",\n                \"fullscreen\": \"全屏\"\n            });\n    }\n\n    /* Pager messages */\n    if (kendo.ui.Pager) {\n        kendo.ui.Pager.prototype.options.messages =\n            $.extend(true, kendo.ui.Pager.prototype.options.messages,{\n                \"allPages\": \"全部\",\n                \"display\": \"{0} - {1} 条　共 {2} 条数据\",\n                \"empty\": \"无相关数据\",\n                \"page\": \"转到第\",\n                \"of\": \"页　共 {0} 页\",\n                \"itemsPerPage\": \"条每页\",\n                \"first\": \"首页\",\n                \"previous\": \"上一页\",\n                \"next\": \"下一页\",\n                \"last\": \"末页\",\n                \"refresh\": \"刷新\",\n                \"morePages\": \"更多...\"\n            });\n    }\n\n    /* PivotGrid messages */\n    if (kendo.ui.PivotGrid) {\n        kendo.ui.PivotGrid.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n                \"measureFields\": \"拖拽数据字段到此处\",\n                \"columnFields\": \"拖拽列字段到此处\",\n                \"rowFields\": \"拖拽行字段到此处\"\n            });\n    }\n\n    /* PivotFieldMenu messages */\n    if (kendo.ui.PivotFieldMenu) {\n        kendo.ui.PivotFieldMenu.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n                \"info\": \"筛选条件：\",\n                \"sortAscending\": \"升序排列\",\n                \"sortDescending\": \"降序排列\",\n                \"filterFields\": \"字段筛选\",\n                \"filter\": \"筛选\",\n                \"include\": \"包含字段...\",\n                \"title\": \"包含的字段\",\n                \"clear\": \"清空\",\n                \"ok\": \"确定\",\n                \"cancel\": \"取消\",\n                \"operators\": {\n                    \"contains\": \"包含\",\n                    \"doesnotcontain\": \"不含\",\n                    \"startswith\": \"开头是\",\n                    \"endswith\": \"结尾是\",\n                    \"eq\": \"等于\",\n                    \"neq\": \"不等于\"\n                }\n            });\n    }\n\n    /* PivotSettingTarget messages */\n    if (kendo.ui.PivotSettingTarget) {\n        kendo.ui.PivotSettingTarget.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotSettingTarget.prototype.options.messages,{\n                \"empty\": \"拖拽字段到此处\"\n            });\n    }\n\n    /* PivotConfigurator messages */\n    if (kendo.ui.PivotConfigurator) {\n        kendo.ui.PivotConfigurator.prototype.options.messages =\n            $.extend(true, kendo.ui.PivotConfigurator.prototype.options.messages,{\n                \"measures\": \"拖拽数据字段到此处\",\n                \"columns\": \"拖拽列字段到此处\",\n                \"rows\": \"拖拽行字段到此处\",\n                \"measuresLabel\": \"量\",\n                \"columnsLabel\": \"列\",\n                \"rowsLabel\": \"行\",\n                \"fieldsLabel\": \"字段\"\n            });\n    }\n\n    /* RecurrenceEditor messages */\n    if (kendo.ui.RecurrenceEditor) {\n        kendo.ui.RecurrenceEditor.prototype.options.messages =\n            $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n                \"recurrenceEditorTitle\": \"周期类型事件编辑\",\n                \"frequencies\": {\n                    \"never\": \"从不\",\n                    \"hourly\": \"每小时\",\n                    \"daily\": \"每天\",\n                    \"weekly\": \"每周\",\n                    \"monthly\": \"每月\",\n                    \"yearly\": \"每年\"\n                },\n                \"hourly\": {\n                    \"repeatEvery\": \"周期\",\n                    \"interval\": \" 小时\"\n                },\n                \"daily\": {\n                    \"repeatEvery\": \"周期\",\n                    \"interval\": \" 天\"\n                },\n                \"weekly\": {\n                    \"repeatEvery\": \"周期\",\n                    \"repeatOn\": \"重复于\",\n                    \"interval\": \" 周\"\n                },\n                \"monthly\": {\n                    \"repeatEvery\": \"周期\",\n                    \"repeatOn\": \"重复于\",\n                    \"interval\": \" 月\",\n                    \"day\": \"几号 \"\n                },\n                \"yearly\": {\n                    \"repeatEvery\": \"周期\",\n                    \"repeatOn\": \"重复于\",\n                    \"interval\": \" 年\",\n                    \"of\": \" 在 \"\n                },\n                \"end\": {\n                    \"label\": \"截止\",\n                    \"mobileLabel\": \"截止\",\n                    \"never\": \"从不\",\n                    \"after\": \"重复 \",\n                    \"occurrence\": \" 次后\",\n                    \"on\": \"止于 \"\n                },\n                \"offsetPositions\": {\n                    \"first\": \"第一\",\n                    \"second\": \"第二\",\n                    \"third\": \"第三\",\n                    \"fourth\": \"第四\",\n                    \"last\": \"最后一\"\n                },\n                \"weekdays\": {\n                    \"day\": \"天\",\n                    \"weekday\": \"工作日\",\n                    \"weekend\": \"周末\"\n                }\n            });\n    }\n\n    /* MobileRecurrenceEditor messages */\n    if (kendo.ui.MobileRecurrenceEditor) {\n        kendo.ui.MobileRecurrenceEditor.prototype.options.messages =\n            $.extend(true, kendo.ui.MobileRecurrenceEditor.prototype.options.messages,{\n                \"cancel\": \"取消\",\n                \"update\": \"保存\",\n                \"endTitle\": \"周期截止\",\n                \"repeatTitle\": \"周期模式\",\n                \"headerTitle\": \"周期事件\",\n                \"end\": {\n                    \"patterns\": {\n                        \"never\": \"从不\",\n                        \"after\": \"重复...\",\n                        \"on\": \"止于...\"\n                    },\n                    \"never\": \"从不\",\n                    \"after\": \"周期后截止\",\n                    \"on\": \"周期时截止\"\n                },\n                \"daily\": {\n                    \"interval\": \"\"\n                },\n                \"hourly\": {\n                    \"interval\": \"\"\n                },\n                \"weekly\": {\n                    \"interval\": \"\"\n                },\n                \"monthly\": {\n                    \"interval\": \"\",\n                    \"repeatBy\": \"重复到：\",\n                    \"dayOfMonth\": \"几号\",\n                    \"dayOfWeek\": \"周几\",\n                    \"repeatEvery\": \"全部重复\",\n                    \"every\": \"每\",\n                    \"day\": \"天\"\n                },\n                \"yearly\": {\n                    \"interval\": \"\",\n                    \"repeatBy\": \"重复到：\",\n                    \"dayOfMonth\": \"几号\",\n                    \"dayOfWeek\": \"周几\",\n                    \"repeatEvery\": \"全部重复\",\n                    \"every\": \"每\",\n                    \"month\": \"月\",\n                    \"day\": \"天\"\n                }\n            });\n    }\n\n    /* Scheduler messages */\n    if (kendo.ui.Scheduler) {\n        kendo.ui.Scheduler.prototype.options.messages =\n            $.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n                \"allDay\": \"全天\",\n                \"date\": \"日期\",\n                \"event\": \"事件\",\n                \"time\": \"时间\",\n                \"showFullDay\": \"显示全天\",\n                \"showWorkDay\": \"显示工作时间\",\n                \"today\": \"今天\",\n                \"pdf\": \"导出 PDF\",\n                \"save\": \"保存\",\n                \"cancel\": \"取消\",\n                \"destroy\": \"删除\",\n                \"deleteWindowTitle\": \"删除事件\",\n                \"next\": \"往后\",\n                \"previous\": \"往前\",\n                \"ariaSlotLabel\": \"从 {0:t} 到 {1:t} 的选择\",\n                \"ariaEventLabel\": \"在 {1:D} {2:t} 的 {0}\",\n                \"editable\": {\n                    \"confirmation\": \"你确定要删除这个事件吗？\"\n                },\n                \"views\": {\n                    \"day\": \"日视图\",\n                    \"week\": \"周视图\",\n                    \"workWeek\": \"工作日视图\",\n                    \"agenda\": \"列表视图\",\n                    \"month\": \"月视图\",\n                    \"timeline\": \"时间线\",\n                    \"timelineWeek\": \"时间线周视图\",\n                    \"timelineWorkWeek\": \"时间线工作日视图\",\n                    \"timelineMonth\": \"时间线月视图\"\n                },\n                \"recurrenceMessages\": {\n                    \"deleteWindowTitle\": \"删除周期类型事件\",\n                    \"deleteWindowOccurrence\": \"删除当前事件\",\n                    \"deleteWindowSeries\": \"删除整个周期事件\",\n                    \"editWindowTitle\": \"编辑周期类型事件\",\n                    \"editWindowOccurrence\": \"编辑当前事件\",\n                    \"editWindowSeries\": \"编辑整个周期事件\",\n                    \"deleteRecurring\": \"你想仅删除当前事件还是整个周期事件？\",\n                    \"editRecurring\": \"你想仅编辑当前事件还是整个周期事件？\"\n                },\n                \"editor\": {\n                    \"title\": \"事件标题\",\n                    \"start\": \"开始时间\",\n                    \"end\": \"结束时间\",\n                    \"allDayEvent\": \"全天事件\",\n                    \"description\": \"描述\",\n                    \"repeat\": \"重复\",\n                    \"timezone\": \"时区\",\n                    \"startTimezone\": \"开始时区\",\n                    \"endTimezone\": \"结束时区\",\n                    \"separateTimezones\": \"使用独立的开始和结束时区\",\n                    \"timezoneEditorTitle\": \"时区设置\",\n                    \"timezoneEditorButton\": \"时区选择\",\n                    \"timezoneTitle\": \"选择时区\",\n                    \"noTimezone\": \"无时区\",\n                    \"editorTitle\": \"事件\"\n                }\n            });\n    }\n\n    /* Spreadsheet messages */\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\n        kendo.spreadsheet.messages.borderPalette =\n            $.extend(true, kendo.spreadsheet.messages.borderPalette,{\n                \"allBorders\": \"内外框线\",\n                \"insideBorders\": \"内框线\",\n                \"insideHorizontalBorders\": \"横向内框线\",\n                \"insideVerticalBorders\": \"纵向内框线\",\n                \"outsideBorders\": \"外框线\",\n                \"leftBorder\": \"左框线\",\n                \"topBorder\": \"上框线\",\n                \"rightBorder\": \"右框线\",\n                \"bottomBorder\": \"下框线\",\n                \"noBorders\": \"无框线\",\n                \"reset\": \"无填充颜色\",\n                \"customColor\": \"其他颜色...\",\n                \"apply\": \"确定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\n        kendo.spreadsheet.messages.dialogs =\n            $.extend(true, kendo.spreadsheet.messages.dialogs,{\n                \"apply\": \"确定\",\n                \"save\": \"保存\",\n                \"cancel\": \"取消\",\n                \"remove\": \"移除\",\n                \"retry\": \"重试\",\n                \"revert\": \"复原\",\n                \"okText\": \"确定\",\n                \"formatCellsDialog\": {\n                    \"title\": \"单元格格式\",\n                    \"categories\": {\n                        \"number\": \"数字\",\n                        \"currency\": \"货币\",\n                        \"date\": \"日期\"\n                    }\n                },\n                \"fontFamilyDialog\": {\n                    \"title\": \"字体\"\n                },\n                \"fontSizeDialog\": {\n                    \"title\": \"字号\"\n                },\n                \"bordersDialog\": {\n                    \"title\": \"边框\"\n                },\n                \"alignmentDialog\": {\n                    \"title\": \"对齐\",\n                    \"buttons\": {\n                        \"justtifyLeft\": \"左对齐\",\n                        \"justifyCenter\": \"水平居中\",\n                        \"justifyRight\": \"右对齐\",\n                        \"justifyFull\": \"两端对齐\",\n                        \"alignTop\": \"顶端对齐\",\n                        \"alignMiddle\": \"垂直居中\",\n                        \"alignBottom\": \"底端对齐\"\n                    }\n                },\n                \"mergeDialog\": {\n                    \"title\": \"合并单元格\",\n                    \"buttons\": {\n                        \"mergeCells\": \"全部合并\",\n                        \"mergeHorizontally\": \"水平合并\",\n                        \"mergeVertically\": \"垂直合并\",\n                        \"unmerge\": \"取消合并\"\n                    }\n                },\n                \"freezeDialog\": {\n                    \"title\": \"冻结窗格\",\n                    \"buttons\": {\n                        \"freezePanes\": \"冻结上行左列\",\n                        \"freezeRows\": \"冻结上部行\",\n                        \"freezeColumns\": \"冻结左侧列\",\n                        \"unfreeze\": \"取消冻结\"\n                    }\n                },\n                \"confirmationDialog\": {\n                    \"text\": \"你确定要删除这张工作表吗？\",\n                    \"title\": \"删除工作表\"\n                },\n                \"validationDialog\": {\n                    \"title\": \"数据验证\",\n                    \"hintMessage\": \"请输入一个{1}的{0}值\",\n                    \"hintTitle\": \"验证{0}\",\n                    \"criteria\": {\n                        \"any\": \"任何值\",\n                        \"number\": \"数字\",\n                        \"text\": \"文本\",\n                        \"date\": \"日期\",\n                        \"custom\": \"自定义公式\",\n                        \"list\": \"序列\"\n                    },\n                    \"comparers\": {\n                        \"greaterThan\": \"大于\",\n                        \"lessThan\": \"小于\",\n                        \"between\": \"介于\",\n                        \"notBetween\": \"不介于\",\n                        \"equalTo\": \"等于\",\n                        \"notEqualTo\": \"不等于\",\n                        \"greaterThanOrEqualTo\": \"大于等于\",\n                        \"lessThanOrEqualTo\": \"小于等于\"\n                    },\n                    \"comparerMessages\": {\n                        \"greaterThan\": \"大于{0}\",\n                        \"lessThan\": \"小于{0}\",\n                        \"between\": \"介于{0}和{1}之间\",\n                        \"notBetween\": \"不介于{0}和{1}之间\",\n                        \"equalTo\": \"等于{0}\",\n                        \"notEqualTo\": \"不等于{0}\",\n                        \"greaterThanOrEqualTo\": \"大于等于{0}\",\n                        \"lessThanOrEqualTo\": \"小于等于{0}\",\n                        \"custom\": \"满足公式{0}\"\n                    },\n                    \"labels\": {\n                        \"criteria\": \"允许\",\n                        \"comparer\": \"比较\",\n                        \"min\": \"最小值\",\n                        \"max\": \"最大值\",\n                        \"value\": \"值\",\n                        \"start\": \"开始\",\n                        \"end\": \"结束\",\n                        \"onInvalidData\": \"对无效的数据\",\n                        \"rejectInput\": \"拒绝输入\",\n                        \"showWarning\": \"显示警告\",\n                        \"showHint\": \"选定单元格时显示\",\n                        \"hintTitle\": \"选定时的标题\",\n                        \"hintMessage\": \"选定时的信息\",\n                        \"ignoreBlank\": \"忽略空值\",\n                        \"showListButton\": \"显示序列按钮\",\n                        \"showCalendarButton\": \"显示日历按钮\"\n                    },\n                    \"placeholders\": {\n                        \"typeTitle\": \"请输入标题\",\n                        \"typeMessage\": \"请输入信息\"\n                    }\n                },\n                \"exportAsDialog\": {\n                    \"title\": \"导出...\",\n                    \"labels\": {\n                        \"scale\": \"缩放\",\n                        \"fit\": \"调整为一页\",\n                        \"fileName\": \"文件名\",\n                        \"saveAsType\": \"保存类型\",\n                        \"exportArea\": \"导出范围\",\n                        \"paperSize\": \"纸张大小\",\n                        \"margins\": \"页边距\",\n                        \"orientation\": \"纸张方向\",\n                        \"print\": \"打印\",\n                        \"guidelines\": \"网格线\",\n                        \"center\": \"居中方式\",\n                        \"horizontally\": \"水平\",\n                        \"vertically\": \"垂直\"\n                    }\n                },\n                \"modifyMergedDialog\": {\n                    \"errorMessage\": \"不能更改已合并单元格的局部。\"\n                },\n                \"rangeDisabledDialog\": {\n                    \"errorMessage\": \"指定范围包含禁用单元格。\"\n                },\n                \"incompatibleRangesDialog\": {\n                    \"errorMessage\": \"范围不匹配\"\n                },\n                \"noFillDirectionDialog\": {\n                    \"errorMessage\": \"无法判断填充方向\"\n                },\n                \"duplicateSheetNameDialog\": {\n                    \"errorMessage\": \"工作表名称重复\"\n                },\n                \"overflowDialog\": {\n                    \"errorMessage\": \"不能粘贴，因为复制区域和粘贴区域的大小和形状不一样。\"\n                },\n                \"useKeyboardDialog\": {\n                    \"title\": \"复制和粘贴\",\n                    \"errorMessage\": \"不能通过菜单调用这些操作，请使用键盘快捷键代替：\",\n                    \"labels\": {\n                        \"forCopy\": \"为复制\",\n                        \"forCut\": \"为剪切\",\n                        \"forPaste\": \"为粘贴\"\n                    }\n                },\n                \"unsupportedSelectionDialog\": {\n                    \"errorMessage\": \"不能在多选的情况下执行该操作。\"\n                },\n                \"linkDialog\": {\n                    \"title\": \"链接\",\n                    \"labels\": {\n                        \"text\": \"链接文字\",\n                        \"url\": \"链接地址\",\n                        \"removeLink\": \"移除链接\"\n                    }\n                }\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\n        kendo.spreadsheet.messages.filterMenu =\n            $.extend(true, kendo.spreadsheet.messages.filterMenu,{\n                \"sortAscending\": \"按A到Z升序排列\",\n                \"sortDescending\": \"按Z到A降序排列\",\n                \"filterByValue\": \"内容筛选\",\n                \"filterByCondition\": \"条件筛选\",\n                \"apply\": \"确定\",\n                \"search\": \"搜索\",\n                \"addToCurrent\": \"添加到当前选择\",\n                \"clear\": \"清空\",\n                \"blanks\": \"（无）\",\n                \"operatorNone\": \"无\",\n                \"and\": \"并且\",\n                \"or\": \"或者\",\n                \"operators\": {\n                    \"string\": {\n                        \"contains\": \"文本包含\",\n                        \"doesnotcontain\": \"文本不包含\",\n                        \"startswith\": \"文本开头是\",\n                        \"endswith\": \"文本结尾是\",\n                        \"matches\": \"文本等于\",\n                        \"doesnotmatch\": \"文本不等于\"\n                    },\n                    \"date\": {\n                        \"eq\":  \"日期等于\",\n                        \"neq\": \"日期不等于\",\n                        \"lt\":  \"日期早于\",\n                        \"gt\":  \"日期晚于\"\n                    },\n                    \"number\": {\n                        \"eq\": \"数字等于\",\n                        \"neq\": \"数字不等于\",\n                        \"gte\": \"数字大于等于\",\n                        \"gt\": \"数字大于\",\n                        \"lte\": \"数字小于等于\",\n                        \"lt\": \"数字小于\"\n                    }\n                }\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\n        kendo.spreadsheet.messages.colorPicker =\n            $.extend(true, kendo.spreadsheet.messages.colorPicker,{\n                \"reset\": \"无填充颜色\",\n                \"customColor\": \"其他颜色...\",\n                \"apply\": \"确定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\n        kendo.spreadsheet.messages.toolbar =\n            $.extend(true, kendo.spreadsheet.messages.toolbar,{\n                \"addColumnLeft\": \"在左侧插入列\",\n                \"addColumnRight\": \"在右侧插入列\",\n                \"addRowAbove\": \"在上面插入行\",\n                \"addRowBelow\": \"在下面插入行\",\n                \"alignment\": \"对齐\",\n                \"alignmentButtons\": {\n                    \"justtifyLeft\": \"左对齐\",\n                    \"justifyCenter\": \"水平居中\",\n                    \"justifyRight\": \"右对齐\",\n                    \"justifyFull\": \"两端对齐\",\n                    \"alignTop\": \"顶端对齐\",\n                    \"alignMiddle\": \"垂直居中\",\n                    \"alignBottom\": \"底端对齐\"\n                },\n                \"backgroundColor\": \"背景颜色\",\n                \"bold\": \"粗体\",\n                \"borders\": \"边框\",\n                \"colorPicker\": {\n                    \"reset\": \"无填充颜色\",\n                    \"customColor\": \"其他颜色...\"\n                },\n                \"copy\": \"复制\",\n                \"cut\": \"剪切\",\n                \"deleteColumn\": \"删除整列\",\n                \"deleteRow\": \"删除整行\",\n                \"excelImport\": \"从 Excel 导入...\",\n                \"exportAs\": \"导出为...\",\n                \"filter\": \"筛选\",\n                \"fontFamily\": \"字体\",\n                \"fontSize\": \"字号\",\n                \"format\": \"定制格式...\",\n                \"formatTypes\": {\n                    \"automatic\": \"自动\",\n                    \"text\": \"文本\",\n                    \"number\": \"数字\",\n                    \"percent\": \"百分比\",\n                    \"financial\": \"财务\",\n                    \"currency\": \"货币\",\n                    \"date\": \"日期\",\n                    \"time\": \"时间\",\n                    \"dateTime\": \"日期时间\",\n                    \"duration\": \"时分秒\",\n                    \"moreFormats\": \"更多格式...\"\n                },\n                \"formatDecreaseDecimal\": \"减少小数位数\",\n                \"formatIncreaseDecimal\": \"增加小数位数\",\n                \"freeze\": \"冻结窗格\",\n                \"freezeButtons\": {\n                    \"freezePanes\": \"冻结上行左列\",\n                    \"freezeRows\": \"冻结上部行\",\n                    \"freezeColumns\": \"冻结左侧列\",\n                    \"unfreeze\": \"取消冻结\"\n                },\n                \"italic\": \"斜体\",\n                \"merge\": \"合并单元格\",\n                \"mergeButtons\": {\n                    \"mergeCells\": \"全部合并\",\n                    \"mergeHorizontally\": \"水平合并\",\n                    \"mergeVertically\": \"垂直合并\",\n                    \"unmerge\": \"取消合并\"\n                },\n                \"open\": \"打开...\",\n                \"paste\": \"粘贴\",\n                \"quickAccess\": {\n                    \"redo\": \"重做\",\n                    \"undo\": \"撤消\"\n                },\n                \"toggleGridlines\": \"切换网格线\",\n                \"saveAs\": \"另存为...\",\n                \"sort\": \"排序\",\n                \"sortAsc\": \"升序排列\",\n                \"sortDesc\": \"降序排列\",\n                \"sortButtons\": {\n                    \"sortSheetAsc\": \"表格按A到Z升序排列\",\n                    \"sortSheetDesc\": \"表格按Z到A降序排列\",\n                    \"sortRangeAsc\": \"选定范围按A到Z升序排列\",\n                    \"sortRangeDesc\": \"选定范围按Z到A降序排列\"\n                },\n                \"textColor\": \"文字颜色\",\n                \"textWrap\": \"文字换行\",\n                \"underline\": \"下划线\",\n                \"validation\": \"数据验证...\",\n                \"hyperlink\": \"链接\"\n            });\n    }\n\n    if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\n        kendo.spreadsheet.messages.view =\n            $.extend(true, kendo.spreadsheet.messages.view,{\n                \"errors\": {\n                    \"openUnsupported\": \"不支持的格式，请选择一个后缀名为 .xlsx 的文件。\",\n                    \"shiftingNonblankCells\": \"由于数据可能丢失无法插入单元格，请选择另一处插入或者从工作表的尾部删除数据。\",\n                    \"insertColumnWhenRowIsSelected\": \"在选择所有列时不能插入列。\",\n                    \"insertRowWhenColumnIsSelected\": \"在选择所有行时不能插入行。\",\n                    \"filterRangeContainingMerges\": \"无法在包含合并单元格的情况下进行筛选\",\n                    \"sortRangeContainingMerges\": \"无法在包含合并单元格的情况下排序\",\n                    \"cantSortMultipleSelection\": \"无法在多选的情况下排序\",\n                    \"cantSortNullRef\": \"无法在没有选择的情况下排序\",\n                    \"cantSortMixedCells\": \"无法在包含有混合形状单元格的情况下排序\",\n                    \"validationError\": \"你输入的值不符合当前单元格的验证规则。\",\n                    \"cannotModifyDisabled\": \"不能修改被禁用的单元格。\"\n                },\n                \"tabs\": {\n                    \"home\": \"开始\",\n                    \"insert\": \"插入\",\n                    \"data\": \"数据\"\n                }\n            });\n    }\n\n    /* Slider messages */\n    if (kendo.ui.Slider) {\n        kendo.ui.Slider.prototype.options =\n            $.extend(true, kendo.ui.Slider.prototype.options,{\n                \"increaseButtonTitle\": \"增加\",\n                \"decreaseButtonTitle\": \"减少\",\n                \"dragHandleTitle\": \"拖\"\n            });\n    }\n\n    /* ListBox messages */\n    if (kendo.ui.ListBox) {\n        kendo.ui.ListBox.prototype.options.messages =\n            $.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n                \"tools\": {\n                    \"remove\": \"删除\",\n                    \"moveUp\": \"上移\",\n                    \"moveDown\": \"下移\",\n                    \"transferTo\": \"转移过去\",\n                    \"transferFrom\": \"转移回来\",\n                    \"transferAllTo\": \"全部转移过去\",\n                    \"transferAllFrom\": \"全部转移回来\"\n                }\n            });\n    }\n\n    /* TreeList messages */\n    if (kendo.ui.TreeList) {\n        kendo.ui.TreeList.prototype.options.messages =\n            $.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n                \"noRows\": \"无相关数据\",\n                \"loading\": \"载入中...\",\n                \"requestFailed\": \"请求失败！\",\n                \"retry\": \"重试\",\n                \"commands\": {\n                    \"edit\": \"编辑\",\n                    \"update\": \"更新\",\n                    \"canceledit\": \"取消\",\n                    \"create\": \"新增\",\n                    \"createchild\": \"新增子项\",\n                    \"destroy\": \"删除\",\n                    \"excel\": \"导出 Excel\",\n                    \"pdf\": \"导出 PDF\"\n                }\n            });\n    }\n\n    /* TreeView messages */\n    if (kendo.ui.TreeView) {\n        kendo.ui.TreeView.prototype.options.messages =\n            $.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n                \"loading\": \"载入中...\",\n                \"requestFailed\": \"请求失败！\",\n                \"retry\": \"重试\"\n            });\n    }\n\n    /* PanelBar messages */\n    if (kendo.ui.PanelBar) {\n        kendo.ui.PanelBar.prototype.options.messages =\n            $.extend(true, kendo.ui.PanelBar.prototype.options.messages,{\n                \"loading\": \"载入中...\",\n                \"requestFailed\": \"请求失败！\",\n                \"retry\": \"重试\"\n            });\n    }\n\n    /* Upload messages */\n    if (kendo.ui.Upload) {\n        kendo.ui.Upload.prototype.options.localization=\n            $.extend(true, kendo.ui.Upload.prototype.options.localization,{\n                \"select\": \"选择文件\",\n                \"cancel\": \"取消\",\n                \"retry\": \"重试\",\n                \"remove\": \"移除\",\n                \"pause\": \"暂停\",\n                \"resume\": \"恢复\",\n                \"clearSelectedFiles\": \"清空\",\n                \"uploadSelectedFiles\": \"上传\",\n                \"dropFilesHere\": \"将文件拖拽到此处上传\",\n                \"invalidFiles\": \"文件不符合要求！\",\n                \"statusUploading\": \"上传中...\",\n                \"statusUploaded\": \"上传成功！\",\n                \"statusWarning\": \"上传警告！\",\n                \"statusFailed\": \"上传失败！\",\n                \"headerStatusUploading\": \"上传中...\",\n                \"headerStatusPaused\": \"上传暂停\",\n                \"headerStatusUploaded\": \"上传完成！\",\n                \"invalidMaxFileSize\": \"文件太大！\",\n                \"invalidMinFileSize\": \"文件太小！\",\n                \"invalidFileExtension\": \"不支持的文件格式！\"\n            });\n    }\n\n    /* Validator messages */\n    if (kendo.ui.Validator) {\n        kendo.ui.Validator.prototype.options.messages =\n            $.extend(true, kendo.ui.Validator.prototype.options.messages,{\n                \"required\": \"{0} 是必填项！\",\n                \"pattern\": \"{0} 的格式不正确！\",\n                \"min\": \"{0} 必须大于或等于 {1} ！\",\n                \"max\": \"{0} 必须小于或等于 {1} ！\",\n                \"step\": \"{0} 不是正确的步进值！\",\n                \"email\": \"{0} 不是正确的电子邮件格式！\",\n                \"url\": \"{0} 不是正确的网址格式！\",\n                \"date\": \"{0} 不是正确的日期格式！\",\n                \"dateCompare\": \"结束日期必须晚于或等于开始日期！\"\n            });\n    }\n\n    /* Progress messages */\n    if (kendo.ui.progress) {\n        kendo.ui.progress.messages =\n            $.extend(true, kendo.ui.progress.messages, {\n                \"loading\": \"载入中...\"\n            });\n    }\n\n    /* Dialog messages */\n    if (kendo.ui.Dialog) {\n        kendo.ui.Dialog.prototype.options.messages =\n            $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n                \"close\": \"关闭\"\n            });\n    }\n\n    /* Calendar messages */\n    if (kendo.ui.Calendar) {\n        kendo.ui.Calendar.prototype.options.messages =\n            $.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n                \"weekColumnHeader\": \"\"\n            });\n    }\n\n    /* Alert messages */\n    if (kendo.ui.Alert) {\n        kendo.ui.Alert.prototype.options.messages =\n            $.extend(true, kendo.ui.Alert.prototype.options.localization, {\n                \"okText\": \"确定\"\n            });\n    }\n\n    /* Confirm messages */\n    if (kendo.ui.Confirm) {\n        kendo.ui.Confirm.prototype.options.messages =\n            $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n                \"okText\": \"确定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* Prompt messages */\n    if (kendo.ui.Prompt) {\n        kendo.ui.Prompt.prototype.options.messages =\n            $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n                \"okText\": \"确定\",\n                \"cancel\": \"取消\"\n            });\n    }\n\n    /* DateInput messages */\n    if (kendo.ui.DateInput) {\n        kendo.ui.DateInput.prototype.options.messages =\n            $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n                \"year\": \"年\",\n                \"month\": \"月\",\n                \"day\": \"日\",\n                \"weekday\": \"周\",\n                \"hour\": \"时\",\n                \"minute\": \"分\",\n                \"second\": \"秒\",\n                \"dayperiod\": \"上午/下午\"\n            });\n    }\n\n    /* Mobile Messages ------------------------------ */\n\n    /* Mobile Scroller messages */\n    if (kendo.mobile.ui.Scroller) {\n        kendo.mobile.ui.Scroller.prototype.options.messages =\n            $.extend(true, kendo.mobile.ui.Scroller.prototype.options.messages, {\n                \"pullTemplate\": \"下拉刷新\",\n                \"releaseTemplate\": \"释放刷新\",\n                \"refreshTemplate\": \"刷新中...\"\n            });\n    }\n\n    /* Mobile ListView messages */\n    if (kendo.mobile.ui.ListView) {\n        kendo.mobile.ui.ListView.prototype.options.messages =\n            $.extend(true, kendo.mobile.ui.ListView.prototype.options.messages, {\n                \"loadMoreText\": \"点击载入更多\",\n                \"pullTemplate\": \"下拉刷新\",\n                \"releaseTemplate\": \"释放刷新\",\n                \"refreshTemplate\": \"刷新中...\"\n            });\n    }\n\n})(window.kendo.jQuery);\n}));"]}