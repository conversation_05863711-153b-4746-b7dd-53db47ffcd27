.k-theme-test-class,
.ktb-theme-id-bootstrap {
  opacity: 0;
}
.ktb-var-accent {
  color: #428bca;
}
.ktb-var-base {
  color: #ebebeb;
}
.ktb-var-background {
  color: #ffffff;
}
.ktb-var-border-radius {
  border-radius: 4px;
}
.ktb-var-normal-background {
  color: #ffffff;
}
.ktb-var-normal-gradient {
  background-image: none;
}
.ktb-var-normal-text-color {
  color: #333333;
}
.ktb-var-hover-background {
  color: #ebebeb;
}
.ktb-var-hover-gradient {
  background-image: none;
}
.ktb-var-hover-text-color {
  color: #333333;
}
.ktb-var-selected-background {
  color: #428bca;
}
.ktb-var-selected-gradient {
  background-image: none;
}
.ktb-var-selected-text-color {
  color: #ffffff;
}
.ktb-var-error {
  color: #ffe0d9;
}
.ktb-var-warning {
  color: #fbeed5;
}
.ktb-var-success {
  color: #eaf7ec;
}
.ktb-var-info {
  color: #e5f5fa;
}
.ktb-var-series-a {
  color: #428bca;
}
.ktb-var-series-b {
  color: #5bc0de;
}
.ktb-var-series-c {
  color: #5cb85c;
}
.ktb-var-series-d {
  color: #f2b661;
}
.ktb-var-series-e {
  color: #e67d4a;
}
.ktb-var-series-f {
  color: #da3b36;
}
.k-grid-norecords-template {
  background-color: #ffffff;
  border: 1px solid #cccccc;
}
.k-button {
  border-radius: 4px;
  border-color: #cccccc;
  color: #333333;
  background-color: #ffffff;
  background-position: 50% 50%;
}
.k-button:hover,
.k-button.k-state-hover {
  color: #333333;
  border-color: #aeaeae;
  background-color: #ebebeb;
}
.k-button:active,
.k-button.k-state-active {
  color: #333333;
  background-color: #ebebeb;
  border-color: #aeaeae;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: none;
}
.k-button.k-state-active:hover {
  color: #ffffff;
  border-color: #285e8e;
  background-color: #3276b1;
}
.k-button:focus:active {
  box-shadow: 0 0 7px 0 #76abd9, inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.k-button[disabled],
.k-state-disabled .k-button,
.k-state-disabled .k-button:hover,
.k-button.k-state-disabled,
.k-button.k-state-disabled:hover {
  color: #a1a1a1;
  border-color: #cccccc;
  background-color: #ffffff;
  background-image: none;
}
.k-button[disabled],
.k-button.k-state-disabled,
.k-button.k-state-disabled:active {
  box-shadow: none;
}
.k-button:focus,
.k-button:focus:hover,
.k-button.k-state-focused,
.k-button.k-state-focused.k-state-disabled,
.k-state-disabled .k-button.k-state-focused {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-primary {
  color: #ffffff;
  border-color: #357ebd;
  background-color: #428bca;
}
.k-primary:hover,
.k-primary.k-state-hover {
  color: #ffffff;
  border-color: #285e8e;
  background-color: #3276b1;
}
.k-primary:active,
.k-primary.k-state-active {
  color: #ffffff;
  border-color: #285e8e;
  background-color: #3276b1;
  box-shadow: null;
}
.k-primary:focus:active:not(.k-state-disabled):not([disabled]) {
  box-shadow: 0 0 8px 0 #cbebf5, inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.k-primary[disabled],
.k-state-disabled .k-primary,
.k-state-disabled .k-primary:hover,
.k-primary.k-state-disabled,
.k-primary.k-state-disabled:hover {
  color: #ffffff;
  border-color: #7aadda;
  background-color: #82b2dc;
  box-shadow: none;
}
.k-primary[disabled],
.k-primary.k-state-disabled {
  box-shadow: none;
}
.k-primary:focus,
.k-primary.k-state-focused {
  border-color: #eef8fc;
  box-shadow: 0 0 8px 0 #cbebf5;
}
.k-button-group {
  border-radius: 4px;
}
.k-button-group .k-button {
  border-radius: 0;
}
.k-button-group .k-group-start,
.k-button-group .k-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.k-button-group .k-group-end,
.k-button-group .k-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.k-button-group .k-group-start.k-group-end,
.k-button-group .k-button:first-child:last-child {
  border-radius: 4px;
}
.k-rtl .k-button-group .k-button {
  border-radius: 0;
}
.k-rtl .k-button-group .k-group-start,
.k-rtl .k-button-group .k-button:first-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.k-rtl .k-button-group .k-group-end,
.k-rtl .k-button-group .k-button:last-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.k-rtl .k-button-group .k-group-start.k-group-end,
.k-rtl .k-button-group .k-button:first-child:last-child {
  border-radius: 4px;
}
.k-split-button {
  border-radius: 4px;
}
.k-split-button.k-state-border-down > .k-button,
.k-split-button.k-state-border-up > .k-button {
  color: #333333;
  background-color: #ebebeb;
  border-color: #aeaeae;
  box-shadow: none;
}
.k-split-button:focus {
  border-color: #76abd9;
  outline: none;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-split-button:focus > .k-button {
  background: transparent;
  border-color: #76abd9;
}
.k-split-button:focus:not(.k-state-disabled) > .k-state-active,
.k-split-button:focus:not(.k-state-disabled) > .k-button:hover {
  color: #333333;
  background-color: #ebebeb;
  border-color: #76abd9;
  box-shadow: none;
}
.k-split-button.k-state-disabled {
  color: #a1a1a1;
  background: #ffffff;
  background-image: none;
}
.k-edit-buttons {
  border-color: #cccccc;
  background: #f5f5f5;
}
undefined undefined .k-in,
.k-item {
  border-color: transparent;
}
.k-splitbar .k-resize-handle {
  background-color: #333333;
}
.k-block,
.k-widget {
  background-color: #ffffff;
}
.k-block,
.k-widget,
.k-input,
.k-textbox,
.k-group,
.k-content,
.k-header,
.k-filter-row > th,
.k-editable-area,
.k-separator,
.k-textbox > input,
.k-autocomplete,
.k-dropdown-wrap,
.k-toolbar,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-state-default,
.k-state-default .k-select,
.k-state-disabled,
.k-grid-header,
.k-grid-header-wrap,
.k-grid-header-locked,
.k-grid-footer-locked,
.k-grid-content-locked,
.k-grid td,
.k-grid td.k-state-selected,
.k-grid-footer-wrap,
.k-pager-wrap,
.k-pager-wrap .k-link,
.k-pager-refresh,
.k-grouping-header,
.k-grouping-header .k-group-indicator,
.k-panelbar > .k-item > .k-link,
.k-panel > .k-item > .k-link,
.k-panelbar .k-panel,
.k-panelbar .k-content,
.k-treemap-tile,
.k-calendar th,
.k-slider-track,
.k-splitbar,
.k-dropzone-active,
.k-tiles,
.k-tooltip,
.k-button-group .k-tool,
.k-popup.k-align .k-list .k-item:last-child,
.k-upload-files {
  border-color: #cccccc;
}
.k-group,
.k-grouping-header,
.k-pager-wrap,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-widget .k-status,
.k-calendar th,
.k-calendar .k-alt,
.k-dropzone-hovered,
.k-popup {
  background-color: #f5f5f5;
}
.k-grouping-row td,
td.k-group-cell,
.k-resize-handle-inner,
.k-grid .k-state-selected:hover .k-group-cell {
  background-color: #f5f5f5;
}
.k-list-container {
  border-color: rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
}
.k-content,
.k-panelbar > li.k-item,
.k-panel > li.k-item,
.k-tiles {
  background-color: #ffffff;
}
.k-alt,
.k-separator,
.k-resource.k-alt,
.k-pivot-layout > tbody > tr:first-child > td:first-child {
  background-color: #f5f5f5;
}
.k-pivot-rowheaders .k-alt .k-alt,
.k-header.k-alt {
  background-color: #e1e1e1;
}
.k-textbox,
.k-autocomplete.k-header,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active {
  border-color: #cccccc;
  background-color: #ebebeb;
}
.k-maskedtextbox.k-state-disabled > .k-textbox:hover,
.k-dateinput.k-state-disabled > .k-textbox:hover {
  border-color: #cccccc;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-dropdown-wrap .k-input,
.k-autocomplete.k-state-focused .k-input,
.k-dropdown-wrap.k-state-focused .k-input,
.k-picker-wrap.k-state-focused .k-input,
.k-numeric-wrap.k-state-focused .k-input {
  border-color: #cccccc;
}
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input {
  background: none;
}
.k-input,
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input,
.k-multiselect-wrap {
  background-color: #ffffff;
  color: #333333;
}
.k-input[readonly] {
  background-color: #ffffff;
  color: #333333;
}
.k-block,
.k-widget,
.k-popup,
.k-content,
.k-toolbar,
.k-dropdown .k-input {
  color: #333333;
}
.k-inverse {
  color: #ffffff;
}
.k-block {
  color: #333333;
}
.k-link:link,
.k-link:visited,
.k-nav-current.k-state-hover .k-link {
  color: #428bca;
}
.k-tabstrip-items .k-link,
.k-panelbar > li > .k-link {
  color: #333333;
}
.k-header,
.k-treemap-title,
.k-grid-header .k-header > .k-link {
  color: #333333;
}
.k-header,
.k-grid-header,
.k-toolbar,
.k-dropdown-wrap,
.k-picker-wrap,
.k-numeric-wrap,
.k-grouping-header,
.k-pager-wrap,
.k-textbox,
.k-progressbar,
.k-draghandle,
.k-autocomplete,
.k-state-highlight,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.km-pane-wrapper > .km-pane > .km-view > .km-content {
  background-position: 50% 50%;
  background-color: #f5f5f5;
}
.k-widget.k-tooltip {
  background-image: none;
}
.k-block,
.k-treemap-tile,
html .km-pane-wrapper .k-header {
  background-color: #f5f5f5;
}
thead.k-grid-header,
thead.k-grid-header tr:first-child {
  background: transparent;
}
.k-mediaplayer-toolbar {
  background-color: rgba(245, 245, 245, 0.85);
}
.k-hr {
  border-color: #cccccc;
}
.k-badge {
  background-color: #428bca;
  color: #ffffff;
  border-radius: 3px;
}
.k-tool {
  border-color: transparent;
}
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked,
.k-mobile-list .k-edit-field [type=radio]:checked {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/sprite.png');
  border-color: transparent;
}
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked {
  font-family: 'WebComponentsIcons';
  background-image: none;
}
.k-mobile-list .k-check:checked:before,
.k-mobile-list .k-edit-field [type=checkbox]:checked:before {
  content: "e118";
}
.k-i-loading {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/loading.gif');
}
.k-loading-image {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/loading-image.gif');
}
.k-loading-color {
  background-color: #ffffff;
}
.k-draghandle {
  border-color: #ffffff;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #c4c4c4;
}
.k-draghandle:hover {
  border-color: #aeaeae;
  background-color: #ebebeb;
  box-shadow: none;
}
.k-scheduler {
  color: #ffffff;
  background-color: #ffffff;
}
.k-scheduler-layout {
  color: #333333;
}
.k-scheduler-datecolumn,
.k-scheduler-groupcolumn {
  background-color: #ffffff;
  color: #333333;
}
.k-scheduler-times tr,
.k-scheduler-times th,
.k-scheduler-table td,
.k-scheduler-header th,
.k-scheduler-header-wrap,
.k-scheduler-times {
  border-color: #d2d2d2;
}
.k-nonwork-hour,
.k-scheduler-dayview .k-today.k-nonwork-hour,
.k-scheduler-timelineview .k-today.k-nonwork-hour {
  background-color: #f5f5f5;
}
.k-gantt .k-nonwork-hour {
  background-color: rgba(0, 0, 0, 0.02);
}
.k-gantt .k-header.k-nonwork-hour {
  background-color: rgba(0, 0, 0, 0.2);
}
.k-scheduler-table .k-today,
.k-today > .k-scheduler-datecolumn,
.k-today > .k-scheduler-groupcolumn {
  background-color: #eeeeee;
}
.k-scheduler-now-arrow {
  border-left-color: #dbdbdb;
}
.k-scheduler-now-line {
  background-color: #dbdbdb;
}
.k-event,
.k-task-complete {
  border-color: #3174ad;
  background: #3174ad 0 -257px none repeat-x;
  color: #ffffff;
}
.k-event-inverse {
  color: #333333;
}
.k-event.k-state-selected {
  background-position: 0 0;
  box-shadow: 0 0 0 2px #333333;
}
.k-event .k-resize-handle:after,
.k-task-single .k-resize-handle:after {
  background-color: #ffffff;
}
.k-scheduler-marquee:before,
.k-scheduler-marquee:after {
  border-color: #428bca;
}
.k-panelbar .k-content,
.k-panelbar .k-panel,
.k-panelbar .k-item {
  background-color: #ffffff;
  color: #333333;
  border-color: #cccccc;
}
.k-panelbar > li > .k-link {
  color: #333333;
}
.k-panelbar > .k-item > .k-link {
  border-color: #cccccc;
}
.k-panel > li.k-item {
  background-color: #ffffff;
}
.k-state-active,
.k-state-active:hover,
.k-active-filter {
  background-color: #ffffff;
  border-color: #cccccc;
  color: #333333;
}
.k-fieldselector .k-list-container {
  background-color: #ffffff;
}
.k-menu .k-state-hover > .k-state-active {
  background-color: transparent;
}
.k-state-highlight {
  background: #ffffff;
  color: #333333;
}
.k-state-focused,
.k-grouping-row .k-state-focused {
  border-color: #76abd9;
}
.k-mediaplayer-toolbar .k-button.k-bare:active,
.k-mediaplayer-toolbar .k-button.k-bare.k-state-active,
.k-mediaplayer-toolbar .k-button.k-bare.k-state-active:hover {
  color: #428bca;
}
.k-calendar .k-link {
  color: #333333;
}
.k-calendar .k-footer {
  padding: 0;
}
.k-calendar .k-footer .k-nav-today {
  color: #333333;
  text-decoration: none;
  background-color: #f5f5f5;
}
.k-calendar .k-footer .k-nav-today:hover,
.k-calendar .k-footer .k-nav-today.k-state-hover {
  background-color: #ffffff;
  text-decoration: underline;
}
.k-calendar .k-footer .k-nav-today:active {
  background-color: #ffffff;
}
.k-calendar .k-link.k-nav-fast {
  color: #333333;
}
.k-calendar .k-nav-fast.k-state-hover {
  text-decoration: none;
  background-color: #ebebeb;
  color: #333333;
}
.k-calendar .k-link.k-state-hover {
  border-radius: 4px;
}
.k-calendar .k-state-focused.k-state-hover {
  background-color: #d5e5f3;
}
.k-calendar .k-state-selected.k-state-hover {
  background-color: #3276b1;
}
.k-calendar .k-state-selected.k-state-hover .k-link {
  color: #ffffff;
}
.k-calendar td.k-state-focused.k-state-selected.k-state-hover {
  box-shadow: inset 0 0 7px 0 #1f496e;
}
.k-calendar .k-today {
  background-color: #ebebeb;
}
.k-calendar .k-today.k-state-selected {
  background-color: #428bca;
}
.k-calendar .k-today.k-state-hover {
  background-color: #d9d9d9;
}
.k-calendar .k-today.k-state-focused.k-state-hover {
  background-color: #d5e5f3;
}
.k-calendar .k-today.k-state-selected.k-state-hover {
  background-color: #3276b1;
}
.k-calendar .k-footer .k-link {
  border-radius: 0;
}
.k-calendar th {
  background-color: #f5f5f5;
}
.k-calendar-container.k-group {
  border-color: rgba(0, 0, 0, 0.2);
}
.k-state-selected,
.k-state-selected:link,
.k-state-selected:visited,
.k-tool.k-state-selected,
.k-list > .k-state-selected,
.k-list > .k-state-highlight,
.k-panel > .k-state-selected,
.k-ghost-splitbar-vertical,
.k-ghost-splitbar-horizontal,
.k-draghandle.k-state-selected:hover,
.k-scheduler .k-scheduler-toolbar .k-state-selected,
.k-scheduler .k-today.k-state-selected,
.k-marquee-color {
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
}
.k-virtual-item.k-first,
.k-group-header + .k-list > .k-item.k-first,
.k-static-header + .k-list > .k-item.k-first {
  border-top-color: #aeaeae;
}
.k-group-header + div > .k-list > .k-item.k-first:before {
  border-top-color: #aeaeae;
}
.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  background: #aeaeae;
  color: #ffffff;
}
.k-popup .k-list .k-item > .k-group {
  background: #aeaeae;
  color: #ffffff;
  border-bottom-left-radius: 3px;
}
.k-marquee-text {
  color: #ffffff;
}
.k-state-focused,
.k-list > .k-state-focused,
.k-listview > .k-state-focused,
.k-grid-header th.k-state-focused,
td.k-state-focused {
  box-shadow: inset 0 0 7px 0 #76abd9;
}
.k-popup .k-list .k-state-focused {
  border-radius: 4px;
  box-shadow: inset 0 0 2px 0 #428bca, inset 0 0 7px 0 #76abd9;
}
.k-state-focused.k-state-selected,
.k-list > .k-state-focused.k-state-selected,
.k-listview > .k-state-focused.k-state-selected,
td.k-state-focused.k-state-selected {
  box-shadow: inset 0 0 10px 3px #3276b1;
}
.k-list-optionlabel.k-state-selected.k-state-focused {
  box-shadow: none;
}
.k-state-selected > .k-link,
.k-panelbar > li > .k-state-selected,
.k-panelbar > li.k-state-default > .k-link.k-state-selected {
  color: #ffffff;
}
.k-state-hover,
.k-state-hover:hover,
.k-splitbar-horizontal-hover:hover,
.k-splitbar-vertical-hover:hover,
.k-list > .k-state-hover,
.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,
.k-pager-wrap .k-link:hover,
.k-dropdown .k-state-focused,
.k-filebrowser-dropzone,
.k-mobile-list .k-item > .k-link:active,
.k-mobile-list .k-item > .k-label:active,
.k-mobile-list .k-edit-label.k-check:active,
.k-mobile-list .k-recur-view .k-check:active,
.k-listbox .k-item:hover:not(.k-state-disabled) {
  color: #333333;
  background-color: #ebebeb;
  border-color: #aeaeae;
}
.k-pager-wrap .k-link.k-state-disabled:hover {
  background-color: #ffffff;
}
.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {
  color: #333333;
  background-color: #ebebeb;
  border-color: #aeaeae;
}
.k-state-hover > .k-select,
.k-state-focused > .k-select {
  border-color: #aeaeae;
}
.k-textbox:hover,
.k-state-hover,
.k-state-hover:hover,
.k-pager-wrap .k-link:hover,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em,
.k-draghandle:hover,
.k-listbox .k-item:hover {
  background-image: none;
}
.k-pager-wrap {
  background-color: #f5f5f5;
  color: #333333;
}
.k-autocomplete.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active,
.k-dropdown-wrap.k-state-active,
.k-state-active,
.k-state-active:hover,
.k-state-active > .k-link,
.k-panelbar > .k-item > .k-state-focused {
  background-image: none;
}
.k-state-selected,
.k-draghandle.k-state-selected:hover {
  background-image: none;
}
.k-draghandle.k-state-selected:hover {
  background-position: 50% 50%;
}
.k-state-hover > .k-link,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em {
  color: #333333;
}
.k-autocomplete.k-state-hover,
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-hover,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-hover,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-hover,
.k-dropdown-wrap.k-state-focused {
  background-color: #ebebeb;
  background-image: none;
  background-position: 50% 50%;
  border-color: #aeaeae;
}
.km-pane-wrapper .k-mobile-list input:not([type="checkbox"]):not([type="radio"]),
.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),
.km-pane-wrapper .k-mobile-list textarea,
.k-dropdown .k-state-focused .k-input {
  color: #333333;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {
  background-position: 50% 50%;
  background: #ebebeb;
  border-color: #ebebeb;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {
  background: #ffffff;
  border-color: #cccccc;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-dropdown .k-state-hover .k-input {
  color: #333333;
}
.k-state-error {
  border-color: #dbdbdb;
  background-color: #e3e3e3;
  color: #6b6b6b;
}
.k-ie11 .k-select,
.k-edge .k-select,
.k-ie11 .k-popup-edit-form,
.k-edge .k-popup-edit-form {
  opacity: .9;
}
.k-tile-empty.k-state-selected,
.k-loading-mask.k-state-selected {
  border-width: 0;
  background-image: none;
  background-color: transparent;
}
.k-state-disabled,
.k-state-disabled .k-link,
.k-other-month,
.k-other-month .k-link,
.k-dropzone em,
.k-dropzone .k-upload-status,
.k-tile-empty strong,
.k-slider .k-draghandle {
  color: #a1a1a1;
}
.k-progressbar-indeterminate {
  background: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/indeterminate.gif');
}
.k-progressbar-indeterminate .k-progress-status-wrap,
.k-progressbar-indeterminate .k-state-selected {
  display: none;
}
.k-slider-track {
  background-color: #cccccc;
}
.k-slider-selection {
  background-color: #428bca;
}
.k-slider-horizontal .k-tick {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/slider-h.gif');
}
.k-slider-vertical .k-tick {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/slider-v.gif');
}
.k-widget.k-tooltip,
.k-chart-crosshair-tooltip,
.k-chart-shared-tooltip {
  border-color: #000000;
  background-color: #000000;
  color: #ffffff;
}
.k-widget.k-tooltip-validation {
  border-color: #fbeed5;
  background-color: #fbeed5;
  color: #90640e;
}
.input-prepend .k-tooltip-validation,
.input-append .k-tooltip-validation {
  font-size: 12px;
  position: relative;
  top: 3px;
}
.k-callout-n {
  border-bottom-color: #000000;
}
.k-callout-w {
  border-right-color: #000000;
}
.k-callout-s {
  border-top-color: #000000;
}
.k-callout-e {
  border-left-color: #000000;
}
.k-tooltip-validation .k-callout-n {
  border-bottom-color: #fbeed5;
}
.k-tooltip-validation .k-callout-w {
  border-right-color: #fbeed5;
}
.k-tooltip-validation .k-callout-s {
  border-top-color: #fbeed5;
}
.k-tooltip-validation .k-callout-e {
  border-left-color: #fbeed5;
}
.k-splitbar {
  background-color: #f5f5f5;
}
.k-restricted-size-vertical,
.k-restricted-size-horizontal {
  background-color: #6b6b6b;
}
.k-file {
  background-color: #ffffff;
  border-color: #d2d2d2;
}
.k-file-progress {
  color: #2498bc;
}
.k-file-progress .k-progress {
  background-color: #2498bc;
}
.k-file-success .k-file-name,
.k-file-success .k-upload-pct {
  color: #3ea44e;
}
.k-file-success .k-progress {
  background-color: #3ea44e;
}
.k-file-error {
  color: #d92800;
}
.k-file-error .k-file-extension-wrapper,
.k-file-error .k-multiple-files-extension-wrapper {
  color: #d92800;
  border-color: #d92800;
}
.k-file-error .k-file-extension-wrapper:before,
.k-file-error .k-multiple-files-extension-wrapper:before {
  background-color: #ffffff;
  border-color: transparent transparent #d92800 #d92800;
}
.k-file-error .k-progress {
  background-color: #d92800;
}
.k-file-extension-wrapper,
.k-multiple-files-extension-wrapper {
  color: #a1a1a1;
  border-color: #a1a1a1;
}
.k-file-invalid .k-file-name-invalid {
  color: #d92800;
}
.k-file-invalid-extension-wrapper,
.k-multiple-files-invalid-extension-wrapper {
  color: #d92800;
  border-color: #d92800;
}
.k-file-extension-wrapper:before,
.k-multiple-files-extension-wrapper:before {
  background-color: #ffffff;
  border-color: transparent transparent #a1a1a1 #a1a1a1;
}
.k-file-invalid-extension-wrapper:before,
.k-multiple-files-invalid-extension-wrapper:before {
  background-color: #ffffff;
  border-color: transparent transparent #d92800 #d92800;
}
.k-multiple-files-extension-wrapper:after {
  border-top-color: #a1a1a1;
  border-left-color: #a1a1a1;
}
.k-multiple-files-invalid-extension-wrapper:after {
  border-top-color: #d92800;
  border-left-color: #d92800;
}
.k-file-size,
.k-file-information,
.k-file-validation-message {
  color: #a1a1a1;
}
.k-upload .k-upload-selected {
  color: #428bca;
  border-color: #cccccc;
}
.k-upload .k-upload-selected:hover {
  color: #ffffff;
  background-color: #428bca;
}
.k-tile {
  border-color: #ffffff;
}
.k-textbox:hover,
.k-tiles li.k-state-hover {
  border-color: #aeaeae;
}
.k-tiles li.k-state-selected {
  border-color: #428bca;
}
.k-leaf,
.k-leaf.k-state-hover:hover {
  color: #fff;
}
.k-leaf.k-inverse,
.k-leaf.k-inverse.k-state-hover:hover {
  color: #000;
}
.k-widget {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.k-calendar-container > .k-calendar {
  box-shadow: none;
}
.k-slider,
.k-treeview,
.k-upload {
  box-shadow: none;
}
.k-state-hover {
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
}
.k-autocomplete.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused {
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-state-selected {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
}
.k-state-active {
  box-shadow: none;
}
.k-grid td.k-state-selected.k-state-focused {
  background-color: #4e92cd;
}
.k-popup,
.k-menu .k-menu-group,
.k-grid .k-filter-options,
.k-time-popup,
.k-datepicker-calendar,
.k-autocomplete.k-state-border-down,
.k-autocomplete.k-state-border-up,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-multiselect.k-state-focused,
.k-filebrowser .k-image,
.k-tooltip {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
}
.k-treemap-tile.k-state-hover {
  box-shadow: inset 0 0 0 3px #cccccc;
}
.k-window {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.2);
}
.k-window.k-state-focused {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.2);
}
.k-window.k-window-maximized,
.k-window-maximized .k-window-titlebar,
.k-window-maximized .k-window-content {
  border-radius: 0;
}
.k-shadow {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}
.k-inset {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
}
.k-editor-inline ::selection {
  background-color: #428bca;
  text-shadow: none;
  color: #fff;
}
.k-editor-inline ::-moz-selection {
  background-color: #428bca;
  text-shadow: none;
  color: #fff;
}
.k-notification-info {
  background-color: #e5f5fa;
  color: #2498bc;
  border-color: #bae5f2;
}
.k-notification-success {
  background-color: #eaf7ec;
  color: #3ea44e;
  border-color: #c5e9cb;
}
.k-notification-warning {
  background-color: #fbeed5;
  color: #bf8412;
  border-color: #f7dba6;
}
.k-notification-error {
  background-color: #ffe0d9;
  color: #d92800;
  border-color: #ffb6a6;
}
.k-gantt .k-treelist {
  background: #f5f5f5;
}
.k-gantt .k-treelist .k-alt {
  background-color: #dcdcdc;
}
.k-gantt .k-treelist tr:hover {
  background-color: #ebebeb;
}
.k-gantt .k-treelist .k-state-selected,
.k-gantt .k-treelist .k-state-selected td,
.k-gantt .k-treelist .k-alt.k-state-selected,
.k-gantt .k-treelist .k-alt.k-state-selected > td {
  background-color: #428bca;
}
.k-task-dot:after {
  background-color: #333333;
  border-color: #333333;
}
.k-task-dot:hover:after {
  background-color: #ffffff;
}
.k-task-summary {
  border-color: #737373;
  background: #737373;
}
.k-task-milestone,
.k-task-summary-complete {
  border-color: #333333;
  background: #333333;
}
.k-state-selected.k-task-summary {
  border-color: #a6c8e6;
  background: #a6c8e6;
}
.k-state-selected.k-task-milestone,
.k-state-selected .k-task-summary-complete {
  border-color: #428bca;
  background: #428bca;
}
.k-task-single {
  background-color: #3a86c8;
  border-color: #3174ad;
  color: #ffffff;
}
.k-state-selected.k-task-single {
  border-color: #428bca;
}
.k-line {
  background-color: #333333;
  color: #333333;
}
.k-state-selected.k-line {
  background-color: #428bca;
  color: #428bca;
}
.k-resource {
  background-color: #ffffff;
}
.k-block,
.k-textbox,
.k-drag-clue,
.k-touch-scrollbar,
.k-window,
.k-window-titleless .k-window-content,
.k-inline-block,
.k-grid .k-filter-options,
.k-grouping-header .k-group-indicator,
.k-autocomplete,
.k-multiselect,
.k-combobox,
.k-dropdown,
.k-dropdown-wrap,
.k-datepicker,
.k-timepicker,
.k-colorpicker,
.k-datetimepicker,
.k-notification,
.k-numerictextbox,
.k-picker-wrap,
.k-numeric-wrap,
.k-list-container,
.k-calendar-container,
.k-calendar td,
.k-calendar .k-link,
.k-treeview .k-in,
.k-editor-inline,
.k-tooltip,
.k-tile,
.k-slider-track,
.k-slider-selection,
.k-upload,
.k-split-button {
  border-radius: 4px;
}
.k-tool {
  text-align: center;
  vertical-align: middle;
}
.k-tool.k-group-start,
.k-toolbar .k-split-button .k-button,
.k-toolbar .k-button-group .k-group-start {
  border-radius: 4px 0 0 4px;
}
.k-rtl .k-tool.k-group-start,
.k-rtl .k-toolbar .k-split-button .k-button,
.k-rtl .k-toolbar .k-button-group .k-group-start {
  border-radius: 0 4px 4px 0;
}
.k-toolbar .k-button-group > .k-group-end {
  border-radius: 4px;
}
.k-tool.k-group-end,
.k-toolbar .k-button-group .k-button + .k-group-end,
.k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 0 4px 4px 0;
}
.k-rtl .k-tool.k-group-end,
.k-rtl .k-toolbar .k-button-group .k-group-end,
.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 4px 0 0 4px;
}
.k-overflow-container .k-button-group .k-button {
  border-radius: 4px;
}
.k-overflow-container .k-separator {
  border-color: #cccccc;
}
.k-group-start.k-group-end.k-tool {
  border-radius: 4px;
}
.k-calendar-container.k-state-border-up,
.k-list-container.k-state-border-up,
.k-autocomplete.k-state-border-up,
.k-multiselect.k-state-border-up,
.k-dropdown-wrap.k-state-border-up,
.k-picker-wrap.k-state-border-up,
.k-numeric-wrap.k-state-border-up,
.k-window-content,
.k-filter-menu {
  border-radius: 0 0 4px 4px;
}
.k-autocomplete.k-state-border-up .k-input,
.k-dropdown-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-selected-color,
.k-numeric-wrap.k-state-border-up .k-input {
  border-radius: 0 0 0 4px;
}
.k-multiselect.k-state-border-up .k-multiselect-wrap {
  border-radius: 0 0 4px 4px;
}
.k-window-titlebar,
.k-block > .k-header,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.k-tabstrip-items .k-link,
.k-calendar-container.k-state-border-down,
.k-list-container.k-state-border-down,
.k-autocomplete.k-state-border-down,
.k-multiselect.k-state-border-down,
.k-dropdown-wrap.k-state-border-down,
.k-picker-wrap.k-state-border-down,
.k-numeric-wrap.k-state-border-down {
  border-radius: 4px 4px 0 0;
}
.k-split-button.k-state-border-down > .k-button {
  border-radius: 4px 0 0 0;
}
.k-split-button.k-state-border-up > .k-button {
  border-radius: 0 0 0 4px;
}
.k-split-button.k-state-border-down > .k-split-button-arrow {
  border-radius: 0 4px 0 0;
}
.k-split-button.k-state-border-up > .k-split-button-arrow {
  border-radius: 0 0 4px 0;
}
.k-dropdown-wrap .k-input,
.k-picker-wrap .k-input,
.k-numeric-wrap .k-input {
  border-radius: 3px 0 0 3px;
}
.k-rtl .k-dropdown-wrap .k-input,
.k-rtl .k-picker-wrap .k-input,
.k-rtl .k-numeric-wrap .k-input {
  border-radius: 0 3px 3px 0;
}
.k-numeric-wrap .k-link {
  border-radius: 0 3px 0 0;
}
.k-numeric-wrap .k-link + .k-link {
  border-radius: 0 0 3px 0;
}
.k-colorpicker .k-selected-color {
  border-radius: 3px 0 0 3px;
}
.k-rtl .k-colorpicker .k-selected-color {
  border-radius: 0 3px 3px 0;
}
.k-autocomplete.k-state-border-down .k-input {
  border-radius: 4px 4px 0 0;
}
.k-dropdown-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-selected-color,
.k-numeric-wrap.k-state-border-down .k-input {
  border-radius: 4px 0 0 0;
}
.k-numeric-wrap .k-link.k-state-selected {
  background-color: #ebebeb;
}
.k-multiselect.k-state-border-down .k-multiselect-wrap {
  border-radius: 3px 3px 0 0;
}
.k-dropdown-wrap .k-select,
.k-picker-wrap .k-select,
.k-numeric-wrap .k-select,
.k-datetimepicker .k-select + .k-select,
.k-list-container.k-state-border-right {
  border-radius: 0 4px 4px 0;
}
.k-rtl .k-dropdown-wrap .k-select,
.k-rtl .k-picker-wrap .k-select,
.k-rtl .k-numeric-wrap .k-select,
.k-rtl .k-datetimepicker .k-select + .k-select,
.k-rtl .k-list-container.k-state-border-right {
  border-radius: 4px 0 0 4px;
}
.k-numeric-wrap.k-expand-padding .k-input {
  border-radius: 4px;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-multiselect-wrap {
  border-radius: 3px;
}
.k-list .k-state-hover,
.k-list .k-state-focused,
.k-list .k-state-highlight,
.k-list .k-state-selected,
.k-fieldselector .k-list .k-item,
.k-list-optionlabel,
.k-dropzone,
.k-listbox .k-item {
  border-radius: 3px;
}
.k-slider .k-button,
.k-grid .k-slider .k-button {
  border-radius: 13px;
}
.k-draghandle {
  border-radius: 13px;
}
.k-scheduler-toolbar > ul li:first-child,
.k-scheduler-toolbar > ul li:first-child .k-link,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,
.k-gantt-toolbar > ul.k-gantt-views li:first-child + li,
.k-gantt-toolbar > ul.k-gantt-views li:first-child + li .k-link {
  border-radius: 4px 0 0 4px;
}
.k-rtl .k-scheduler-toolbar > ul li:first-child,
.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link,
.k-rtl .k-gantt-toolbar > ul.k-gantt-views li:first-child + li,
.k-rtl .k-gantt-toolbar > ul.k-gantt-views li:first-child + li .k-link,
.km-view.k-popup-edit-form .k-gantt-toolbar > ul li:last-child,
.km-view.k-popup-edit-form .k-gantt-toolbar > ul li:last-child .k-link {
  border-radius: 0 4px 4px 0;
}
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,
.k-edit-field > .k-scheduler-navigation {
  border-radius: 4px;
}
.k-scheduler-toolbar .k-nav-next,
.k-scheduler-toolbar ul + ul li:last-child,
.k-scheduler-toolbar .k-nav-next .k-link,
.k-scheduler-toolbar ul + ul li:last-child .k-link,
.k-gantt-toolbar ul.k-gantt-views li:last-child,
.k-gantt-toolbar ul.k-gantt-views li:last-child .k-link {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.k-rtl .k-scheduler-toolbar .k-nav-next,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child,
.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link,
.k-rtl .k-gantt-toolbar ul.k-gantt-views li:last-child,
.k-rtl .k-gantt-toolbar ul.k-gantt-views li:last-child .k-link {
  border-radius: 4px 0 0 4px;
}
.k-scheduler div.k-scheduler-footer ul li,
.k-scheduler div.k-scheduler-footer .k-link {
  border-radius: 4px;
}
.k-more-events,
.k-event,
.k-task-single,
.k-task-complete,
.k-event .k-link {
  border-radius: 3px;
}
.k-scheduler-mobile .k-event {
  border-radius: 2px;
}
.k-grid-mobile .k-column-active + th.k-header {
  border-left-color: #333333;
}
html .km-pane-wrapper .km-widget,
.k-ie .km-pane-wrapper .k-widget,
.k-ie .km-pane-wrapper .k-group,
.k-ie .km-pane-wrapper .k-content,
.k-ie .km-pane-wrapper .k-header,
.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-label,
.km-pane-wrapper .k-mobile-list .k-edit-field {
  color: #333333;
}
@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {
  div.km-pane-wrapper a {
    color: #333333;
  }
}
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-field,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {
  background-color: #ffffff;
  border-top: 1px solid #d2d2d2;
}
.km-pane-wrapper .k-mobile-list .k-edit-field textarea {
  outline-width: 0;
}
.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {
  background-color: #428bca;
  border-top-color: #428bca;
}
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {
  border-top-color: transparent;
}
.km-pane-wrapper .k-mobile-list .k-item:last-child {
  box-shadow: inset 0 -1px 0 #d2d2d2;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),
.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {
  color: #858585;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link {
  border-bottom: 1px solid #d2d2d2;
}
.km-pane-wrapper .k-mobile-list .k-edit-field {
  box-shadow: 0 1px 1px #d2d2d2;
}
.km-actionsheet .k-grid-delete,
.km-actionsheet .k-scheduler-delete,
.km-pane-wrapper .k-scheduler-delete,
.km-pane-wrapper .k-filter-menu .k-button[type=reset] {
  color: #fff;
  border-color: #dbdbdb;
  background-color: red;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}
.km-actionsheet .k-grid-delete:active,
.km-actionsheet .k-scheduler-delete:active,
.km-pane-wrapper .k-scheduler-delete:active,
.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {
  background-color: #990000;
}
.km-pane-wrapper .k-pager-numbers .k-link,
.km-pane-wrapper .k-pager-numbers .k-state-selected,
.km-pane-wrapper .k-pager-wrap > .k-link {
  border-radius: 0;
}
.km-pane-wrapper .k-pager-nav.k-pager-first {
  border-radius: 4px 0 0 4px;
}
.k-autocomplete.k-state-default,
.k-picker-wrap.k-state-default,
.k-numeric-wrap.k-state-default,
.k-dropdown-wrap.k-state-default {
  background-position: 50% 50%;
  background-color: #ffffff;
  border-color: #cccccc;
}
.k-autocomplete.k-state-hover,
.k-picker-wrap.k-state-hover,
.k-numeric-wrap.k-state-hover,
.k-dropdown-wrap.k-state-hover {
  background-color: #ebebeb;
  background-image: none;
  background-position: 50% 50%;
  border-color: #aeaeae;
}
.k-multiselect-wrap {
  border-color: #cccccc;
}
.k-multiselect-wrap.k-state-hover,
.k-state-hover > .k-multiselect-wrap,
.k-multiselect-wrap.k-state-hover {
  border-color: #aeaeae;
  background-color: #ffffff;
  color: #ffffff;
}
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-focused {
  background-color: #ebebeb;
  background-image: none;
  background-position: 50% 50%;
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-multiselect-wrap.k-state-focused,
.k-state-focused > .k-multiselect-wrap {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-list-container {
  color: #333333;
}
.k-nodata {
  color: #a1a1a1;
}
.k-dropdown .k-input,
.k-dropdown .k-state-focused .k-input,
.k-menu .k-popup {
  color: #333333;
}
.k-state-default > .k-select {
  border-color: #cccccc;
}
.k-state-hover > .k-select {
  border-color: #aeaeae;
}
.k-state-focused > .k-select {
  border-color: #76abd9;
}
.k-tabstrip:focus {
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-tabstrip-items .k-state-default .k-link,
.k-panelbar > li.k-state-default > .k-link {
  color: #428bca;
}
.k-tabstrip-items .k-state-hover .k-link,
.k-panelbar > li.k-state-hover > .k-link,
.k-panelbar > li.k-state-default > .k-link.k-state-hover {
  color: #333333;
}
.k-panelbar > .k-state-focused.k-state-hover {
  background: #ebebeb;
  box-shadow: none;
}
.k-tabstrip-items .k-item {
  border-color: transparent;
}
.k-tabstrip-items .k-state-hover {
  border-color: #aeaeae;
}
.k-tabstrip-items .k-state-active,
.k-panelbar .k-tabstrip-items .k-state-active {
  background-color: #ffffff;
  background-image: none;
  border-color: #cccccc;
}
.k-tabstrip-top > .k-tabstrip-items .k-state-active,
.k-panelbar .k-tabstrip-top > .k-tabstrip-items .k-state-active {
  border-bottom-color: #ffffff;
}
.k-tabstrip .k-content.k-state-active {
  background-color: #ffffff;
  color: #333333;
}
.k-menu.k-header,
.k-menu .k-item {
  border-color: #cccccc;
}
.k-column-menu,
.k-column-menu .k-item,
.k-overflow-container .k-overflow-group {
  border-color: #cccccc;
}
.k-overflow-container .k-overflow-group {
  box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;
}
.k-toolbar-first-visible.k-overflow-group,
.k-overflow-container .k-overflow-group + .k-overflow-group {
  box-shadow: 0 1px 0 #ffffff;
}
.k-toolbar-last-visible.k-overflow-group {
  box-shadow: inset 0 1px 0 #ffffff;
}
.k-toolbar:not(.k-spreadsheet-toolbar) .k-button-group .k-button:focus,
.k-toolbar:not(.k-spreadsheet-toolbar) .k-button.k-state-disabled:focus {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-column-menu .k-separator {
  border-color: #cccccc;
  background-color: transparent;
}
.k-menu .k-group {
  border-color: rgba(0, 0, 0, 0.2);
}
.k-grid-filter.k-state-active {
  background-color: #ffffff;
}
.k-grouping-row td,
.k-group-footer td,
.k-grid-footer td {
  color: #333333;
  border-color: #cccccc;
  font-weight: bold;
}
.k-grouping-header {
  color: #333333;
}
.k-grid tr:hover {
  background-color: #ebebeb;
}
.k-grid .k-filter-row:hover,
.k-pivot-rowheaders .k-grid tr:hover {
  background: none;
}
.k-grid td.k-state-focused {
  box-shadow: inset 0 0 0 1px inset 0 0 7px 0 #76abd9;
}
.k-grid tr.k-state-focused:hover td {
  background-color: #d5e5f3;
}
.k-grid td.k-state-selected:hover,
.k-grid tr.k-state-selected:hover td {
  background-color: #3379b5;
}
.k-grid tr:hover .k-state-focused.k-state-selected,
.k-grid tr.k-state-selected:hover td.k-state-focused {
  box-shadow: inset 0 0 7px 0 #193c5a;
}
.k-header,
.k-grid-header-wrap,
.k-grid .k-grouping-header,
.k-grid-header,
.k-pager-wrap,
.k-pager-wrap .k-textbox,
.k-pager-wrap .k-link,
.k-grouping-header .k-group-indicator,
.k-gantt-toolbar .k-state-default {
  border-color: #cccccc;
}
.k-pager-numbers .k-link,
.k-treeview .k-in {
  border-color: transparent;
}
.k-treeview .k-icon,
.k-scheduler-table .k-icon,
.k-grid .k-hierarchy-cell .k-icon {
  background-color: transparent;
  border-radius: 4px;
}
.k-scheduler-table .k-state-hover .k-icon {
  background-color: transparent;
}
.k-editor .k-tool:focus {
  outline: 0;
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-checkbox-label:before {
  border-color: #cccccc;
  background: #ffffff;
  border-radius: 3px;
}
.k-checkbox:hover + .k-checkbox-label:before,
.k-checkbox:checked:hover + .k-checkbox-label:before,
.k-checkbox-label:hover:before,
.k-checkbox:checked + .k-checkbox-label:hover:before {
  border-color: #aeaeae;
  box-shadow: none;
}
.k-checkbox:checked + .k-checkbox-label:before {
  background-color: #ffffff;
  border-color: #cccccc;
  color: #428bca;
}
.k-checkbox:active + .k-checkbox-label:before,
.k-checkbox-label:active:before {
  box-shadow: 0 0 2px 0 #76abd9;
  border-color: #76abd9;
}
.k-checkbox:checked:active + .k-checkbox-label:before,
.k-checkbox:checked + .k-checkbox-label:active:before {
  box-shadow: 0 0 2px 0 #76abd9;
  border-color: #76abd9;
}
.k-checkbox:disabled + .k-checkbox-label {
  color: #a1a1a1;
}
.k-checkbox:disabled + .k-checkbox-label:hover:before {
  box-shadow: none;
}
.k-checkbox:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:active:before,
.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {
  color: #a1a1a1;
  background: #ffffff;
  border-color: #cccccc;
  border-radius: 3px;
}
.k-checkbox:focus + .k-checkbox-label:before,
.k-checkbox:focus + .k-checkbox-label:hover:before {
  border-color: #76abd9;
  box-shadow: 0 0 2px 0 #76abd9;
}
.k-checkbox:indeterminate + .k-checkbox-label:after {
  background-color: #428bca;
  background-image: none;
  border-color: #76abd9;
  border-radius: 2px;
}
.k-checkbox:indeterminate:hover + .k-checkbox-label:after {
  border-color: #428bca;
  background-color: #428bca;
}
.k-radio-label:before {
  border-color: #cccccc;
  border-radius: 50%;
  background-color: #ffffff;
  border-width: 1px;
}
.k-radio-label:hover:before,
.k-radio:checked + .k-radio-label:hover:before {
  border-color: #aeaeae;
  box-shadow: none;
}
.k-radio:checked + .k-radio-label:after {
  background-color: #428bca;
  border-radius: 50%;
}
.k-radio-label:active:before {
  border-color: #76abd9;
  box-shadow: 0 0 2px 0 #76abd9;
}
.k-radio:checked + .k-radio-label:active:before {
  box-shadow: 0 0 2px 0 #76abd9;
  border-color: #76abd9;
}
.k-radio:disabled + .k-radio-label {
  color: #d2d2d2;
}
.k-radio:disabled + .k-radio-label:before,
.k-radio:disabled + .k-radio-label:active:before,
.k-radio:disabled + .k-radio-label:hover:after,
.k-radio:disabled + .k-radio-label:hover:before {
  background: #ffffff;
  border-color: #cccccc;
  box-shadow: none;
}
.k-radio:disabled:checked + .k-radio-label:after {
  background-color: #428bca;
  opacity: .5;
}
.k-radio:focus + .k-radio-label:before {
  border-color: #76abd9;
  box-shadow: 0 0 2px 0 #76abd9;
}
@media screen and (-ms-high-contrast: active) {
  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,
  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {
    border-color: #fff;
  }
}
@media only screen and (max-width: 1024px) {
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-edge .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    background-position: 50% 50%;
    background-color: #ffffff;
    border-color: #cccccc;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-edge .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    border-color: #cccccc;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {
    border-radius: 4px;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {
    border-radius: 3px 3px 0 0;
  }
  .k-webkit .k-scheduler-toolbar > ul li:first-child,
  .k-ff .k-scheduler-toolbar > ul li:first-child,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child,
  .k-edge .k-scheduler-toolbar > ul li:first-child,
  .k-safari .k-scheduler-toolbar > ul li:first-child,
  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-edge .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {
    border-radius: 0 0 3px 3px;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link:hover,
  .k-ff .k-pager-numbers .k-current-page .k-link:hover,
  .k-ie11 .k-pager-numbers .k-current-page .k-link:hover,
  .k-edge .k-pager-numbers .k-current-page .k-link:hover,
  .k-safari .k-pager-numbers .k-current-page .k-link:hover,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {
    border-color: #aeaeae;
    background-image: none;
    background-color: #ebebeb;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {
    color: #333333;
    min-width: 75px;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {
    color: #333333;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link:after,
  .k-ff .k-pager-numbers .k-current-page .k-link:after,
  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,
  .k-edge .k-pager-numbers .k-current-page .k-link:after,
  .k-safari .k-pager-numbers .k-current-page .k-link:after,
  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-edge .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {
    display: block;
    content: "";
    position: absolute;
    top: 50%;
    margin-top: -0.5em;
    right: 0.333em;
    width: 1.333em;
    height: 1.333em;
  }
  .k-webkit .k-pager-numbers.k-state-expanded,
  .k-ff .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-pager-numbers.k-state-expanded,
  .k-edge .k-pager-numbers.k-state-expanded,
  .k-safari .k-pager-numbers.k-state-expanded,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
    border-width: 1px 1px 0 1px;
    border-style: solid;
    border-color: #cccccc;
    background-color: #f5f5f5;
    border-radius: 4px 4px 0 0;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
    border-width: 1px;
    background-image: none;
    border-radius: 4px;
  }
  .k-webkit .k-pager-numbers .k-state-selected,
  .k-ff .k-pager-numbers .k-state-selected,
  .k-ie11 .k-pager-numbers .k-state-selected,
  .k-edge .k-pager-numbers .k-state-selected,
  .k-safari .k-pager-numbers .k-state-selected,
  .k-webkit .k-pager-numbers .k-link,
  .k-ff .k-pager-numbers .k-link,
  .k-ie11 .k-pager-numbers .k-link,
  .k-edge .k-pager-numbers .k-link,
  .k-safari .k-pager-numbers .k-link {
    border-radius: 3px;
  }
  .k-webkit .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-ff .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-ie11 .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-edge .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-safari .k-widget.k-grid .k-pager-nav + .k-pager-numbers {
    position: absolute;
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers,
  .k-ff .km-pane-wrapper .k-pager-numbers,
  .k-ie11 .km-pane-wrapper .k-pager-numbers,
  .k-edge .km-pane-wrapper .k-pager-numbers,
  .k-safari .km-pane-wrapper .k-pager-numbers,
  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-edge .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers {
    transform: translate(-50%, -100%);
    -webkit-transform: translate(-50%, -100%);
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-ff .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-ie11 .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-edge .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-safari .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-edge .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded {
    transform: translate(-50%, -100%);
    -webkit-transform: translate(-50%, -100%);
  }
  .k-webkit .k-grid .k-pager-numbers:not(.k-state-expanded),
  .k-ff .k-grid .k-pager-numbers:not(.k-state-expanded),
  .k-ie11 .k-grid .k-pager-numbers:not(.k-state-expanded),
  .k-edge .k-grid .k-pager-numbers:not(.k-state-expanded),
  .k-safari .k-grid .k-pager-numbers:not(.k-state-expanded) {
    transform: none;
  }
  .k-webkit .k-grid .k-pager-numbers:not(.k-state-expanded) .k-current-page,
  .k-ff .k-grid .k-pager-numbers:not(.k-state-expanded) .k-current-page,
  .k-ie11 .k-grid .k-pager-numbers:not(.k-state-expanded) .k-current-page,
  .k-edge .k-grid .k-pager-numbers:not(.k-state-expanded) .k-current-page,
  .k-safari .k-grid .k-pager-numbers:not(.k-state-expanded) .k-current-page {
    transform: none;
  }
}
.k-grid,
.k-scheduler,
.k-menu,
.k-editor {
  border-radius: 4px;
}
.k-grid > .k-grouping-header,
.k-grid-header:first-child,
.k-grid-toolbar:first-child,
.k-scheduler > .k-scheduler-toolbar:first-child {
  border-radius: 3px 3px 0 0;
}
.k-grid-header:first-child th.k-header:first-child,
thead.k-grid-header th.k-header:first-child,
.k-rtl thead.k-grid-header th.k-header:last-child {
  border-radius: 3px 0 0 0;
}
.k-rtl .k-grid-header:first-child th.k-header:first-child,
.k-rtl thead.k-grid-header th.k-header:first-child,
thead.k-grid-header th.k-header:last-child {
  border-radius: 0 3px 0 0;
}
.k-grid-pager,
.k-scheduler-footer {
  border-radius: 0 0 3px 3px;
}
.k-menu > .k-first {
  border-radius: 3px 0 0 3px;
}
.k-rtl .k-menu > .k-first {
  border-radius: 0 3px 3px 0;
}
.k-widget.k-treeview {
  color: #428bca;
}
.k-treeview .k-in.k-state-hover {
  color: #428bca;
}
.k-treeview .k-state-selected,
.k-draghandle.k-state-selected {
  box-shadow: none;
}
.k-tabstrip-items .k-state-hover .k-link {
  color: #428bca;
}
.k-tabstrip-items .k-state-active .k-link {
  color: #333333;
}
.k-tabstrip-items .k-state-active .k-link {
  color: #333333;
}
.k-tabstrip-items .k-item {
  background: transparent;
}
.k-tabstrip-items .k-item.k-state-active {
  background: #ffffff;
}
.k-tabstrip-items .k-item.k-state-hover {
  background: #ebebeb;
}
.k-tabstrip-items .k-state-focused {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-tabstrip-items .k-state-default.k-state-hover {
  border-color: #ebebeb;
}
.k-multiselect .k-button {
  color: #ffffff;
  border-color: #357ebd;
  background-color: #428bca;
}
.k-multiselect .k-button.k-state-hover {
  border-color: #285e8e;
  background-color: #3276b1;
}
.k-multiselect .k-button:active {
  color: #ffffff;
  box-shadow: inset 0 0 10px 3px #3276b1;
}
.k-list > .k-state-hover {
  border-color: #ebebeb;
}
.k-scheduler-toolbar > ul > li {
  background-color: #ffffff;
  border-color: #cccccc;
}
.k-menu .k-group {
  background: #ffffff;
}
.k-menu .k-state-default .k-state-border-down {
  background-color: #e8e8e8;
  box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.125);
}
.k-menu .k-item > .k-state-border-right,
.k-menu .k-item > .k-state-border-left {
  background-color: #428bca;
  color: #ffffff;
}
.k-menu .k-state-selected > .k-link {
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
  background-image: none;
}
.k-menu .k-link.k-state-active,
.k-menu .k-state-hover > .k-link,
.k-menu .k-menu-scroll-button:hover {
  color: #333333;
  background-color: #ebebeb;
  border-color: #aeaeae;
  background-image: none;
}
.k-menu .k-state-hover .k-link.k-state-active {
  background-color: #d9d9d9;
}
.k-slider-track {
  background-color: #ffffff;
  border-color: #cccccc;
  border-radius: 0;
}
.k-slider-selection {
  background-color: #ebebeb;
  border-radius: 0;
}
.k-slider-horizontal .k-tick {
  background-image: url('https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/slider-h.gif');
}
.k-draghandle.k-state-selected,
.k-draghandle.k-state-selected:link,
.k-draghandle.k-state-selected:hover {
  background-color: #cccccc;
  border-color: #cccccc;
}
.k-draghandle.k-state-focused,
.k-draghandle.k-state-focused:link,
.k-slider-buttons .k-button:focus,
.k-slider-buttons .k-button:active {
  box-shadow: 0 0 7px 0 #76abd9;
  border-color: #76abd9;
  background-color: #ffffff;
}
.k-autocomplete.k-state-default,
.k-picker-wrap.k-state-default,
.k-numeric-wrap.k-state-default,
.k-dropdown-wrap.k-state-default,
.k-multiselect-wrap {
  border-color: #cccccc;
}
.k-widget.k-autocomplete,
.k-widget.k-combobox,
.k-widget.k-dropdown,
.k-widget.k-datepicker,
.k-widget.k-datetimepicker,
.k-widget.k-timepicker,
.k-widget.k-numerictextbox,
.k-widget.k-multiselect,
.k-widget.k-menu,
.k-widget.k-progressbar,
.k-progressbar > .k-state-selected {
  box-shadow: none;
}
.k-autocomplete.k-state-default .k-input,
.k-combobox .k-state-default .k-input,
.k-picker-wrap.k-state-default .k-input,
.k-numeric-wrap.k-state-default .k-input,
.k-multiselect-wrap,
.k-slider-track,
.k-slider-selection,
.k-progress-status-wrap {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.k-textbox:focus,
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-multiselect.k-header.k-state-focused {
  background-color: #ffffff;
  background-image: none;
  background-position: 50% 50%;
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-multiselect-wrap.k-state-focused,
.k-state-focused > .k-multiselect-wrap {
  border-color: #76abd9;
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-textbox:hover,
.k-autocomplete.k-state-hover,
.k-picker-wrap.k-state-hover,
.k-numeric-wrap.k-state-hover,
.k-dropdown-wrap.k-state-hover {
  background-color: #ebebeb;
  background-image: none;
  background-position: 50% 50%;
  border-color: #aeaeae;
}
.k-autocomplete.k-state-border-down,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active,
.k-pager-numbers.k-state-expanded .k-current-page .k-link {
  border-color: #cccccc;
  background-color: #ebebeb;
}
.k-grid-filter.k-state-active {
  border-color: #aeaeae;
  background-color: #ebebeb;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
}
.k-picker-wrap.k-state-active.k-state-border-down,
.k-dropdown-wrap.k-state-active.k-state-border-down,
.k-numeric-wrap .k-link.k-state-selected,
.k-pager-numbers.k-state-expanded .k-current-page .k-link {
  box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.125);
}
.k-picker-wrap.k-state-focused > .k-select,
.k-picker-wrap.k-state-active > .k-select,
.k-dropdown-wrap.k-state-focused > .k-select,
.k-dropdown-wrap.k-state-active > .k-select {
  border-color: #cccccc;
}
.k-panelbar > .k-item > .k-link {
  border-color: #cccccc;
}
.k-panelbar > li.k-state-default > .k-link {
  color: #333333;
}
.k-panelbar > .k-state-focused {
  box-shadow: inset 0 0 7px 0 #76abd9;
}
.k-panelbar > li > .k-link.k-state-selected {
  color: #ffffff;
}
.k-panelbar .k-link.k-state-selected.k-state-hover {
  color: #333333;
}
td.k-state-focused.k-state-selected,
.k-state-selected td.k-state-focused,
.k-listview > .k-state-focused.k-state-selected {
  box-shadow: inset 0 0 10px 3px #3276b1;
}
.k-panelbar > .k-state-focused.k-state-hover {
  background: #ebebeb;
  box-shadow: none;
}
.k-panelbar > li .k-state-selected.k-state-hover {
  box-shadow: none;
}
.k-pager-wrap {
  color: #a6a6a6;
}
.k-pager-wrap > .k-link,
.k-pager-numbers .k-link,
.k-pager-numbers .k-state-selected {
  border-radius: 0;
}
.k-grid tr td {
  border-style: solid;
  border-color: #cccccc;
}
.k-pager-wrap .k-link {
  background: #ffffff;
  border-color: #cccccc;
  border-right: none;
}
.k-pager-wrap .k-pager-refresh {
  background: #f5f5f5;
}
.k-pager-wrap .k-link:hover {
  color: #428bca;
  border-color: #cccccc;
  background: #ebebeb;
}
.k-grid-header .k-link:link,
.k-grid-header .k-link:visited,
.k-grid-header .k-nav-current.k-state-hover .k-link,
.k-grouping-header .k-link {
  color: #333333;
}
.k-pager-wrap .k-pager-nav.k-pager-last {
  border-radius: 0 4px 4px 0;
  border-right-color: #cccccc;
}
.k-rtl .k-pager-wrap .k-pager-nav.k-pager-last {
  border-radius: 4px 0 0 4px;
  border-right-width: 0;
}
.k-pager-nav.k-pager-first {
  border-radius: 4px 0 0 4px;
}
.k-rtl .k-pager-nav.k-pager-first {
  border-radius: 0 4px 4px 0;
  border-right-width: 1px;
  border-right-style: solid;
  border-right-color: #cccccc;
  margin-right: 6px;
}
.k-dropzone .k-upload-status {
  color: #333333;
}
.k-file .k-upload-status {
  color: #333333;
}
.k-menu .k-group,
.k-grid .k-filter-options,
.k-time-popup,
.k-datepicker-calendar,
.k-autocomplete.k-state-border-down,
.k-autocomplete.k-state-border-up,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-multiselect.k-state-focused,
.k-filebrowser .k-image,
.k-tooltip {
  box-shadow: none;
}
.k-window {
  border-color: #cccccc;
}
.k-state-focused {
  box-shadow: 0 0 7px 0 #76abd9;
}
.k-list > .k-state-focused {
  box-shadow: none;
}
.k-popup {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.k-tooltip {
  box-shadow: none;
}
.k-list-container {
  border-color: #cccccc;
}
.k-flatcolorpicker .k-hue-slider .k-draghandle {
  box-shadow: 0 0 0 1px #c4c4c4;
  background: transparent;
  border: 5px solid #ffffff;
}
.k-flatcolorpicker .k-hue-slider .k-draghandle:hover,
.k-flatcolorpicker .k-hue-slider .k-draghandle:focus {
  border-color: #ffffff;
  box-shadow: 0 0 8px 0 rgba(102, 175, 233, 0.75);
  border-width: 5px;
  padding: 0;
}
.k-pager-numbers .k-link,
.k-pager-numbers .k-state-selected {
  margin-right: 0;
}
.k-grid .k-pager-numbers,
.k-pager-wrap > .k-link {
  margin: 0;
}
.k-pager-wrap .k-link {
  border-right: none;
}
.k-pager-wrap .k-link.k-pager-last {
  border-right-width: 1px;
  border-right-style: solid;
}
.k-editor-toolbar .k-tool {
  color: #333333;
  background-color: #ffffff;
  border-color: #cccccc;
}
.k-editor-toolbar .k-tool.k-state-hover {
  color: #333333;
  border-color: #aeaeae;
  background-color: #ebebeb;
}
.k-editor-toolbar .k-state-selected {
  box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.125);
  background-color: #ebebeb;
  border-color: #aeaeae;
}
.k-editor-toolbar .k-button-group .k-tool-icon {
  border-color: #cccccc;
}
.k-splitbar,
.k-splitbar:hover {
  border-color: transparent;
}
.k-pager-nav.k-pager-first + .k-link {
  border-right: 1px solid #cccccc;
}
@media only screen and (max-width: 1024px) {
  .k-webkit .k-pager-wrap,
  .k-ff .k-pager-wrap,
  .k-ie11 .k-pager-wrap,
  .k-edge .k-pager-wrap,
  .k-safari .k-pager-wrap {
    min-height: 2.4em;
  }
  .k-webkit .k-pager-wrap .k-pager-nav,
  .k-ff .k-pager-wrap .k-pager-nav,
  .k-ie11 .k-pager-wrap .k-pager-nav,
  .k-edge .k-pager-wrap .k-pager-nav,
  .k-safari .k-pager-wrap .k-pager-nav,
  .k-webkit .k-pager-input,
  .k-ff .k-pager-input,
  .k-ie11 .k-pager-input,
  .k-edge .k-pager-input,
  .k-safari .k-pager-input {
    display: inline-block;
    vertical-align: top;
  }
  .k-webkit .k-pager-numbers,
  .k-ff .k-pager-numbers,
  .k-ie11 .k-pager-numbers,
  .k-edge .k-pager-numbers,
  .k-safari .k-pager-numbers,
  .k-webkit .k-grid .k-pager-numbers,
  .k-ff .k-grid .k-pager-numbers,
  .k-ie11 .k-grid .k-pager-numbers,
  .k-edge .k-grid .k-pager-numbers,
  .k-safari .k-grid .k-pager-numbers {
    position: absolute;
    display: inline-flex;
    flex-direction: column-reverse;
    left: 5.6em;
    overflow: visible;
    height: auto;
    transform: translatey(-100%);
    -webkit-transform: translatey(-100%);
  }
  .k-webkit .k-pager-numbers:first-child,
  .k-ff .k-pager-numbers:first-child,
  .k-ie11 .k-pager-numbers:first-child,
  .k-edge .k-pager-numbers:first-child,
  .k-safari .k-pager-numbers:first-child,
  .k-webkit .k-grid .k-pager-numbers:first-child,
  .k-ff .k-grid .k-pager-numbers:first-child,
  .k-ie11 .k-grid .k-pager-numbers:first-child,
  .k-edge .k-grid .k-pager-numbers:first-child,
  .k-safari .k-grid .k-pager-numbers:first-child {
    left: .3em;
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,
  .k-ff .km-pane-wrapper .k-pager-numbers .k-link,
  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,
  .k-edge .km-pane-wrapper .k-pager-numbers .k-link,
  .k-safari .km-pane-wrapper .k-pager-numbers .k-link,
  .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-edge .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-edge .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-edge .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {
    padding-top: 0;
    padding-bottom: 0;
  }
  .k-webkit .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,
  .k-ff .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,
  .k-ie11 .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,
  .k-edge .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,
  .k-safari .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,
  .k-webkit .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav,
  .k-ff .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav,
  .k-ie11 .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav,
  .k-edge .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav,
  .k-safari .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav {
    right: 2.7em;
  }
  .k-webkit .k-rtl .k-pager-numbers,
  .k-ff .k-rtl .k-pager-numbers,
  .k-ie11 .k-rtl .k-pager-numbers,
  .k-edge .k-rtl .k-pager-numbers,
  .k-safari .k-rtl .k-pager-numbers,
  .k-webkit .k-rtl .k-grid .k-pager-numbers,
  .k-ff .k-rtl .k-grid .k-pager-numbers,
  .k-ie11 .k-rtl .k-grid .k-pager-numbers,
  .k-edge .k-rtl .k-grid .k-pager-numbers,
  .k-safari .k-rtl .k-grid .k-pager-numbers {
    right: 5.68em;
    width: 4.45em;
  }
  .k-webkit .k-rtl .k-pager-numbers:first-child,
  .k-ff .k-rtl .k-pager-numbers:first-child,
  .k-ie11 .k-rtl .k-pager-numbers:first-child,
  .k-edge .k-rtl .k-pager-numbers:first-child,
  .k-safari .k-rtl .k-pager-numbers:first-child,
  .k-webkit .k-rtl .k-grid .k-pager-numbers:first-child,
  .k-ff .k-rtl .k-grid .k-pager-numbers:first-child,
  .k-ie11 .k-rtl .k-grid .k-pager-numbers:first-child,
  .k-edge .k-rtl .k-grid .k-pager-numbers:first-child,
  .k-safari .k-rtl .k-grid .k-pager-numbers:first-child {
    right: .3em;
  }
  .k-webkit .k-pager-numbers .k-current-page,
  .k-ff .k-pager-numbers .k-current-page,
  .k-ie11 .k-pager-numbers .k-current-page,
  .k-edge .k-pager-numbers .k-current-page,
  .k-safari .k-pager-numbers .k-current-page,
  .k-webkit .k-grid .k-pager-numbers .k-current-page,
  .k-ff .k-grid .k-pager-numbers .k-current-page,
  .k-ie11 .k-grid .k-pager-numbers .k-current-page,
  .k-edge .k-grid .k-pager-numbers .k-current-page,
  .k-safari .k-grid .k-pager-numbers .k-current-page {
    display: block;
    border-left: 0;
    transform: translatey(100%);
    -webkit-transform: translatey(100%);
  }
  .k-webkit .k-pager-numbers li:not(.k-current-page),
  .k-ff .k-pager-numbers li:not(.k-current-page),
  .k-ie11 .k-pager-numbers li:not(.k-current-page),
  .k-edge .k-pager-numbers li:not(.k-current-page),
  .k-safari .k-pager-numbers li:not(.k-current-page) {
    display: none;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-edge .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    width: 3.2em;
    padding: 0 .429em 0 .714em;
    border-radius: 4px;
    border-right: 1px solid #cccccc;
    color: #333333;
    background-color: #ffffff;
  }
  .k-webkit .k-pager-numbers .k-link,
  .k-ff .k-pager-numbers .k-link,
  .k-ie11 .k-pager-numbers .k-link,
  .k-edge .k-pager-numbers .k-link,
  .k-safari .k-pager-numbers .k-link {
    color: #333333;
  }
  .k-webkit .k-pager-nav.k-pager-first + .k-link,
  .k-ff .k-pager-nav.k-pager-first + .k-link,
  .k-ie11 .k-pager-nav.k-pager-first + .k-link,
  .k-edge .k-pager-nav.k-pager-first + .k-link,
  .k-safari .k-pager-nav.k-pager-first + .k-link {
    border-right: 1px solid #cccccc;
    border-radius: 0 4px 4px 0;
  }
  .k-webkit .k-pager-nav.k-pager-first,
  .k-ff .k-pager-nav.k-pager-first,
  .k-ie11 .k-pager-nav.k-pager-first,
  .k-edge .k-pager-nav.k-pager-first,
  .k-safari .k-pager-nav.k-pager-first,
  .k-webkit .k-rtl .k-pager-nav.k-pager-first + .k-link,
  .k-ff .k-rtl .k-pager-nav.k-pager-first + .k-link,
  .k-ie11 .k-rtl .k-pager-nav.k-pager-first + .k-link,
  .k-edge .k-rtl .k-pager-nav.k-pager-first + .k-link,
  .k-safari .k-rtl .k-pager-nav.k-pager-first + .k-link {
    border-right: 0;
    border-radius: 4px 0 0 4px;
  }
  .k-webkit .k-pager-numbers + .k-link,
  .k-ff .k-pager-numbers + .k-link,
  .k-ie11 .k-pager-numbers + .k-link,
  .k-edge .k-pager-numbers + .k-link,
  .k-safari .k-pager-numbers + .k-link {
    margin-left: 5.6em;
    border-radius: 4px 0 0 4px;
  }
  .k-webkit .k-rtl .k-pager-numbers + .k-link,
  .k-ff .k-rtl .k-pager-numbers + .k-link,
  .k-ie11 .k-rtl .k-pager-numbers + .k-link,
  .k-edge .k-rtl .k-pager-numbers + .k-link,
  .k-safari .k-rtl .k-pager-numbers + .k-link {
    margin-right: 5.6em;
    margin-left: 0;
    border-radius: 0 4px 4px 0;
    border-right-width: 1px;
    border-right-style: solid;
    border-right-color: #cccccc;
  }
  .k-webkit .k-pager-numbers .k-state-selected,
  .k-ff .k-pager-numbers .k-state-selected,
  .k-ie11 .k-pager-numbers .k-state-selected,
  .k-edge .k-pager-numbers .k-state-selected,
  .k-safari .k-pager-numbers .k-state-selected,
  .k-webkit .k-pager-numbers .k-link,
  .k-ff .k-pager-numbers .k-link,
  .k-ie11 .k-pager-numbers .k-link,
  .k-edge .k-pager-numbers .k-link,
  .k-safari .k-pager-numbers .k-link {
    display: block;
    margin-right: 0;
    padding: 1px 5px 1px 5px;
    text-align: left;
  }
  .k-webkit .k-pager-numbers.k-state-expanded,
  .k-ff .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-pager-numbers.k-state-expanded,
  .k-edge .k-pager-numbers.k-state-expanded,
  .k-safari .k-pager-numbers.k-state-expanded {
    box-sizing: border-box;
    padding: 2px 2px 0;
    background-color: #ffffff;
  }
  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,
  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,
  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,
  .k-edge .k-pager-numbers.k-state-expanded .k-current-page,
  .k-safari .k-pager-numbers.k-state-expanded .k-current-page {
    margin: -2em -3px 0;
    padding: 0;
  }
  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-edge .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {
    border-radius: 0 0 4px 4px;
  }
  .k-webkit .k-pager-numbers.k-state-expanded li,
  .k-ff .k-pager-numbers.k-state-expanded li,
  .k-ie11 .k-pager-numbers.k-state-expanded li,
  .k-edge .k-pager-numbers.k-state-expanded li,
  .k-safari .k-pager-numbers.k-state-expanded li {
    display: inline-block;
  }
  .k-webkit .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link,
  .k-ff .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link,
  .k-ie11 .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link,
  .k-edge .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link,
  .k-safari .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link {
    border: 0;
    background-color: #ffffff;
    border-radius: 3px;
  }
  .k-webkit .k-pager-numbers .k-state-selected,
  .k-ff .k-pager-numbers .k-state-selected,
  .k-ie11 .k-pager-numbers .k-state-selected,
  .k-edge .k-pager-numbers .k-state-selected,
  .k-safari .k-pager-numbers .k-state-selected {
    border-radius: 3px;
  }
  .k-webkit .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link:hover,
  .k-ff .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link:hover,
  .k-ie11 .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link:hover,
  .k-edge .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link:hover,
  .k-safari .k-pager-numbers.k-state-expanded li:not(.k-current-page) .k-link:hover {
    color: #333333;
    background-color: #ebebeb;
  }
}
@media only screen and (max-width: 640px) {
  .k-webkit .k-pager-info,
  .k-ff .k-pager-info,
  .k-ie11 .k-pager-info,
  .k-edge .k-pager-info,
  .k-safari .k-pager-info {
    display: none;
  }
}
@media only screen and (max-width: 480px) {
  .k-webkit .k-pager-sizes,
  .k-ff .k-pager-sizes,
  .k-ie11 .k-pager-sizes,
  .k-edge .k-pager-sizes,
  .k-safari .k-pager-sizes {
    display: none;
  }
}
.k-chart .k-selection {
  border-color: #cccccc;
  transition: box-shadow 0.2s linear, border-color 0.2s linear;
}
.k-chart .k-selection:hover {
  border-color: #9e9e9e;
}
.k-chart .k-handle {
  background-color: #ffffff;
  width: 15px;
  height: 15px;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.k-chart .k-handle:hover {
  background-color: #ffffff;
  border-color: #cccccc;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3);
}
.k-chart .k-left-handle {
  left: -8px;
}
.k-chart .k-right-handle {
  right: -8px;
}
.k-chart .k-navigator-hint .k-tooltip {
  border: 3px solid #ffffff;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
  background: #ffffff;
  color: #676767;
}
.k-chart .k-navigator-hint .k-scroll {
  background: rgba(0, 124, 204, 0.7);
  height: 4px;
}
.k-chart .k-tooltip,
.k-sparkline .k-tooltip,
.k-chart-crosshair-tooltip,
.k-chart-shared-tooltip {
  background-image: none;
}
.k-map .k-marker {
  font-size: 28px;
  color: #428bca;
}
@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {
  .k-map .k-marker {
    background-image: url("https://kendo.cdn.telerik.com/2018.3.911/styles/Bootstrap/markers_2x.png");
  }
}
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #ffffff;
}
.k-spreadsheet-top-corner,
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #ebebeb;
  background-image: none;
  color: #000000;
  border-color: #b8b8b8;
}
.k-spreadsheet-top-corner {
  border-color: #b8b8b8;
}
.k-spreadsheet-top-corner:after {
  border-color: transparent #b8b8b8 #b8b8b8 transparent;
}
.k-spreadsheet-pane {
  border-color: #b8b8b8;
}
.k-spreadsheet-pane .k-spreadsheet-vaxis,
.k-spreadsheet-pane .k-spreadsheet-haxis {
  border-color: #d2d2d2;
}
.k-spreadsheet-pane .k-spreadsheet-column-header,
.k-spreadsheet-pane .k-spreadsheet-row-header {
  border-color: #b8b8b8;
}
.k-spreadsheet-pane .k-spreadsheet-merged-cell {
  background-color: #ffffff;
}
.k-spreadsheet-pane .k-selection-partial,
.k-spreadsheet-pane .k-selection-full {
  border-color: rgba(66, 139, 202, 0.2);
  background-color: rgba(66, 139, 202, 0.2);
}
.k-spreadsheet-pane .k-filter-range {
  border-color: #428bca;
}
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {
  border-bottom-color: #428bca;
}
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {
  border-right-color: #428bca;
}
.k-auto-fill,
.k-spreadsheet-selection {
  border-color: #428bca;
  box-shadow: inset 0 0 0 1px #428bca;
}
.k-auto-fill-wrapper .k-tooltip {
  background: #ffffff;
}
.k-spreadsheet-selection {
  background-color: rgba(66, 139, 202, 0.2);
}
.k-spreadsheet-active-cell {
  box-shadow: inset 0 0 0 1px #428bca;
  background-color: #ffffff;
}
.k-spreadsheet-active-cell.k-right {
  box-shadow: inset 0 0 0 1px #428bca, inset -1px 0 0 1px #428bca;
}
.k-spreadsheet-active-cell.k-bottom {
  box-shadow: inset 0 0 0 1px #428bca, inset 0 -1px 0 1px #428bca;
}
.k-spreadsheet-active-cell.k-bottom.k-right {
  box-shadow: inset 0 0 0 1px #428bca, inset -1px -1px 0 1px #428bca;
}
.k-spreadsheet-active-cell.k-single {
  color: #333333;
  background-color: #ffffff;
}
.k-spreadsheet .k-spreadsheet-action-bar {
  background-color: #ffffff;
  border-color: #cccccc;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-name-editor {
  border-color: #b8b8b8;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-formula-bar::before {
  border-color: #b8b8b8;
}
.k-spreadsheet .k-spreadsheet-formula-input {
  background-color: #ffffff;
  color: #333333;
}
.k-spreadsheet .k-resize-handle,
.k-spreadsheet .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-marker {
  background-color: #428bca;
}
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {
  background-color: #428bca;
}
.k-spreadsheet .k-single-selection::after {
  background-color: #428bca;
  border-color: #ffffff;
}
.k-spreadsheet .k-auto-fill-punch {
  background-color: rgba(255, 255, 255, 0.5);
}
.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {
  background-color: rgba(66, 139, 202, 0.5);
}
.k-spreadsheet-format-cells .k-spreadsheet-preview {
  border-color: #cccccc;
}
.k-spreadsheet-filter {
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: inset 0 0 0 1px #d2d2d2;
}
.k-spreadsheet-filter.k-state-active {
  color: #ffffff;
  background-color: #428bca;
}
.k-spreadsheet-filter:hover {
  color: #333333;
  background: #ebebeb;
  border-color: #d7d7d7;
}
.k-action-window .k-action-buttons {
  border-color: #cccccc;
  background: #f5f5f5;
}
.k-spreadsheet-sample {
  color: #808080;
}
.k-state-selected .k-spreadsheet-sample {
  color: inherit;
}
.k-spreadsheet-window .k-list {
  border-color: #cccccc;
  border-radius: 4px;
}
.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button:not(.k-toggle-button) {
  border-radius: 4px;
}
.k-spreadsheet-toolbar > .k-widget,
.k-spreadsheet-toolbar > .k-button,
.k-spreadsheet-toolbar > .k-button-group {
  border-radius: 4px;
}
.k-spreadsheet-toolbar > .k-separator {
  border-color: #cccccc;
}
.k-spreadsheet-toolbar .k-overflow-anchor {
  border-radius: 0;
}
.k-spreadsheet-popup {
  border-radius: 4px;
}
.k-spreadsheet-popup .k-separator {
  background-color: #cccccc;
}
.k-spreadsheet-popup .k-button {
  background-color: transparent;
}
.k-spreadsheet-popup .k-button:hover {
  background-color: #ebebeb;
}
.k-spreadsheet-popup .k-state-active {
  background-color: #428bca;
  color: #000000;
}
.k-spreadsheet-popup .k-state-active:hover {
  background-color: #3071a9;
}
.k-spreadsheet-filter-menu .k-details {
  border-color: #cccccc;
}
.k-spreadsheet-filter-menu .k-details-content .k-space-right {
  background-color: #ffffff;
}
.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {
  background-color: #ffffff;
  border-color: #cccccc;
  border-radius: 4px 0 0 4px;
}
.k-syntax-ref {
  color: #ff8822;
}
.k-syntax-num {
  color: #0099ff;
}
.k-syntax-func {
  font-weight: bold;
}
.k-syntax-str {
  color: #38b714;
}
.k-syntax-error {
  color: red;
}
.k-syntax-bool {
  color: #a9169c;
}
.k-syntax-startexp {
  font-weight: bold;
}
.k-syntax-paren-match {
  background-color: #caf200;
}
.k-series-a {
  border-color: #428bca;
  background-color: rgba(66, 139, 202, 0.15);
}
.k-series-b {
  border-color: #5bc0de;
  background-color: rgba(91, 192, 222, 0.15);
}
.k-series-c {
  border-color: #5cb85c;
  background-color: rgba(92, 184, 92, 0.15);
}
.k-series-d {
  border-color: #f2b661;
  background-color: rgba(242, 182, 97, 0.15);
}
.k-series-e {
  border-color: #e67d4a;
  background-color: rgba(230, 125, 74, 0.15);
}
.k-series-f {
  border-color: #da3b36;
  background-color: rgba(218, 59, 54, 0.15);
}
.k-spreadsheet-sheets-remove:hover .k-icon {
  color: #cc2222;
}
.k-spreadsheet-formula-list .k-state-focused {
  background-color: #428bca;
  color: #ffffff;
}
.k-spreadsheet .k-widget[data-property='fontSize'] {
  width: 70px;
}
.k-spreadsheet .k-widget[data-property='format'] {
  width: 100px;
}
.k-spreadsheet .k-widget[data-property='fontFamily'] {
  width: 130px;
}
.k-window .k-popup-edit-form .k-edit-field input.k-checkbox {
  margin-top: 1.1em;
}
.k-window .k-popup-edit-form .k-edit-field label.k-checkbox-label {
  margin-top: 0.7em;
}
.k-dialog .k-window-titlebar {
  background: none;
  border-color: #ebebeb;
}
.k-dialog a.k-dialog-action.k-dialog-close {
  cursor: pointer;
  z-index: 10000;
}
.k-dialog .k-content {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.k-dialog.k-dialog-titleless .k-content {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button {
  padding: 12px;
  border-color: #ebebeb;
}
.k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal {
  border-top: 1px solid #ebebeb;
  border-radius: 4px;
}
.k-rtl .k-dialog a.k-dialog-action.k-dialog-close {
  left: 0.5em;
}
.k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button:first-child {
  border-bottom-right-radius: 4px;
}
.k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button:last-child {
  border-bottom-left-radius: 4px;
}
.k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal {
  text-align: left;
}
.k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal .k-button {
  margin-left: 0;
  margin-right: 0.5em;
}
.k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal .k-button:first-child {
  margin-right: 0;
}
.k-editor-dialog .k-tabstrip-items {
  border-color: #cccccc;
}
.k-numeric-wrap .k-i-warning {
  color: #d92800;
  position: absolute;
  top: 0;
  right: 1.9em;
  width: 1.9em;
}
.k-numeric-wrap.k-state-invalid {
  border-color: #d92800;
}
.k-numeric-wrap.k-state-invalid .k-select {
  border-color: #d92800;
}
.k-numeric-wrap.k-state-invalid input {
  color: #d92800;
}
.k-rtl .k-numeric-wrap.k-state-invalid .k-i-warning {
  right: auto;
  left: 1.9em;
}
.k-maskedtextbox.k-state-invalid .k-textbox {
  border-color: #d92800;
  color: #d92800;
}
.k-maskedtextbox.k-state-invalid .k-i-warning {
  color: #d92800;
}
.k-dateinput.k-state-invalid .k-textbox {
  color: #d92800;
  border-color: #d92800;
}
.k-dateinput.k-state-invalid .k-i-warning {
  margin-left: 0;
  margin-right: 0.7em;
  color: #d92800;
}
.k-rtl .k-dateinput .k-i-warning {
  margin-right: 0;
  margin-left: 0.7em;
}
.k-datepicker .k-picker-wrap.k-state-invalid,
.k-timepicker .k-picker-wrap.k-state-invalid {
  border-color: #d92800;
}
.k-datepicker .k-picker-wrap.k-state-invalid .k-input,
.k-timepicker .k-picker-wrap.k-state-invalid .k-input {
  color: #d92800;
}
.k-datepicker .k-picker-wrap .k-i-warning,
.k-timepicker .k-picker-wrap .k-i-warning {
  color: #d92800;
  margin-left: 0;
  margin-right: 2.1em;
}
.k-rtl .k-datepicker .k-picker-wrap .k-i-warning,
.k-rtl .k-timepicker .k-picker-wrap .k-i-warning {
  margin-right: 0;
  margin-left: 2.1em;
}
.k-datetimepicker .k-picker-wrap.k-state-invalid {
  border-color: #d92800;
}
.k-datetimepicker .k-picker-wrap.k-state-invalid .k-input {
  color: #d92800;
}
.k-datetimepicker .k-picker-wrap .k-i-warning {
  color: #d92800;
  margin-left: 0;
  margin-right: 4.3em;
}
.k-rtl .k-datetimepicker .k-picker-wrap .k-icon.k-i-warning {
  margin-right: 0;
  margin-left: 4.3em;
}
.k-listbox .k-list-scroller {
  padding: 2px;
  border-color: #cccccc;
  background-color: #ffffff;
}
.k-listbox .k-item:hover {
  border-color: transparent;
}
.k-listbox .k-item.k-state-focused {
  border-radius: 4px;
  box-shadow: inset 0 0 2px 0 #428bca, inset 0 0 7px 0 #76abd9;
}
.k-listbox .k-drop-hint {
  height: 0;
  border-top: 1px solid #428bca;
}
.k-grid-header .k-i-sort-asc-sm,
.k-grid-header .k-i-sort-desc-sm,
.k-grid-header .k-sort-order {
  color: #428bca;
}
.k-menu-scroll-button {
  border-color: #cccccc;
  color: #333333;
  background-position: 50% 50%;
  background-color: #f5f5f5;
}
.k-multicheck-wrap .k-item .k-label {
  font-weight: 400;
  margin-bottom: 0;
}
.k-dropdowngrid-popup {
  border-color: #cccccc;
  color: #333333;
  background-color: #ffffff;
}
.k-dropdowngrid-popup .k-header {
  border-color: #cccccc;
  color: #333333;
  background-color: #f5f5f5;
}
.k-dropdowngrid-popup .k-group-header {
  border-color: #cccccc;
  color: #333333;
  background-color: #f5f5f5;
}
.k-dropdowngrid-popup .k-cell {
  border-color: #cccccc;
}
.k-dropdowngrid-popup .k-item:nth-child(2n) {
  background-color: #f5f5f5;
}
.k-dropdowngrid-popup .k-footer {
  border-color: #cccccc;
  color: #333333;
  background-color: #f5f5f5;
}
.k-dropdowngrid-popup .k-item.k-state-hover {
  border-color: #cccccc;
  color: #333333;
  background-color: #ebebeb;
}
.k-dropdowngrid-popup .k-item.k-state-selected {
  border-color: #cccccc;
  color: #ffffff;
  background-color: #428bca;
}
.k-dropdowngrid-popup .k-group-cell span {
  background-color: #333333;
  color: #ffffff;
}
.k-grid-list > .k-item.k-last > .k-cell,
.k-grid-list > .k-item.k-last > .k-group-cell,
.k-grid-list > .k-item.k-last > .k-spacer-cell {
  border-bottom-color: #333333;
}
